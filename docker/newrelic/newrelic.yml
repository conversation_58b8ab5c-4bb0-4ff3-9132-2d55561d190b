# This file configures the New Relic agent.  New Relic monitors
# Java applications with deep visibility and low overhead.  For more details and additional
# configuration options visit https://docs.newrelic.com/docs/agents/java-agent/configuration/java-agent-configuration-config-file.
#
# <%= generated_for_user %>
#
# This section is for settings common to all environments.
# Do not add anything above this next line.
common: &default_settings

  # ============================== LICENSE KEY ===============================
  # You must specify the license key associated with your New Relic
  # account. For example, if your license key is 12345 use this:
  # license_key: '12345'
  # The key binds your agent's data to your account in the New Relic service.
  license_key: 'eu01xx4f48f215b1fd525714e8929d91FFFFNRAL'

  # Agent Enabled
  # Use this setting to disable the agent instead of removing it from the startup command.
  # Default is true.
  agent_enabled: true

  # Set the name of your application as you'd like it show up in New Relic.
  # If enable_auto_app_naming is false, the agent reports all data to this application.
  # Otherwise, the agent reports only background tasks (transactions for non-web applications)
  # to this application. To report data to more than one application
  # (useful for rollup reporting), separate the application names with ";".
  # For example, to report data to "My Application" and "My Application 2" use this:
  # app_name: My Application;My Application 2
  # This setting is required. Up to 3 different application names can be specified.
  # The first application name must be unique.
  app_name: 'fabriqon-be'

  # To enable high security, set this property to true. When in high
  # security mode, the agent will use SSL and obfuscated SQL. Additionally,
  # request parameters and message parameters will not be sent to New Relic.
  high_security: false

  # Set to true to enable support for auto app naming.
  # The name of each web app is detected automatically
  # and the agent reports data separately for each one.
  # This provides a finer-grained performance breakdown for
  # web apps in New Relic.
  # Default is false.
  enable_auto_app_naming: false

  # Set to true to enable component-based transaction naming.
  # Set to false to use the URI of a web request as the name of the transaction.
  # Default is true.
  enable_auto_transaction_naming: true

  # The agent uses its own log file to keep its logging
  # separate from that of your application.  Specify the log level here.
  # This setting is dynamic, so changes do not require restarting your application.
  # The levels in increasing order of verboseness are:
  #   off, severe, warning, info, fine, finer, finest
  # Default is info.
  log_level: info

  # Log all data sent to and from New Relic in plain text.
  # This setting is dynamic, so changes do not require restarting your application.
  # Default is false.
  audit_mode: false

  # The number of backup log files to save.
  # Default is 1.
  log_file_count: 1

  # The maximum number of kbytes to write to any one log file.
  # The log_file_count must be set greater than 1.
  # Default is 0 (no limit).
  log_limit_in_kbytes: 0

  # Override other log rolling configuration and roll the logs daily.
  # Default is false.
  log_daily: false

  # The name of the log file.
  # Default is newrelic_agent.log.
  log_file_name: newrelic_agent.log

  # The log file directory.
  # Default is the logs directory in the newrelic.jar parent directory.
  #log_file_path:

  # Provides the ability to forward application logs to New Relic, generate log usage metrics,
  # and decorate local application log files with agent metadata for use with third party log forwarders.
  # The application_logging.forwarding and application_logging.local_decorating should not be used together.
  application_logging:

    # Provides control over all the application logging features for forwarding, local log
    # decorating, and metrics features. Set as false to disable all application logging features.
    # Default is true.
    enabled: true

    # The agent will automatically forward application logs to New Relic in
    # a format that includes agent metadata for linking them to traces and errors.
    forwarding:

      # When true, application logs will be forwarded to New Relic. The default is true.
      enabled: true
      context_data:
        enabled: true

      # Application log events are collected up to the configured amount. Afterwards,
      # events are sampled to maintain an even distribution across the harvest cycle.
      # Default is 10000. Setting to 0 will disable.
      #max_samples_stored: 10000

    # The agent will generate metrics to indicate the number of
    # application log events occurring at each distinct log level.
    metrics:

      # When true, application log metrics will be reported. The default is true.
      enabled: true

      # The agent will add linking metadata to each log line in your application log files.
      # This feature should only be used if you want to use a third party log forwarder, instead
      # of the agent's built-in forwarding feature, to send your application log events to New Relic.
      #local_decorating:

      # When true, the agent will decorate your application log files with linking metadata. The default is false.
      #enabled: false

  # Proxy settings for connecting to the New Relic server:
  # If a proxy is used, the host setting is required.  Other settings
  # are optional.  Default port is 8080.  The username and password
  # settings will be used to authenticate to Basic Auth challenges
  # from a proxy server. Proxy scheme will allow the agent to
  # connect through proxies using the HTTPS scheme.
  #proxy_host: hostname
  #proxy_port: 8080
  #proxy_user: username
  #proxy_password: password
  #proxy_scheme: https

  # Limits the number of lines to capture for each stack trace.
  # Default is 30
  max_stack_trace_lines: 30

  # Provides the ability to configure the attributes sent to New Relic. These
  # attributes can be found in transaction traces, traced errors, Insight's
  # transaction events, and Insight's page views.
  attributes:

    # When true, attributes will be sent to New Relic. The default is true.
    enabled: true

    #A comma separated list of attribute keys whose values should
    # be sent to New Relic.
    #include:

    # A comma separated list of attribute keys whose values should
    # not be sent to New Relic.
    #exclude:

  # Transaction tracer captures deep information about slow
  # transactions and sends this to the New Relic service once a
  # minute. Included in the transaction is the exact call sequence of
  # the transactions including any SQL statements issued.
  transaction_tracer:

    # Transaction tracer is enabled by default. Set this to false to turn it off.
    # This feature is not available to Lite accounts and is automatically disabled.
    # Default is true.
    enabled: true

    # Threshold in seconds for when to collect a transaction
    # trace. When the response time of a controller action exceeds
    # this threshold, a transaction trace will be recorded and sent to
    # New Relic. Valid values are any float value, or (default) "apdex_f",
    # which will use the threshold for the "Frustrated" Apdex level
    # (greater than four times the apdex_t value).
    # Default is apdex_f.
    transaction_threshold: apdex_f

    # When transaction tracer is on, SQL statements can optionally be
    # recorded. The recorder has three modes, "off" which sends no
    # SQL, "raw" which sends the SQL statement in its original form,
    # and "obfuscated", which strips out numeric and string literals.
    # Default is obfuscated.
    record_sql: raw

    # Set this to true to log SQL statements instead of recording them.
    # SQL is logged using the record_sql mode.
    # Default is false.
    log_sql: false

    # Threshold in seconds for when to collect stack trace for a SQL
    # call. In other words, when SQL statements exceed this threshold,
    # then capture and send to New Relic the current stack trace. This is
    # helpful for pinpointing where long SQL calls originate from.
    # Default is 0.5 seconds.
    stack_trace_threshold: 0.5

    # Determines whether the agent will capture query plans for slow
    # SQL queries. Only supported for MySQL and PostgreSQL.
    # Default is true.
    explain_enabled: true

    # Threshold for query execution time below which query plans will not
    # not be captured.  Relevant only when `explain_enabled` is true.
    # Default is 0.5 seconds.
    explain_threshold: 0.5

    # Use this setting to control the variety of transaction traces.
    # The higher the setting, the greater the variety.
    # Set this to 0 to always report the slowest transaction trace.
    # Default is 20.
    top_n: 20

  # Error collector captures information about uncaught exceptions and
  # sends them to New Relic for viewing.
  error_collector:

    # This property enables the collection of errors. If the property is not
    # set or the property is set to false, then errors will not be collected.
    # Default is true.
    enabled: true

    # Use this property to exclude specific exceptions from being reported as errors
    # by providing a comma separated list of full class names.
    # The default is to exclude akka.actor.ActorKilledException. If you want to override
    # this, you must provide any new value as an empty list is ignored.
    ignore_errors: akka.actor.ActorKilledException

    # Use this property to exclude specific http status codes from being reported as errors
    # by providing a comma separated list of status codes.
    # The default is to exclude 404s. If you want to override
    # this, you must provide any new value as an empty list is ignored.
    ignore_status_codes: 404

  # Transaction Events are used for Histograms and Percentiles. Un-aggregated data is collected
  # for each web transaction and sent to the server on harvest.
  transaction_events:

    # Set to false to disable transaction events.
    # Default is true.
    enabled: true

    # Events are collected up to the configured amount. Afterwards, events are sampled to
    # maintain an even distribution across the harvest cycle.
    # Default is 2000.  Setting to 0 will disable.
    max_samples_stored: 2000

  # Distributed tracing lets you see the path that a request takes through your distributed system.
  # This replaces the legacy Cross Application Tracing feature.
  distributed_tracing:

    # Set to false to disable distributed tracing.
    # Default is true.
    enabled: true

    # Agent versions 5.10.0+ utilize both the newrelic header and W3C Trace Context headers for distributed tracing.
    # The newrelic distributed tracing header allows interoperability with older agents that don't support W3C Trace Context headers.
    # Agent versions that support W3C Trace Context headers will prioritize them over newrelic headers for distributed tracing.
    # If you do not want to utilize the newrelic header, setting this to true will result in the agent excluding the newrelic header
    # and only using W3C Trace Context headers for distributed tracing.
    # Default is false.
    exclude_newrelic_header: false

  # New Relic's distributed tracing UI uses Span events to disPlay traces across different services.
  # Span events capture attributes that describe execution context and provide linking metadata.
  # Span events require distributed tracing to be enabled.
  span_events:

    # Set to false to disable Span events.
    # Default is true.
    enabled: true

    # Determines the number of Span events that can be captured during an agent harvest cycle.
    # Increasing the number of Span events can lead to additional agent overhead. A maximum value may be imposed server side by New Relic.
    # Default is 2000
    max_samples_stored: 2000

    # Provides the ability to filter the attributes attached to Span events.
    # Custom attributes can be added to Span events using the NewRelic.getAgent().getTracedMethod().addCustomAttribute(...) API.
    attributes:

      # When true, attributes will be sent to New Relic. The default is true.
      enabled: true

      # A comma separated list of attribute keys whose values should be sent to New Relic.
      #include:

      # A comma separated list of attribute keys whose values should not be sent to New Relic.
      #exclude:

  # Cross Application Tracing adds request and response headers to
  # external calls using supported HTTP libraries to provide better
  # performance data when calling applications monitored by other New Relic agents.
  #
  # Distributed tracing is replacing cross application tracing as the default
  # means of tracing between services. To continue using cross application
  # tracing, enable it with `cross_application_tracer.enabled = true` and
  # `distributed_tracing.enabled = false`
  cross_application_tracer:

    # Set to true to enable cross application tracing.
    # Default is false.
    enabled: false

  # Thread profiler measures wall clock time, CPU time, and method call counts
  # in your application's threads as they run.
  # This feature is not available to Lite accounts and is automatically disabled.
  thread_profiler:

    # Set to false to disable the thread profiler.
    # Default is true.
    enabled: true

  # New Relic Real User Monitoring gives you insight into the performance real users are
  # experiencing with your website. This is accomplished by measuring the time it takes for
  # your users' browsers to download and render your web pages by injecting a small amount
  # of JavaScript code into the header and footer of each page.
  browser_monitoring:

    # By default the agent automatically inserts API calls in compiled JSPs to
    # inject the monitoring JavaScript into web pages. Not all rendering engines are supported.
    # See https://docs.newrelic.com/docs/agents/java-agent/instrumentation/new-relic-browser-java-agent#manual_instrumentation
    # for instructions to add these manually to your pages.
    # Set this attribute to false to turn off this behavior.
    auto_instrument: true

  # Class transformer can be used to disable all agent instrumentation or specific instrumentation modules.
  # All instrumentation modules can be found here: https://github.com/newrelic/newrelic-java-agent/tree/main/instrumentation
  class_transformer:

    # This instrumentation reports the name of the user principal returned from
    # HttpServletRequest.getUserPrincipal() when servlets and filters are invoked.
    com.newrelic.instrumentation.servlet-user:
      enabled: false

    com.newrelic.instrumentation.spring-aop-2:
      enabled: false

    # This instrumentation reports metrics for resultset operations.
    com.newrelic.instrumentation.jdbc-resultset:
      enabled: false

    # Classes loaded by classloaders in this list will not be instrumented.
    # This is a useful optimization for runtimes which use classloaders to
    # load dynamic classes which the agent would not instrument.
    classloader_excludes:
      groovy.lang.GroovyClassLoader$InnerLoader,
      org.codehaus.groovy.runtime.callsite.CallSiteClassLoader,
      com.collaxa.cube.engine.deployment.BPELClassLoader,
      org.springframework.data.convert.ClassGeneratingEntityInstantiator$ObjectInstantiatorClassGenerator,
      org.mvel2.optimizers.impl.asm.ASMAccessorOptimizer$ContextClassLoader,
      gw.internal.gosu.compiler.SingleServingGosuClassLoader,

  # Real-time profiling using Java Flight Recorder (JFR).
  # This feature reports dimensional metrics to the ingest endpoint configured by
  # metric_ingest_uri and events to the ingest endpoint configured by event_ingest_uri.
  # Both ingest endpoints default to US production but they will be automatically set to EU
  # production when using an EU license key. Other ingest endpoints can be configured manually.
  # Requires a JVM that provides the JFR library.
  jfr:

    # Set to true to enable Real-time profiling with JFR.
    # Default is false.
    enabled: false

    # Set to true to enable audit logging which will disPlay all JFR metrics and events in each harvest batch.
    # Audit logging is extremely verbose and should only be used for troubleshooting purposes.
    # Default is false.
    audit_logging: false

  # User-configurable custom labels for this agent.  Labels are name-value pairs.
  # There is a maximum of 64 labels per agent.  Names and values are limited to 255 characters.
  # Names and values may not contain colons (:) or semicolons (;).
  labels:

  # An example label
  #label_name: label_value

# Application Environments
# ------------------------------------------
# Environment specific settings are in this section.
# You can use the environment to override the default settings.
# For example, to change the app_name setting.
# Use -Dnewrelic.environment=<environment> on the Java startup command line
# to set the environment.
# The default environment is production.

# NOTE if your application has other named environments, you should
# provide configuration settings for these environments here.

development:
  <<: *default_settings
  app_name: 'fabriqon-be (Development)'
  agent_enabled: false

test:
  <<: *default_settings
  app_name: 'fabriqon-be (Test)'

production:
  <<: *default_settings

staging:
  <<: *default_settings
  app_name: 'fabriqon-be (Staging)'