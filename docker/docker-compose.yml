version: '3'
services:
  fabriqon:
    image: fabriqon/backend:latest
    ports:
      - 8080:8080
      - 5005:5005
    volumes:
      - ${PWD}/local-config/fabriqon-app-prod-firebase-adminsdk-urti0-6b1cacbada.json:/usr/app/fabriqon-app-prod-firebase-adminsdk-urti0-6b1cacbada.json
    environment:
      - SPRING_DATASOURCE_URL=*****************************************************
      - FABRIQON_HTML2PDF_URL=http://host.docker.internal:9999/convert
      - SPRING_MAIL_HOST=host.docker.internal
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:5005,server=y,suspend=n
      - GOOGLE_APPLICATION_CREDENTIALS=/usr/app/fabriqon-app-prod-firebase-adminsdk-urti0-6b1cacbada.json
    env_file:
      - ${PWD}/local-config/local.env
