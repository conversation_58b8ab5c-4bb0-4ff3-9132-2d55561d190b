ARG ENVIRONMENT=development

FROM maven:3.9.9 as maven
ENV HOME=/usr/app
RUN mkdir -p $HOME
WORKDIR $HOME
ADD . $HOME
RUN --mount=type=cache,target=/root/.m2 mvn -f $HOME/pom.xml clean package -DskipTests

FROM eclipse-temurin:21
ENV HOME=/usr/app
RUN mkdir -p $HOME
WORKDIR $HOME
COPY --from=maven $HOME/application/target/*.jar $HOME/app.jar

#add newrelic
RUN mkdir -p /usr/local/newrelic
ADD ./docker/newrelic/newrelic.jar /usr/local/newrelic/newrelic.jar
ADD ./docker/newrelic/newrelic.yml /usr/local/newrelic/newrelic.yml

#RUN addgroup -S spring && adduser -S spring -G spring
#USER spring:spring

CMD java -javaagent:/usr/local/newrelic/newrelic.jar -Dnewrelic.environment=$ENVIRONMENT -jar app.jar