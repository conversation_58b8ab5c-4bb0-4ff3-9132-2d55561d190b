COMPOSE_BACKING_FILE:=-f docker/docker-compose.backing.yml
COMPOSE_FILE:=$(COMPOSE_BACKING_FILE) -f docker/docker-compose.yml
DOCKER_IMAGE_NAME:=fabriqon/backend

.PHONY: help
help:
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

.PHONY: build
build: ## Builds a docker image with a fat jar file
	docker build -t $(DOCKER_IMAGE_NAME) -f docker/Dockerfile .

.PHONY: up
up: ## Starts the application and all dependent services
	docker-compose $(COMPOSE_FILE) up -d

.PHONY: up_backing
up_backing: ## Starts all dependent services
	docker-compose $(COMPOSE_BACKING_FILE) up -d

.PHONY: down
down: ## Tears down the application and dependent services & removing volumes
	docker-compose $(COMPOSE_FILE) down --rmi local --volumes

.PHONY: down_backing
down_backing: ## Tears down dependent services & removing volumes
	docker-compose $(COMPOSE_BACKING_FILE) down --rmi local --volumes

.PHONY: attach
attach: ## Attach bash shell to running application container
	docker-compose exec fabriqon bash

.PHONY: shell
shell: ## Starts a bash shell inside application container
	docker run -ti $(DOCKER_IMAGE_NAME) bash

.PHONY: local_build
local_build: ## Builds the application locally
	./mvnw clean package

.PHONY: local_run
local_run: ## Runs the application locally
	./mvnw spring-boot:run -Dspring-boot.run.profiles=dev

.PHONY: snapshot_data_do_test
snapshot_data_do_test: ## makes a snapshot of the data on do_test
	docker run --rm -e PGPASSWORD=password -w /snapshot -v "$(shell pwd)"/db_snapshot:/snapshot -ti postgres:14 pg_dump -U postgres -h ************** -p 54329 -F t fabriqon -f /snapshot/snapshot.tar

.PHONY: load_snapshot_data_do_test
load_snapshot_data_do_test: ## restores the snapshot
	docker run --rm -e PGPASSWORD=password -w /snapshot -v "$(shell pwd)"/db_snapshot:/snapshot -ti postgres:14 pg_restore -U postgres -h ************** -p 54329 -d fabriqon /snapshot/snapshot.tar

.PHONY: load_snapshot_data_local
load_snapshot_data_local: ## restores the snapshot to local
	docker run --rm -e PGPASSWORD=password -w /snapshot -v "$(shell pwd)"/db_snapshot:/snapshot -ti postgres:14 pg_restore -U postgres -h host.docker.internal -p 54329 -d demo /snapshot/snapshot.tar