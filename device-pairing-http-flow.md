# Device Pairing HTTP Flow Documentation

This document describes the HTTP flow for device pairing in the Fabriqon system. It can be used as a reference for implementing a JavaScript HTTP client.

## Overview

The device pairing process involves three main steps:
1. Initiating the pairing process by getting a pairing link for an employee
2. Using that link to get a confirmation token
3. Confirming the pairing with the token to get an access token
4. Using the access token for subsequent API calls

There's also an optional step for removing a device pairing.

## Detailed Flow

### 1. Initiate Device Pairing

This endpoint initiates the device pairing process for a specific employee.

**Request:**
```
GET /employees/{employeeId}/initiate-device-pairing
```

**Headers:**
```
Authorization: Bearer {accountToken}
```

**Path Parameters:**
- `employeeId`: UUID of the employee to pair a device with

**Response:**
```json
{
  "token": "string",
  "pairingLink": "URL"
}
```

**Example Response:**
```json
{
  "token": "550e8400-e29b-41d4-a716-************",
  "pairingLink": "https://example.com/pairing/confirmation/550e8400-e29b-41d4-a716-************"
}
```

### 2. Get Confirmation Token

This endpoint uses the token from the pairing link to get a confirmation token.

**Request:**
```
GET /fabrication/devices/pairing/initiate/{token}
```

**Headers:**
```
Authorization: Bearer {accountToken}
```

**Path Parameters:**
- `token`: The token extracted from the pairingLink URL

**Response:**
```json
{
  "employeeName": "string",
  "confirmationToken": "string"
}
```

**Example Response:**
```json
{
  "employeeName": "John Doe",
  "confirmationToken": "550e8400-e29b-41d4-a716-************"
}
```

### 3. Confirm Pairing

This endpoint confirms the pairing with the confirmation token and returns an access token.

**Request:**
```
POST /fabrication/devices/pairing/confirm
```

**Headers:**
```
Authorization: Bearer {accountToken}
Content-Type: application/json
```

**Request Body:**
```json
{
  "token": "string"
}
```

**Example Request Body:**
```json
{
  "token": "550e8400-e29b-41d4-a716-************"
}
```

**Response:**
```json
{
  "token": "string"
}
```

**Example Response:**
```json
{
  "token": "550e8400-e29b-41d4-a716-************"
}
```

### 4. Using the Access Token

After obtaining the access token, it can be used for subsequent API calls by including it in the `X-Auth` header.

**Example:**
```
GET /fabrication/manufacturing/tasks/list
```

**Headers:**
```
X-Auth: {accessToken}
Content-Type: application/json
```

### 5. Remove Pairing (Optional)

This endpoint removes a device pairing.

**Request:**
```
POST /fabrication/devices/pairing/remove
```

**Headers:**
```
Authorization: Bearer {accountToken}
Content-Type: application/json
```

**Request Body:**
```json
{
  "token": "string"
}
```

**Example Request Body:**
```json
{
  "token": "550e8400-e29b-41d4-a716-************"
}
```