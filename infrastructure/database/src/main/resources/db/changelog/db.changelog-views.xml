<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="add-inventory-view" author="csaba.birtalan" runOnChange="true">
        <sql>
            CREATE OR REPLACE VIEW inventory_entries_view AS
            SELECT inventory.owner_id,
                   inventory.create_time,
                   inventory.sales_order_id,
                   inventory.manufacturing_order_id,
                   inventory.reception_receipt_id,
                   inventory.supplier_id,
                   (inventory.reception_price_amount * ABS(inventory.quantity)) / 100 AS "reception_price",
                   (inventory.exit_price_amount * ABS(inventory.quantity)) / 100      AS "exit_price",
                   inventory.quantity,
                   material_good.name                                                 AS "item_name",
                   category.details ->> 'name'                                        AS "category",
                   'PRODUCT'                                                          AS "product"
            FROM inventory
                     JOIN material_good ON inventory.material_good_id = material_good.id
                     JOIN category ON material_good.category_id = category.id
            UNION ALL
            SELECT executed_services.owner_id,
                   executed_services.create_time,
                   executed_services.sales_order_id,
                   executed_services.servicing_order_id,
                   NULL::uuid                                                         AS "reception_receipt_id",
                   NULL::uuid                                                         AS "supplier_id",
                   (executed_services.cost_amount * executed_services.quantity) / 100 AS "reception_price",
                   (executed_services.sale_amount * executed_services.quantity) / 100 AS "exit_price",
                   executed_services.quantity,
                   service_template.name                                              AS "item_name",
                   'SERVICE'                                                          AS "category",
                   'SERVICE'                                                          AS "product"
            FROM executed_services
                     JOIN service_template ON executed_services.service_id = service_template.id;
        </sql>
    </changeSet>

    <changeSet id="add-sales-view" author="csaba.birtalan" runOnChange="true">
        <sql>
            CREATE OR REPLACE VIEW sales_orders_view AS
            SELECT inventory.owner_id,
                   inventory.create_time,
                   inventory.sales_order_id,
                   (inventory.exit_price_amount * ABS(inventory.quantity)) / 100 AS "exit_price",
                   inventory.quantity,
                   company.company_name                                          AS "customer",
                   'PRODUCT'                                                     AS "product"
            FROM inventory
                     JOIN sales_order ON inventory.sales_order_id = sales_order.id
                     JOIN company ON sales_order.customer_id = company.id
            WHERE inventory.sales_order_id is not null
            UNION ALL
            SELECT executed_services.owner_id,
                   executed_services.create_time,
                   executed_services.sales_order_id,
                   (executed_services.sale_amount * executed_services.quantity) / 100 AS "exit_price",
                   executed_services.quantity,
                   company.company_name                                               AS "customer",
                   'SERVICE'                                                          AS "product"
            FROM executed_services
                     JOIN sales_order ON executed_services.sales_order_id = sales_order.id
                     JOIN company ON sales_order.customer_id = company.id;
        </sql>
    </changeSet>

    <changeSet id="add-manufacturing-tasks-view" author="csaba.birtalan" runOnChange="true">
        <sql>
            CREATE OR REPLACE VIEW manufacturing_tasks_view AS
            SELECT manufacturing_task.owner_id,
                   manufacturing_task.end_time          AS "execution_time",
                   manufacturing_task.details -> 'name' AS "task_name",
                   users.name                           AS "employee_name",
                   manufacturing_order.quantity         AS "quantity",
                   material_good.name                   AS "product_name",
                   'PRODUCT'                            AS "product"
            FROM manufacturing_task
                     JOIN manufacturingtask_employee ON manufacturing_task.id = manufacturingtask_employee.manufacturing_task_id
                     JOIN users ON manufacturingtask_employee.user_id = users.id
                     JOIN manufacturing_order ON manufacturing_task.manufacturing_order_id = manufacturing_order.id
                     JOIN material_good ON manufacturing_order.product_id = material_good.id
            WHERE manufacturing_task.status = 'DONE'
            UNION ALL
            SELECT manufacturing_task.owner_id,
                   manufacturing_task.end_time          AS "execution_time",
                   manufacturing_task.details -> 'name' AS "task_name",
                   users.name                           AS "employee_name",
                   manufacturing_order.quantity         AS "quantity",
                   service_template.name                AS "product_name",
                   'SERVICE'                            AS "product"
            FROM manufacturing_task
                     JOIN manufacturingtask_employee ON manufacturing_task.id = manufacturingtask_employee.manufacturing_task_id
                     JOIN users ON manufacturingtask_employee.user_id = users.id
                     JOIN manufacturing_order ON manufacturing_task.manufacturing_order_id = manufacturing_order.id
                     JOIN service_template ON manufacturing_order.service_id = service_template.id
            WHERE manufacturing_task.status = 'DONE';
        </sql>
    </changeSet>

    <changeSet id="add-purchases-view" author="csaba.birtalan" runOnChange="true">
        <sql>
            CREATE OR REPLACE VIEW purchases_view AS
            SELECT inventory.owner_id,
                   inventory.create_time,
                   inventory.reception_receipt_id,
                   (inventory.reception_price_amount * inventory.quantity) / 100 AS "reception_price",
                   inventory.quantity,
                   company.company_name                                          AS "supplier"
            FROM inventory
                     JOIN company ON inventory.supplier_id = company.id
            WHERE inventory.reception_receipt_id is not null;
        </sql>
    </changeSet>

    <changeSet id="add-future-sales-view" author="csaba.birtalan" runOnChange="true">
        <sql>
            CREATE OR REPLACE VIEW future_sales_view AS
            SELECT so.id,
                   so.create_time,
                   so.owner_id,
                   CASE
                       WHEN so.material_good_id IS NOT NULL THEN material_good.name
                       WHEN so.service_id IS NOT NULL THEN service_template.name
                       ELSE 'ERROR'
                       END                                                   AS "name",
                   so.quantity,
                   ((so.price - so.price * so.discount) * so.quantity) / 100 AS "exit_price",
                   CASE
                       WHEN so.material_good_id is not null THEN 'PRODUCT'
                       WHEN so.service_id is not null THEN 'SERVICE'
                       ELSE 'ERROR'
                       END                                                   AS "product"
            FROM (SELECT id,
                         create_time,
                         owner_id,
                         (item ->> 'productId')::uuid                AS material_good_id,
                          (item ->> 'serviceId')::uuid                AS service_id,
                          (item ->> 'quantity')::int                  AS quantity,
                          (item -> 'price' ->> 'amount')::numeric     AS price,
                          coalesce((item ->> 'discount')::numeric, 0) AS discount
                  FROM (SELECT id,
                               create_time,
                               owner_id,
                               jsonb_array_elements(items) AS item
                        FROM sales_order
                        WHERE status in ('SUBMITTED', 'PROCESSING', 'PICKING_PACKING','READY_TO_SHIP')) AS s) AS so
                     LEFT JOIN material_good ON material_good.id = so.material_good_id
                     LEFT JOIN service_template ON service_template.id = so.service_id;
        </sql>
    </changeSet>

    <changeSet id="add-sales-order-processing-times-view" author="csaba.birtalan" runOnChange="true">
        <sql>
            CREATE OR REPLACE VIEW sales_orders_processing_time_view AS
            WITH order_processing_times AS (SELECT owner_id,
                                                   target_entity_id,
                                                   COALESCE(
                                                           MIN(CASE WHEN transition = 'SUBMITTED' THEN se.create_time END), -- Use SUBMITTED if it exists
                                                           MIN(CASE WHEN transition = 'CREATE' THEN se.create_time END) -- Otherwise, fallback to CREATE
                                                   )                                                               AS start_time,
                                                   MIN(CASE WHEN transition = 'DELIVERED' THEN se.create_time END) AS delivered_time,
                                                   COUNT(CASE WHEN transition = 'CANCELED' THEN 1 END)             AS cancel_count,
                                                   a.settings -> 'manufacturing' ->> 'workDayStartTime'            AS work_start,
                                                   a.settings -> 'manufacturing' ->> 'workDayEndTime'              AS work_end,
                                                   a.settings -> 'manufacturing' -> 'workingDays'                  AS working_days,
                                                   a.settings -> 'general' ->> 'defaultTimeZone'                   AS time_zone -- Extract the time zone from JSON
                                            FROM system_event se
                                                     JOIN
                                                 account a ON se.owner_id = a.id -- Join with account to get work schedule
                                            WHERE transition IN ('CREATE', 'SUBMITTED', 'DELIVERED', 'CANCELED') -- considering relevant events
                                            GROUP BY owner_id, target_entity_id, a.settings
                                            HAVING COALESCE(
                                                    MIN(CASE WHEN transition = 'SUBMITTED' THEN se.create_time END),
                                                    MIN(CASE WHEN transition = 'CREATE' THEN se.create_time END)
                                                   ) IS NOT NULL
                                               AND MIN(CASE WHEN transition = 'DELIVERED' THEN se.create_time END) IS NOT NULL
                                               AND COUNT(CASE WHEN transition = 'CANCELED' THEN 1 END) = 0),
                 working_days_and_hours AS (
                     -- Generate a series of dates between start_time and delivered_time
                     SELECT o.owner_id,
                            o.target_entity_id                                           AS sales_order_id,
                            d::DATE                                                      AS day_of_processing,
                            o.work_start::TIME                                           AS work_start,
                            o.work_end::TIME                                             AS work_end,
                            o.start_time AT TIME ZONE 'UTC' AT TIME ZONE o.time_zone     AS start_time_local,
                            o.delivered_time AT TIME ZONE 'UTC' AT TIME ZONE o.time_zone AS delivered_time_local,
                            o.time_zone
                     FROM order_processing_times o,
                          GENERATE_SERIES(o.start_time::DATE, o.delivered_time::DATE, INTERVAL '1 day') d
                     -- Filter out non-working days
                     WHERE EXTRACT(DOW FROM d) IN
                           (SELECT (jsonb_array_elements_text(o.working_days)::INT + 6) % 7 -- Adjust for Java's 1-7 to Postgres 0-6
                           ))
            SELECT owner_id,
                   sales_order_id,
                   start_time_local,
                   delivered_time_local,
                   GREATEST(
                           SUM(
                                   CASE
                                       -- Calculate partial seconds on the first day
                                       WHEN day_of_processing = start_time_local::DATE THEN
                                           EXTRACT(EPOCH FROM (
                                               LEAST(
                                                       (day_of_processing + work_end::TIME)::TIMESTAMP,
                                                       delivered_time_local
                                               ) - GREATEST(
                                                       start_time_local::TIMESTAMP,
                                                       (day_of_processing + work_start::TIME)::TIMESTAMP
                                                   )
                                               ))
                                       -- Calculate partial seconds on the last day
                                       WHEN day_of_processing = delivered_time_local::DATE THEN
                                           EXTRACT(EPOCH FROM (
                                               LEAST(
                                                       delivered_time_local::TIMESTAMP,
                                                       (day_of_processing + work_end::TIME)::TIMESTAMP
                                               ) - (day_of_processing + work_start::TIME)::TIMESTAMP
                                               ))
                                       -- Full working seconds for intermediate days
                                       ELSE
                                           EXTRACT(EPOCH FROM (work_end::TIME - work_start::TIME)::INTERVAL)
                                       END
                           ),
                           0 -- Ensure non-negative result
                   ) AS total_working_seconds -- Total working time in seconds
            FROM working_days_and_hours
            GROUP BY owner_id, sales_order_id, start_time_local, delivered_time_local;
        </sql>
    </changeSet>

    <changeSet id="add-manufacturing-order-processing-times-view" author="csaba.birtalan" runOnChange="true">
        <sql>
            CREATE OR REPLACE VIEW manufacturing_orders_processing_time_view AS
            WITH manufacturing_order_times AS (SELECT mo.owner_id,
                                                      mo.id                                                AS manufacturing_order_id,
                                                      mo.create_time,                                                          -- No conversion needed since task times are in local time
                                                      MIN(mt.start_time)                                   AS task_start_time, -- First task's start time
                                                      MAX(mt.end_time)                                     AS task_end_time,   -- Last task's end time
                                                      a.settings -> 'manufacturing' ->> 'workDayStartTime' AS work_start,
                                                      a.settings -> 'manufacturing' ->> 'workDayEndTime'   AS work_end,
                                                      a.settings -> 'manufacturing' -> 'workingDays'       AS working_days
                                               FROM manufacturing_order mo
                                                        JOIN
                                                    account a ON mo.owner_id = a.id
                                                        LEFT JOIN
                                                    manufacturing_task mt ON mt.manufacturing_order_id = mo.id
                                               WHERE mo.status IN ('MANUFACTURED', 'CONSUMPTION_RECORDED', 'ACCOUNTED') -- Only completed manufacturing orders
                                               GROUP BY mo.owner_id, mo.id, a.settings),
                 working_days_and_hours AS (
                     -- Generate a series of dates between task start and end times
                     SELECT mot.owner_id,
                            mot.manufacturing_order_id,
                            d::DATE              AS day_of_processing,
                            mot.work_start::TIME AS work_start,
                            mot.work_end::TIME   AS work_end,
                            mot.task_start_time, -- First task's start time
                            mot.task_end_time    -- Last task's end time
                     FROM manufacturing_order_times mot,
                          GENERATE_SERIES(mot.task_start_time::DATE, mot.task_end_time::DATE, INTERVAL '1 day') d
                     -- Filter out non-working days
                     WHERE EXTRACT(DOW FROM d) IN
                           (SELECT (jsonb_array_elements_text(mot.working_days)::INT + 6) % 7 -- Adjust for Java's 1-7 to Postgres 0-6
                           ))
            SELECT owner_id,
                   manufacturing_order_id,
                   task_start_time,
                   task_end_time,
                   GREATEST(
                           SUM(
                                   CASE
                                       -- Calculate partial seconds on the first day
                                       WHEN day_of_processing = task_start_time::DATE THEN
                                           EXTRACT(EPOCH FROM (
                                               LEAST(
                                                       (day_of_processing + work_end::TIME)::TIMESTAMP,
                                                       task_end_time
                                               ) - GREATEST(
                                                       task_start_time::TIMESTAMP,
                                                       (day_of_processing + work_start::TIME)::TIMESTAMP
                                                   )
                                               ))
                                       -- Calculate partial seconds on the last day
                                       WHEN day_of_processing = task_end_time::DATE THEN
                                           EXTRACT(EPOCH FROM (
                                               LEAST(
                                                       task_end_time::TIMESTAMP,
                                                       (day_of_processing + work_end::TIME)::TIMESTAMP
                                               ) - (day_of_processing + work_start::TIME)::TIMESTAMP
                                               ))
                                       -- Full working seconds for intermediate days
                                       ELSE
                                           EXTRACT(EPOCH FROM (work_end::TIME - work_start::TIME)::INTERVAL)
                                       END
                           ),
                           0 -- Ensure non-negative result
                   ) AS total_working_seconds -- Total working time in seconds
            FROM working_days_and_hours
            GROUP BY owner_id, manufacturing_order_id, task_start_time, task_end_time;
        </sql>
    </changeSet>

    <changeSet id="add-resource-utilization-rate-view" author="csaba.birtalan" runOnChange="true">
        <sql>
            CREATE OR REPLACE VIEW resource_utilization_rate_view AS
            WITH employee_working_hours AS (
                -- For each employee, calculate the total potential working seconds per day, excluding time off
                SELECT e.id                                                                               AS employee_id,
                       e.owner_id,
                       e.name,
                       (o.settings -> 'manufacturing' ->> 'workDayStartTime')::TIME                       AS work_start,
                       (o.settings -> 'manufacturing' ->> 'workDayEndTime')::TIME                         AS work_end,
                       o.settings -> 'manufacturing' -> 'workingDays'                                     AS working_days,
                       o.settings -> 'general' ->> 'defaultTimeZone'                                      AS time_zone,
                       -- Calculate the total potential working seconds for a single workday
                       EXTRACT(EPOCH FROM ((o.settings -> 'manufacturing' ->> 'workDayEndTime')::TIME -
                                           (o.settings -> 'manufacturing' ->> 'workDayStartTime')::TIME)) AS potential_working_seconds_per_day
                FROM users e
                         JOIN account o ON e.owner_id = o.id
                GROUP BY e.id, e.owner_id, e.name, o.settings),
                 employee_timeoff_excluded AS (
                     -- Calculate total time off for each employee in each day (in seconds)
                     SELECT et.user_id,
                            et.owner_id,
                            et.start_time::DATE                                    AS day_of_timeoff,        -- Group by day
                            SUM(EXTRACT(EPOCH FROM (et.end_time - et.start_time))) AS total_time_off_seconds -- Total time off in seconds
                     FROM employee_timeoff et
                     GROUP BY et.user_id, et.owner_id, day_of_timeoff),
                 task_work_per_day AS (
                     -- Calculate work done for each task, splitting across multiple days if necessary
                     SELECT me.user_id,
                            me.owner_id,
                            gs.day_of_work,
                            SUM(
                                -- Handle the first, middle, and last day of a task
                                    CASE
                                        -- First day of the task: from task's start time to end of workday
                                        WHEN gs.day_of_work = mt.start_time::DATE THEN
                                            GREATEST(
                                                    EXTRACT(EPOCH FROM (
                                                        LEAST(
                                                                (o.settings -> 'manufacturing' ->> 'workDayEndTime')::TIME,
                                                            -- Only consider end_time if it's on the same day
                                                                CASE
                                                                    WHEN mt.end_time::DATE = mt.start_time::DATE THEN mt.end_time::TIME
                                                                    ELSE (o.settings -> 'manufacturing' ->> 'workDayEndTime')::TIME
                                                                    END
                                                        ) - GREATEST(
                                                                mt.start_time::TIME, -- Task start time
                                                                (o.settings -> 'manufacturing' ->> 'workDayStartTime')::TIME -- Workday start time
                                                            )
                                                        )),
                                                    0 -- Ensure non-negative seconds
                                            )
                                        -- Last day of the task: from start of workday to task's end time
                                        WHEN gs.day_of_work = mt.end_time::DATE THEN
                                            GREATEST(
                                                    EXTRACT(EPOCH FROM (
                                                        LEAST(
                                                                mt.end_time::TIME, -- Task end time
                                                                (o.settings -> 'manufacturing' ->> 'workDayEndTime')::TIME -- Workday end time
                                                        ) -
                                                        (o.settings -> 'manufacturing' ->> 'workDayStartTime')::TIME -- Workday start time
                                                        )),
                                                    0 -- Ensure non-negative seconds
                                            )
                                        -- Middle days: full working seconds
                                        ELSE
                                            EXTRACT(EPOCH FROM (
                                                (o.settings -> 'manufacturing' ->> 'workDayEndTime')::TIME -
                                                (o.settings -> 'manufacturing' ->> 'workDayStartTime')::TIME
                                                ))
                                        END
                            ) AS actual_working_seconds -- Sum up work done in seconds for all tasks per day
                     FROM manufacturingtask_employee me
                              JOIN manufacturing_task mt ON me.manufacturing_task_id = mt.id
                              JOIN account o ON me.owner_id = o.id
                              CROSS JOIN LATERAL generate_series(mt.start_time::DATE, mt.end_time::DATE, '1 day') AS gs(day_of_work)
                     WHERE mt.status = 'DONE' -- Only consider tasks that are DONE
                     GROUP BY me.user_id, me.owner_id, gs.day_of_work, o.settings),
                 calendar_days AS (
                     -- Generate series of days between the min and max dates in manufacturing_task
                     SELECT d::DATE AS day_of_work
                     FROM GENERATE_SERIES(
                                  (SELECT MIN(start_time) FROM manufacturing_task WHERE status = 'DONE')::DATE,
                                  (SELECT MAX(end_time) FROM manufacturing_task WHERE status = 'DONE')::DATE,
                                  INTERVAL '1 day'
                          ) d)
                        SELECT ewh.employee_id,
                               ewh.owner_id,
                               ewh.name,
                               cd.day_of_work,                      -- Show results per day
                               -- Total potential output: potential working seconds per day minus time off (but not below zero)
                               GREATEST(
                                       COALESCE(ewh.potential_working_seconds_per_day, 0) - COALESCE(eto.total_time_off_seconds, 0),
                                       0 -- Ensure potential output is non-negative
                               )       AS potential_output_seconds, -- Potential output minus time off (in seconds)
                               -- Cap the actual output to the potential output seconds for the day
                               LEAST(
                                       COALESCE(twd.actual_working_seconds, 0),
                                       GREATEST(
                                               COALESCE(ewh.potential_working_seconds_per_day, 0) - COALESCE(eto.total_time_off_seconds, 0),
                                               0
                                       )
                               )       AS actual_output_seconds,    -- Actual work done capped by the potential output seconds
                               CASE
                                   WHEN GREATEST(
                                                COALESCE(ewh.potential_working_seconds_per_day, 0) - COALESCE(eto.total_time_off_seconds, 0),
                                                0
                                        ) > 0 THEN
                                       ROUND(
                                               (LEAST(
                                                        COALESCE(twd.actual_working_seconds, 0),
                                                        GREATEST(
                                                                COALESCE(ewh.potential_working_seconds_per_day, 0) -
                                                                COALESCE(eto.total_time_off_seconds, 0),
                                                                0
                                                        )
                                                ) / GREATEST(
                                                        COALESCE(ewh.potential_working_seconds_per_day, 0) -
                                                        COALESCE(eto.total_time_off_seconds, 0),
                                                        1 -- Avoid division by zero
                                                    )), 4
                                       )
                                   ELSE 0
                                   END AS resource_utilization_rate -- Calculate utilization rate
                        FROM calendar_days cd -- Generate the days
                                 JOIN employee_working_hours ewh
                                      ON CASE WHEN EXTRACT(DOW FROM cd.day_of_work) = 0 THEN 7 ELSE EXTRACT(DOW FROM cd.day_of_work) END
                                          IN
                                         (SELECT jsonb_array_elements_text(ewh.working_days)::INT) -- Adjust PostgreSQL Sunday (0) to Java Sunday (7)
                                 LEFT JOIN employee_timeoff_excluded eto
                                           ON ewh.employee_id = eto.user_id AND ewh.owner_id = eto.owner_id AND
                                              cd.day_of_work = eto.day_of_timeoff -- Exclude time off
                                 LEFT JOIN task_work_per_day twd
                                           ON ewh.employee_id = twd.user_id AND ewh.owner_id = twd.owner_id AND
                                              cd.day_of_work = twd.day_of_work -- Join actual work done
                        ORDER BY cd.day_of_work, ewh.employee_id;
        </sql>
    </changeSet>

    <changeSet id="add-inventory-items-view" author="csaba.birtalan" runOnChange="true">
        <sql>
            CREATE OR REPLACE VIEW inventory_items_view AS
            WITH last_supplier AS (SELECT inventory.material_good_id,
                                          inventory.supplier_id,
                                          company.company_name                                                               AS supplier_name,
                                          ROW_NUMBER()
                                          OVER (PARTITION BY inventory.material_good_id ORDER BY inventory.create_time DESC) AS rn
                                   FROM inventory
                                            LEFT JOIN company ON inventory.supplier_id = company.id
                                   WHERE inventory.supplier_id IS NOT NULL),
                 committed_stock AS (SELECT owner_id, material_good_id, SUM(committed_quantity) AS committed_quantity
                                     FROM (
                                              -- Sales Orders where material is committed
                                              SELECT so.owner_id,
                                                     (item ->> 'productId')::UUID        AS material_good_id,
                                                     SUM((item ->> 'quantity')::NUMERIC) AS committed_quantity
                                              FROM sales_order so,
                                                   LATERAL jsonb_array_elements(so.items) AS item
                                              WHERE so.deleted = FALSE
                                                AND so.status IN ('SUBMITTED', 'PROCESSING', 'PICKING_PACKING', 'READY_TO_SHIP')
                                              GROUP BY so.owner_id, material_good_id

                                              UNION ALL

                                              -- Manufacturing Orders where material is required
                                              SELECT mo.owner_id,
                                                     material_id::UUID                                 AS material_good_id,
                                                     SUM((material ->> 'quantity')::NUMERIC * mo.quantity) AS committed_quantity
                                              FROM manufacturing_order mo,
                                                   LATERAL jsonb_array_elements(mo.manufacturing_operations) AS operation,
                                                   LATERAL jsonb_array_elements(operation -> 'materials') AS material,
                                                   LATERAL jsonb_array_elements_text(material -> 'materialIds') AS material_id
                                              WHERE mo.deleted = FALSE
                                                AND mo.status IN ('SUBMITTED', 'MANUFACTURING', 'MANUFACTURED')
                                              GROUP BY mo.owner_id, material_good_id) combined
                                     GROUP BY owner_id, material_good_id),
                 incoming_stock AS (SELECT owner_id, material_good_id, SUM(incoming_quantity) AS incoming_quantity
                                    FROM (SELECT po.owner_id,
                                                 (item ->> 'materialGoodId')::UUID   AS material_good_id,
                                                 SUM((item ->> 'quantity')::NUMERIC) AS incoming_quantity
                                          FROM purchase_order po,
                                               LATERAL jsonb_array_elements(po.items) AS item
                                          WHERE po.deleted = FALSE
                                            AND po.status IN ('SUBMITTED', 'SENT_FOR_QUOTE', 'SENT')
                                          GROUP BY po.owner_id, material_good_id

                                          UNION ALL

                                          SELECT mo.owner_id,
                                                 mo.product_id    AS material_good_id,
                                                 SUM(mo.quantity) AS incoming_quantity
                                          FROM manufacturing_order mo
                                          WHERE mo.deleted = FALSE
                                            AND mo.status IN ('SUBMITTED', 'MANUFACTURING')
                                            AND mo.product_id is not null
                                          GROUP BY mo.owner_id, material_good_id) combined
                                    GROUP BY owner_id, material_good_id)
            SELECT  mg.id                                        AS material_good_id,
                    mg.owner_id,
                    mg.name,
                    mg.code,
                    mg.external_code,
                    mg.description,
                    (mg.details ->> 'criticalOnHand')::numeric   AS critical_on_hand,
                    (mg.details ->> 'produced')::boolean         AS produced,
                    (mg.details ->> 'unitOfProduction')::numeric AS unit_of_production,
                    mg.details ->> 'measurementUnit'             AS measurement_unit,
                    mg.sell_price_amount,
                    mg.sell_price_currency,
                    mg.category_id,
                    mg.text_search,
                    c.details ->> 'name'                         AS category_name,
                    iu.id                                        AS inventory_unit_id,
                    iu.name                                      AS inventory_unit_name,
                    ics.currency,
                    COALESCE(ics.quantity, 0)                    AS quantity,
                    COALESCE(ics.cost, 0)                        AS cost,
                    COALESCE(ics.total_value, 0)                 AS total_value,
                    COALESCE(cs.committed_quantity, 0)           AS committed_quantity,
                    COALESCE(incoming_s.incoming_quantity, 0)    AS incoming_quantity,
                    ls.supplier_id,
                    ls.supplier_name
            FROM material_good mg
                LEFT JOIN category c ON mg.category_id = c.id
                LEFT JOIN inventory_unit iu ON (mg.owner_id = iu.owner_id and iu.deleted = false)
                FULL OUTER JOIN inventory_current_stock ics
                ON iu.id = ics.unit_id AND mg.id = ics.material_good_id
                LEFT JOIN last_supplier ls ON mg.id = ls.material_good_id AND ls.rn = 1
                LEFT JOIN committed_stock cs ON mg.id = cs.material_good_id
                LEFT JOIN incoming_stock incoming_s ON mg.id = incoming_s.material_good_id
            WHERE mg.deleted is false;
        </sql>
    </changeSet>

</databaseChangeLog>