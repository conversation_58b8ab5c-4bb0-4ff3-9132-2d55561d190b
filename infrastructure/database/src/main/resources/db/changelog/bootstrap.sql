BEGIN;
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION update_timestamp()
    RETURNS TRIGGER AS
'
    BEGIN NEW.update_time = now(); RETURN NEW; END;
' language 'plpgsql';
COMMIT;

CREATE TABLE IF NOT EXISTS account
(
    id          UUID PRIMARY KEY,
    create_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted     BOOLEAN      NOT NULL DEFAULT FALSE,
    name        VARCHAR(200) NOT NULL,
    information JSONB        NOT NULL,
    settings    JSONB        NOT NULL
);

DROP TRIGGER IF EXISTS account_timestamp ON account;
CREATE TRIGGER account_timestamp
    BEFORE INSERT OR UPDATE
    ON account
    FOR EACH ROW
EXECUTE PROCEDURE update_timestamp();

CREATE TABLE IF NOT EXISTS company
(
    id           UUID PRIMARY KEY,
    create_time  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted      BOOLEAN      NOT NULL DEFAULT FALSE,
    owner_id     UUID         NOT NULL REFERENCES account (id),
    company_name varchar(200) not null,
    details      JSONB        NOT NULL
);

DROP TRIGGER IF EXISTS company_timestamp ON account;
CREATE TRIGGER company_timestamp
    BEFORE INSERT OR UPDATE
    ON company
    FOR EACH ROW
EXECUTE PROCEDURE update_timestamp();

CREATE TABLE IF NOT EXISTS account_supplier
(
    supplier_id UUID NOT NULL REFERENCES company (id),
    owner_id    UUID NOT NULL REFERENCES account (id)
);

CREATE TABLE IF NOT EXISTS account_customer
(
    customer_id UUID NOT NULL REFERENCES company (id),
    owner_id    UUID NOT NULL REFERENCES account (id)
);

CREATE TABLE IF NOT EXISTS category
(
    id          UUID PRIMARY KEY,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted     BOOLEAN            DEFAULT FALSE,
    owner_id    UUID REFERENCES account (id),
    details     JSONB
);

DROP TRIGGER IF EXISTS category_timestamp on category;
CREATE TRIGGER category_timestamp
    BEFORE INSERT OR UPDATE
    ON category
    FOR EACH ROW
EXECUTE PROCEDURE update_timestamp();


CREATE TABLE IF NOT EXISTS location
(
    id          UUID PRIMARY KEY,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted     BOOLEAN   NOT NULL DEFAULT FALSE,
    owner_id    UUID      NOT NULL REFERENCES account (id),
    details     JSONB
);

DROP TRIGGER IF EXISTS location_timestamp on location;
CREATE TRIGGER location_timestamp
    BEFORE INSERT OR UPDATE
    ON location
    FOR EACH ROW
EXECUTE PROCEDURE update_timestamp();

CREATE TABLE IF NOT EXISTS inventory_adjustment_order
(
    id          UUID PRIMARY KEY,
    create_time TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted     BOOLEAN     NOT NULL DEFAULT FALSE,
    owner_id    UUID        NOT NULL REFERENCES account (id),
    number      VARCHAR(20) NOT NULL,
    details     JSONB
);

DROP TRIGGER IF EXISTS inventory_adjustment_order_timestamp on inventory_adjustment_order;
CREATE TRIGGER inventory_adjustment_order_timestamp
    BEFORE INSERT OR UPDATE
    ON inventory_adjustment_order
    FOR EACH ROW
EXECUTE PROCEDURE update_timestamp();

CREATE TABLE IF NOT EXISTS "sales_order"
(
    id                UUID PRIMARY KEY,
    create_time       TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time       TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted           BOOLEAN     NOT NULL DEFAULT FALSE,
    owner_id          UUID        NOT NULL REFERENCES account (id),
    customer_id       UUID REFERENCES company (id),
    number            VARCHAR(20) NOT NULL,
    ranking           INTEGER,
    delivery_deadline TIMESTAMP   NOT NULL,
    status            VARCHAR(32) NOT NULL,
    products          JSONB,
    shipping_address  JSONB,
    notes             VARCHAR(10000)
);

DROP TRIGGER IF EXISTS sales_order_timestamp on "sales_order";
CREATE TRIGGER sales_order_timestamp
    BEFORE INSERT OR UPDATE
    ON "sales_order"
    FOR EACH ROW
EXECUTE PROCEDURE update_timestamp();

CREATE TABLE IF NOT EXISTS "manufacturing_order"
(
    id                       UUID PRIMARY KEY,
    create_time              TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time              TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted                  BOOLEAN     NOT NULL DEFAULT FALSE,
    owner_id                 UUID        NOT NULL REFERENCES account (id),
    sales_order_id           UUID REFERENCES sales_order (id),
    number                   VARCHAR(20) NOT NULL,
    production_deadline      TIMESTAMP,
    status                   VARCHAR(32) NOT NULL,
    product_id               UUID,
    quantity                 NUMERIC(12, 4),
    manufactured_products    JSONB,
    ranking                  INTEGER,
    notes                    VARCHAR(10000),
    required_materials       JSONB,
    manufacturing_operations JSONB
);

DROP TRIGGER IF EXISTS manufacturing_order_timestamp on "manufacturing_order";
CREATE TRIGGER manufacturing_order_timestamp
    BEFORE INSERT OR UPDATE
    ON "manufacturing_order"
    FOR EACH ROW
EXECUTE PROCEDURE update_timestamp();

CREATE TABLE IF NOT EXISTS material_good
(
    id                  UUID PRIMARY KEY,
    create_time         TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time         TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted             BOOLEAN      NOT NULL DEFAULT FALSE,
    material_good_type  VARCHAR(32)  NOT NULL,
    owner_id            UUID         NOT NULL REFERENCES account (id),
    code                VARCHAR(20)  NOT NULL,
    name                VARCHAR(200) NOT NULL,
    category_id         UUID REFERENCES category (id),
    parent_id           UUID,
    description         VARCHAR(500),
    sell_price_amount   BIGINT,
    sell_price_currency VARCHAR(3),
    default_supplier    UUID REFERENCES company (id),
    common_details      JSONB,
    product_details     JSONB
);

DROP TRIGGER IF EXISTS material_good_timestamp on material_good;
CREATE TRIGGER material_good_timestamp
    BEFORE INSERT OR UPDATE
    ON material_good
    FOR EACH ROW
EXECUTE PROCEDURE update_timestamp();

CREATE TABLE IF NOT EXISTS inventory
(
    id                            UUID PRIMARY KEY,
    create_time                   TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time                   TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted                       BOOLEAN   NOT NULL DEFAULT FALSE,
    owner_id                      UUID      NOT NULL REFERENCES account (id),
    material_good_id              UUID      NOT NULL REFERENCES material_good (id),
--     location_id            UUID      NOT NULL REFERENCES location (id),
    inventory_adjustment_order_id UUID REFERENCES inventory_adjustment_order (id),
    sales_order_id                UUID REFERENCES sales_order (id),
    manufacturing_order_id        UUID REFERENCES manufacturing_order (id),
--  should supplier be tracked in the fill order?
    supplier_id                   UUID REFERENCES company (id),
    purchase_date                 TIMESTAMP,
    expiry_date                   TIMESTAMP,
    price_amount                  BIGINT,
    price_currency                VARCHAR(3),
    quantity                      NUMERIC(12, 4)
);

DROP TRIGGER IF EXISTS inventory_timestamp on inventory;
CREATE TRIGGER inventory_timestamp
    BEFORE INSERT OR UPDATE
    ON inventory
    FOR EACH ROW
EXECUTE PROCEDURE update_timestamp();

CREATE TABLE IF NOT EXISTS sequence
(
    id          UUID PRIMARY KEY,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted     BOOLEAN   NOT NULL DEFAULT FALSE,
    owner_id    UUID      NOT NULL REFERENCES account (id),
    section     VARCHAR(32),
    pattern     VARCHAR(32),
    value       BIGINT
);

DROP TRIGGER IF EXISTS sequence_timestamp on sequence;
CREATE TRIGGER sequence_timestamp
    BEFORE INSERT OR UPDATE
    ON sequence
    FOR EACH ROW
EXECUTE PROCEDURE update_timestamp();

CREATE TABLE IF NOT EXISTS reserved_inventory
(
    id                     UUID PRIMARY KEY,
    create_time            TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time            TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted                BOOLEAN   NOT NULL DEFAULT FALSE,
    owner_id               UUID      NOT NULL REFERENCES account (id),
    material_good_id       UUID      NOT NULL REFERENCES material_good (id),
    location_id            UUID REFERENCES location (id),
    sales_order_id         UUID REFERENCES "sales_order" (id),
    manufacturing_order_id UUID REFERENCES "manufacturing_order" (id),
    quantity               NUMERIC(12, 4)
--     on_stock               BOOLEAN   NOT NULL DEFAULT FALSE
);

DROP TRIGGER IF EXISTS reserved_inventory_timestamp on reserved_inventory;
CREATE TRIGGER reserved_inventory_timestamp
    BEFORE INSERT OR UPDATE
    ON reserved_inventory
    FOR EACH ROW
EXECUTE PROCEDURE update_timestamp();

CREATE TABLE IF NOT EXISTS purchase_wishlist
(
    id                      UUID PRIMARY KEY,
    create_time             TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time             TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted                 BOOLEAN   NOT NULL DEFAULT FALSE,
    owner_id                UUID      NOT NULL REFERENCES account (id),
    material_good_id        UUID      NOT NULL REFERENCES material_good (id),
    quantity                NUMERIC(12, 4),
    expected_price_amount   BIGINT,
    expected_price_currency VARCHAR(3),
    expected_delivery       TIMESTAMP,
    supplier_id             UUID      NOT NULL REFERENCES company (id)
);

DROP TRIGGER IF EXISTS purchase_wishlist_timestamp on purchase_wishlist;
CREATE TRIGGER purchase_wishlist_timestamp
    BEFORE INSERT OR UPDATE
    ON purchase_wishlist
    FOR EACH ROW
EXECUTE PROCEDURE update_timestamp();

CREATE TABLE IF NOT EXISTS "purchase_order"
(
    id           UUID PRIMARY KEY,
    create_time  TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time  TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted      BOOLEAN     NOT NULL DEFAULT FALSE,
    owner_id     UUID        NOT NULL REFERENCES account (id),
    supplier_id  UUID REFERENCES company (id),
    number       VARCHAR(20) NOT NULL,
    status       VARCHAR(32) NOT NULL,
    delivered_at TIMESTAMP,
    items        JSONB
);

DROP TRIGGER IF EXISTS purchase_order_timestamp on "purchase_order";
CREATE TRIGGER purchase_order_timestamp
    BEFORE INSERT OR UPDATE
    ON "purchase_order"
    FOR EACH ROW
EXECUTE PROCEDURE update_timestamp();

CREATE TABLE IF NOT EXISTS employee
(
    id          UUID PRIMARY KEY,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted     BOOLEAN   NOT NULL DEFAULT FALSE,
    owner_id    UUID REFERENCES account (id),
    name        VARCHAR(200),
    details     JSONB
);

DROP TRIGGER IF EXISTS employee_timestamp on employee;
CREATE TRIGGER employee_timestamp
    BEFORE INSERT OR UPDATE
    ON employee
    FOR EACH ROW
EXECUTE PROCEDURE update_timestamp();

CREATE TABLE IF NOT EXISTS manufacturing_workstation
(
    id          UUID PRIMARY KEY,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted     BOOLEAN   NOT NULL DEFAULT FALSE,
    owner_id    UUID REFERENCES account (id),
    name        VARCHAR(200),
    details     JSONB
);

DROP TRIGGER IF EXISTS manufacturing_workstation_timestamp on manufacturing_workstation;
CREATE TRIGGER manufacturing_workstation_timestamp
    BEFORE INSERT OR UPDATE
    ON manufacturing_workstation
    FOR EACH ROW
EXECUTE PROCEDURE update_timestamp();

CREATE TABLE IF NOT EXISTS manufacturing_operation_template
(
    id          UUID PRIMARY KEY,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted     BOOLEAN   NOT NULL DEFAULT FALSE,
    owner_id    UUID REFERENCES account (id),
    name        VARCHAR(200),
    details     JSONB
);

DROP TRIGGER IF EXISTS manufacturing_operation_timestamp on manufacturing_operation_template;
CREATE TRIGGER manufacturing_operation_timestamp
    BEFORE INSERT OR UPDATE
    ON manufacturing_operation_template
    FOR EACH ROW
EXECUTE PROCEDURE update_timestamp();

CREATE TABLE IF NOT EXISTS employee_manufacturingoperationtemplate
(
    owner_id                            UUID NOT NULL REFERENCES account (id),
    employee_id                         UUID NOT NULL REFERENCES employee (id),
    manufacturing_operation_template_id UUID NOT NULL REFERENCES manufacturing_operation_template (id)
);

CREATE TABLE IF NOT EXISTS manufacturingworkstation_manufacturingoperationtemplate
(
    owner_id                            UUID NOT NULL REFERENCES account (id),
    manufacturing_workstation_id        UUID NOT NULL REFERENCES manufacturing_workstation (id),
    manufacturing_operation_template_id UUID NOT NULL REFERENCES manufacturing_operation_template (id)
);

CREATE TABLE IF NOT EXISTS manufacturing_task
(
    id                     UUID PRIMARY KEY,
    create_time            TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time            TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted                BOOLEAN   NOT NULL DEFAULT FALSE,
    owner_id               UUID REFERENCES account (id),
    manufacturing_order_id UUID REFERENCES manufacturing_order (id),
    status                 VARCHAR(50),
    status_reason          VARCHAR(50),
    estimated_start_time   TIMESTAMP,
    estimated_end_time     TIMESTAMP,
    duration_in_minutes    INTEGER,
    details                JSONB
);

DROP TRIGGER IF EXISTS manufacturing_task_timestamp on manufacturing_task;
CREATE TRIGGER manufacturing_task_timestamp
    BEFORE INSERT OR UPDATE
    ON manufacturing_task
    FOR EACH ROW
EXECUTE PROCEDURE update_timestamp();

CREATE TABLE IF NOT EXISTS manufacturingtask_workstation
(
    owner_id                     UUID NOT NULL REFERENCES account (id),
    manufacturing_workstation_id UUID NOT NULL REFERENCES manufacturing_workstation (id),
    manufacturing_task_id        UUID NOT NULL REFERENCES manufacturing_task (id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS manufacturingtask_employee
(
    owner_id              UUID NOT NULL REFERENCES account (id),
    employee_id           UUID NOT NULL REFERENCES employee (id),
    manufacturing_task_id UUID NOT NULL REFERENCES manufacturing_task (id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS invoice
(
    id             UUID PRIMARY KEY,
    create_time    TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time    TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted        BOOLEAN     NOT NULL DEFAULT FALSE,
    owner_id       UUID        NOT NULL REFERENCES account (id),
    sales_order_id UUID REFERENCES sales_order (id) UNIQUE,
    customer_id    UUID REFERENCES company (id),
    number         VARCHAR(20) NOT NULL,
    due_date       TIMESTAMP   NOT NULL,
    paid           BOOLEAN     NOT NULL DEFAULT FALSE,
    sent_at        TIMESTAMP,
    items          JSONB
);

DROP TRIGGER IF EXISTS invoice_timestamp on invoice;
CREATE TRIGGER invoice_timestamp
    BEFORE INSERT OR UPDATE
    ON invoice
    FOR EACH ROW
EXECUTE PROCEDURE update_timestamp();

CREATE TABLE IF NOT EXISTS salesorder_invoice
(
    sales_order_id UUID NOT NULL REFERENCES sales_order (id),
    invoice_id     UUID NOT NULL REFERENCES invoice (id)
);

CREATE TABLE IF NOT EXISTS device_pairing_token
(
    create_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    employee_id UUID         NOT NULL REFERENCES employee (id),
    token       VARCHAR(100) NOT NULL,
    token_type  VARCHAR(32)  NOT NULL
);