{"name": "htmp-template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3002", "build": "next build", "start": "next start", "lint": "next lint", "tw": "npx tailwindcss -o build.css --minify"}, "dependencies": {"@types/node": "22.15.3", "@types/react": "19.1.2", "@types/react-dom": "19.1.3", "clsx": "2.1.1", "eslint": "8.57.1", "eslint-config-next": "15.3.1", "eslint-config-prettier": "10.1.2", "eslint-plugin-prettier": "5.2.6", "eslint-plugin-react": "7.37.5", "eslint-plugin-security": "3.0.1", "eslint-plugin-simple-import-sort": "12.1.1", "eslint-plugin-sort-destructure-keys": "2.0.0", "eslint-plugin-sort-imports-es6-autofix": "0.6.0", "eslint-plugin-sort-keys-fix": "1.1.2", "eslint-plugin-tailwindcss": "3.18.0", "eslint-plugin-typescript-sort-keys": "3.3.0", "next": "15.3.1", "prettier": "3.5.3", "react": "19.1.0", "react-dom": "19.1.0", "typescript": "5.8.3"}, "devDependencies": {"autoprefixer": "10.4.21", "postcss": "8.5.3", "tailwindcss": "3.4.17"}}