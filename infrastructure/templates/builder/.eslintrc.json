{
  "extends": [
    "next/core-web-vitals",
//    "prettier",
    "plugin:security/recommended-legacy",
    "plugin:tailwindcss/recommended",
    "plugin:react/recommended"
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": [
//    "prettier",
    "@typescript-eslint",
    "tailwindcss",
    "sort-imports-es6-autofix",
    "sort-keys-fix",
    "typescript-sort-keys",
    "sort-destructure-keys",
    "simple-import-sort"
  ],
  "settings": {
    "tailwindcss": {
      "callees": ["classes", "cva"]
    }
  },
  "rules": {
    "@typescript-eslint/no-unused-vars": [
      "warn",
      {
        "argsIgnorePattern": "^_",
        "caughtErrorsIgnorePattern": "^_",
        "varsIgnorePattern": "^_"
      }
    ],
    "no-unused-vars": "off",
//    "prettier/prettier": "warn",
    "react/no-unused-state": "warn",
    "react/display-name": "off",
    "security/detect-object-injection": "off",
    "react/no-unknown-property": "warn",
    "sort-imports-es6-autofix/sort-imports-es6": "warn",
    "sort-keys-fix/sort-keys-fix": "warn",
    "react/jsx-sort-props": "warn",
    "react/react-in-jsx-scope": "off",
    "react/prop-types": "off",
    "@typescript-eslint/sort-type-constituents": "warn",
    "typescript-sort-keys/interface": "warn",
    "typescript-sort-keys/string-enum": "warn",
    "sort-destructure-keys/sort-destructure-keys": "warn",
    "simple-import-sort/exports": "warn",
    "padding-line-between-statements": ["warn", {"blankLine": "never", "prev": "import", "next": "import"}],
    "@next/next/no-img-element": "off"
  }
}
