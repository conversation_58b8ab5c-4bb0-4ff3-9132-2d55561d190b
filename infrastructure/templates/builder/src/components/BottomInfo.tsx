import React, {FC, ReactNode} from 'react';
import clsx from 'clsx';

type Props = {
  children?: ReactNode;
  className?: string;
  textSize?: string;
};

const BottomInfo: FC<Props> = ({children, className, textSize}) => {
  return <div className={clsx('flex justify-end py-4', textSize, className)}>{children}</div>;
};

const Container: FC<Props> = ({children}) => {
  return <table><tbody>{children}</tbody></table>;
};

const Info: FC<Props & { label: string; labelClassName?: string; labelTextSize?: string; valueClassName?: string; valueTextSize?: string}> = ({children, className, label, labelClassName, labelTextSize, valueClassName, valueTextSize}) => {
  return (
    <tr className={className}>
      <td className={clsx('pb-2', labelTextSize ? labelTextSize : 'text-xs', labelClassName)}>{label}</td>
      <td className={clsx('pb-2 pl-16 font-medium text-right', valueTextSize ? valueTextSize : 'text-xs', valueClassName)}>{children}</td>
    </tr>
  );
};

export default Object.assign(BottomInfo, {Container, Info});
