import {useCallback, useState} from 'react';
import {useRouter} from 'next/router';

const unique_class = 'copy-button';

const useCopyHtml = (fileName: string) => {
  const [replaced, setReplaced] = useState(false);
  const {push} = useRouter();

  // Recursively formats the node using 2-space indents.
  // If an element has only text nodes, join them on one line.
  const formatNode = (node: Node, level: number = 0): string => {
    const indent = '  '.repeat(level);
    let formatted = '';

    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent || '';
      return text.trim();
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as Element;

      // Determine if all children are text nodes.
      let allText = true;
      element.childNodes.forEach((child) => {
        if (child.nodeType !== Node.TEXT_NODE) {
          allText = false;
        }
      });

      // Build the opening tag with attributes.
      let tagLine = `${indent}<${element.tagName.toLowerCase()}`;
      Array.from(element.attributes).forEach((attr) => {
        tagLine += ` ${attr.name}="${attr.value}"`;
      });
      tagLine += '>';
      formatted += tagLine;

      if (allText) {
        // Join all text nodes together with a space.
        const texts = Array.from(element.childNodes)
          .filter((child) => child.nodeType === Node.TEXT_NODE)
          .map((child) => child.textContent?.trim() || '');
        let innerText = texts.join(' ');
        // Remove any unwanted space before punctuation (like a colon)
        innerText = innerText.replace(/\s+([:.,!?])/g, '$1');
        formatted += innerText;
      } else {
        formatted += '\n';
        element.childNodes.forEach((child) => {
          const childFormatted = formatNode(child, level + 1);
          if (childFormatted) {
            formatted += `${childFormatted}\n`;
          }
        });
        formatted += indent;
      }
      formatted += `</${element.tagName.toLowerCase()}>`;
      return formatted;
    }
    return '';
  };

  const copy = useCallback(async () => {
    // Clone the entire document (<html> tag)
    const docClone = document.documentElement.cloneNode(true) as HTMLElement;

    // 1. Replace <html lang="en"> with a plain <html>
    docClone?.removeAttribute('lang');
    docClone?.removeAttribute('style');

    // 2. Remove all <meta>, <noscript>, and <script> tags.
    docClone.querySelectorAll('meta, noscript, script').forEach((el) => el.remove());

    // 3. Remove all comment nodes from the cloned document.
    const removeComments = (node: Node) => {
      for (let i = 0; i < node.childNodes.length; i++) {
        const child = node.childNodes[i];
        if (child.nodeType === Node.COMMENT_NODE) {
          child.parentNode?.removeChild(child);
          i--; // Adjust index after removal
        } else {
          removeComments(child);
        }
      }
    };
    removeComments(docClone);

    // 4. Remove specific unwanted elements.
    docClone.querySelectorAll(`#__next-build-watcher, next-route-announcer, .${unique_class}, [id^="loom-companion"]`).forEach((el) => el.remove());

    // 5. Replace <div id="__next"> with a plain <div>
    const nextDiv = docClone.querySelector('div#__next');
    if (nextDiv) {
      const newDiv = document.createElement('div');
      while (nextDiv.firstChild) {
        newDiv.appendChild(nextDiv.firstChild);
      }
      nextDiv.parentNode?.replaceChild(newDiv, nextDiv);
    }

    // 6. Replace <body style=""> with a plain <body>
    const body = docClone.querySelector('body');
    body?.removeAttribute('style');

    // 7. Replace extra styles
    docClone.querySelectorAll('style').forEach((style) => {
      const styleContent = style.textContent?.trim() || '';
      if (!styleContent.startsWith('*')) {
        style.remove();
      }
    });

    // 8. Remove <nextjs-portal> elements
    docClone.querySelectorAll('nextjs-portal').forEach((portal) => portal.remove());

    // 9. Get the formatted HTML string using the helper function.
    let formattedHtml = formatNode(docClone);

    // 10. Remove inline block comments (/* ... */) if any.
    formattedHtml = formattedHtml.replace(/\/\*[\s\S]*?\*\//g, '');
    formattedHtml += '\n';

    // 11. Export and replace current HTML file
    try {
      const response = await fetch('/api/save_html', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({fileName, htmlContent: formattedHtml}),
      });

      const data = await response.json();

      if (response.ok) {
        setReplaced(true);
        setTimeout(() => setReplaced(false), 2000);
        console.log(data.message);
      } else {
        console.error(`Failed to save HTML: ${data.message}`);
      }
    } catch (error) {
      console.error(`Error saving HTML: ${error}`);
    }
  }, [fileName]);

  const copyElement = (
    <div className={`${unique_class} absolute flex gap-2 items-center top-0.5 left-1/2 -translate-x-1/2`}>
      <button className='px-1 py-0.5 text-xs bg-white text-black rounded hover:bg-gray-100 border' onClick={() => push('/')}>
        Back
      </button>
      <button className='px-1 py-0.5 text-xs bg-blue-600 text-white rounded hover:bg-blue-700' onClick={copy}>
        {replaced ? 'Replaced' : 'Generate and replace HTML'}
      </button>
    </div>
  );

  return {copy, copyElement};
};

export default useCopyHtml;
