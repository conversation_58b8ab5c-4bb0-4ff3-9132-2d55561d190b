import React, {FC, ReactNode} from 'react';
import clsx from 'clsx';

type Props = {
  children: ReactNode;
  className?: string;
  textSize?: string;
  title?: string;
  withoutCurrency?: boolean;
  withPriceCheck?: boolean
};

const Table: FC<Props & {titleTextSize?: string;}> = ({children, className, textSize, title, titleTextSize, withoutCurrency, withPrice<PERSON>heck}) => {
  return (
    <div className={clsx('py-4', className)}>
      {!withoutCurrency && <>
        {withPriceCheck && '{{#showPrice}}'}
        <div className='flex items-center justify-end gap-4 pb-4'>
          <div className={clsx(textSize ? textSize : 'text-xs')}>{`{{labels.currency}}`}:</div>
          <div className={clsx('font-semibold', textSize ? textSize : 'text-xs')}>{`{{currency}}`}</div>
        </div>
        {withPriceCheck && '{{/showPrice}}'}</>}
      {title && <div className={clsx('mb-2', titleTextSize ? titleTextSize : 'text-sm')}>{title}</div>}
      <table className='w-full'>{children}</table>
    </div>
  );
};

const Header: FC<Props> = ({children, className}) => {
  return (
    <thead className={className}>
      <Row>{children}</Row>
    </thead>
  );
};

const Head: FC<Optional<Props, 'children'> & {direction?: 'center' | 'left' | 'right'}> = ({children, className, direction = 'left', textSize}) => {
  return (
    <th
      className={clsx(
        'font-light px-2',
        textSize ? textSize : 'text-[10px]',
        direction === 'left' && 'text-left',
        direction === 'right' && 'text-right',
        className,
      )}
    >
      {children}
    </th>
  );
};

const Body: FC<Props> = ({children, className, textSize}) => {
  return <tbody className={clsx('text-center', textSize ? textSize : 'text-xs', className)}>{children}</tbody>;
};

const Row: FC<Props> = ({children, className}) => {
  return <tr className={clsx('not-last-of-type:border-b', className)}>{children}</tr>;
};

const Cell: FC<Props & {direction?: 'center' | 'left' | 'right'}> = ({children, className, direction = 'left', textSize}) => {
  return (
    <td
      className={clsx(
        'py-4 px-2',
        textSize,
        direction === 'left' && 'text-left',
        direction === 'right' && 'text-right',
        className,
      )}
    >
      {children}
    </td>
  );
};

const Footer: FC<Props> = ({children, className, textSize}) => {
  return <tfoot className={clsx(textSize ? textSize : 'text-sm', className)}>{children}</tfoot>;
};

const FooterCell: FC<Props & {direction?: 'left' | 'normal' | 'right'; start?: number}> = ({
  children,
  className,
  direction = 'normal',
  start = 0,
  textSize,
}) => {
  return (
    <>
      {Array.from({length: start}).map((_, index) => (
        <td key={index} />
      ))}
      <td className={clsx(textSize, direction === 'left' && 'text-left', direction === 'right' && 'text-right', className)}>
        {children}
      </td>
    </>
  );
};

export default Object.assign(Table, {Body, Cell, Footer, FooterCell, Head, Header, Row});
