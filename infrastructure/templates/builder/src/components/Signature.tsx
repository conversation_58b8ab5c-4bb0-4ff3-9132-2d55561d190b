import {FC, ReactNode} from 'react';
import clsx from 'clsx';

type Props = {
  children?: ReactNode;
  className?: string;
  label: string;
  labelTextSize?: string;
  valueTextSize?: string;
};

const Signature: FC<Props> = ({children, className, label, labelTextSize, valueTextSize}) => {
  return <div className='flex w-full max-w-96 flex-col items-center'>
    <div className={clsx('font-medium text-center', labelTextSize ? labelTextSize : 'text-sm')}>{children}</div>
    <div className={clsx('border-t border-black flex w-full justify-center', valueTextSize ? valueTextSize : 'text-xs', className)}>{label}</div>
  </div>;
};

export default Signature;
