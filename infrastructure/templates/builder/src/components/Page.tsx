import {FC, ReactNode} from 'react';
import clsx from 'clsx';
import useCopyHtml from 'components/useCopyHtml';

type Props = {
  children: ReactNode;
  fileName: string;
  className?: string;
};

const Page: FC<Props> = ({children, className, fileName}) => {
  const {copyElement} = useCopyHtml(fileName);

  return (
    <>
      {copyElement}
      <div className={clsx('flex flex-col divide-y', className)}>{children}</div>
    </>
  );
};

export default Page;
