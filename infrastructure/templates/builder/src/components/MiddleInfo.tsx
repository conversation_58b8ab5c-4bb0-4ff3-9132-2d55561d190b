import React, {FC, ReactNode} from 'react';
import clsx from 'clsx';

type Props = {
  children: ReactNode;
  textSize?: string;
};

const MiddleInfo: FC<Props> = ({children, textSize}) => {
  return <div className={clsx('flex justify-between gap-16 py-4', textSize)}>{children}</div>;
};

const Details: FC<Props> = ({children}) => {
  return <table><tbody>{children}</tbody></table>;
};

const Info: FC<Props & {label: string; labelTextSize?: string; valueTextSize?: string}> = ({children, label, labelTextSize, valueTextSize}) => {
  return (
    <tr>
      <td className={clsx('flex pb-1 font-light', labelTextSize ? labelTextSize : 'text-[10px]')}>{label}:</td>
      <td className={clsx('pb-1 pl-4 font-medium', valueTextSize ? valueTextSize : 'text-[10px]')}>{children}</td>
    </tr>
  );
};

const Container: FC<Props> = ({children}) => {
  return <div className='max-w-[50vw]'>{children}</div>;
};

const Special: FC<Props & {label: string; labelTextSize?: string; valueTextSize?: string}> = ({children, label, labelTextSize, valueTextSize}) => {
  return (
    <div className='mb-1 flex flex-col gap-1'>
      <div className={clsx('font-light', labelTextSize ? labelTextSize : 'text-[10px]')}>{label}</div>
      <div className={clsx('font-medium', valueTextSize ? valueTextSize : 'text-xs')}>{children}</div>
    </div>
  );
};

export default Object.assign(MiddleInfo, {Container, Details, Info, Special});
