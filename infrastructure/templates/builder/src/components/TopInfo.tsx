import React, {FC, ReactNode} from 'react';
import clsx from 'clsx';

type Props = {
  children: ReactNode;
  className?: string;
  textSize?: string;
};

const TopInfo: FC<Props> = ({children, className, textSize}) => {
  return <div className={clsx('flex gap-16', textSize, className)}>{children}</div>;
};

const Info: FC<Props & {label: string, labelTextSize?: string; valueTextSize?: string;}> = ({children, className, label, labelTextSize, valueTextSize}) => {
  return (
    <div className={clsx('flex flex-col font-medium gap-1', className)}>
      <div className={clsx(labelTextSize ? labelTextSize : 'text-[10px]')}>{label}</div>
      <div className={clsx(valueTextSize ? valueTextSize : 'text-xs')}>{children}</div>
    </div>
  );
};

export default Object.assign(TopInfo, {Info});
