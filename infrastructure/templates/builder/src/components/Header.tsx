import clsx from 'clsx';
import Logo from 'components/Logo';
import React, {FC, ReactNode} from 'react';

type Props = {
  children: ReactNode;
  textSize?:string;
};

const Header: FC<Props> = ({children, textSize}) => {
  return (
    <div className={clsx('flex flex-col pb-4', textSize)}>
      {children}
      <Logo />
    </div>
  );
};

const Title: FC<Props> = ({children, textSize}) => {
  return <div className={clsx('font-semibold', textSize ? textSize : 'text-2xl')}>{children}</div>;
};

const Subtitle: FC<Props> = ({children, textSize}) => {
  return <div className={clsx('pb-4 font-medium', textSize ? textSize : 'text-xs')}>{children}</div>;
};

export default Object.assign(Header, {Subtitle, Title});
