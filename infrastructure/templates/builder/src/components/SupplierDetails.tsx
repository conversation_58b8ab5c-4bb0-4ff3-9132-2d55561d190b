import MiddleInfo from 'components/MiddleInfo';
import React, {FC} from 'react';


const SupplierDetails: FC = () => {
  return <MiddleInfo.Container>
    <MiddleInfo.Special label={`{{labels.supplier}}`}>{`{{supplier.name}}`}</MiddleInfo.Special>
    <MiddleInfo.Details>
      {`{{#supplier.taxIdentificationNumber}}`}
      <MiddleInfo.Info
        label={`{{labels.tax_identification_number}}`}
      >{`{{supplier.taxIdentificationNumber}}`}</MiddleInfo.Info>
      {`{{/supplier.taxIdentificationNumber}}`}
      {`{{#supplier.identificationNumber}}`}
      <MiddleInfo.Info
        label={`{{labels.identification_number}}`}
      >{`{{supplier.identificationNumber}}`}</MiddleInfo.Info>
      {`{{/supplier.identificationNumber}}`}
      {`{{#supplier.socialCapital}}`}
      <MiddleInfo.Info label={`{{labels.social_capital}}`}>{`{{supplier.socialCapital}}`}</MiddleInfo.Info>
      {`{{/supplier.socialCapital}}`}
      {`{{#supplier.address}}`}
      <MiddleInfo.Info label={`{{labels.address}}`}>{`{{supplier.address}}`}</MiddleInfo.Info>
      {`{{/supplier.address}}`}
      {`{{#supplier.bankAccount}}`}
      <MiddleInfo.Info label={`{{labels.bank_name}}`}>{`{{supplier.bankAccount.bank}}`}</MiddleInfo.Info>
      {`{{#supplier.bankAccount.swift}}`}
      <MiddleInfo.Info label={`{{labels.swift_number}}`}>{`{{supplier.bankAccount.swift}}`}</MiddleInfo.Info>
      {`{{/supplier.bankAccount.swift}}`}
      <MiddleInfo.Info
        label={`{{labels.bank_account_number}}`}
      >{`{{supplier.bankAccount.number}}`}</MiddleInfo.Info>
      {`{{/supplier.bankAccount}}`}
      {`{{#supplier.email}}`}
      <MiddleInfo.Info label={`{{labels.email}}`}>{`{{supplier.email}}`}</MiddleInfo.Info>
      {`{{/supplier.email}}`}
      {`{{#supplier.phone}}`}
      <MiddleInfo.Info label={`{{labels.phone}}`}>{`{{supplier.phone}}`}</MiddleInfo.Info>
      {`{{/supplier.phone}}`}
    </MiddleInfo.Details>
  </MiddleInfo.Container>;
};

export default SupplierDetails;
