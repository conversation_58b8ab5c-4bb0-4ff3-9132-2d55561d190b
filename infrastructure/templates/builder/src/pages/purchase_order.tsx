import BottomInfo from 'components/BottomInfo';
import Header from 'components/Header';
import MiddleInfo from 'components/MiddleInfo';
import Page from 'components/Page';
import React, {FC, useEffect, useState} from 'react';
import Table from 'components/Table';
import TopInfo from 'components/TopInfo';

const PurchaseOrder: FC = () => {
  const [hydrated, setHydrated] = useState(false);

  useEffect(() => {
    setHydrated(true);
  }, []);

  if (!hydrated) {
    return null;
  }

  return (
    <Page fileName='purchase_order'>
      <Header>
        {'{{#title}}'}
        <Header.Title>{`{{title}}`}</Header.Title>
        {'{{/title}}'}
        {'{{^title}}'}
        <Header.Title>{`{{labels.purchase_order}}`}</Header.Title>
        {'{{/title}}'}
        <Header.Subtitle>{`{{labels.number_abbreviation}} {{order.number}}`}</Header.Subtitle>
        <TopInfo>
          <TopInfo.Info label={`{{labels.order_date}}`}>{`{{order.creationDate}}`}</TopInfo.Info>
          <TopInfo.Info label={`{{labels.supplier}}`}>{`{{supplier.name}}`}</TopInfo.Info>
          <TopInfo.Info label={`{{labels.expected_by}}`}>{`{{order.expectedDate}}`}</TopInfo.Info>
        </TopInfo>
      </Header>
      <MiddleInfo>
        <MiddleInfo.Container>
          <MiddleInfo.Special label={`{{labels.ordered_by}}`}>{`{{account.name}}`}</MiddleInfo.Special>
          <MiddleInfo.Details>
            <MiddleInfo.Info label={`{{labels.address}}`}>{`{{account.address}}`}</MiddleInfo.Info>
            <MiddleInfo.Info label={`{{labels.phone}}`}>{`{{account.phone}}`}</MiddleInfo.Info>
            <MiddleInfo.Info label={`{{labels.email}}`}>{`{{account.email}}`}</MiddleInfo.Info>
          </MiddleInfo.Details>
        </MiddleInfo.Container>
        <MiddleInfo.Container>
          <MiddleInfo.Special label={`{{labels.shipping_address}}`}>{`{{account.address}}`}</MiddleInfo.Special>
        </MiddleInfo.Container>
      </MiddleInfo>
      <Table withPriceCheck>
        <Table.Header>
          <Table.Head>{`{{labels.number_abbreviation}}`}</Table.Head>
          <Table.Head>{`{{labels.item_name}}`}</Table.Head>
          {'{{#showQuantity}}'}
          <Table.Head direction='right'>{`{{labels.quantity}}`}</Table.Head>
          {'{{/showQuantity}}'}
          {'{{#showPrice}}'}
          <Table.Head direction='right'>{`{{labels.unit_price}}`}</Table.Head>
          {'{{/showPrice}}'}
        </Table.Header>
        <Table.Body>
          {`{{#order.items}}`}
          <Table.Row>
            <Table.Cell>{`{{index}}`}</Table.Cell>
            <Table.Cell>{`{{materialName}}`}</Table.Cell>
            {'{{#showQuantity}}'}
            <Table.Cell direction='right'>{`{{quantity}}`}</Table.Cell>
            {'{{/showQuantity}}'}
            {'{{#showPrice}}'}
            <Table.Cell direction='right'>{`{{price}}`}</Table.Cell>
            {'{{/showPrice}}'}
          </Table.Row>
          {`{{/order.items}}`}
        </Table.Body>
      </Table>
      <BottomInfo>
        <BottomInfo.Container>
          {'{{#showPrice}}'}
          {`{{#order.hasPrice}}`}
          <BottomInfo.Info className='border-b' label={`{{labels.subtotal}}`}>{`{{order.subTotalAmount}}`}</BottomInfo.Info>
          <BottomInfo.Info label={`{{labels.total}}`} labelClassName='!text-sm pt-4' valueClassName='!text-sm pt-4'>{`{{order.totalAmount}}`}</BottomInfo.Info>
          {`{{/order.hasPrice}}`}
          {'{{/showPrice}}'}
        </BottomInfo.Container>
      </BottomInfo>
    </Page>
  );
};

export default PurchaseOrder;
