import BottomInfo from 'components/BottomInfo';
import Header from 'components/Header';
import Page from 'components/Page';
import RandomInfo from 'components/RandomInfo';
import React, {FC, useEffect, useState} from 'react';
import Signature from 'components/Signature';
import Table from 'components/Table';
import TopInfo from 'components/TopInfo';

const MaterialIssueNote: FC = () => {
  const [hydrated, setHydrated] = useState(false);

  useEffect(() => {
    setHydrated(true);
  }, []);

  if (!hydrated) {
    return null;
  }

  return (
    <div className='flex min-h-screen flex-col gap-8'>
      <Page fileName='material_issue_note'>
        <Header>
          <Header.Title>{`{{labels.material_issue_title}}`}</Header.Title>
          <Header.Subtitle>{`{{labels.number}}: {{note.number}}`}</Header.Subtitle>
          <TopInfo>
            <TopInfo.Info label={`{{labels.date}}`}>{`{{note.date}}`}</TopInfo.Info>
            <TopInfo.Info label={`{{labels.manufacturing_order}}`}>{`{{note.manufacturingOrderNumber}}`}</TopInfo.Info>
            <TopInfo.Info label={`{{labels.product_name}}`}>{`{{note.productName}}`}</TopInfo.Info>
            <TopInfo.Info label={`{{labels.product_sku}}`}>{`{{note.productSku}}`}</TopInfo.Info>
            <TopInfo.Info label={`{{labels.product_quantity}}`}>{`{{note.productQuantity}}`}</TopInfo.Info>
            <TopInfo.Info label={`{{labels.business_unit}}`}>{`{{note.businessUnit}}`}</TopInfo.Info>
          </TopInfo>
        </Header>
        <Table>
          <Table.Header>
            <Table.Head>{`{{labels.number_abbreviation}}`}</Table.Head>
            <Table.Head>{`{{labels.material_issue_item_name}}`}</Table.Head>
            <Table.Head>{`{{labels.measurement_unit_abbreviation}}`}</Table.Head>
            <Table.Head direction='right'>{`{{labels.needed_quantity}}`}</Table.Head>
            <Table.Head direction='right'>{`{{labels.used_quantity}}`}</Table.Head>
            <Table.Head direction='right'>{`{{labels.unit_price}}`}</Table.Head>
            <Table.Head direction='right'>{`{{labels.value}}`}</Table.Head>
          </Table.Header>
          <Table.Body>
            {`{{#note.materials}}`}
            <Table.Row>
              <Table.Cell>{`{{index}}`}</Table.Cell>
              <Table.Cell>{`{{name}}`}</Table.Cell>
              <Table.Cell>{`{{measurementUnit}}`}</Table.Cell>
              <Table.Cell direction='right'>{`{{neededQuantity}}`}</Table.Cell>
              <Table.Cell direction='right'>{`{{usedQuantity}}`}</Table.Cell>
              <Table.Cell direction='right'>{`{{price}}`}</Table.Cell>
              <Table.Cell direction='right'>{`{{value}}`}</Table.Cell>
            </Table.Row>
            {`{{/note.materials}}`}
          </Table.Body>
        </Table>
        <BottomInfo>
          <BottomInfo.Info label={`{{labels.total_value}}`} labelClassName='!text-sm'
                           valueClassName='!text-sm'>{`{{note.totalValue}}`}</BottomInfo.Info>
        </BottomInfo>
      </Page>
      <div className='grow'/>
      <div className='mx-20 mb-4 flex justify-between gap-20 items-end`'>
        <Signature label={`{{labels.inventory_manager}}`}>{`{{note.inventory_manager}}`}</Signature>
        <Signature label={`{{labels.receiver_person}}`}>{`{{note.receiver_person}}`}</Signature>
      </div>
    </div>
  );
};

export default MaterialIssueNote;
