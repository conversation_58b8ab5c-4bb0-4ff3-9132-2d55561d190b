import BottomInfo from 'components/BottomInfo';
import Header from 'components/Header';
import Page from 'components/Page';
import React, {FC, useEffect, useState} from 'react';
import Signature from 'components/Signature';
import Table from 'components/Table';
import TopInfo from 'components/TopInfo';

const ReceptionReceipt: FC = () => {
  const [hydrated, setHydrated] = useState(false);

  useEffect(() => {
    setHydrated(true);
  }, []);

  if (!hydrated) {
    return null;
  }

  return (
    <div className='flex min-h-screen flex-col gap-8'>
      <Page fileName='reception_receipt'>
        <Header>
          <Header.Title>{`{{labels.receipt_title}}`}</Header.Title>
          <Header.Subtitle>{`{{labels.number}}: {{receipt.number}}`}</Header.Subtitle>
          <TopInfo>
            <TopInfo.Info label={`{{labels.date}}`}>{`{{receipt.date}}`}</TopInfo.Info>
          </TopInfo>
        </Header>
        <div className='py-4 text-xs'>
          Am recepționat materialele furnizate de <span className='text-sm font-medium'>{`{{supplier.name}}`}</span>,
          livrat cu
          auto nr.
          <span className='text-sm font-medium'>{`{{receipt.transportedWith}}`}</span>
          delegat
          <span className='text-sm font-medium'>{`{{receipt.transportedBy}}`}</span>
          , și documente insoțitoare{' '}
          <span className='text-sm font-medium'>{`{{receipt.documentType}}`}</span>:
          <span className='text-sm font-medium'>{`{{receipt.documentNumber}} / {{receipt.documentDate}}`}</span>,
          pentru:

        </div>
        <Table>
          <Table.Header>
            <Table.Head>{`{{labels.item_name}}`}</Table.Head>
            <Table.Head>{`{{labels.measurement_unit_abbreviation}}`}</Table.Head>
            <Table.Head direction='right'>{`{{labels.ordered_quantity}}`}</Table.Head>
            <Table.Head direction='right'>{`{{labels.received_quantity}}`}</Table.Head>
            <Table.Head direction='right'>{`{{labels.unit_price}}`}</Table.Head>
            <Table.Head direction='right'>{`{{labels.additional_costs}}`}</Table.Head>
            <Table.Head direction='right'>{`{{labels.value}}`}</Table.Head>
          </Table.Header>
          <Table.Body>
            {`{{#receipt.goods}}`}
            <Table.Row>
              <Table.Cell>{`{{name}}`}</Table.Cell>
              <Table.Cell>{`{{measurementUnit}}`}</Table.Cell>
              <Table.Cell direction='right'>{`{{orderedQuantity}}`}</Table.Cell>
              <Table.Cell direction='right'>{`{{receivedQuantity}}`}</Table.Cell>
              <Table.Cell direction='right'>{`{{price}}`}</Table.Cell>
              <Table.Cell direction='right'>{`{{additionalCosts}}`}</Table.Cell>
              <Table.Cell direction='right'>{`{{value}}`}</Table.Cell>
            </Table.Row>
            {`{{/receipt.goods}}`}
          </Table.Body>
        </Table>
        <BottomInfo>
          <BottomInfo.Container>
            <BottomInfo.Info
              label={`{{labels.total_value}}`}
              labelClassName='!text-sm'
              valueClassName='!text-sm'
            >{`{{receipt.totalValue}}`}</BottomInfo.Info>
          </BottomInfo.Container>
        </BottomInfo>
      </Page>
      <div className='grow'/>
      <div className='mx-20 mb-4'>
        <Signature label={`{{labels.received_by}}`}>{`{{receipt.receivedBy}}`}</Signature>
      </div>
    </div>
  );
};

export default ReceptionReceipt;
