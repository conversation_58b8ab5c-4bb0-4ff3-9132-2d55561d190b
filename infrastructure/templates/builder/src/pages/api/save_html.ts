import fs from 'fs';
import path from 'path';
import { NextApiRequest, NextApiResponse } from 'next';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    const { fileName, htmlContent } = req.body;

    if (!fileName || !htmlContent) {
      return res.status(400).json({ message: 'File name and HTML content are required.' });
    }

    const filePath = path.join(process.cwd(), '../src/main/resources/templates/pdf', `${fileName}.html`);
    const dirPath = path.dirname(filePath);

    try {
      // Create directory structure if it doesn't exist
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }

      fs.writeFileSync(filePath, htmlContent, 'utf8');
      return res.status(200).json({ message: 'HTML saved successfully!' });
    } catch (error) {
      console.error('Error saving file:', error);
      return res.status(500).json({ message: 'Failed to save HTML file.' });
    }
  } else {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ message: `Method ${req.method} Not Allowed` });
  }
}
