import BottomInfo from 'components/BottomInfo';
import Header from 'components/Header';
import MiddleInfo from 'components/MiddleInfo';
import Page from 'components/Page';
import React, {useEffect, useState} from 'react';
import SupplierDetails from 'components/SupplierDetails';
import Table from 'components/Table';
import TopInfo from 'components/TopInfo';

const Invoice = () => {
  const [hydrated, setHydrated] = useState(false);

  useEffect(() => {
    setHydrated(true);
  }, []);

  if (!hydrated) {
    return null;
  }

  return (
    <div className='flex min-h-screen flex-col gap-8'>
      <Page fileName='invoice'>
        <Header>
          {`{{#invoice.proforma}}`}
          <Header.Title>{`{{labels.invoice_title_proforma}}`}</Header.Title>
          {`{{/invoice.proforma}}`}
          {`{{^invoice.proforma}}`}
          <Header.Title>{`{{labels.invoice_title}}`}</Header.Title>
          {`{{/invoice.proforma}}`}
          <Header.Subtitle>{`{{labels.number}}: {{invoice.number}}`}</Header.Subtitle>
          <TopInfo>
            <TopInfo.Info label={`{{labels.invoice_date}}`}>{`{{invoice.date}}`}</TopInfo.Info>
            <TopInfo.Info label={`{{labels.invoice_due_date}}`}>{`{{invoice.dueDate}}`}</TopInfo.Info>
          </TopInfo>
        </Header>
        <MiddleInfo>
          <SupplierDetails/>
          <MiddleInfo.Container>
            <MiddleInfo.Special label={`{{labels.client}}`}>{`{{client.name}}`}</MiddleInfo.Special>
            <MiddleInfo.Details>
              <MiddleInfo.Info
                label={`{{labels.tax_identification_number}}`}
              >{`{{client.taxIdentificationNumber}}`}</MiddleInfo.Info>
              <MiddleInfo.Info
                label={`{{labels.identification_number}}`}
              >{`{{client.identificationNumber}}`}</MiddleInfo.Info>
              <MiddleInfo.Info label={`{{labels.address}}`}>{`{{client.address}}`}</MiddleInfo.Info>
            </MiddleInfo.Details>
          </MiddleInfo.Container>
        </MiddleInfo>
        <Table>
          <Table.Header>
            <Table.Head>{`{{labels.number_abbreviation}}`}</Table.Head>
            <Table.Head>{`{{labels.item_name}}`}</Table.Head>
            <Table.Head direction='right'>{`{{labels.quantity}}`}</Table.Head>
            <Table.Head>{`{{labels.measurement_unit_abbreviation}}`}</Table.Head>
            <Table.Head direction='right'>{`{{labels.unit_price}}`}</Table.Head>
            {'{{#showDiscount}}'}
            <Table.Head direction='right'>{`{{labels.discount}}`}</Table.Head>
            {'{{/showDiscount}}'}
            <Table.Head direction='right'>{`{{labels.value_net}}`}</Table.Head>
            {'{{#invoice.isVatApplicable}}'}
            <Table.Head direction='right'>{`{{labels.VAT_quote}}`}</Table.Head>
            <Table.Head direction='right'>{`{{labels.VAT_amount}}`}</Table.Head>
            {'{{/invoice.isVatApplicable}}'}
          </Table.Header>
          <Table.Body>
            {`{{#invoice.items}}`}
            <Table.Row>
              <Table.Cell>{`{{index}}`}</Table.Cell>
              <Table.Cell>{`{{name}}`}</Table.Cell>
              <Table.Cell className='text-right'>{`{{quantity}}`}</Table.Cell>
              <Table.Cell>{`{{measurementUnit}}`}</Table.Cell>
              <Table.Cell direction='right'>{`{{price}}`}</Table.Cell>
              {'{{#showDiscount}}'}
              <Table.Cell direction='right'>{`{{discount}}`}</Table.Cell>
              {'{{/showDiscount}}'}
              <Table.Cell direction='right'>{`{{value}}`}</Table.Cell>
              {'{{#invoice.isVatApplicable}}'}
              <Table.Cell direction='right'>{`{{VAT}}`}</Table.Cell>
              <Table.Cell direction='right'>{`{{vatAmount}}`}</Table.Cell>
              {'{{/invoice.isVatApplicable}}'}
            </Table.Row>
            {`{{/invoice.items}}`}
          </Table.Body>
        </Table>
        <BottomInfo>
          <BottomInfo.Container>
            <BottomInfo.Info label={`{{labels.subtotal}}`}>{`{{invoice.subTotalAmount}}`}</BottomInfo.Info>
            <BottomInfo.Info label={`{{labels.discount}}`}>-{`{{invoice.discountAmount}}`}</BottomInfo.Info>
            {'{{#invoice.isVatApplicable}}'}
            <BottomInfo.Info className='border-b' label={`{{labels.VAT_amount}}`}>{`{{invoice.taxAmount}}`}</BottomInfo.Info>
            {'{{/invoice.isVatApplicable}}'}
            <BottomInfo.Info label={`{{labels.total}}`} labelClassName='!text-sm pt-4'
                             valueClassName='!text-sm pt-4'>{`{{invoice.totalAmount}}`}</BottomInfo.Info>
          </BottomInfo.Container>
        </BottomInfo>
      </Page>
      <div className='grow'/>
      {`{{#invoice.notes}}`}
      <div className='flex items-center gap-4'>
        <div className='text-xs font-light'>{`{{labels.notes}}`}:</div>
        <div className='text-xs whitespace-pre'>{`{{invoice.notes}}`}</div>
      </div>
      {`{{/invoice.notes}}`}
    </div>
  );
};

export default Invoice;
