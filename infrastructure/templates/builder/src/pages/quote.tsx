import BottomInfo from 'components/BottomInfo';
import Header from 'components/Header';
import MiddleInfo from 'components/MiddleInfo';
import Page from 'components/Page';
import React, {FC, useEffect, useState} from 'react';
import SupplierDetails from 'components/SupplierDetails';
import Table from 'components/Table';
import TopInfo from 'components/TopInfo';
import RandomInfo from 'components/RandomInfo';

const Quote: FC = () => {
  const [hydrated, setHydrated] = useState(false);

  useEffect(() => {
    setHydrated(true);
  }, []);

  if (!hydrated) {
    return null;
  }

  return (
    <Page fileName='quote'>
      <Header>
        {`{{#order.quote}}`}
        <Header.Title>{`{{labels.sales_quote}}`}</Header.Title>
        {`{{/order.quote}}`}
        {`{{^order.quote}}`}
        <Header.Title>{`{{labels.sales_order}}`}</Header.Title>
        {`{{/order.quote}}`}
        <Header.Subtitle>{`{{labels.number}}: {{order.number}}`}</Header.Subtitle>
        <TopInfo>
          <TopInfo.Info label={`{{labels.creation_date}}`}>{`{{order.creationDate}}`}</TopInfo.Info>
          <TopInfo.Info label={`{{labels.expiration_date}}`}>{`{{order.expirationDate}}`}</TopInfo.Info>
        </TopInfo>
      </Header>
      <MiddleInfo>
        <SupplierDetails />
        <MiddleInfo.Container>
          <MiddleInfo.Special label={`{{labels.client}}`}>{`{{client.name}}`}</MiddleInfo.Special>

          <MiddleInfo.Details>
            <MiddleInfo.Info label={`{{labels.billing_address}}`}>{`{{client.billingAddress}}`}</MiddleInfo.Info>
          </MiddleInfo.Details>
        </MiddleInfo.Container>
      </MiddleInfo>
      <Table>
        <Table.Header>
          <Table.Head>{`{{labels.number_abbreviation}}`}</Table.Head>
          <Table.Head>{`{{labels.item_name}}`}</Table.Head>
          <Table.Head direction='right'>{`{{labels.quantity}}`}</Table.Head>
          <Table.Head>{`{{labels.measurement_unit_abbreviation}}`}</Table.Head>
          <Table.Head direction='right'>{`{{labels.unit_price}}`}</Table.Head>
          {'{{#showDiscount}}'}
          <Table.Head direction='right'>{`{{labels.discount}}`}</Table.Head>
          {'{{/showDiscount}}'}
          <Table.Head direction='right'>{`{{labels.value_net}}`}</Table.Head>
          {'{{#order.isVatApplicable}}'}
          {'{{#showVatColumns}}'}
          <Table.Head direction='right'>{`{{labels.VAT_quote}}`}</Table.Head>
          <Table.Head direction='right'>{`{{labels.VAT_amount}}`}</Table.Head>
          {'{{/showVatColumns}}'}
          {'{{/order.isVatApplicable}}'}
        </Table.Header>
        <Table.Body>
          {`{{#order.items}}`}
          <Table.Row>
            <Table.Cell>{`{{index}}`}</Table.Cell>
            <Table.Cell>{`{{name}}`}</Table.Cell>
            <Table.Cell className='text-right'>{`{{quantity}}`}</Table.Cell>
            <Table.Cell>{`{{measurementUnit}}`}</Table.Cell>
            <Table.Cell direction='right'>{`{{price}}`}</Table.Cell>
            {'{{#showDiscount}}'}
            <Table.Cell direction='right'>{`{{discount}}`}</Table.Cell>
            {'{{/showDiscount}}'}
            <Table.Cell direction='right'>{`{{value}}`}</Table.Cell>
            {'{{#order.isVatApplicable}}'}
            {'{{#showVatColumns}}'}
            <Table.Cell direction='right'>{`{{VAT}}`}</Table.Cell>
            <Table.Cell direction='right'>{`{{vatAmount}}`}</Table.Cell>
            {'{{/showVatColumns}}'}
            {'{{/order.isVatApplicable}}'}
          </Table.Row>
          {`{{/order.items}}`}
        </Table.Body>
      </Table>
      <BottomInfo>
        <BottomInfo.Container>
          {'{{#order.isVatApplicable}}'}
          <BottomInfo.Info label={`{{labels.subtotal}}`}>{`{{order.subTotalAmount}}`}</BottomInfo.Info>
          {'{{/order.isVatApplicable}}'}
          <BottomInfo.Info label={`{{labels.discount}}`}>{`{{order.discountAmount}}`}</BottomInfo.Info>
          {'{{#order.isVatApplicable}}'}
          <BottomInfo.Info className='border-b' label={`{{labels.VAT_amount}}`}>{`{{order.taxAmount}}`}</BottomInfo.Info>
          {'{{/order.isVatApplicable}}'}
          {'{{#order.isVatApplicable}}'}
          <BottomInfo.Info label={`{{labels.total}}`} labelClassName='!text-sm pt-4' valueClassName='!text-sm pt-4'>{`{{order.totalAmount}}`}</BottomInfo.Info>
          {'{{/order.isVatApplicable}}'}
          {'{{^order.isVatApplicable}}'}
          <BottomInfo.Info label={`{{labels.total}} ({{labels.without_vat}})`} labelClassName='!text-sm pt-4' valueClassName='!text-sm pt-4'>{`{{order.totalAmount}}`}</BottomInfo.Info>
          {'{{/order.isVatApplicable}}'}
        </BottomInfo.Container>
      </BottomInfo>
      {`{{#order.notes}}`}
      <RandomInfo>
        <div className='flex flex-col'>
          <div className='text-[10px] font-light text-sm'>{`{{labels.notes}}`}:</div>
          <div className='text-xs'>{`{{{order.notes}}}`}</div>
        </div>
      </RandomInfo>
      {`{{/order.notes}}`}
    </Page>
  );
};

export default Quote;
