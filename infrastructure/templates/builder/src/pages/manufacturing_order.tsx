import Header from 'components/Header';
import Page from 'components/Page';
import RandomInfo from 'components/RandomInfo';
import React, {useEffect, useState} from 'react';
import Table from 'components/Table';
import TopInfo from 'components/TopInfo';

const MODump = () => {
  const [hydrated, setHydrated] = useState(false);

  useEffect(() => {
    setHydrated(true);
  }, []);

  if (!hydrated) {
    return null;
  }

  return (
      <Page fileName='manufacturing_order'>
        <Header>
          <Header.Title>{`{{labels.manufacturing_order}}`}</Header.Title>
          <Header.Subtitle textSize='text-base'>
            <div>{`{{labels.number}}`}: {`{{order.number}}`}</div>
            {`{{#saleOrderNumber}}`}
            <div>{`{{labels.sales_order}}`}: {`{{saleOrderNumber}}`}</div>
            {`{{/saleOrderNumber}}`}
          </Header.Subtitle>
          <TopInfo>
            {`{{#clientName}}`}
            <TopInfo.Info label={`{{labels.client}}`} labelTextSize='text-base' valueTextSize='text-base'>{`{{clientName}}`}</TopInfo.Info>
          {`{{/clientName}}`}
            <TopInfo.Info label={`{{labels.order_date}}`} labelTextSize='text-base' valueTextSize='text-base'>{`{{order.createTime}}`}</TopInfo.Info>
            <TopInfo.Info label={`{{labels.production_deadline}}`} labelTextSize='text-base' valueTextSize='text-base'>{`{{order.productionDeadline}}`}</TopInfo.Info>
            <TopInfo.Info label={`{{labels.expected_by}}`} labelTextSize='text-base' valueTextSize='text-base'>{`{{order.estimatedCompletionDate}}`}</TopInfo.Info>
          </TopInfo>
        </Header>
        <RandomInfo>
          <div className='flex gap-4'>
            <div className='text-base font-light'>{`{{labels.product}}`}:</div>
            <div className='text-base font-semibold'>{`{{product.name}}`}</div>
          </div>
          <div className='flex gap-4'>
            <div className='text-base font-light'>{`{{labels.code}}`}:</div>
            <div className='text-base font-semibold'>{`{{product.code}}`}</div>
          </div>
          {`{{#product.customProduct}}`}
          <div className='flex gap-4'>
            <div className='text-base font-light'>{`{{labels.customProduct}}`}:</div>
            <div className='text-base font-semibold'>{`{{product.customProduct}}`}</div>
          </div>
          {`{{/product.customProduct}}`}
          <div className='flex gap-4'>
            <div className='text-base font-light'>{`{{labels.quantity}}`}:</div>
            <div className='text-base font-semibold'>{`{{product.quantity}} {{product.measurementUnit}}`}</div>
          </div>
          <div className='flex gap-4'>
            <div className='text-base font-light whitespace-pre'>{`{{labels.notes}}`}:</div>
            <div className='text-base font-semibold'>{`{{order.notes}}`}</div>
          </div>
        </RandomInfo>
        <Table title={`{{labels.tasks}}`} titleTextSize='text-base' withoutCurrency>
          <Table.Header>
            <Table.Head textSize='text-base'>{`{{labels.number_abbreviation}}`}</Table.Head>
            <Table.Head textSize='text-base'>{`{{labels.name}}`}</Table.Head>
            <Table.Head textSize='text-base' direction='right'>{`{{labels.duration}}`}</Table.Head>
            <Table.Head textSize='text-base'>{`{{labels.employees}}`}</Table.Head>
            <Table.Head textSize='text-base'>{`{{labels.workstations}}`}</Table.Head>
            <Table.Head direction='right' textSize='text-base'>{`{{labels.done}}`}</Table.Head>
          </Table.Header>
          <Table.Body>
            {`{{#order.manufacturingTasks}}`}
            <Table.Row>
              <Table.Cell textSize='text-base'>{`{{index}}`}</Table.Cell>
              <Table.Cell textSize='text-base'>{`{{name}}`}</Table.Cell>
              <Table.Cell textSize='text-base' direction='right'>{`{{duration}}`}</Table.Cell>
              <Table.Cell textSize='text-base'>{`{{employees}}`}</Table.Cell>
              <Table.Cell textSize='text-base'>{`{{workstations}}`}</Table.Cell>
              <Table.Cell direction='right' textSize='text-base'><input className='size-8'
                                                                        type='checkbox'/></Table.Cell>
            </Table.Row>
            {/* Materials row (only when task has materials) */}
            {`{{#materials}}`}
            <tr>
              <td colSpan={99} className='p-0'>
                {`{{> materialsTable}}`}
              </td>
            </tr>
            {`{{/materials}}`}
            {`{{/order.manufacturingTasks}}`}
          </Table.Body>
        </Table>
        <Table title={`{{labels.materials}}`} titleTextSize='text-base' withoutCurrency>
          <Table.Header>
            <Table.Head textSize='text-base'>{`{{labels.number_abbreviation}}`}</Table.Head>
            <Table.Head textSize='text-base'>{`{{labels.name}}`}</Table.Head>
            <Table.Head textSize='text-base'>{`{{labels.code}}`}</Table.Head>
            <Table.Head textSize='text-base'>{`{{labels.measurement_unit_abbreviation}}`}</Table.Head>
            <Table.Head direction='right' textSize='text-base'>{`{{labels.required}}`}</Table.Head>
            <Table.Head direction='right' textSize='text-base'>{`{{labels.required_total}}`}</Table.Head>
            {`{{#order.hasDimensions}}`}
            <Table.Head textSize='text-base'>{`{{labels.dimensions}}`}</Table.Head>
            {`{{/order.hasDimensions}}`}
          </Table.Header>
          <Table.Body>
            {`{{#order.requiredMaterials}}`}
            <Table.Row>
              <Table.Cell textSize='text-base'>{`{{index}}`}</Table.Cell>
              <Table.Cell textSize='text-base'>{`{{name}}`}</Table.Cell>
              <Table.Cell textSize='text-base'>{`{{code}}`}</Table.Cell>
              <Table.Cell textSize='text-base'>{`{{measurementUnit}}`}</Table.Cell>
              <Table.Cell direction='right' textSize='text-base'>{`{{required}}`}</Table.Cell>
              <Table.Cell direction='right' textSize='text-base'>{`{{requiredTotal}}`}</Table.Cell>
              {`{{#order.hasDimensions}}`}
              <Table.Cell textSize='text-base' className='whitespace-pre'>{`{{dimensions}}`}</Table.Cell>
              {`{{/order.hasDimensions}}`}
            </Table.Row>
            {`{{/order.requiredMaterials}}`}
          </Table.Body>
        </Table>
      </Page>
  );
};

export default MODump;
