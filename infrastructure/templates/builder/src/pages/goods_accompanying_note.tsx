import Header from 'components/Header';
import MiddleInfo from 'components/MiddleInfo';
import Page from 'components/Page';
import RandomInfo from 'components/RandomInfo';
import React, {FC, useEffect, useState} from 'react';
import Signature from 'components/Signature';
import SupplierDetails from 'components/SupplierDetails';
import Table from 'components/Table';
import TopInfo from 'components/TopInfo';

const GoodsAccompanyingNote: FC = () => {
  const [hydrated, setHydrated] = useState(false);

  useEffect(() => {
    setHydrated(true);
  }, []);

  if (!hydrated) {
    return null;
  }

  return (
    <div className='flex min-h-screen flex-col gap-8'>
      <Page fileName='goods_accompanying_note'>
        <Header>
          <Header.Title>{`{{labels.goods_accompanying_note_title}}`}</Header.Title>
          <Header.Subtitle>{`{{labels.number}}: {{note.number}}`}</Header.Subtitle>
          <TopInfo>
            <TopInfo.Info label={`{{labels.date}}`}>{`{{note.date}}`}</TopInfo.Info>
            <TopInfo.Info label={`{{labels.delivery_date}}`}>{`{{note.deliveryDate}}`}</TopInfo.Info>
          </TopInfo>
        </Header>
        <MiddleInfo>
          <SupplierDetails />
          {`{{#client.name}}`}
          <MiddleInfo.Container>
            <MiddleInfo.Special label={`{{labels.client}}`}>{`{{client.name}}`}</MiddleInfo.Special>
            <MiddleInfo.Details>
              <MiddleInfo.Info label={`{{labels.tax_identification_number}}`}>{`{{client.taxIdentificationNumber}}`}</MiddleInfo.Info>
              <MiddleInfo.Info label={`{{labels.identification_number}}`}>{`{{client.identificationNumber}}`}</MiddleInfo.Info>
              <MiddleInfo.Info label={`{{labels.address}}`}>{`{{client.address}}`}</MiddleInfo.Info>
            </MiddleInfo.Details>
          </MiddleInfo.Container>
          {`{{/client.name}}`}
        </MiddleInfo>
        <RandomInfo>
          <div className='mb-4 flex items-center gap-4'>
            <div className='text-[10px] font-light'>{`{{labels.loading_point}}`}:</div>
            <div className='text-[10px] font-semibold'>{`{{note.from}}`}</div>
          </div>
          <div className='flex gap-4'>
            <div className='text-[10px] font-light'>{`{{labels.unloading_point}}`}:</div>
            <div className='text-[10px] font-semibold'>{`{{note.to}}`}</div>
          </div>
        </RandomInfo>
        <Table>
          <Table.Header>
            <Table.Head>{`{{labels.number_abbreviation}}`}</Table.Head>
            <Table.Head>{`{{labels.item_name}}`}</Table.Head>
            <Table.Head direction='right'>{`{{labels.quantity}}`}</Table.Head>
            <Table.Head>{`{{labels.measurement_unit_abbreviation}}`}</Table.Head>
            <Table.Head direction='right'>{`{{labels.unit_price}}`}</Table.Head>
            <Table.Head direction='right'>{`{{labels.discount}}`}</Table.Head>
            <Table.Head direction='right'>{`{{labels.value_net}}`}</Table.Head>
            <Table.Head direction='right'>{`{{labels.VAT_quote}}`}</Table.Head>
            <Table.Head direction='right'>{`{{labels.VAT_amount}}`}</Table.Head>
          </Table.Header>
          <Table.Body>
            {`{{#note.items}}`}
            <Table.Row>
              <Table.Cell>{`{{index}}`}</Table.Cell>
              <Table.Cell>{`{{name}}`}</Table.Cell>
              <Table.Cell direction='right'>{`{{quantity}}`}</Table.Cell>
              <Table.Cell>{`{{measurementUnit}}`}</Table.Cell>
              <Table.Cell direction='right'>{`{{price}}`}</Table.Cell>
              <Table.Cell direction='right'>{`{{discount}}`}</Table.Cell>
              <Table.Cell direction='right'>{`{{value}}`}</Table.Cell>
              <Table.Cell direction='right'>{`{{VAT}}`}</Table.Cell>
              <Table.Cell direction='right'>{`{{vatAmount}}`}</Table.Cell>
            </Table.Row>
            {`{{/note.items}}`}
          </Table.Body>
        </Table>
        <RandomInfo>
          <div className='flex items-center gap-4'>
            <div className='text-xs font-light'>{`{{labels.notes}}`}:</div>
            <div className='text-xs whitespace-pre'>{`{{note.notes}}`}</div>
          </div>
          <div className='mt-4 flex flex-col gap-4'>
            <div className='text-[10px]'>{`{{labels.transport_details}}`}:</div>
            <div className='flex items-center gap-4'>
              <div className='text-[10px] font-light'>{`{{labels.delegate_name}}`}:</div>
              <div className='text-xs'>{`{{note.delegate.name}}`}</div>
            </div>
            <div className='flex items-center gap-4'>
              <div className='text-[10px] font-light'>{`{{labels.transportation_registration_number}}`}:</div>
              <div className='text-xs'>{`{{note.transportRegistrationNumber}}`}</div>
            </div>
          </div>
        </RandomInfo>
      </Page>
      <div className='grow' />
      <div className='mx-20 mb-4 flex justify-between gap-20 items-end'>
        <Signature label={`{{labels.sender_signature}}`}>{`{{note.sender_signature}}`}</Signature>
        <Signature label={`{{labels.driver_signature}}`}>{`{{note.driver_signature}}`}</Signature>
        <Signature label={`{{labels.receiver_signature}}`}>{`{{note.receiver_signature}}`}</Signature>
      </div>
    </div>
  );
};

export default GoodsAccompanyingNote;
