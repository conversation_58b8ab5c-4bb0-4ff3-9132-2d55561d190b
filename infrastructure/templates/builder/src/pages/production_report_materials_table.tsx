import Page from 'components/Page';
import React, {FC, useEffect, useState} from 'react';
import Table from 'components/Table';

const ProductionReportMaterialsTable: FC = () => {
  const [hydrated, setHydrated] = useState(false);

  useEffect(() => {
    setHydrated(true);
  }, []);

  if (!hydrated) {
    return null;
  }

  return (
    <Page fileName='production_report_materials_table'>
      <Table title={`{{labels.tasks}}`}>
        <Table.Header>
          <Table.Head>{`{{labels.number_abbreviation}}`}</Table.Head>
          <Table.Head>{`{{labels.material_name}}`}</Table.Head>
          <Table.Head>{`{{labels.code}}`}</Table.Head>
          <Table.Head>{`{{labels.measurement_unit_abbreviation}}`}</Table.Head>
          <Table.Head direction='right'>{`{{labels.standard_consumption}}`}</Table.Head>
          <Table.Head direction='right'>{`{{labels.actual_consumption}}`}</Table.Head>
          <Table.Head direction='right'>{`{{labels.unit_cost}}`}</Table.Head>
          <Table.Head direction='right'>{`{{labels.total_cost}}`}</Table.Head>
        </Table.Header>
        <Table.Body>
          {`{{#items}}`}
          <Table.Row>
            <Table.Cell>
              <span
                dangerouslySetInnerHTML={{
                  __html: `<span style="padding-left: calc({{level}} * 20px);">{{index}}</span>`,
                }}
              />
              <Table.Cell>{`{{name}}`}</Table.Cell>
              <Table.Cell>{`{{code}}`}</Table.Cell>
              <Table.Cell>{`{{measurementUnit}}`}</Table.Cell>
              <Table.Cell direction='right'>{`{{standardConsumption}}`}</Table.Cell>
              <Table.Cell direction='right'>{`{{actualConsumption}}`}</Table.Cell>
              <Table.Cell direction='right'>{`{{unitCost}}`}</Table.Cell>
              <Table.Cell direction='right'>{`{{totalCost}}`}</Table.Cell>
            </Table.Cell>
          </Table.Row>
          {/* Materials row (only when material has materials) */}
          {`{{#materials}}`}
          <tr>
            <td colSpan={99} className='p-0'>
              {`{{> materialsTable}}`}
            </td>
          </tr>
          {`{{/materials}}`}
          {`{{/items}}`}
        </Table.Body>
      </Table>
    </Page>
  );
};

export default ProductionReportMaterialsTable;
