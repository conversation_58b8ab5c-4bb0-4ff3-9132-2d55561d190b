import Header from 'components/Header';
import Page from 'components/Page';
import RandomInfo from 'components/RandomInfo';
import React, {FC, useEffect, useState} from 'react';
import Table from 'components/Table';
import TopInfo from 'components/TopInfo';
import Signature from 'components/Signature';
import Image from '../components/Image';

const ProductionReport: FC = () => {
  const [hydrated, setHydrated] = useState(false);

  useEffect(() => {
    setHydrated(true);
  }, []);

  if (!hydrated) {
    return null;
  }

  return (
    <div className='flex min-h-screen flex-col gap-8'>
    <Page fileName='service_report'>
      <Header>
        <Header.Title>{`{{labels.service_report_title}}`}</Header.Title>
        <Header.Subtitle>{`{{labels.number}}: {{number}}`}</Header.Subtitle>
        <TopInfo>
          <TopInfo.Info label={`{{labels.date}}`}>{`{{date}}`}</TopInfo.Info>
          <TopInfo.Info label={`{{labels.supplier}}`}>{`{{supplier}}`}</TopInfo.Info>
          <TopInfo.Info label={`{{labels.client}}`}>{`{{customer}}`}</TopInfo.Info>
        </TopInfo>
      </Header>
      <RandomInfo>
        <div className='flex flex-col'>
          <div className='text-[10px] font-light text-sm'>{`{{labels.service_details}}`}:</div>
          <div className='text-xs'>{`{{{notes}}}`}</div>
        </div>
      </RandomInfo>
      <Table title={`{{labels.material_consumption}}`} withoutCurrency>
        <Table.Header>
          <Table.Head>{`{{labels.number_abbreviation}}`}</Table.Head>
          <Table.Head>{`{{labels.material_name}}`}</Table.Head>
          <Table.Head>{`{{labels.code}}`}</Table.Head>
          <Table.Head>{`{{labels.measurement_unit_abbreviation}}`}</Table.Head>
          <Table.Head direction='right'>{`{{labels.quantity}}`}</Table.Head>
        </Table.Header>
        <Table.Body>
          {`{{#materials}}`}
          <Table.Row>
            <Table.Cell>{`{{index}}`}</Table.Cell>
            <Table.Cell>{`{{name}}`}</Table.Cell>
            <Table.Cell>{`{{code}}`}</Table.Cell>
            <Table.Cell>{`{{measurementUnit}}`}</Table.Cell>
            <Table.Cell direction='right'>{`{{quantity}}`}</Table.Cell>
          </Table.Row>
          {`{{/materials}}`}
        </Table.Body>
      </Table>
      <Table title={`{{labels.work_performed}}`} withoutCurrency>
        <Table.Header>
          <Table.Head />
          <Table.Head direction='right'>{`{{labels.duration}}`}</Table.Head>
        </Table.Header>
        <Table.Body>
          {`{{#operations}}`}
          <Table.Row>
            <Table.Cell>{`{{name}}`}</Table.Cell>
            <Table.Cell direction='right'>{`{{duration}}`}</Table.Cell>
          </Table.Row>
          {`{{/operations}}`}
        </Table.Body>
      </Table>

    </Page><div className='grow'/>
      <div className='mx-20 mb-4 flex justify-between gap-20 items-end'>
        <Signature label={`{{labels.worker}}`}><Image image="workerSignature" />{`{{worker}}`}</Signature>
        <Signature label={`{{labels.on_behalf_of_customer}}`}><Image image="clientSignature" />{`{{clientRepresentative}}`}</Signature>
      </div>
    </div>
  );
};

export default ProductionReport;
