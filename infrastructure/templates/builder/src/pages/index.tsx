import {FC, useState} from 'react';
import Link from 'next/link';

interface PageData {
  name: string;
  partials?: string[];
}

const Main: FC = () => {
  const [searchTerm, setSearchTerm] = useState<string>('');

  const pages: PageData[] = [
    {name: 'invoice'},
    {name: 'invoice_proforma'},
    {name: 'purchase_order'},
    {name: 'quote'},
    {name: 'reception_receipt'},
    {name: 'material_issue_note'},
    {name: 'goods_accompanying_note'},
    {name: 'manufacturing_order'},
    {name: 'production_report', partials: ['production_report_materials_table']},
    {name: 'service_report'},
  ].sort((a, b) => a.name.localeCompare(b.name));

  const filteredPages = pages.filter((page) => {
    return searchTerm === '' || page.name.toLowerCase().includes(searchTerm.toLowerCase());
  });

  return (
    <div className='min-h-screen bg-gray-900 text-white'>
      <main className='container mx-auto py-20'>
        <div className='text-center mb-8'>
          <h1 className='text-4xl font-bold mb-6'>Document Templates</h1>

          <div className='mb-8'>
            <input
              type='text'
              placeholder='Search pages...'
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className='w-80 px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
            />
          </div>

          <div className='grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3'>
            {filteredPages.map((page) => (
              <div key={page.name} className='bg-gray-800 rounded-lg overflow-hidden flex flex-col'>
                <Link className='flex-1 flex items-center justify-center text-center font-bold text-white hover:bg-gray-700 hover:text-blue-300 transition-colors p-6' href={`/${page.name}`}>
                  {page.name}
                </Link>

                <div className='border-t border-gray-700 flex items-center justify-center'>
                  {page.partials && page.partials.length > 0 ? (
                    page.partials.map((partial) => (
                      <Link key={partial} className='block text-xs text-orange-300 hover:text-orange-200 hover:bg-gray-700 transition-colors px-4 py-2 border-t border-gray-700 first:border-t-0' href={`/${partial}`}>
                        {partial}
                      </Link>
                    ))
                  ) : (
                    <div className='text-xs text-gray-600 px-4 py-2'>No partials</div>
                  )}
                </div>
              </div>
            ))}
          </div>

          {filteredPages.length === 0 && (
            <div className='text-center py-12'>
              <p className='text-gray-400'>No pages found matching your search.</p>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default Main;
