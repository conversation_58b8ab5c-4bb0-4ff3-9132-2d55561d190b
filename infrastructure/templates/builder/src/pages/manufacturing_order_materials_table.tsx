import Page from 'components/Page';
import React, {FC, useEffect, useState} from 'react';
import Table from 'components/Table';

const ManufacturingOrderMaterialsTable: FC = () => {
  const [hydrated, setHydrated] = useState(false);

  useEffect(() => {
    setHydrated(true);
  }, []);

  if (!hydrated) {
    return null;
  }

  return (
    <Page fileName='manufacturing_order_materials_table'>
      <Table title={`{{labels.materials}}`}>
        <Table.Header>
          <Table.Head>{`{{labels.number_abbreviation}}`}</Table.Head>
          <Table.Head>{`{{labels.material_name}}`}</Table.Head>
          <Table.Head>{`{{labels.code}}`}</Table.Head>
          <Table.Head>{`{{labels.measurement_unit_abbreviation}}`}</Table.Head>
          <Table.Head direction='right'>{`{{labels.required}}`}</Table.Head>
          <Table.Head direction='right'>{`{{labels.required_total}}`}</Table.Head>
          {`{{#order.hasDimensions}}`}
          <Table.Head>{`{{labels.dimensions}}`}</Table.Head>
          {`{{/order.hasDimensions}}`}
        </Table.Header>
        <Table.Body>
          {`{{#items}}`}
          <Table.Row>
            <Table.Cell>
              <span
                dangerouslySetInnerHTML={{
                  __html: `<span style="padding-left: calc({{level}} * 20px);">{{index}}</span>`,
                }}
              />
            </Table.Cell>
            <Table.Cell>{`{{name}}`}</Table.Cell>
            <Table.Cell>{`{{code}}`}</Table.Cell>
            <Table.Cell>{`{{measurementUnit}}`}</Table.Cell>
            <Table.Cell direction='right'>{`{{required}}`}</Table.Cell>
            <Table.Cell direction='right'>{`{{requiredTotal}}`}</Table.Cell>
            {`{{#order.hasDimensions}}`}
            <Table.Cell className='whitespace-pre'>{`{{dimensions}}`}</Table.Cell>
            {`{{/order.hasDimensions}}`}
          </Table.Row>
          {/* Materials row (only when material has sub-materials) */}
          {`{{#materials}}`}
          <tr>
            <td colSpan={99} className='p-0'>
              {`{{> templates/pdf/manufacturing_order_materials_table}}`}
            </td>
          </tr>
          {`{{/materials}}`}
          {`{{/items}}`}
        </Table.Body>
      </Table>
    </Page>
  );
};

export default ManufacturingOrderMaterialsTable;
