import Page from 'components/Page';
import React, {FC, useEffect, useState} from 'react';
import Table from 'components/Table';

const ManufacturingOrderOperationsTable: FC = () => {
  const [hydrated, setHydrated] = useState(false);

  useEffect(() => {
    setHydrated(true);
  }, []);

  if (!hydrated) {
    return null;
  }

  return (
    <Page fileName='manufacturing_order_operations_table'>
      <Table title={`{{labels.operations}}`}>
        <Table.Header>
          <Table.Head>{`{{labels.number_abbreviation}}`}</Table.Head>
          <Table.Head>{`{{labels.name}}`}</Table.Head>
          <Table.Head direction='right'>{`{{labels.duration}}`}</Table.Head>
          <Table.Head>{`{{labels.employees}}`}</Table.Head>
          <Table.Head>{`{{labels.workstations}}`}</Table.Head>
        </Table.Header>
        <Table.Body>
          {`{{#items}}`}
          <Table.Row>
            <Table.Cell>
              <span
                dangerouslySetInnerHTML={{
                  __html: `<span style="padding-left: calc({{level}} * 20px);">{{index}}</span>`,
                }}
              />
            </Table.Cell>
            <Table.Cell>{`{{name}}`}</Table.Cell>
            <Table.Cell direction='right'>{`{{duration}}`}</Table.Cell>
            <Table.Cell>{`{{employees}}`}</Table.Cell>
            <Table.Cell>{`{{workstations}}`}</Table.Cell>
          </Table.Row>
          {/* Materials row (only when operation has materials) */}
          {`{{#materials}}`}
          <tr>
            <td colSpan={99} className='p-0'>
              {`{{> materialsTable}}`}
            </td>
          </tr>
          {`{{/materials}}`}
          {`{{/items}}`}
        </Table.Body>
      </Table>
    </Page>
  );
};

export default ManufacturingOrderOperationsTable;
