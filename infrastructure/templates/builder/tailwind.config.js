const defaultTheme = require('tailwindcss/defaultTheme');
const plugin = require('tailwindcss/plugin');

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{js,ts,jsx,tsx}'],
  plugins: [
    plugin(({addVariant}) => {
      addVariant('not-first-of-type', '&:not(:first-of-type)');
      addVariant('not-last-of-type', '&:not(:last-of-type)');
      addVariant('odd', '&:nth-of-type(odd)');
      addVariant('even', '&:nth-of-type(even)');
      addVariant('not-disabled', '&:not([disabled])');
    }),
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', ...defaultTheme.fontFamily.sans],
      },
    },
  },
};
