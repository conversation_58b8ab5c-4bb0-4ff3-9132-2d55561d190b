<html>
  <head>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=block" rel="stylesheet"></link>
    <style>*,::before,::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

*,::before,::after {
  box-sizing: border-box; 
  border-width: 0; 
  border-style: solid; 
  border-color: #e5e7eb; 
}::before,::after {
  --tw-content: '';
}



html,:host {
  line-height: 1.5; 
  -webkit-text-size-adjust: 100%; 
  -moz-tab-size: 4; 
  -o-tab-size: 4;
     tab-size: 4; 
  font-family: Inter, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; 
  font-feature-settings: normal; 
  font-variation-settings: normal; 
  -webkit-tap-highlight-color: transparent; 
}



body {
  margin: 0; 
  line-height: inherit; 
}



hr {
  height: 0; 
  color: inherit; 
  border-top-width: 1px; 
}



abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}



h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}



a {
  color: inherit;
  text-decoration: inherit;
}



b,
strong {
  font-weight: bolder;
}



code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; 
  font-feature-settings: normal; 
  font-variation-settings: normal; 
  font-size: 1em; 
}



small {
  font-size: 80%;
}



sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}



table {
  text-indent: 0; 
  border-color: inherit; 
  border-collapse: collapse; 
}



button,
input,
optgroup,
select,
textarea {
  font-family: inherit; 
  font-feature-settings: inherit; 
  font-variation-settings: inherit; 
  font-size: 100%; 
  font-weight: inherit; 
  line-height: inherit; 
  letter-spacing: inherit; 
  color: inherit; 
  margin: 0; 
  padding: 0; 
}



button,
select {
  text-transform: none;
}



button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; 
  background-color: transparent; 
  background-image: none; 
}

:-moz-focusring {
  outline: auto;
}

:-moz-ui-invalid {
  box-shadow: none;
}



progress {
  vertical-align: baseline;
}

::-webkit-inner-spin-button,::-webkit-outer-spin-button {
  height: auto;
}



[type='search'] {
  -webkit-appearance: textfield; 
  outline-offset: -2px; 
}

::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button; 
  font: inherit; 
}



summary {
  display: list-item;
}



blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}


dialog {
  padding: 0;
}



textarea {
  resize: vertical;
}



input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; 
  color: #9ca3af; 
}

input::placeholder,
textarea::placeholder {
  opacity: 1; 
  color: #9ca3af; 
}



button,
[role="button"] {
  cursor: pointer;
}

:disabled {
  cursor: default;
}



img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; 
  vertical-align: middle; 
}



img,
video {
  max-width: 100%;
  height: auto;
}


[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}.container {
  width: 100%;
}
@media (min-width: 640px) {.container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {.container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {.container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {.container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {.container {
    max-width: 1536px;
  }
}.absolute {
  position: absolute;
}.left-1\/2 {
  left: 50%;
}.right-0 {
  right: 0px;
}.top-0 {
  top: 0px;
}.top-0\.5 {
  top: 0.125rem;
}.mx-20 {
  margin-left: 5rem;
  margin-right: 5rem;
}.mx-auto {
  margin-left: auto;
  margin-right: auto;
}.mb-1 {
  margin-bottom: 0.25rem;
}.mb-2 {
  margin-bottom: 0.5rem;
}.mb-4 {
  margin-bottom: 1rem;
}.mb-6 {
  margin-bottom: 1.5rem;
}.mb-8 {
  margin-bottom: 2rem;
}.mt-4 {
  margin-top: 1rem;
}.block {
  display: block;
}.inline {
  display: inline;
}.flex {
  display: flex;
}.table {
  display: table;
}.grid {
  display: grid;
}.size-8 {
  width: 2rem;
  height: 2rem;
}.h-12 {
  height: 3rem;
}.h-24 {
  height: 6rem;
}.min-h-screen {
  min-height: 100vh;
}.w-80 {
  width: 20rem;
}.w-full {
  width: 100%;
}.max-w-96 {
  max-width: 24rem;
}.max-w-\[50vw\] {
  max-width: 50vw;
}.flex-1 {
  flex: 1 1 0%;
}.grow {
  flex-grow: 1;
}.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}.flex-col {
  flex-direction: column;
}.items-end {
  align-items: flex-end;
}.items-center {
  align-items: center;
}.justify-end {
  justify-content: flex-end;
}.justify-center {
  justify-content: center;
}.justify-between {
  justify-content: space-between;
}.gap-1 {
  gap: 0.25rem;
}.gap-16 {
  gap: 4rem;
}.gap-2 {
  gap: 0.5rem;
}.gap-20 {
  gap: 5rem;
}.gap-4 {
  gap: 1rem;
}.gap-6 {
  gap: 1.5rem;
}.gap-8 {
  gap: 2rem;
}.divide-y >:not([hidden]) ~:not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}.overflow-hidden {
  overflow: hidden;
}.whitespace-pre {
  white-space: pre;
}.rounded {
  border-radius: 0.25rem;
}.rounded-lg {
  border-radius: 0.5rem;
}.border {
  border-width: 1px;
}.border-b {
  border-bottom-width: 1px;
}.border-t {
  border-top-width: 1px;
}.border-black {
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}.border-gray-700 {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}.p-0 {
  padding: 0px;
}.p-6 {
  padding: 1.5rem;
}.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}.pb-1 {
  padding-bottom: 0.25rem;
}.pb-2 {
  padding-bottom: 0.5rem;
}.pb-4 {
  padding-bottom: 1rem;
}.pl-16 {
  padding-left: 4rem;
}.pl-4 {
  padding-left: 1rem;
}.pt-4 {
  padding-top: 1rem;
}.text-left {
  text-align: left;
}.text-center {
  text-align: center;
}.text-right {
  text-align: right;
}.\!text-sm {
  font-size: 0.875rem!important;
  line-height: 1.25rem!important;
}.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}.text-\[10px\] {
  font-size: 10px;
}.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}.font-bold {
  font-weight: 700;
}.font-light {
  font-weight: 300;
}.font-medium {
  font-weight: 500;
}.font-semibold {
  font-weight: 600;
}.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}.text-orange-300 {
  --tw-text-opacity: 1;
  color: rgb(253 186 116 / var(--tw-text-opacity, 1));
}.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}.placeholder-gray-400::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}.placeholder-gray-400::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.first\:border-t-0:first-child {
  border-top-width: 0px;
}.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}.hover\:bg-gray-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}.hover\:text-blue-300:hover {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}.hover\:text-orange-200:hover {
  --tw-text-opacity: 1;
  color: rgb(254 215 170 / var(--tw-text-opacity, 1));
}.focus\:border-transparent:focus {
  border-color: transparent;
}.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.focus\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}.not-last-of-type\:border-b:not(:last-of-type) {
  border-bottom-width: 1px;
}
@media (min-width: 640px) {.sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
@media (min-width: 768px) {.md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

</style>
  </head>
  <body>
    <div>
      <div class="flex flex-col divide-y">
        <div class="flex flex-col pb-4">
          <div class="font-semibold text-2xl">{{labels.manufacturing_order}}</div>
          <div class="pb-4 font-medium text-base">
            <div>{{labels.number}}: {{order.number}}</div>
{{#saleOrderNumber}}
            <div>{{labels.sales_order}}: {{saleOrderNumber}}</div>
{{/saleOrderNumber}}
          </div>
          <div class="flex gap-16">
{{#clientName}}
            <div class="flex flex-col font-medium gap-1">
              <div class="text-base">{{labels.client}}</div>
              <div class="text-base">{{clientName}}</div>
            </div>
{{/clientName}}
            <div class="flex flex-col font-medium gap-1">
              <div class="text-base">{{labels.order_date}}</div>
              <div class="text-base">{{order.createTime}}</div>
            </div>
            <div class="flex flex-col font-medium gap-1">
              <div class="text-base">{{labels.production_deadline}}</div>
              <div class="text-base">{{order.productionDeadline}}</div>
            </div>
            <div class="flex flex-col font-medium gap-1">
              <div class="text-base">{{labels.expected_by}}</div>
              <div class="text-base">{{order.estimatedCompletionDate}}</div>
            </div>
          </div>
{{#base64Logo}}
          <img alt="Logo Icon" class="absolute right-0 top-0 h-12" src="data:image/png;base64,{{base64Logo}}"></img>
{{/base64Logo}}
        </div>
        <div class="flex flex-col py-4">
          <div class="flex gap-4">
            <div class="text-base font-light">{{labels.product}}:</div>
            <div class="text-base font-semibold">{{product.name}}</div>
          </div>
          <div class="flex gap-4">
            <div class="text-base font-light">{{labels.code}}:</div>
            <div class="text-base font-semibold">{{product.code}}</div>
          </div>
{{#product.customProduct}}
          <div class="flex gap-4">
            <div class="text-base font-light">{{labels.customProduct}}:</div>
            <div class="text-base font-semibold">{{product.customProduct}}</div>
          </div>
{{/product.customProduct}}
          <div class="flex gap-4">
            <div class="text-base font-light">{{labels.quantity}}:</div>
            <div class="text-base font-semibold">{{product.quantity}} {{product.measurementUnit}}</div>
          </div>
          <div class="flex gap-4">
            <div class="text-base font-light whitespace-pre">{{labels.notes}}:</div>
            <div class="text-base font-semibold">{{order.notes}}</div>
          </div>
        </div>
        <div class="py-4">
          <div class="mb-2 text-base">{{labels.tasks}}</div>
          <table class="w-full">
            <thead>
              <tr class="not-last-of-type:border-b">
                <th class="font-light px-2 text-base text-left">{{labels.number_abbreviation}}</th>
                <th class="font-light px-2 text-base text-left">{{labels.name}}</th>
                <th class="font-light px-2 text-base text-right">{{labels.duration}}</th>
                <th class="font-light px-2 text-base text-left">{{labels.employees}}</th>
                <th class="font-light px-2 text-base text-left">{{labels.workstations}}</th>
                <th class="font-light px-2 text-base text-right">{{labels.done}}</th>
              </tr>
            </thead>
            <tbody class="text-center text-xs">
{{#order.manufacturingTasks}}
              <tr class="not-last-of-type:border-b">
                <td class="py-4 px-2 text-base text-left">{{index}}</td>
                <td class="py-4 px-2 text-base text-left">{{name}}</td>
                <td class="py-4 px-2 text-base text-right">{{duration}}</td>
                <td class="py-4 px-2 text-base text-left">{{employees}}</td>
                <td class="py-4 px-2 text-base text-left">{{workstations}}</td>
                <td class="py-4 px-2 text-base text-right">
                  <input class="size-8" type="checkbox"></input>
                </td>
              </tr>
{{#materials}}
              <tr>
                <td colspan="99" class="p-0">{{> templates/pdf/manufacturing_order_materials_table}}</td>
              </tr>
{{/materials}}
{{/order.manufacturingTasks}}
            </tbody>
          </table>
        </div>
        <div class="py-4">
          <div class="mb-2 text-base">{{labels.materials}}</div>
          <table class="w-full">
            <thead>
              <tr class="not-last-of-type:border-b">
                <th class="font-light px-2 text-base text-left">{{labels.number_abbreviation}}</th>
                <th class="font-light px-2 text-base text-left">{{labels.name}}</th>
                <th class="font-light px-2 text-base text-left">{{labels.code}}</th>
                <th class="font-light px-2 text-base text-left">{{labels.measurement_unit_abbreviation}}</th>
                <th class="font-light px-2 text-base text-right">{{labels.required}}</th>
                <th class="font-light px-2 text-base text-right">{{labels.required_total}}</th>
{{#order.hasDimensions}}
                <th class="font-light px-2 text-base text-left">{{labels.dimensions}}</th>
{{/order.hasDimensions}}
              </tr>
            </thead>
            <tbody class="text-center text-xs">
{{#order.requiredMaterials}}
              <tr class="not-last-of-type:border-b">
                <td class="py-4 px-2 text-base text-left">{{index}}</td>
                <td class="py-4 px-2 text-base text-left">{{name}}</td>
                <td class="py-4 px-2 text-base text-left">{{code}}</td>
                <td class="py-4 px-2 text-base text-left">{{measurementUnit}}</td>
                <td class="py-4 px-2 text-base text-right">{{required}}</td>
                <td class="py-4 px-2 text-base text-right">{{requiredTotal}}</td>
{{#order.hasDimensions}}
                <td class="py-4 px-2 text-base text-left whitespace-pre">{{dimensions}}</td>
{{/order.hasDimensions}}
              </tr>
{{/order.requiredMaterials}}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </body>
</html>
