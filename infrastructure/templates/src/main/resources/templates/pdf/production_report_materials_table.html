<html>
  <head>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=block" rel="stylesheet"></link>
    <style>*,::before,::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

*,::before,::after {
  box-sizing: border-box; 
  border-width: 0; 
  border-style: solid; 
  border-color: #e5e7eb; 
}::before,::after {
  --tw-content: '';
}



html,:host {
  line-height: 1.5; 
  -webkit-text-size-adjust: 100%; 
  -moz-tab-size: 4; 
  -o-tab-size: 4;
     tab-size: 4; 
  font-family: Inter, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; 
  font-feature-settings: normal; 
  font-variation-settings: normal; 
  -webkit-tap-highlight-color: transparent; 
}



body {
  margin: 0; 
  line-height: inherit; 
}



hr {
  height: 0; 
  color: inherit; 
  border-top-width: 1px; 
}



abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}



h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}



a {
  color: inherit;
  text-decoration: inherit;
}



b,
strong {
  font-weight: bolder;
}



code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; 
  font-feature-settings: normal; 
  font-variation-settings: normal; 
  font-size: 1em; 
}



small {
  font-size: 80%;
}



sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}



table {
  text-indent: 0; 
  border-color: inherit; 
  border-collapse: collapse; 
}



button,
input,
optgroup,
select,
textarea {
  font-family: inherit; 
  font-feature-settings: inherit; 
  font-variation-settings: inherit; 
  font-size: 100%; 
  font-weight: inherit; 
  line-height: inherit; 
  letter-spacing: inherit; 
  color: inherit; 
  margin: 0; 
  padding: 0; 
}



button,
select {
  text-transform: none;
}



button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; 
  background-color: transparent; 
  background-image: none; 
}

:-moz-focusring {
  outline: auto;
}

:-moz-ui-invalid {
  box-shadow: none;
}



progress {
  vertical-align: baseline;
}

::-webkit-inner-spin-button,::-webkit-outer-spin-button {
  height: auto;
}



[type='search'] {
  -webkit-appearance: textfield; 
  outline-offset: -2px; 
}

::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button; 
  font: inherit; 
}



summary {
  display: list-item;
}



blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}


dialog {
  padding: 0;
}



textarea {
  resize: vertical;
}



input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; 
  color: #9ca3af; 
}

input::placeholder,
textarea::placeholder {
  opacity: 1; 
  color: #9ca3af; 
}



button,
[role="button"] {
  cursor: pointer;
}

:disabled {
  cursor: default;
}



img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; 
  vertical-align: middle; 
}



img,
video {
  max-width: 100%;
  height: auto;
}


[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}.container {
  width: 100%;
}
@media (min-width: 640px) {.container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {.container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {.container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {.container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {.container {
    max-width: 1536px;
  }
}.fixed {
  position: fixed;
}.absolute {
  position: absolute;
}.relative {
  position: relative;
}.inset-0 {
  inset: 0px;
}.left-1\/2 {
  left: 50%;
}.right-0 {
  right: 0px;
}.top-0 {
  top: 0px;
}.top-0\.5 {
  top: 0.125rem;
}.left-3 {
  left: 0.75rem;
}.top-1\/2 {
  top: 50%;
}.mx-20 {
  margin-left: 5rem;
  margin-right: 5rem;
}.mx-auto {
  margin-left: auto;
  margin-right: auto;
}.mb-1 {
  margin-bottom: 0.25rem;
}.mb-2 {
  margin-bottom: 0.5rem;
}.mb-4 {
  margin-bottom: 1rem;
}.mt-4 {
  margin-top: 1rem;
}.mb-12 {
  margin-bottom: 3rem;
}.mb-8 {
  margin-bottom: 2rem;
}.mb-3 {
  margin-bottom: 0.75rem;
}.ml-1 {
  margin-left: 0.25rem;
}.mt-16 {
  margin-top: 4rem;
}.mb-6 {
  margin-bottom: 1.5rem;
}.mt-2 {
  margin-top: 0.5rem;
}.block {
  display: block;
}.inline {
  display: inline;
}.flex {
  display: flex;
}.inline-flex {
  display: inline-flex;
}.table {
  display: table;
}.grid {
  display: grid;
}.size-8 {
  width: 2rem;
  height: 2rem;
}.h-12 {
  height: 3rem;
}.h-24 {
  height: 6rem;
}.h-fit {
  height: -moz-fit-content;
  height: fit-content;
}.h-32 {
  height: 8rem;
}.h-8 {
  height: 2rem;
}.min-h-screen {
  min-height: 100vh;
}.w-full {
  width: 100%;
}.w-0 {
  width: 0px;
}.w-80 {
  width: 20rem;
}.max-w-96 {
  max-width: 24rem;
}.max-w-\[50vw\] {
  max-width: 50vw;
}.max-w-3xl {
  max-width: 48rem;
}.max-w-2xl {
  max-width: 42rem;
}.flex-1 {
  flex: 1 1 0%;
}.grow {
  flex-grow: 1;
}.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.columns-1 {
  -moz-columns: 1;
       columns: 1;
}.break-inside-avoid {
  -moz-column-break-inside: avoid;
       break-inside: avoid;
}.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}.grid-rows-2 {
  grid-template-rows: repeat(2, minmax(0, 1fr));
}.flex-col {
  flex-direction: column;
}.flex-wrap {
  flex-wrap: wrap;
}.items-start {
  align-items: flex-start;
}.items-end {
  align-items: flex-end;
}.items-center {
  align-items: center;
}.items-stretch {
  align-items: stretch;
}.justify-end {
  justify-content: flex-end;
}.justify-center {
  justify-content: center;
}.justify-between {
  justify-content: space-between;
}.gap-1 {
  gap: 0.25rem;
}.gap-16 {
  gap: 4rem;
}.gap-2 {
  gap: 0.5rem;
}.gap-20 {
  gap: 5rem;
}.gap-4 {
  gap: 1rem;
}.gap-6 {
  gap: 1.5rem;
}.gap-8 {
  gap: 2rem;
}.gap-3 {
  gap: 0.75rem;
}.space-y-2 >:not([hidden]) ~:not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}.space-y-6 >:not([hidden]) ~:not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}.divide-y >:not([hidden]) ~:not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}.overflow-hidden {
  overflow: hidden;
}.whitespace-pre {
  white-space: pre;
}.rounded {
  border-radius: 0.25rem;
}.rounded-lg {
  border-radius: 0.5rem;
}.rounded-full {
  border-radius: 9999px;
}.rounded-xl {
  border-radius: 0.75rem;
}.border {
  border-width: 1px;
}.border-b {
  border-bottom-width: 1px;
}.border-t {
  border-top-width: 1px;
}.border-black {
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}.border-white\/20 {
  border-color: rgb(255 255 255 / 0.2);
}.border-gray-600\/50 {
  border-color: rgb(75 85 99 / 0.5);
}.border-orange-500\/30 {
  border-color: rgb(249 115 22 / 0.3);
}.border-white\/10 {
  border-color: rgb(255 255 255 / 0.1);
}.border-gray-700 {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}.bg-white\/10 {
  background-color: rgb(255 255 255 / 0.1);
}.bg-blue-400\/20 {
  background-color: rgb(96 165 250 / 0.2);
}.bg-gray-700\/50 {
  background-color: rgb(55 65 81 / 0.5);
}.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}.bg-gray-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}.from-indigo-500 {
  --tw-gradient-from: #6366f1 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(99 102 241 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}.from-orange-500 {
  --tw-gradient-from: #f97316 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}.from-yellow-500 {
  --tw-gradient-from: #eab308 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}.from-indigo-400 {
  --tw-gradient-from: #818cf8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(129 140 248 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}.from-blue-400 {
  --tw-gradient-from: #60a5fa var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}.from-slate-900 {
  --tw-gradient-from: #0f172a var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(15 23 42 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}.from-blue-500 {
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}.from-orange-500\/20 {
  --tw-gradient-from: rgb(249 115 22 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}.via-purple-900 {
  --tw-gradient-to: rgb(88 28 135 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #581c87 var(--tw-gradient-via-position), var(--tw-gradient-to);
}.to-blue-600 {
  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);
}.to-orange-600 {
  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);
}.to-purple-400 {
  --tw-gradient-to: #c084fc var(--tw-gradient-to-position);
}.to-slate-900 {
  --tw-gradient-to: #0f172a var(--tw-gradient-to-position);
}.to-purple-600 {
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}.to-yellow-500\/20 {
  --tw-gradient-to: rgb(234 179 8 / 0.2) var(--tw-gradient-to-position);
}.bg-clip-text {
  -webkit-background-clip: text;
          background-clip: text;
}.p-0 {
  padding: 0px;
}.p-3 {
  padding: 0.75rem;
}.p-4 {
  padding: 1rem;
}.p-6 {
  padding: 1.5rem;
}.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}.pb-1 {
  padding-bottom: 0.25rem;
}.pb-2 {
  padding-bottom: 0.5rem;
}.pb-4 {
  padding-bottom: 1rem;
}.pl-16 {
  padding-left: 4rem;
}.pl-4 {
  padding-left: 1rem;
}.pt-4 {
  padding-top: 1rem;
}.pl-10 {
  padding-left: 2.5rem;
}.pt-8 {
  padding-top: 2rem;
}.text-left {
  text-align: left;
}.text-center {
  text-align: center;
}.text-right {
  text-align: right;
}.\!text-sm {
  font-size: 0.875rem!important;
  line-height: 1.25rem!important;
}.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}.text-\[10px\] {
  font-size: 10px;
}.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}.text-5xl {
  font-size: 3rem;
  line-height: 1;
}.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}.font-bold {
  font-weight: 700;
}.font-light {
  font-weight: 300;
}.font-medium {
  font-weight: 500;
}.font-semibold {
  font-weight: 600;
}.uppercase {
  text-transform: uppercase;
}.leading-relaxed {
  line-height: 1.625;
}.tracking-wide {
  letter-spacing: 0.025em;
}.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}.text-transparent {
  color: transparent;
}.text-blue-400 {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}.text-green-400 {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}.text-orange-300 {
  --tw-text-opacity: 1;
  color: rgb(253 186 116 / var(--tw-text-opacity, 1));
}.text-purple-400 {
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}.placeholder-gray-400::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}.placeholder-gray-400::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}.opacity-20 {
  opacity: 0.2;
}.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.duration-300 {
  transition-duration: 300ms;
}.duration-200 {
  transition-duration: 200ms;
}.first\:border-t-0:first-child {
  border-top-width: 0px;
}.hover\:-translate-y-0\.5:hover {
  --tw-translate-y: -0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}.hover\:bg-white\/20:hover {
  background-color: rgb(255 255 255 / 0.2);
}.hover\:bg-orange-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}.hover\:bg-white\/15:hover {
  background-color: rgb(255 255 255 / 0.15);
}.hover\:bg-gray-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}.hover\:bg-gray-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}.hover\:from-blue-600:hover {
  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}.hover\:to-purple-700:hover {
  --tw-gradient-to: #7e22ce var(--tw-gradient-to-position);
}.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}.hover\:text-blue-300:hover {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}.hover\:text-orange-200:hover {
  --tw-text-opacity: 1;
  color: rgb(254 215 170 / var(--tw-text-opacity, 1));
}.hover\:shadow-2xl:hover {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.focus\:border-transparent:focus {
  border-color: transparent;
}.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.focus\:ring-blue-400:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(96 165 250 / var(--tw-ring-opacity, 1));
}.focus\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}.group:hover.group-hover\:text-blue-300 {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}.not-last-of-type\:border-b:not(:last-of-type) {
  border-bottom-width: 1px;
}
@media (min-width: 640px) {.sm\:columns-2 {
    -moz-columns: 2;
         columns: 2;
  }.sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }.sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }.sm\:flex-row {
    flex-direction: row;
  }
}
@media (min-width: 768px) {.md\:columns-3 {
    -moz-columns: 3;
         columns: 3;
  }.md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}
@media (min-width: 1024px) {.lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
@media (min-width: 1280px) {.xl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

</style>
  </head>
  <body>
    <div>
      <div class="flex flex-col divide-y">
        <div class="py-4">
          <div class="flex items-center justify-end gap-4 pb-4">
            <div class="text-xs">{{labels.currency}}:</div>
            <div class="font-semibold text-xs">{{currency}}</div>
          </div>
          <div class="mb-2 text-sm">{{labels.tasks}}</div>
          <table class="w-full">
            <thead>
              <tr class="not-last-of-type:border-b">
                <th class="font-light px-2 text-[10px] text-left">{{labels.number_abbreviation}}</th>
                <th class="font-light px-2 text-[10px] text-left">{{labels.material_name}}</th>
                <th class="font-light px-2 text-[10px] text-left">{{labels.code}}</th>
                <th class="font-light px-2 text-[10px] text-left">{{labels.measurement_unit_abbreviation}}</th>
                <th class="font-light px-2 text-[10px] text-right">{{labels.standard_consumption}}</th>
                <th class="font-light px-2 text-[10px] text-right">{{labels.actual_consumption}}</th>
                <th class="font-light px-2 text-[10px] text-right">{{labels.unit_cost}}</th>
                <th class="font-light px-2 text-[10px] text-right">{{labels.total_cost}}</th>
              </tr>
            </thead>
            <tbody class="text-center text-xs">
{{#items}}
              <tr class="not-last-of-type:border-b">
                <td class="py-4 px-2 text-left">
                  <span>
                    <span style="padding-left: calc({{level}} * 20px);">{{index}}</span>
                  </span>
                  <td class="py-4 px-2 text-left">{{name}}</td>
                  <td class="py-4 px-2 text-left">{{code}}</td>
                  <td class="py-4 px-2 text-left">{{measurementUnit}}</td>
                  <td class="py-4 px-2 text-right">{{standardConsumption}}</td>
                  <td class="py-4 px-2 text-right">{{actualConsumption}}</td>
                  <td class="py-4 px-2 text-right">{{unitCost}}</td>
                  <td class="py-4 px-2 text-right">{{totalCost}}</td>
                </td>
              </tr>
{{#materials}}
              <tr>
                <td colspan="99" class="p-0">{{> materialsTable}}</td>
              </tr>
{{/materials}}
{{/items}}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </body>
</html>
