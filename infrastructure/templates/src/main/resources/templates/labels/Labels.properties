purchase_order=Purchase order
order_date=Order date
supplier=Supplier
expected_by=Expected by
ordered_by=Ordered by
number_abbreviation=No.
item_name=Item Name
product_name=Product Name
product_quantity=Product Quantity
quantity=Quantity
unit_price=Unit price
VAT=VAT
VAT_amount=VAT Amount
VAT_quote=VAT Quote
total_units=Total units
subtotal=Subtotal
discount=Discount
total=Total
without_vat=Without VAT
date=Date
due_date=Due Date
invoice_date=Date
invoice_due_date=Due Date
identification_number=Identification Number
tax_identification_number=Tax Identification Number
bank_name=Bank
swift_number=SWIFT
bank_account_number=Bank Account Number
account_number=Account Number
email=Email
client=Client
value=Value
value_without_vat=Value (Without VAT)
value_net=Net Value
address=Address
billing_address=Billing Address
shipping_address=Shipping Address
expiration_date=Expiration Date
number=Number
invoice_title=Invoice
invoice_title_proforma=Proforma Invoice
measurement_unit_abbreviation=MU
sales_quote=Sales Quotation
sales_order=Sales Order
manufacturing_order=Manufacturing Order
business_unit=Business Unit
product_sku=Product SKU
material_issue_item_name=Material name and details
needed_quantity=Necessary quantity
used_quantity=Used quantity
inventory_manager=Inventory manager
receiver_person=Receiver
receipt_title=Reception receipt
ordered_quantity=Ordered quantity
received_quantity=Received quantity
reception_committee=Reception committee
inventory_reception_by=Received on inventory by
NONE=None
INVOICE=Invoice
ACCOMPANYING_LETTER=Accompanying letter
total_value=Total Value
received_by=Received by
goods_accompanying_note_title=Goods Accompanying Note
delivery_date=Delivery Date
loading_point=Loading point
unloading_point=Unloading point
notes=Notes
transport_details=Transport details
delegate_name=Delegate Name
delegate_id=Delegate ID
transportation_registration_number=Transport registration number
sender_signature=Sender signature
driver_signature=Driver Signature
receiver_signature=Receiver Signature
social_capital=Social Capital
swift=SWIFT
phone=Phone
currency=Currency
creation_date=Creation Date
material_issue_title=Material issue note
product=Product
tasks=Tasks
name=Name
entity=Entity
task_name=Task Name
duration=Duration
employees=Employees
workstations=Workstations
employee_hourly_cost=Employee Hourly Cost
employee_total_cost=Employee Total Cost
workstation_hourly_cost=Workstation Hourly Cost
workstation_total_cost=Workstation Hourly Cost
overhead_cost=Overhead Cost
total_cost=Total Cost
standard_duration=Standard Duration
actual_duration=Actual Duration
status=Status
materials=Materials
code=Code
required=Required
required_total=Total Required
production_deadline=Production Deadline
done=Done
production_report_title=Production Report
material_name=Material Name
standard_consumption=Standard Consumption
actual_consumption=Actual Consumption
material_consumption=Material Consumption
labor_and_equipment_costs=Labor and equipment costs
production_cost_summary=Production Cost Summary
raw_materials_and_direct_materials=Raw Materials and Direct Materials
direct_labor=Direct Labor
labor_costs_total=Total Labor Costs
equipment_costs_total=Total Equipment Costs
equipment_cost=Equipment Cost
total_direct_cost=Total Direct Costs
total_indirect_cost=Total Indirect Costs
wasted_quantity=Wasted Quantity

# measurement units
MM=Millimeter
CM=Centimeter
M=Meter
KM=Kilometer

CM2=Square Centimeter
M2=Square Meter

CM3=Cubic Centimeter
M3=Cubic Meter

LITER=Liter
MILLI_LITER=Milliliter

G=Gram
KG=Kilogram
TONNE=Tonne

PIECE=Piece
SET=Set
ROLL=Roll
UN=Unit
PACK=Pack
BOX=Box
BAG=Bag
SACK=Sack
PAIR=Pair
KWH=Kilowatt Hour
HOUR=Hour
MINUTE=Minute
DAY=Day
CAN=Can
SERVICE=Service
PLATE=Plate
PALLET=Pallet
#end measurement units

unit_cost=Unit Cost
additional_costs=Additional Costs
dimensions=Dimensions
length=Length
width=Width
height=Height
weight=Weight
service_report_title=Service Report
service_details=Service Details
worker=Worker
on_behalf_of_customer=On Behalf of Customer
work_performed=Work performed
work_item=Work Item
material_costs_total=Total Material Costs
child_manufacturing_order=Child MO
from_stock=From Stock

