purchase_order=Comandă achiziție
order_date=Data comenzii
supplier=Furnizor
expected_by=Așteptat pe
ordered_by=Comandat de
number_abbreviation=Nr.
item_name=Nume articol
product_name=Nume produs
product_quantity=Cantitate produs
quantity=Cantitate
unit_price=Preț unitar
unit_cost=Cost unitar
VAT=TVA
VAT_amount=Valoare TVA
VAT_quote=Cotă TVA
total_units=Total articole
subtotal=Subtotal
discount=Reducere
total=Total
without_vat=Fără TVA
date=Data
due_date=Termen de livrare
invoice_date=Data emiterii
invoice_due_date=Data scadenței
identification_number=Nr. reg. com.
tax_identification_number=C.I.F.
bank_name=Banca
swift_number=SWIFT
bank_account_number=Cont bancar
account_number=Număr cont
email=Email
client=Client
value=Valoare
value_without_vat=Valoare (Fără TVA)
value_net=Valoare netă
address=Adresă
billing_address=Adresă facturare
shipping_address=Adresă livrare
expiration_date=Data expirării
number=Număr
invoice_title=Factură fiscală
invoice_title_proforma=Factură proformă
measurement_unit_abbreviation=U.M.
pcs=buc
sales_quote=Ofertă de preț
sales_order=Comandă de vânzare
manufacturing_order=Comandă de producție
business_unit=Unitatea
product_sku=Cod produs
material_issue_item_name=Denumirea materialelor (inclusiv sortiment, marcă, dimensiune, profile)
needed_quantity=Cantitate necesară
used_quantity=Cantitate eliberată
inventory_manager=Gestionar
receiver_person=Primitor
receipt_title=Notă de recepție și constatare diferențe
ordered_quantity=Cantitate comandată
received_quantity=Cantitate primită
reception_committee=Comisia de recepție
inventory_reception_by=Primit în gestiune
NONE=Nespecificat
INVOICE=Factură
ACCOMPANYING_LETTER=Aviz de însoțire a mărfii
total_value=Valoare totală
received_by=Recepționat de
goods_accompanying_note_title=Aviz de însoțire a mărfii
delivery_date=Data expedierii
loading_point=Punct de încărcare
unloading_point=Punct de descărcare
notes=Mențiuni
transport_details=Detalii transport
delegate_name=Nume delegat
delegate_id=Serie/Număr CI
transportation_registration_number=Număr înmatriculare
sender_signature=Semnătură emitent
driver_signature=Semnătură șofer
receiver_signature=Semnătură client
social_capital=Capital social
swift=SWIFT
phone=Telefon
currency=Monedă
creation_date=Data emiterii
material_issue_title=Bon de consum
product=Produs
tasks=Taskuri
name=Nume
entity=Entitatea
task_name=Sarcinǎ
duration=Durata
employees=Angajați
workstations=Stații de lucru
employee_hourly_cost=Cost orar manoperǎ
employee_total_cost=Cost total manoperǎ
workstation_hourly_cost=Cost orar stație de lucru
workstation_total_cost=Cost total stație de lucru
overhead_cost=Cost indirect
total_cost=Cost total
standard_duration=Ore normate
actual_duration=Ore efective
status=Status
materials=Materiale
code=Cod
required=Necesar
required_total=Total Necesar
production_deadline=Termen producție
done=Terminat
production_report_title=Raport de producție
material_name=Denumire material
standard_consumption=Consum normat
actual_consumption=Consum efectiv
material_consumption=Consumuri materiale
labor_and_equipment_costs=Costuri manoperǎ și utilaje
production_cost_summary=Centralizare costuri producție
raw_materials_and_direct_materials=Materii prime si materiale directe
direct_labor=Manoperǎ directǎ
labor_costs_total=Cost total manoperǎ
equipment_costs_total=Cost total utilaje
equipment_cost=Cost utilaje
total_direct_cost=Total costuri directe
total_indirect_cost=Total costuri indirecte
wasted_quantity=Pierderi

# measurement units
MM=Milimetru
CM=Centimetru
M=Metru
KM=Kilometru

CM2=Centimetru Pătrat
M2=Metru Pătrat

CM3=Centimetru Cub
M3=Metru Cub

LITER=Litru
MILLI_LITER=Mililitru

G=Gram
KG=Kilogram
TONNE=Tonă

PIECE=Buc
SET=Set
ROLL=Rol
UN=Unitate
PACK=Pachet
BOX=Cutie
BAG=Pungă
SACK=Sac
PAIR=Pereche
KWH=Kilowatt Oră
HOUR=Oră
MINUTE=Minut
DAY=Zi
CAN=Doză
SERVICE=Serviciu
PLATE=Placă
PALLET=Palet
# end measurement units

additional_costs=Costuri adiționale
dimensions=Dimensiuni
length=Lungime
width=Lǎțime
height=Înǎlțime
weight=Greutate
service_report_title=Raport de service
service_details=Detalii servicii
worker=Executant
on_behalf_of_customer=Client prin
work_performed=Manoperǎ
work_item=Lucrare efectuatǎ
material_costs_total=Cost total materiale
child_manufacturing_order=Comandă Copil
from_stock=Din Stoc
