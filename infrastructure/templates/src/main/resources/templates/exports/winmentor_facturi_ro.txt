[InfoPachet]
AnLucru={{meta.year}}
LunaLucru={{meta.month}}
Tipdocument=FACTURA IESIRE
TotalFacturi={{meta.count}}

{{#invoices}}
[Factura_{{index}}]
NrDoc={{number}}
ClasificareSAFT=0
CasaDeMarcat=N
SerieCarnet={{series}}
MarcaAgent=10
Data={{date}}
CodClient={{customer.taxIdentificationNumber}}
Localitate={{customer.billingAddressCity}}
TVAINCASARE=N
AutoFactura=N
FacturaSimplificata=N
EmisClient=D
EmisTert=N
Stornare=N
TipTVA=0
ANULAT=N
TaxareInversa=N
Observatii={{notes}}
Scadenta={{dueDate}}

TotalArticole={{itemCount}}
{{#items}}
[Items_{{index}}]
Item_{{index}}={{code}};{{measurementUnit}};{{quantity}};{{subTotal}}
Item_{{index}}_TVA={{vat}}
{{/items}}

{{/invoices}}