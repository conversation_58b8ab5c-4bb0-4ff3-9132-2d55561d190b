package fabriqon.templates;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.FormatStyle;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;

public class Localizer {

    private Locale locale;
    private Map<String, String> labels;

    public Localizer(Locale locale) {
        this.locale = locale;
    }

    public String localizeDate(Instant instant) {
        if (instant == null) {
            return null;
        }
        return DateTimeFormatter.ofLocalizedDate(FormatStyle.SHORT).withLocale(locale).format(LocalDate.ofInstant(instant, ZoneId.systemDefault()));
    }

    public String localizeDate(LocalDate date) {
        if (date == null) {
            return null;
        }
        return DateTimeFormatter.ofLocalizedDate(FormatStyle.SHORT).withLocale(locale).format(date);
    }

    public String humanReadableDuration(Duration d) {
        if (d.toDaysPart() > 0) {
            return String.format("%sd %sh %sm",
                    d.toDaysPart(),
                    d.toHoursPart(),
                    d.toMinutesPart());
        }
        return String.format("%sh %sm",
                d.toHoursPart(),
                d.toMinutesPart());
    }

    public Map<String, String> labels() {
        if (labels == null) {
            return labels = resourceBundleToMap(ResourceBundle.getBundle("templates.labels.Labels", locale));
        }
        return labels;
    }

    private Map<String, String> resourceBundleToMap(ResourceBundle resource) {
        Map<String, String> map = new HashMap<>();
        var keys = resource.getKeys();
        while (keys.hasMoreElements()) {
            String key = keys.nextElement();
            map.put(key, resource.getString(key));
        }
        return map;
    }

    public String localizedValue(String value) {
        return labels().get(value) != null ? labels().get(value) : value;
    }
}
