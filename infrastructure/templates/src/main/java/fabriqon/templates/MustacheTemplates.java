package fabriqon.templates;

import com.samskivert.mustache.Mustache;
import com.samskivert.mustache.Template;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class MustacheTemplates implements Templates {

    private final TemplateLoader templateLoader;
    private final Map<String, Template> templates;

    @Autowired
    public MustacheTemplates(TemplateLoader templateLoader) {
        this.templateLoader = templateLoader;
        this.templates = new ConcurrentHashMap<>();
    }

    @Override
    public String render(String template, Object context) {
        return template(template).execute(context);
    }

    private Template template(String name) {
        return templates.computeIfAbsent(name, __ -> Mustache.compiler()
                .defaultValue("")
                .withLoader(partialName -> templateLoader.load("pdf/" + partialName + ".html"))
                .compile(templateLoader.load(name)));
    }

}
