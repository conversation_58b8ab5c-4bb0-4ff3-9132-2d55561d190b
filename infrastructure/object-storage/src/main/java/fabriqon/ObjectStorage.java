package fabriqon;


import fabriqon.misc.Tuple;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;

import java.io.IOException;
import java.net.URLConnection;
import java.time.Duration;
import java.util.Map;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.FILE;

@Slf4j
@Component
public class ObjectStorage {

    @Value("${s3.bucket.name:fbq-test}")
    private String s3BucketName;

    private final DSLContext db;
    private final S3Client s3Client;

    @Autowired
    public ObjectStorage(DSLContext db) {
        this.db = db;
        s3Client = S3Client.builder()
                .region(Region.EU_CENTRAL_1)
                .build();
    }

    @Transactional
    public UUID store(UUID ownerId, String name, byte[] content) {
        var id = UUID.randomUUID();

        db.insertInto(FILE)
                .set(FILE.ID, id)
                .set(FILE.OWNER_ID, ownerId)
                .set(FILE.FILE_NAME, name)
                .set(FILE.FILE_SIZE, content.length)
                .execute();

        s3Client.putObject(PutObjectRequest.builder()
                        .bucket(s3BucketName)
                        .key(id.toString())
                        .metadata(Map.of("title", name))
                        .contentType(getContentType(name))
                        .contentLength((long) content.length)
                        .build(),
                RequestBody.fromBytes(content));
        return id;
    }

    @Transactional
    public void delete(UUID ownerId, UUID id) {
        db.deleteFrom(FILE)
                .where(FILE.OWNER_ID.eq(ownerId), FILE.ID.eq(id))
                .execute();
        s3Client.deleteObject(DeleteObjectRequest.builder()
                .bucket(s3BucketName)
                .key(id.toString())
                .build());
    }

    public String getLink(UUID ownerId, UUID id, boolean preview) {
        var file = db.selectFrom(FILE)
                .where(FILE.OWNER_ID.eq(ownerId), FILE.ID.eq(id))
                .fetchSingle();

        try (S3Presigner presigner = S3Presigner.builder().region(Region.EU_CENTRAL_1).build()) {
            return presigner.presignGetObject(
                            GetObjectPresignRequest.builder()
                                    .signatureDuration(Duration.ofMinutes(10))
                                    .getObjectRequest(
                                            GetObjectRequest.builder()
                                                    .bucket(s3BucketName)
                                                    .key(id.toString())
                                                    .responseContentDisposition(preview
                                                            ? "inline; filename =" + file.getFileName()
                                                            : "attachment; filename =" + file.getFileName())
                                                    .build())
                                    .build())
                    .url().toExternalForm();
        }

    }

    public Tuple.Tuple2<String, byte[]> getFile(UUID ownerId, UUID id) {
        //this is here to check access
        var file = db.selectFrom(FILE)
                .where(FILE.OWNER_ID.eq(ownerId), FILE.ID.eq(id))
                .fetchSingle();
        try {
            return Tuple.of(file.getFileName(), s3Client.getObject(GetObjectRequest.builder()
                    .bucket(s3BucketName)
                    .key(id.toString())
                    .build())
                    .readAllBytes());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private String getContentType(String name) {
        return URLConnection.guessContentTypeFromName(name);
    }

}