package fabriqon;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.FirebaseMessagingException;
import com.google.firebase.messaging.Message;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static fabriqon.jooq.classes.Tables.FIREBASE_REGISTRATION_TOKEN;

@Slf4j
@Component("notifier")
public class FirebaseNotifier implements Notifier {

    private final DSLContext db;

    @Autowired
    public FirebaseNotifier(DSLContext db) {
        this.db = db;
    }

    @PostConstruct
    private void postConstruct() {
        try {
            if (FirebaseApp.getApps().isEmpty()) {
                FirebaseOptions options = FirebaseOptions.builder()
                        .setCredentials(GoogleCredentials.getApplicationDefault())
                        .build();
                FirebaseApp.initializeApp(options);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void send(Notification notification, List<Recipient> recipients) {
        registrationTokens(recipients).forEach(token -> {
            try {
                var message = Message.builder()
                        .putData("id", notification.id().toString())
                        .putData("type", notification.type().name())
                        .putData("section", notification.section().name())
                        .putData("targetEntityId", notification.targetEntityId().toString())
                        .putAllData(notification.details() != null
                                ? notification.details().entrySet().stream()
                                .collect(Collectors.toMap(e -> "details_" + e.getKey(), e -> e.getValue().toString()))
                                : Map.of());
                if (notification.triggeredBy() != null) {
                    message.putData("triggeredBy", notification.triggeredBy());
                }
                String messageId = FirebaseMessaging.getInstance().send(message.setToken(token).build());
            } catch (FirebaseMessagingException e) {
                //todo check the error and remove the token if needed
                log.error("Firebase messaging error.", e);
            }
        });
    }

    private List<String> registrationTokens(List<Recipient> recipients) {
        var ids = recipients.stream().map(Recipient::userId).toList();
        return db.select(FIREBASE_REGISTRATION_TOKEN.TOKEN)
                .from(FIREBASE_REGISTRATION_TOKEN)
                .where(FIREBASE_REGISTRATION_TOKEN.USER_ID.in(ids).or(FIREBASE_REGISTRATION_TOKEN.EMPLOYEE_ID.in(ids)))
                .fetchInto(String.class);
    }


}
