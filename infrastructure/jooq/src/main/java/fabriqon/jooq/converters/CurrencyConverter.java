package fabriqon.jooq.converters;

import org.jooq.Converter;

import java.util.Currency;

public class CurrencyConverter implements Converter<String, Currency> {

    @Override
    public Currency from(String databaseObject) {
        return databaseObject != null ? Currency.getInstance(databaseObject) : null;
    }

    @Override
    public String to(Currency userObject) {
        return userObject != null ? userObject.getCurrencyCode() : null;
    }

    @Override
    public Class<String> fromType() {
        return String.class;
    }

    @Override
    public Class<Currency> toType() {
        return Currency.class;
    }

}
