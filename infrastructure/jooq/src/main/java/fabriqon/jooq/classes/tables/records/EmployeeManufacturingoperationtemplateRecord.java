/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.EmployeeManufacturingoperationtemplate;

import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record4;
import org.jooq.Row4;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class EmployeeManufacturingoperationtemplateRecord extends TableRecordImpl<EmployeeManufacturingoperationtemplateRecord> implements Record4<UUID, UUID, UUID, Boolean> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for
     * <code>public.employee_manufacturingoperationtemplate.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for
     * <code>public.employee_manufacturingoperationtemplate.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(0);
    }

    /**
     * Setter for
     * <code>public.employee_manufacturingoperationtemplate.user_id</code>.
     */
    public void setUserId(UUID value) {
        set(1, value);
    }

    /**
     * Getter for
     * <code>public.employee_manufacturingoperationtemplate.user_id</code>.
     */
    public UUID getUserId() {
        return (UUID) get(1);
    }

    /**
     * Setter for
     * <code>public.employee_manufacturingoperationtemplate.manufacturing_operation_template_id</code>.
     */
    public void setManufacturingOperationTemplateId(UUID value) {
        set(2, value);
    }

    /**
     * Getter for
     * <code>public.employee_manufacturingoperationtemplate.manufacturing_operation_template_id</code>.
     */
    public UUID getManufacturingOperationTemplateId() {
        return (UUID) get(2);
    }

    /**
     * Setter for
     * <code>public.employee_manufacturingoperationtemplate.preferential</code>.
     */
    public void setPreferential(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for
     * <code>public.employee_manufacturingoperationtemplate.preferential</code>.
     */
    public Boolean getPreferential() {
        return (Boolean) get(3);
    }

    // -------------------------------------------------------------------------
    // Record4 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row4<UUID, UUID, UUID, Boolean> fieldsRow() {
        return (Row4) super.fieldsRow();
    }

    @Override
    public Row4<UUID, UUID, UUID, Boolean> valuesRow() {
        return (Row4) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return EmployeeManufacturingoperationtemplate.EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.OWNER_ID;
    }

    @Override
    public Field<UUID> field2() {
        return EmployeeManufacturingoperationtemplate.EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.USER_ID;
    }

    @Override
    public Field<UUID> field3() {
        return EmployeeManufacturingoperationtemplate.EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_OPERATION_TEMPLATE_ID;
    }

    @Override
    public Field<Boolean> field4() {
        return EmployeeManufacturingoperationtemplate.EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.PREFERENTIAL;
    }

    @Override
    public UUID component1() {
        return getOwnerId();
    }

    @Override
    public UUID component2() {
        return getUserId();
    }

    @Override
    public UUID component3() {
        return getManufacturingOperationTemplateId();
    }

    @Override
    public Boolean component4() {
        return getPreferential();
    }

    @Override
    public UUID value1() {
        return getOwnerId();
    }

    @Override
    public UUID value2() {
        return getUserId();
    }

    @Override
    public UUID value3() {
        return getManufacturingOperationTemplateId();
    }

    @Override
    public Boolean value4() {
        return getPreferential();
    }

    @Override
    public EmployeeManufacturingoperationtemplateRecord value1(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public EmployeeManufacturingoperationtemplateRecord value2(UUID value) {
        setUserId(value);
        return this;
    }

    @Override
    public EmployeeManufacturingoperationtemplateRecord value3(UUID value) {
        setManufacturingOperationTemplateId(value);
        return this;
    }

    @Override
    public EmployeeManufacturingoperationtemplateRecord value4(Boolean value) {
        setPreferential(value);
        return this;
    }

    @Override
    public EmployeeManufacturingoperationtemplateRecord values(UUID value1, UUID value2, UUID value3, Boolean value4) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached EmployeeManufacturingoperationtemplateRecord
     */
    public EmployeeManufacturingoperationtemplateRecord() {
        super(EmployeeManufacturingoperationtemplate.EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE);
    }

    /**
     * Create a detached, initialised
     * EmployeeManufacturingoperationtemplateRecord
     */
    public EmployeeManufacturingoperationtemplateRecord(UUID ownerId, UUID userId, UUID manufacturingOperationTemplateId, Boolean preferential) {
        super(EmployeeManufacturingoperationtemplate.EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE);

        setOwnerId(ownerId);
        setUserId(userId);
        setManufacturingOperationTemplateId(manufacturingOperationTemplateId);
        setPreferential(preferential);
    }
}
