/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.SalesOrdersView;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SalesOrdersViewRecord extends TableRecordImpl<SalesOrdersViewRecord> implements Record7<UUID, LocalDateTime, UUID, BigDecimal, BigDecimal, String, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.sales_orders_view.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.sales_orders_view.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.sales_orders_view.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.sales_orders_view.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.sales_orders_view.sales_order_id</code>.
     */
    public void setSalesOrderId(UUID value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.sales_orders_view.sales_order_id</code>.
     */
    public UUID getSalesOrderId() {
        return (UUID) get(2);
    }

    /**
     * Setter for <code>public.sales_orders_view.exit_price</code>.
     */
    public void setExitPrice(BigDecimal value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.sales_orders_view.exit_price</code>.
     */
    public BigDecimal getExitPrice() {
        return (BigDecimal) get(3);
    }

    /**
     * Setter for <code>public.sales_orders_view.quantity</code>.
     */
    public void setQuantity(BigDecimal value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.sales_orders_view.quantity</code>.
     */
    public BigDecimal getQuantity() {
        return (BigDecimal) get(4);
    }

    /**
     * Setter for <code>public.sales_orders_view.customer</code>.
     */
    public void setCustomer(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.sales_orders_view.customer</code>.
     */
    public String getCustomer() {
        return (String) get(5);
    }

    /**
     * Setter for <code>public.sales_orders_view.product</code>.
     */
    public void setProduct(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.sales_orders_view.product</code>.
     */
    public String getProduct() {
        return (String) get(6);
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row7<UUID, LocalDateTime, UUID, BigDecimal, BigDecimal, String, String> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    @Override
    public Row7<UUID, LocalDateTime, UUID, BigDecimal, BigDecimal, String, String> valuesRow() {
        return (Row7) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return SalesOrdersView.SALES_ORDERS_VIEW.OWNER_ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return SalesOrdersView.SALES_ORDERS_VIEW.CREATE_TIME;
    }

    @Override
    public Field<UUID> field3() {
        return SalesOrdersView.SALES_ORDERS_VIEW.SALES_ORDER_ID;
    }

    @Override
    public Field<BigDecimal> field4() {
        return SalesOrdersView.SALES_ORDERS_VIEW.EXIT_PRICE;
    }

    @Override
    public Field<BigDecimal> field5() {
        return SalesOrdersView.SALES_ORDERS_VIEW.QUANTITY;
    }

    @Override
    public Field<String> field6() {
        return SalesOrdersView.SALES_ORDERS_VIEW.CUSTOMER;
    }

    @Override
    public Field<String> field7() {
        return SalesOrdersView.SALES_ORDERS_VIEW.PRODUCT;
    }

    @Override
    public UUID component1() {
        return getOwnerId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public UUID component3() {
        return getSalesOrderId();
    }

    @Override
    public BigDecimal component4() {
        return getExitPrice();
    }

    @Override
    public BigDecimal component5() {
        return getQuantity();
    }

    @Override
    public String component6() {
        return getCustomer();
    }

    @Override
    public String component7() {
        return getProduct();
    }

    @Override
    public UUID value1() {
        return getOwnerId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public UUID value3() {
        return getSalesOrderId();
    }

    @Override
    public BigDecimal value4() {
        return getExitPrice();
    }

    @Override
    public BigDecimal value5() {
        return getQuantity();
    }

    @Override
    public String value6() {
        return getCustomer();
    }

    @Override
    public String value7() {
        return getProduct();
    }

    @Override
    public SalesOrdersViewRecord value1(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public SalesOrdersViewRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public SalesOrdersViewRecord value3(UUID value) {
        setSalesOrderId(value);
        return this;
    }

    @Override
    public SalesOrdersViewRecord value4(BigDecimal value) {
        setExitPrice(value);
        return this;
    }

    @Override
    public SalesOrdersViewRecord value5(BigDecimal value) {
        setQuantity(value);
        return this;
    }

    @Override
    public SalesOrdersViewRecord value6(String value) {
        setCustomer(value);
        return this;
    }

    @Override
    public SalesOrdersViewRecord value7(String value) {
        setProduct(value);
        return this;
    }

    @Override
    public SalesOrdersViewRecord values(UUID value1, LocalDateTime value2, UUID value3, BigDecimal value4, BigDecimal value5, String value6, String value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SalesOrdersViewRecord
     */
    public SalesOrdersViewRecord() {
        super(SalesOrdersView.SALES_ORDERS_VIEW);
    }

    /**
     * Create a detached, initialised SalesOrdersViewRecord
     */
    public SalesOrdersViewRecord(UUID ownerId, LocalDateTime createTime, UUID salesOrderId, BigDecimal exitPrice, BigDecimal quantity, String customer, String product) {
        super(SalesOrdersView.SALES_ORDERS_VIEW);

        setOwnerId(ownerId);
        setCreateTime(createTime);
        setSalesOrderId(salesOrderId);
        setExitPrice(exitPrice);
        setQuantity(quantity);
        setCustomer(customer);
        setProduct(product);
    }
}
