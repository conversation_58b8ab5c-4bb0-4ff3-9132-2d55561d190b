/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.MaterialIssueNoteRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row11;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MaterialIssueNote extends TableImpl<MaterialIssueNoteRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.material_issue_note</code>
     */
    public static final MaterialIssueNote MATERIAL_ISSUE_NOTE = new MaterialIssueNote();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<MaterialIssueNoteRecord> getRecordType() {
        return MaterialIssueNoteRecord.class;
    }

    /**
     * The column <code>public.material_issue_note.id</code>.
     */
    public final TableField<MaterialIssueNoteRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.material_issue_note.create_time</code>.
     */
    public final TableField<MaterialIssueNoteRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.material_issue_note.update_time</code>.
     */
    public final TableField<MaterialIssueNoteRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.material_issue_note.deleted</code>.
     */
    public final TableField<MaterialIssueNoteRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.material_issue_note.owner_id</code>.
     */
    public final TableField<MaterialIssueNoteRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID, this, "");

    /**
     * The column
     * <code>public.material_issue_note.manufacturing_order_id</code>.
     */
    public final TableField<MaterialIssueNoteRecord, UUID> MANUFACTURING_ORDER_ID = createField(DSL.name("manufacturing_order_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.material_issue_note.number</code>.
     */
    public final TableField<MaterialIssueNoteRecord, String> NUMBER = createField(DSL.name("number"), SQLDataType.VARCHAR(20).nullable(false), this, "");

    /**
     * The column <code>public.material_issue_note.date</code>.
     */
    public final TableField<MaterialIssueNoteRecord, LocalDateTime> DATE = createField(DSL.name("date"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>public.material_issue_note.details</code>.
     */
    public final TableField<MaterialIssueNoteRecord, JSONB> DETAILS = createField(DSL.name("details"), SQLDataType.JSONB.nullable(false), this, "");

    /**
     * The column <code>public.material_issue_note.materials</code>.
     */
    public final TableField<MaterialIssueNoteRecord, JSONB> MATERIALS = createField(DSL.name("materials"), SQLDataType.JSONB.nullable(false), this, "");

    /**
     * The column <code>public.material_issue_note.servicing_order_id</code>.
     */
    public final TableField<MaterialIssueNoteRecord, UUID> SERVICING_ORDER_ID = createField(DSL.name("servicing_order_id"), SQLDataType.UUID, this, "");

    private MaterialIssueNote(Name alias, Table<MaterialIssueNoteRecord> aliased) {
        this(alias, aliased, null);
    }

    private MaterialIssueNote(Name alias, Table<MaterialIssueNoteRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.material_issue_note</code> table reference
     */
    public MaterialIssueNote(String alias) {
        this(DSL.name(alias), MATERIAL_ISSUE_NOTE);
    }

    /**
     * Create an aliased <code>public.material_issue_note</code> table reference
     */
    public MaterialIssueNote(Name alias) {
        this(alias, MATERIAL_ISSUE_NOTE);
    }

    /**
     * Create a <code>public.material_issue_note</code> table reference
     */
    public MaterialIssueNote() {
        this(DSL.name("material_issue_note"), null);
    }

    public <O extends Record> MaterialIssueNote(Table<O> child, ForeignKey<O, MaterialIssueNoteRecord> key) {
        super(child, key, MATERIAL_ISSUE_NOTE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public UniqueKey<MaterialIssueNoteRecord> getPrimaryKey() {
        return Keys.MATERIAL_ISSUE_NOTE_PKEY;
    }

    @Override
    public List<ForeignKey<MaterialIssueNoteRecord, ?>> getReferences() {
        return Arrays.asList(Keys.MATERIAL_ISSUE_NOTE__MATERIAL_ISSUE_NOTE_OWNER_ID_FKEY, Keys.MATERIAL_ISSUE_NOTE__MATERIAL_ISSUE_NOTE_MANUFACTURING_ORDER_ID_FKEY, Keys.MATERIAL_ISSUE_NOTE__MATERIAL_ISSUE_NOTE_SERVICING_ORDER_ID_FKEY);
    }

    private transient Account _account;
    private transient ManufacturingOrder _manufacturingOrder;
    private transient ServicingOrder _servicingOrder;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.MATERIAL_ISSUE_NOTE__MATERIAL_ISSUE_NOTE_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.manufacturing_order</code>
     * table.
     */
    public ManufacturingOrder manufacturingOrder() {
        if (_manufacturingOrder == null)
            _manufacturingOrder = new ManufacturingOrder(this, Keys.MATERIAL_ISSUE_NOTE__MATERIAL_ISSUE_NOTE_MANUFACTURING_ORDER_ID_FKEY);

        return _manufacturingOrder;
    }

    /**
     * Get the implicit join path to the <code>public.servicing_order</code>
     * table.
     */
    public ServicingOrder servicingOrder() {
        if (_servicingOrder == null)
            _servicingOrder = new ServicingOrder(this, Keys.MATERIAL_ISSUE_NOTE__MATERIAL_ISSUE_NOTE_SERVICING_ORDER_ID_FKEY);

        return _servicingOrder;
    }

    @Override
    public MaterialIssueNote as(String alias) {
        return new MaterialIssueNote(DSL.name(alias), this);
    }

    @Override
    public MaterialIssueNote as(Name alias) {
        return new MaterialIssueNote(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public MaterialIssueNote rename(String name) {
        return new MaterialIssueNote(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public MaterialIssueNote rename(Name name) {
        return new MaterialIssueNote(name, null);
    }

    // -------------------------------------------------------------------------
    // Row11 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row11<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String, LocalDateTime, JSONB, JSONB, UUID> fieldsRow() {
        return (Row11) super.fieldsRow();
    }
}
