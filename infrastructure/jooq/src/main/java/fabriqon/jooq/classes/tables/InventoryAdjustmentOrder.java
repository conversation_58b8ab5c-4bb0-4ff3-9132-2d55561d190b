/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.InventoryAdjustmentOrderRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row8;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class InventoryAdjustmentOrder extends TableImpl<InventoryAdjustmentOrderRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.inventory_adjustment_order</code>
     */
    public static final InventoryAdjustmentOrder INVENTORY_ADJUSTMENT_ORDER = new InventoryAdjustmentOrder();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<InventoryAdjustmentOrderRecord> getRecordType() {
        return InventoryAdjustmentOrderRecord.class;
    }

    /**
     * The column <code>public.inventory_adjustment_order.id</code>.
     */
    public final TableField<InventoryAdjustmentOrderRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.inventory_adjustment_order.create_time</code>.
     */
    public final TableField<InventoryAdjustmentOrderRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.inventory_adjustment_order.update_time</code>.
     */
    public final TableField<InventoryAdjustmentOrderRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.inventory_adjustment_order.deleted</code>.
     */
    public final TableField<InventoryAdjustmentOrderRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.inventory_adjustment_order.owner_id</code>.
     */
    public final TableField<InventoryAdjustmentOrderRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.inventory_adjustment_order.number</code>.
     */
    public final TableField<InventoryAdjustmentOrderRecord, String> NUMBER = createField(DSL.name("number"), SQLDataType.VARCHAR(20).nullable(false), this, "");

    /**
     * The column <code>public.inventory_adjustment_order.details</code>.
     */
    public final TableField<InventoryAdjustmentOrderRecord, JSONB> DETAILS = createField(DSL.name("details"), SQLDataType.JSONB, this, "");

    /**
     * The column <code>public.inventory_adjustment_order.type</code>.
     */
    public final TableField<InventoryAdjustmentOrderRecord, String> TYPE = createField(DSL.name("type"), SQLDataType.VARCHAR(20), this, "");

    private InventoryAdjustmentOrder(Name alias, Table<InventoryAdjustmentOrderRecord> aliased) {
        this(alias, aliased, null);
    }

    private InventoryAdjustmentOrder(Name alias, Table<InventoryAdjustmentOrderRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.inventory_adjustment_order</code> table
     * reference
     */
    public InventoryAdjustmentOrder(String alias) {
        this(DSL.name(alias), INVENTORY_ADJUSTMENT_ORDER);
    }

    /**
     * Create an aliased <code>public.inventory_adjustment_order</code> table
     * reference
     */
    public InventoryAdjustmentOrder(Name alias) {
        this(alias, INVENTORY_ADJUSTMENT_ORDER);
    }

    /**
     * Create a <code>public.inventory_adjustment_order</code> table reference
     */
    public InventoryAdjustmentOrder() {
        this(DSL.name("inventory_adjustment_order"), null);
    }

    public <O extends Record> InventoryAdjustmentOrder(Table<O> child, ForeignKey<O, InventoryAdjustmentOrderRecord> key) {
        super(child, key, INVENTORY_ADJUSTMENT_ORDER);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public UniqueKey<InventoryAdjustmentOrderRecord> getPrimaryKey() {
        return Keys.INVENTORY_ADJUSTMENT_ORDER_PKEY;
    }

    @Override
    public List<ForeignKey<InventoryAdjustmentOrderRecord, ?>> getReferences() {
        return Arrays.asList(Keys.INVENTORY_ADJUSTMENT_ORDER__INVENTORY_ADJUSTMENT_ORDER_OWNER_ID_FKEY);
    }

    private transient Account _account;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.INVENTORY_ADJUSTMENT_ORDER__INVENTORY_ADJUSTMENT_ORDER_OWNER_ID_FKEY);

        return _account;
    }

    @Override
    public InventoryAdjustmentOrder as(String alias) {
        return new InventoryAdjustmentOrder(DSL.name(alias), this);
    }

    @Override
    public InventoryAdjustmentOrder as(Name alias) {
        return new InventoryAdjustmentOrder(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public InventoryAdjustmentOrder rename(String name) {
        return new InventoryAdjustmentOrder(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public InventoryAdjustmentOrder rename(Name name) {
        return new InventoryAdjustmentOrder(name, null);
    }

    // -------------------------------------------------------------------------
    // Row8 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row8<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, JSONB, String> fieldsRow() {
        return (Row8) super.fieldsRow();
    }
}
