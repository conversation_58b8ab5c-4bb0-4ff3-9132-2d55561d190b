/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.NoteRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row7;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Note extends TableImpl<NoteRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.note</code>
     */
    public static final Note NOTE = new Note();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<NoteRecord> getRecordType() {
        return NoteRecord.class;
    }

    /**
     * The column <code>public.note.id</code>.
     */
    public final TableField<NoteRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.note.create_time</code>.
     */
    public final TableField<NoteRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.note.update_time</code>.
     */
    public final TableField<NoteRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.note.deleted</code>.
     */
    public final TableField<NoteRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.note.owner_id</code>.
     */
    public final TableField<NoteRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.note.added_by_id</code>.
     */
    public final TableField<NoteRecord, UUID> ADDED_BY_ID = createField(DSL.name("added_by_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.note.note</code>.
     */
    public final TableField<NoteRecord, String> NOTE_ = createField(DSL.name("note"), SQLDataType.CLOB.nullable(false), this, "");

    private Note(Name alias, Table<NoteRecord> aliased) {
        this(alias, aliased, null);
    }

    private Note(Name alias, Table<NoteRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.note</code> table reference
     */
    public Note(String alias) {
        this(DSL.name(alias), NOTE);
    }

    /**
     * Create an aliased <code>public.note</code> table reference
     */
    public Note(Name alias) {
        this(alias, NOTE);
    }

    /**
     * Create a <code>public.note</code> table reference
     */
    public Note() {
        this(DSL.name("note"), null);
    }

    public <O extends Record> Note(Table<O> child, ForeignKey<O, NoteRecord> key) {
        super(child, key, NOTE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public UniqueKey<NoteRecord> getPrimaryKey() {
        return Keys.NOTE_PKEY;
    }

    @Override
    public List<ForeignKey<NoteRecord, ?>> getReferences() {
        return Arrays.asList(Keys.NOTE__NOTE_OWNER_ID_FKEY, Keys.NOTE__NOTE_ADDED_BY_ID_FKEY);
    }

    private transient Account _account;
    private transient Users _users;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.NOTE__NOTE_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.users</code> table.
     */
    public Users users() {
        if (_users == null)
            _users = new Users(this, Keys.NOTE__NOTE_ADDED_BY_ID_FKEY);

        return _users;
    }

    @Override
    public Note as(String alias) {
        return new Note(DSL.name(alias), this);
    }

    @Override
    public Note as(Name alias) {
        return new Note(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Note rename(String name) {
        return new Note(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public Note rename(Name name) {
        return new Note(name, null);
    }

    // -------------------------------------------------------------------------
    // Row7 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row7<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String> fieldsRow() {
        return (Row7) super.fieldsRow();
    }
}
