/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.SystemEvent;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SystemEventRecord extends UpdatableRecordImpl<SystemEventRecord> implements Record7<UUID, LocalDateTime, UUID, UUID, UUID, String, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.system_event.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.system_event.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.system_event.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.system_event.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.system_event.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.system_event.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(2);
    }

    /**
     * Setter for <code>public.system_event.user_id</code>.
     */
    public void setUserId(UUID value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.system_event.user_id</code>.
     */
    public UUID getUserId() {
        return (UUID) get(3);
    }

    /**
     * Setter for <code>public.system_event.target_entity_id</code>.
     */
    public void setTargetEntityId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.system_event.target_entity_id</code>.
     */
    public UUID getTargetEntityId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.system_event.section</code>.
     */
    public void setSection(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.system_event.section</code>.
     */
    public String getSection() {
        return (String) get(5);
    }

    /**
     * Setter for <code>public.system_event.transition</code>.
     */
    public void setTransition(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.system_event.transition</code>.
     */
    public String getTransition() {
        return (String) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row7<UUID, LocalDateTime, UUID, UUID, UUID, String, String> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    @Override
    public Row7<UUID, LocalDateTime, UUID, UUID, UUID, String, String> valuesRow() {
        return (Row7) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return SystemEvent.SYSTEM_EVENT.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return SystemEvent.SYSTEM_EVENT.CREATE_TIME;
    }

    @Override
    public Field<UUID> field3() {
        return SystemEvent.SYSTEM_EVENT.OWNER_ID;
    }

    @Override
    public Field<UUID> field4() {
        return SystemEvent.SYSTEM_EVENT.USER_ID;
    }

    @Override
    public Field<UUID> field5() {
        return SystemEvent.SYSTEM_EVENT.TARGET_ENTITY_ID;
    }

    @Override
    public Field<String> field6() {
        return SystemEvent.SYSTEM_EVENT.SECTION;
    }

    @Override
    public Field<String> field7() {
        return SystemEvent.SYSTEM_EVENT.TRANSITION;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public UUID component3() {
        return getOwnerId();
    }

    @Override
    public UUID component4() {
        return getUserId();
    }

    @Override
    public UUID component5() {
        return getTargetEntityId();
    }

    @Override
    public String component6() {
        return getSection();
    }

    @Override
    public String component7() {
        return getTransition();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public UUID value3() {
        return getOwnerId();
    }

    @Override
    public UUID value4() {
        return getUserId();
    }

    @Override
    public UUID value5() {
        return getTargetEntityId();
    }

    @Override
    public String value6() {
        return getSection();
    }

    @Override
    public String value7() {
        return getTransition();
    }

    @Override
    public SystemEventRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public SystemEventRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public SystemEventRecord value3(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public SystemEventRecord value4(UUID value) {
        setUserId(value);
        return this;
    }

    @Override
    public SystemEventRecord value5(UUID value) {
        setTargetEntityId(value);
        return this;
    }

    @Override
    public SystemEventRecord value6(String value) {
        setSection(value);
        return this;
    }

    @Override
    public SystemEventRecord value7(String value) {
        setTransition(value);
        return this;
    }

    @Override
    public SystemEventRecord values(UUID value1, LocalDateTime value2, UUID value3, UUID value4, UUID value5, String value6, String value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SystemEventRecord
     */
    public SystemEventRecord() {
        super(SystemEvent.SYSTEM_EVENT);
    }

    /**
     * Create a detached, initialised SystemEventRecord
     */
    public SystemEventRecord(UUID id, LocalDateTime createTime, UUID ownerId, UUID userId, UUID targetEntityId, String section, String transition) {
        super(SystemEvent.SYSTEM_EVENT);

        setId(id);
        setCreateTime(createTime);
        setOwnerId(ownerId);
        setUserId(userId);
        setTargetEntityId(targetEntityId);
        setSection(section);
        setTransition(transition);
    }
}
