/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.MaterialIssueNote;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.Record11;
import org.jooq.Row11;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MaterialIssueNoteRecord extends UpdatableRecordImpl<MaterialIssueNoteRecord> implements Record11<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String, LocalDateTime, JSONB, JSONB, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.material_issue_note.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.material_issue_note.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.material_issue_note.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.material_issue_note.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.material_issue_note.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.material_issue_note.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.material_issue_note.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.material_issue_note.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.material_issue_note.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.material_issue_note.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for
     * <code>public.material_issue_note.manufacturing_order_id</code>.
     */
    public void setManufacturingOrderId(UUID value) {
        set(5, value);
    }

    /**
     * Getter for
     * <code>public.material_issue_note.manufacturing_order_id</code>.
     */
    public UUID getManufacturingOrderId() {
        return (UUID) get(5);
    }

    /**
     * Setter for <code>public.material_issue_note.number</code>.
     */
    public void setNumber(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.material_issue_note.number</code>.
     */
    public String getNumber() {
        return (String) get(6);
    }

    /**
     * Setter for <code>public.material_issue_note.date</code>.
     */
    public void setDate(LocalDateTime value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.material_issue_note.date</code>.
     */
    public LocalDateTime getDate() {
        return (LocalDateTime) get(7);
    }

    /**
     * Setter for <code>public.material_issue_note.details</code>.
     */
    public void setDetails(JSONB value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.material_issue_note.details</code>.
     */
    public JSONB getDetails() {
        return (JSONB) get(8);
    }

    /**
     * Setter for <code>public.material_issue_note.materials</code>.
     */
    public void setMaterials(JSONB value) {
        set(9, value);
    }

    /**
     * Getter for <code>public.material_issue_note.materials</code>.
     */
    public JSONB getMaterials() {
        return (JSONB) get(9);
    }

    /**
     * Setter for <code>public.material_issue_note.servicing_order_id</code>.
     */
    public void setServicingOrderId(UUID value) {
        set(10, value);
    }

    /**
     * Getter for <code>public.material_issue_note.servicing_order_id</code>.
     */
    public UUID getServicingOrderId() {
        return (UUID) get(10);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record11 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row11<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String, LocalDateTime, JSONB, JSONB, UUID> fieldsRow() {
        return (Row11) super.fieldsRow();
    }

    @Override
    public Row11<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String, LocalDateTime, JSONB, JSONB, UUID> valuesRow() {
        return (Row11) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return MaterialIssueNote.MATERIAL_ISSUE_NOTE.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return MaterialIssueNote.MATERIAL_ISSUE_NOTE.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return MaterialIssueNote.MATERIAL_ISSUE_NOTE.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return MaterialIssueNote.MATERIAL_ISSUE_NOTE.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return MaterialIssueNote.MATERIAL_ISSUE_NOTE.OWNER_ID;
    }

    @Override
    public Field<UUID> field6() {
        return MaterialIssueNote.MATERIAL_ISSUE_NOTE.MANUFACTURING_ORDER_ID;
    }

    @Override
    public Field<String> field7() {
        return MaterialIssueNote.MATERIAL_ISSUE_NOTE.NUMBER;
    }

    @Override
    public Field<LocalDateTime> field8() {
        return MaterialIssueNote.MATERIAL_ISSUE_NOTE.DATE;
    }

    @Override
    public Field<JSONB> field9() {
        return MaterialIssueNote.MATERIAL_ISSUE_NOTE.DETAILS;
    }

    @Override
    public Field<JSONB> field10() {
        return MaterialIssueNote.MATERIAL_ISSUE_NOTE.MATERIALS;
    }

    @Override
    public Field<UUID> field11() {
        return MaterialIssueNote.MATERIAL_ISSUE_NOTE.SERVICING_ORDER_ID;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public UUID component6() {
        return getManufacturingOrderId();
    }

    @Override
    public String component7() {
        return getNumber();
    }

    @Override
    public LocalDateTime component8() {
        return getDate();
    }

    @Override
    public JSONB component9() {
        return getDetails();
    }

    @Override
    public JSONB component10() {
        return getMaterials();
    }

    @Override
    public UUID component11() {
        return getServicingOrderId();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public UUID value6() {
        return getManufacturingOrderId();
    }

    @Override
    public String value7() {
        return getNumber();
    }

    @Override
    public LocalDateTime value8() {
        return getDate();
    }

    @Override
    public JSONB value9() {
        return getDetails();
    }

    @Override
    public JSONB value10() {
        return getMaterials();
    }

    @Override
    public UUID value11() {
        return getServicingOrderId();
    }

    @Override
    public MaterialIssueNoteRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public MaterialIssueNoteRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public MaterialIssueNoteRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public MaterialIssueNoteRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public MaterialIssueNoteRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public MaterialIssueNoteRecord value6(UUID value) {
        setManufacturingOrderId(value);
        return this;
    }

    @Override
    public MaterialIssueNoteRecord value7(String value) {
        setNumber(value);
        return this;
    }

    @Override
    public MaterialIssueNoteRecord value8(LocalDateTime value) {
        setDate(value);
        return this;
    }

    @Override
    public MaterialIssueNoteRecord value9(JSONB value) {
        setDetails(value);
        return this;
    }

    @Override
    public MaterialIssueNoteRecord value10(JSONB value) {
        setMaterials(value);
        return this;
    }

    @Override
    public MaterialIssueNoteRecord value11(UUID value) {
        setServicingOrderId(value);
        return this;
    }

    @Override
    public MaterialIssueNoteRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, UUID value6, String value7, LocalDateTime value8, JSONB value9, JSONB value10, UUID value11) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached MaterialIssueNoteRecord
     */
    public MaterialIssueNoteRecord() {
        super(MaterialIssueNote.MATERIAL_ISSUE_NOTE);
    }

    /**
     * Create a detached, initialised MaterialIssueNoteRecord
     */
    public MaterialIssueNoteRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, UUID manufacturingOrderId, String number, LocalDateTime date, JSONB details, JSONB materials, UUID servicingOrderId) {
        super(MaterialIssueNote.MATERIAL_ISSUE_NOTE);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setManufacturingOrderId(manufacturingOrderId);
        setNumber(number);
        setDate(date);
        setDetails(details);
        setMaterials(materials);
        setServicingOrderId(servicingOrderId);
    }
}
