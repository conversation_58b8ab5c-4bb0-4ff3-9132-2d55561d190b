/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.CustomerNoteRecord;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row2;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CustomerNote extends TableImpl<CustomerNoteRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.customer_note</code>
     */
    public static final CustomerNote CUSTOMER_NOTE = new CustomerNote();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CustomerNoteRecord> getRecordType() {
        return CustomerNoteRecord.class;
    }

    /**
     * The column <code>public.customer_note.company_id</code>.
     */
    public final TableField<CustomerNoteRecord, UUID> COMPANY_ID = createField(DSL.name("company_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.customer_note.note_id</code>.
     */
    public final TableField<CustomerNoteRecord, UUID> NOTE_ID = createField(DSL.name("note_id"), SQLDataType.UUID, this, "");

    private CustomerNote(Name alias, Table<CustomerNoteRecord> aliased) {
        this(alias, aliased, null);
    }

    private CustomerNote(Name alias, Table<CustomerNoteRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.customer_note</code> table reference
     */
    public CustomerNote(String alias) {
        this(DSL.name(alias), CUSTOMER_NOTE);
    }

    /**
     * Create an aliased <code>public.customer_note</code> table reference
     */
    public CustomerNote(Name alias) {
        this(alias, CUSTOMER_NOTE);
    }

    /**
     * Create a <code>public.customer_note</code> table reference
     */
    public CustomerNote() {
        this(DSL.name("customer_note"), null);
    }

    public <O extends Record> CustomerNote(Table<O> child, ForeignKey<O, CustomerNoteRecord> key) {
        super(child, key, CUSTOMER_NOTE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<ForeignKey<CustomerNoteRecord, ?>> getReferences() {
        return Arrays.asList(Keys.CUSTOMER_NOTE__CUSTOMER_NOTE_COMPANY_ID_FKEY, Keys.CUSTOMER_NOTE__CUSTOMER_NOTE_NOTE_ID_FKEY);
    }

    private transient Company _company;
    private transient Note _note;

    /**
     * Get the implicit join path to the <code>public.company</code> table.
     */
    public Company company() {
        if (_company == null)
            _company = new Company(this, Keys.CUSTOMER_NOTE__CUSTOMER_NOTE_COMPANY_ID_FKEY);

        return _company;
    }

    /**
     * Get the implicit join path to the <code>public.note</code> table.
     */
    public Note note() {
        if (_note == null)
            _note = new Note(this, Keys.CUSTOMER_NOTE__CUSTOMER_NOTE_NOTE_ID_FKEY);

        return _note;
    }

    @Override
    public CustomerNote as(String alias) {
        return new CustomerNote(DSL.name(alias), this);
    }

    @Override
    public CustomerNote as(Name alias) {
        return new CustomerNote(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CustomerNote rename(String name) {
        return new CustomerNote(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public CustomerNote rename(Name name) {
        return new CustomerNote(name, null);
    }

    // -------------------------------------------------------------------------
    // Row2 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row2<UUID, UUID> fieldsRow() {
        return (Row2) super.fieldsRow();
    }
}
