/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.CompanyFileRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row4;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CompanyFile extends TableImpl<CompanyFileRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.company_file</code>
     */
    public static final CompanyFile COMPANY_FILE = new CompanyFile();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CompanyFileRecord> getRecordType() {
        return CompanyFileRecord.class;
    }

    /**
     * The column <code>public.company_file.create_time</code>.
     */
    public final TableField<CompanyFileRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.company_file.owner_id</code>.
     */
    public final TableField<CompanyFileRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.company_file.company_id</code>.
     */
    public final TableField<CompanyFileRecord, UUID> COMPANY_ID = createField(DSL.name("company_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.company_file.file_id</code>.
     */
    public final TableField<CompanyFileRecord, UUID> FILE_ID = createField(DSL.name("file_id"), SQLDataType.UUID.nullable(false), this, "");

    private CompanyFile(Name alias, Table<CompanyFileRecord> aliased) {
        this(alias, aliased, null);
    }

    private CompanyFile(Name alias, Table<CompanyFileRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.company_file</code> table reference
     */
    public CompanyFile(String alias) {
        this(DSL.name(alias), COMPANY_FILE);
    }

    /**
     * Create an aliased <code>public.company_file</code> table reference
     */
    public CompanyFile(Name alias) {
        this(alias, COMPANY_FILE);
    }

    /**
     * Create a <code>public.company_file</code> table reference
     */
    public CompanyFile() {
        this(DSL.name("company_file"), null);
    }

    public <O extends Record> CompanyFile(Table<O> child, ForeignKey<O, CompanyFileRecord> key) {
        super(child, key, COMPANY_FILE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<ForeignKey<CompanyFileRecord, ?>> getReferences() {
        return Arrays.asList(Keys.COMPANY_FILE__COMPANY_FILE_OWNER_ID_FKEY, Keys.COMPANY_FILE__COMPANY_FILE_COMPANY_ID_FKEY, Keys.COMPANY_FILE__COMPANY_FILE_FILE_ID_FKEY);
    }

    private transient Account _account;
    private transient Company _company;
    private transient File _file;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.COMPANY_FILE__COMPANY_FILE_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.company</code> table.
     */
    public Company company() {
        if (_company == null)
            _company = new Company(this, Keys.COMPANY_FILE__COMPANY_FILE_COMPANY_ID_FKEY);

        return _company;
    }

    /**
     * Get the implicit join path to the <code>public.file</code> table.
     */
    public File file() {
        if (_file == null)
            _file = new File(this, Keys.COMPANY_FILE__COMPANY_FILE_FILE_ID_FKEY);

        return _file;
    }

    @Override
    public CompanyFile as(String alias) {
        return new CompanyFile(DSL.name(alias), this);
    }

    @Override
    public CompanyFile as(Name alias) {
        return new CompanyFile(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CompanyFile rename(String name) {
        return new CompanyFile(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public CompanyFile rename(Name name) {
        return new CompanyFile(name, null);
    }

    // -------------------------------------------------------------------------
    // Row4 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row4<LocalDateTime, UUID, UUID, UUID> fieldsRow() {
        return (Row4) super.fieldsRow();
    }
}
