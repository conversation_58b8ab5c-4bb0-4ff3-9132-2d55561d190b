/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.InventoryCurrentStockRecord;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row7;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class InventoryCurrentStock extends TableImpl<InventoryCurrentStockRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.inventory_current_stock</code>
     */
    public static final InventoryCurrentStock INVENTORY_CURRENT_STOCK = new InventoryCurrentStock();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<InventoryCurrentStockRecord> getRecordType() {
        return InventoryCurrentStockRecord.class;
    }

    /**
     * The column <code>public.inventory_current_stock.update_time</code>.
     */
    public final TableField<InventoryCurrentStockRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.inventory_current_stock.material_good_id</code>.
     */
    public final TableField<InventoryCurrentStockRecord, UUID> MATERIAL_GOOD_ID = createField(DSL.name("material_good_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.inventory_current_stock.unit_id</code>.
     */
    public final TableField<InventoryCurrentStockRecord, UUID> UNIT_ID = createField(DSL.name("unit_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.inventory_current_stock.quantity</code>.
     */
    public final TableField<InventoryCurrentStockRecord, BigDecimal> QUANTITY = createField(DSL.name("quantity"), SQLDataType.NUMERIC(12, 4).nullable(false), this, "");

    /**
     * The column <code>public.inventory_current_stock.currency</code>.
     */
    public final TableField<InventoryCurrentStockRecord, String> CURRENCY = createField(DSL.name("currency"), SQLDataType.VARCHAR(3).nullable(false), this, "");

    /**
     * The column <code>public.inventory_current_stock.cost</code>.
     */
    public final TableField<InventoryCurrentStockRecord, Long> COST = createField(DSL.name("cost"), SQLDataType.BIGINT.nullable(false), this, "");

    /**
     * The column <code>public.inventory_current_stock.total_value</code>.
     */
    public final TableField<InventoryCurrentStockRecord, Long> TOTAL_VALUE = createField(DSL.name("total_value"), SQLDataType.BIGINT.nullable(false), this, "");

    private InventoryCurrentStock(Name alias, Table<InventoryCurrentStockRecord> aliased) {
        this(alias, aliased, null);
    }

    private InventoryCurrentStock(Name alias, Table<InventoryCurrentStockRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.inventory_current_stock</code> table
     * reference
     */
    public InventoryCurrentStock(String alias) {
        this(DSL.name(alias), INVENTORY_CURRENT_STOCK);
    }

    /**
     * Create an aliased <code>public.inventory_current_stock</code> table
     * reference
     */
    public InventoryCurrentStock(Name alias) {
        this(alias, INVENTORY_CURRENT_STOCK);
    }

    /**
     * Create a <code>public.inventory_current_stock</code> table reference
     */
    public InventoryCurrentStock() {
        this(DSL.name("inventory_current_stock"), null);
    }

    public <O extends Record> InventoryCurrentStock(Table<O> child, ForeignKey<O, InventoryCurrentStockRecord> key) {
        super(child, key, INVENTORY_CURRENT_STOCK);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<UniqueKey<InventoryCurrentStockRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.INVENTORY_CURRENT_STOCK_MATERIAL_GOOD_ID_UNIT_ID_KEY);
    }

    @Override
    public List<ForeignKey<InventoryCurrentStockRecord, ?>> getReferences() {
        return Arrays.asList(Keys.INVENTORY_CURRENT_STOCK__INVENTORY_CURRENT_STOCK_MATERIAL_GOOD_ID_FKEY, Keys.INVENTORY_CURRENT_STOCK__INVENTORY_CURRENT_STOCK_UNIT_ID_FKEY);
    }

    private transient MaterialGood _materialGood;
    private transient InventoryUnit _inventoryUnit;

    /**
     * Get the implicit join path to the <code>public.material_good</code>
     * table.
     */
    public MaterialGood materialGood() {
        if (_materialGood == null)
            _materialGood = new MaterialGood(this, Keys.INVENTORY_CURRENT_STOCK__INVENTORY_CURRENT_STOCK_MATERIAL_GOOD_ID_FKEY);

        return _materialGood;
    }

    /**
     * Get the implicit join path to the <code>public.inventory_unit</code>
     * table.
     */
    public InventoryUnit inventoryUnit() {
        if (_inventoryUnit == null)
            _inventoryUnit = new InventoryUnit(this, Keys.INVENTORY_CURRENT_STOCK__INVENTORY_CURRENT_STOCK_UNIT_ID_FKEY);

        return _inventoryUnit;
    }

    @Override
    public InventoryCurrentStock as(String alias) {
        return new InventoryCurrentStock(DSL.name(alias), this);
    }

    @Override
    public InventoryCurrentStock as(Name alias) {
        return new InventoryCurrentStock(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public InventoryCurrentStock rename(String name) {
        return new InventoryCurrentStock(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public InventoryCurrentStock rename(Name name) {
        return new InventoryCurrentStock(name, null);
    }

    // -------------------------------------------------------------------------
    // Row7 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row7<LocalDateTime, UUID, UUID, BigDecimal, String, Long, Long> fieldsRow() {
        return (Row7) super.fieldsRow();
    }
}
