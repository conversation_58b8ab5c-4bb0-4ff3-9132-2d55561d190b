/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.SalesOrderNoteRecord;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row2;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SalesOrderNote extends TableImpl<SalesOrderNoteRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.sales_order_note</code>
     */
    public static final SalesOrderNote SALES_ORDER_NOTE = new SalesOrderNote();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SalesOrderNoteRecord> getRecordType() {
        return SalesOrderNoteRecord.class;
    }

    /**
     * The column <code>public.sales_order_note.sales_order_id</code>.
     */
    public final TableField<SalesOrderNoteRecord, UUID> SALES_ORDER_ID = createField(DSL.name("sales_order_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.sales_order_note.note_id</code>.
     */
    public final TableField<SalesOrderNoteRecord, UUID> NOTE_ID = createField(DSL.name("note_id"), SQLDataType.UUID.nullable(false), this, "");

    private SalesOrderNote(Name alias, Table<SalesOrderNoteRecord> aliased) {
        this(alias, aliased, null);
    }

    private SalesOrderNote(Name alias, Table<SalesOrderNoteRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.sales_order_note</code> table reference
     */
    public SalesOrderNote(String alias) {
        this(DSL.name(alias), SALES_ORDER_NOTE);
    }

    /**
     * Create an aliased <code>public.sales_order_note</code> table reference
     */
    public SalesOrderNote(Name alias) {
        this(alias, SALES_ORDER_NOTE);
    }

    /**
     * Create a <code>public.sales_order_note</code> table reference
     */
    public SalesOrderNote() {
        this(DSL.name("sales_order_note"), null);
    }

    public <O extends Record> SalesOrderNote(Table<O> child, ForeignKey<O, SalesOrderNoteRecord> key) {
        super(child, key, SALES_ORDER_NOTE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<ForeignKey<SalesOrderNoteRecord, ?>> getReferences() {
        return Arrays.asList(Keys.SALES_ORDER_NOTE__SALES_ORDER_NOTE_SALES_ORDER_ID_FKEY, Keys.SALES_ORDER_NOTE__SALES_ORDER_NOTE_NOTE_ID_FKEY);
    }

    private transient SalesOrder _salesOrder;
    private transient Note _note;

    /**
     * Get the implicit join path to the <code>public.sales_order</code> table.
     */
    public SalesOrder salesOrder() {
        if (_salesOrder == null)
            _salesOrder = new SalesOrder(this, Keys.SALES_ORDER_NOTE__SALES_ORDER_NOTE_SALES_ORDER_ID_FKEY);

        return _salesOrder;
    }

    /**
     * Get the implicit join path to the <code>public.note</code> table.
     */
    public Note note() {
        if (_note == null)
            _note = new Note(this, Keys.SALES_ORDER_NOTE__SALES_ORDER_NOTE_NOTE_ID_FKEY);

        return _note;
    }

    @Override
    public SalesOrderNote as(String alias) {
        return new SalesOrderNote(DSL.name(alias), this);
    }

    @Override
    public SalesOrderNote as(Name alias) {
        return new SalesOrderNote(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SalesOrderNote rename(String name) {
        return new SalesOrderNote(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public SalesOrderNote rename(Name name) {
        return new SalesOrderNote(name, null);
    }

    // -------------------------------------------------------------------------
    // Row2 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row2<UUID, UUID> fieldsRow() {
        return (Row2) super.fieldsRow();
    }
}
