/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.ManufacturingtaskWorkstationRecord;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row3;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ManufacturingtaskWorkstation extends TableImpl<ManufacturingtaskWorkstationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>public.manufacturingtask_workstation</code>
     */
    public static final ManufacturingtaskWorkstation MANUFACTURINGTASK_WORKSTATION = new ManufacturingtaskWorkstation();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ManufacturingtaskWorkstationRecord> getRecordType() {
        return ManufacturingtaskWorkstationRecord.class;
    }

    /**
     * The column <code>public.manufacturingtask_workstation.owner_id</code>.
     */
    public final TableField<ManufacturingtaskWorkstationRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column
     * <code>public.manufacturingtask_workstation.manufacturing_workstation_id</code>.
     */
    public final TableField<ManufacturingtaskWorkstationRecord, UUID> MANUFACTURING_WORKSTATION_ID = createField(DSL.name("manufacturing_workstation_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column
     * <code>public.manufacturingtask_workstation.manufacturing_task_id</code>.
     */
    public final TableField<ManufacturingtaskWorkstationRecord, UUID> MANUFACTURING_TASK_ID = createField(DSL.name("manufacturing_task_id"), SQLDataType.UUID.nullable(false), this, "");

    private ManufacturingtaskWorkstation(Name alias, Table<ManufacturingtaskWorkstationRecord> aliased) {
        this(alias, aliased, null);
    }

    private ManufacturingtaskWorkstation(Name alias, Table<ManufacturingtaskWorkstationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.manufacturingtask_workstation</code> table
     * reference
     */
    public ManufacturingtaskWorkstation(String alias) {
        this(DSL.name(alias), MANUFACTURINGTASK_WORKSTATION);
    }

    /**
     * Create an aliased <code>public.manufacturingtask_workstation</code> table
     * reference
     */
    public ManufacturingtaskWorkstation(Name alias) {
        this(alias, MANUFACTURINGTASK_WORKSTATION);
    }

    /**
     * Create a <code>public.manufacturingtask_workstation</code> table
     * reference
     */
    public ManufacturingtaskWorkstation() {
        this(DSL.name("manufacturingtask_workstation"), null);
    }

    public <O extends Record> ManufacturingtaskWorkstation(Table<O> child, ForeignKey<O, ManufacturingtaskWorkstationRecord> key) {
        super(child, key, MANUFACTURINGTASK_WORKSTATION);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<ForeignKey<ManufacturingtaskWorkstationRecord, ?>> getReferences() {
        return Arrays.asList(Keys.MANUFACTURINGTASK_WORKSTATION__MANUFACTURINGTASK_WORKSTATION_OWNER_ID_FKEY, Keys.MANUFACTURINGTASK_WORKSTATION__MANUFACTURINGTASK_WORKSTATION_MANUFACTURING_WORKSTATION_ID_FKEY, Keys.MANUFACTURINGTASK_WORKSTATION__MANUFACTURINGTASK_WORKSTATION_MANUFACTURING_TASK_ID_FKEY);
    }

    private transient Account _account;
    private transient ManufacturingWorkstation _manufacturingWorkstation;
    private transient ManufacturingTask _manufacturingTask;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.MANUFACTURINGTASK_WORKSTATION__MANUFACTURINGTASK_WORKSTATION_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the
     * <code>public.manufacturing_workstation</code> table.
     */
    public ManufacturingWorkstation manufacturingWorkstation() {
        if (_manufacturingWorkstation == null)
            _manufacturingWorkstation = new ManufacturingWorkstation(this, Keys.MANUFACTURINGTASK_WORKSTATION__MANUFACTURINGTASK_WORKSTATION_MANUFACTURING_WORKSTATION_ID_FKEY);

        return _manufacturingWorkstation;
    }

    /**
     * Get the implicit join path to the <code>public.manufacturing_task</code>
     * table.
     */
    public ManufacturingTask manufacturingTask() {
        if (_manufacturingTask == null)
            _manufacturingTask = new ManufacturingTask(this, Keys.MANUFACTURINGTASK_WORKSTATION__MANUFACTURINGTASK_WORKSTATION_MANUFACTURING_TASK_ID_FKEY);

        return _manufacturingTask;
    }

    @Override
    public ManufacturingtaskWorkstation as(String alias) {
        return new ManufacturingtaskWorkstation(DSL.name(alias), this);
    }

    @Override
    public ManufacturingtaskWorkstation as(Name alias) {
        return new ManufacturingtaskWorkstation(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ManufacturingtaskWorkstation rename(String name) {
        return new ManufacturingtaskWorkstation(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ManufacturingtaskWorkstation rename(Name name) {
        return new ManufacturingtaskWorkstation(name, null);
    }

    // -------------------------------------------------------------------------
    // Row3 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row3<UUID, UUID, UUID> fieldsRow() {
        return (Row3) super.fieldsRow();
    }
}
