/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.ManufacturingTasksView;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ManufacturingTasksViewRecord extends TableRecordImpl<ManufacturingTasksViewRecord> implements Record7<UUID, LocalDateTime, JSONB, String, BigDecimal, String, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.manufacturing_tasks_view.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.manufacturing_tasks_view.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.manufacturing_tasks_view.execution_time</code>.
     */
    public void setExecutionTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.manufacturing_tasks_view.execution_time</code>.
     */
    public LocalDateTime getExecutionTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.manufacturing_tasks_view.task_name</code>.
     */
    public void setTaskName(JSONB value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.manufacturing_tasks_view.task_name</code>.
     */
    public JSONB getTaskName() {
        return (JSONB) get(2);
    }

    /**
     * Setter for <code>public.manufacturing_tasks_view.employee_name</code>.
     */
    public void setEmployeeName(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.manufacturing_tasks_view.employee_name</code>.
     */
    public String getEmployeeName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>public.manufacturing_tasks_view.quantity</code>.
     */
    public void setQuantity(BigDecimal value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.manufacturing_tasks_view.quantity</code>.
     */
    public BigDecimal getQuantity() {
        return (BigDecimal) get(4);
    }

    /**
     * Setter for <code>public.manufacturing_tasks_view.product_name</code>.
     */
    public void setProductName(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.manufacturing_tasks_view.product_name</code>.
     */
    public String getProductName() {
        return (String) get(5);
    }

    /**
     * Setter for <code>public.manufacturing_tasks_view.product</code>.
     */
    public void setProduct(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.manufacturing_tasks_view.product</code>.
     */
    public String getProduct() {
        return (String) get(6);
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row7<UUID, LocalDateTime, JSONB, String, BigDecimal, String, String> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    @Override
    public Row7<UUID, LocalDateTime, JSONB, String, BigDecimal, String, String> valuesRow() {
        return (Row7) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return ManufacturingTasksView.MANUFACTURING_TASKS_VIEW.OWNER_ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return ManufacturingTasksView.MANUFACTURING_TASKS_VIEW.EXECUTION_TIME;
    }

    @Override
    public Field<JSONB> field3() {
        return ManufacturingTasksView.MANUFACTURING_TASKS_VIEW.TASK_NAME;
    }

    @Override
    public Field<String> field4() {
        return ManufacturingTasksView.MANUFACTURING_TASKS_VIEW.EMPLOYEE_NAME;
    }

    @Override
    public Field<BigDecimal> field5() {
        return ManufacturingTasksView.MANUFACTURING_TASKS_VIEW.QUANTITY;
    }

    @Override
    public Field<String> field6() {
        return ManufacturingTasksView.MANUFACTURING_TASKS_VIEW.PRODUCT_NAME;
    }

    @Override
    public Field<String> field7() {
        return ManufacturingTasksView.MANUFACTURING_TASKS_VIEW.PRODUCT;
    }

    @Override
    public UUID component1() {
        return getOwnerId();
    }

    @Override
    public LocalDateTime component2() {
        return getExecutionTime();
    }

    @Override
    public JSONB component3() {
        return getTaskName();
    }

    @Override
    public String component4() {
        return getEmployeeName();
    }

    @Override
    public BigDecimal component5() {
        return getQuantity();
    }

    @Override
    public String component6() {
        return getProductName();
    }

    @Override
    public String component7() {
        return getProduct();
    }

    @Override
    public UUID value1() {
        return getOwnerId();
    }

    @Override
    public LocalDateTime value2() {
        return getExecutionTime();
    }

    @Override
    public JSONB value3() {
        return getTaskName();
    }

    @Override
    public String value4() {
        return getEmployeeName();
    }

    @Override
    public BigDecimal value5() {
        return getQuantity();
    }

    @Override
    public String value6() {
        return getProductName();
    }

    @Override
    public String value7() {
        return getProduct();
    }

    @Override
    public ManufacturingTasksViewRecord value1(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public ManufacturingTasksViewRecord value2(LocalDateTime value) {
        setExecutionTime(value);
        return this;
    }

    @Override
    public ManufacturingTasksViewRecord value3(JSONB value) {
        setTaskName(value);
        return this;
    }

    @Override
    public ManufacturingTasksViewRecord value4(String value) {
        setEmployeeName(value);
        return this;
    }

    @Override
    public ManufacturingTasksViewRecord value5(BigDecimal value) {
        setQuantity(value);
        return this;
    }

    @Override
    public ManufacturingTasksViewRecord value6(String value) {
        setProductName(value);
        return this;
    }

    @Override
    public ManufacturingTasksViewRecord value7(String value) {
        setProduct(value);
        return this;
    }

    @Override
    public ManufacturingTasksViewRecord values(UUID value1, LocalDateTime value2, JSONB value3, String value4, BigDecimal value5, String value6, String value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ManufacturingTasksViewRecord
     */
    public ManufacturingTasksViewRecord() {
        super(ManufacturingTasksView.MANUFACTURING_TASKS_VIEW);
    }

    /**
     * Create a detached, initialised ManufacturingTasksViewRecord
     */
    public ManufacturingTasksViewRecord(UUID ownerId, LocalDateTime executionTime, JSONB taskName, String employeeName, BigDecimal quantity, String productName, String product) {
        super(ManufacturingTasksView.MANUFACTURING_TASKS_VIEW);

        setOwnerId(ownerId);
        setExecutionTime(executionTime);
        setTaskName(taskName);
        setEmployeeName(employeeName);
        setQuantity(quantity);
        setProductName(productName);
        setProduct(product);
    }
}
