/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.ManufacturingOrdersProcessingTimeView;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ManufacturingOrdersProcessingTimeViewRecord extends TableRecordImpl<ManufacturingOrdersProcessingTimeViewRecord> implements Record5<UUID, UUID, LocalDateTime, LocalDateTime, BigDecimal> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for
     * <code>public.manufacturing_orders_processing_time_view.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for
     * <code>public.manufacturing_orders_processing_time_view.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(0);
    }

    /**
     * Setter for
     * <code>public.manufacturing_orders_processing_time_view.manufacturing_order_id</code>.
     */
    public void setManufacturingOrderId(UUID value) {
        set(1, value);
    }

    /**
     * Getter for
     * <code>public.manufacturing_orders_processing_time_view.manufacturing_order_id</code>.
     */
    public UUID getManufacturingOrderId() {
        return (UUID) get(1);
    }

    /**
     * Setter for
     * <code>public.manufacturing_orders_processing_time_view.task_start_time</code>.
     */
    public void setTaskStartTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for
     * <code>public.manufacturing_orders_processing_time_view.task_start_time</code>.
     */
    public LocalDateTime getTaskStartTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for
     * <code>public.manufacturing_orders_processing_time_view.task_end_time</code>.
     */
    public void setTaskEndTime(LocalDateTime value) {
        set(3, value);
    }

    /**
     * Getter for
     * <code>public.manufacturing_orders_processing_time_view.task_end_time</code>.
     */
    public LocalDateTime getTaskEndTime() {
        return (LocalDateTime) get(3);
    }

    /**
     * Setter for
     * <code>public.manufacturing_orders_processing_time_view.total_working_seconds</code>.
     */
    public void setTotalWorkingSeconds(BigDecimal value) {
        set(4, value);
    }

    /**
     * Getter for
     * <code>public.manufacturing_orders_processing_time_view.total_working_seconds</code>.
     */
    public BigDecimal getTotalWorkingSeconds() {
        return (BigDecimal) get(4);
    }

    // -------------------------------------------------------------------------
    // Record5 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row5<UUID, UUID, LocalDateTime, LocalDateTime, BigDecimal> fieldsRow() {
        return (Row5) super.fieldsRow();
    }

    @Override
    public Row5<UUID, UUID, LocalDateTime, LocalDateTime, BigDecimal> valuesRow() {
        return (Row5) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return ManufacturingOrdersProcessingTimeView.MANUFACTURING_ORDERS_PROCESSING_TIME_VIEW.OWNER_ID;
    }

    @Override
    public Field<UUID> field2() {
        return ManufacturingOrdersProcessingTimeView.MANUFACTURING_ORDERS_PROCESSING_TIME_VIEW.MANUFACTURING_ORDER_ID;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return ManufacturingOrdersProcessingTimeView.MANUFACTURING_ORDERS_PROCESSING_TIME_VIEW.TASK_START_TIME;
    }

    @Override
    public Field<LocalDateTime> field4() {
        return ManufacturingOrdersProcessingTimeView.MANUFACTURING_ORDERS_PROCESSING_TIME_VIEW.TASK_END_TIME;
    }

    @Override
    public Field<BigDecimal> field5() {
        return ManufacturingOrdersProcessingTimeView.MANUFACTURING_ORDERS_PROCESSING_TIME_VIEW.TOTAL_WORKING_SECONDS;
    }

    @Override
    public UUID component1() {
        return getOwnerId();
    }

    @Override
    public UUID component2() {
        return getManufacturingOrderId();
    }

    @Override
    public LocalDateTime component3() {
        return getTaskStartTime();
    }

    @Override
    public LocalDateTime component4() {
        return getTaskEndTime();
    }

    @Override
    public BigDecimal component5() {
        return getTotalWorkingSeconds();
    }

    @Override
    public UUID value1() {
        return getOwnerId();
    }

    @Override
    public UUID value2() {
        return getManufacturingOrderId();
    }

    @Override
    public LocalDateTime value3() {
        return getTaskStartTime();
    }

    @Override
    public LocalDateTime value4() {
        return getTaskEndTime();
    }

    @Override
    public BigDecimal value5() {
        return getTotalWorkingSeconds();
    }

    @Override
    public ManufacturingOrdersProcessingTimeViewRecord value1(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public ManufacturingOrdersProcessingTimeViewRecord value2(UUID value) {
        setManufacturingOrderId(value);
        return this;
    }

    @Override
    public ManufacturingOrdersProcessingTimeViewRecord value3(LocalDateTime value) {
        setTaskStartTime(value);
        return this;
    }

    @Override
    public ManufacturingOrdersProcessingTimeViewRecord value4(LocalDateTime value) {
        setTaskEndTime(value);
        return this;
    }

    @Override
    public ManufacturingOrdersProcessingTimeViewRecord value5(BigDecimal value) {
        setTotalWorkingSeconds(value);
        return this;
    }

    @Override
    public ManufacturingOrdersProcessingTimeViewRecord values(UUID value1, UUID value2, LocalDateTime value3, LocalDateTime value4, BigDecimal value5) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ManufacturingOrdersProcessingTimeViewRecord
     */
    public ManufacturingOrdersProcessingTimeViewRecord() {
        super(ManufacturingOrdersProcessingTimeView.MANUFACTURING_ORDERS_PROCESSING_TIME_VIEW);
    }

    /**
     * Create a detached, initialised
     * ManufacturingOrdersProcessingTimeViewRecord
     */
    public ManufacturingOrdersProcessingTimeViewRecord(UUID ownerId, UUID manufacturingOrderId, LocalDateTime taskStartTime, LocalDateTime taskEndTime, BigDecimal totalWorkingSeconds) {
        super(ManufacturingOrdersProcessingTimeView.MANUFACTURING_ORDERS_PROCESSING_TIME_VIEW);

        setOwnerId(ownerId);
        setManufacturingOrderId(manufacturingOrderId);
        setTaskStartTime(taskStartTime);
        setTaskEndTime(taskEndTime);
        setTotalWorkingSeconds(totalWorkingSeconds);
    }
}
