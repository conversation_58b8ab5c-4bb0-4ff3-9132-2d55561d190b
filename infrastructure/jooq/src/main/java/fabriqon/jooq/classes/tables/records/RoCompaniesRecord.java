/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.RoCompanies;

import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.Record15;
import org.jooq.Row15;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class RoCompaniesRecord extends TableRecordImpl<RoCompaniesRecord> implements Record15<LocalDateTime, String, String, String, String, String, String, String, String, String, String, String, String, String, Object> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.ro_companies.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.ro_companies.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(0);
    }

    /**
     * Setter for <code>public.ro_companies.nume</code>.
     */
    public void setNume(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.ro_companies.nume</code>.
     */
    public String getNume() {
        return (String) get(1);
    }

    /**
     * Setter for <code>public.ro_companies.cui</code>.
     */
    public void setCui(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.ro_companies.cui</code>.
     */
    public String getCui() {
        return (String) get(2);
    }

    /**
     * Setter for <code>public.ro_companies.nr_reg_com</code>.
     */
    public void setNrRegCom(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.ro_companies.nr_reg_com</code>.
     */
    public String getNrRegCom() {
        return (String) get(3);
    }

    /**
     * Setter for <code>public.ro_companies.judet</code>.
     */
    public void setJudet(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.ro_companies.judet</code>.
     */
    public String getJudet() {
        return (String) get(4);
    }

    /**
     * Setter for <code>public.ro_companies.localitate</code>.
     */
    public void setLocalitate(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.ro_companies.localitate</code>.
     */
    public String getLocalitate() {
        return (String) get(5);
    }

    /**
     * Setter for <code>public.ro_companies.strada</code>.
     */
    public void setStrada(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.ro_companies.strada</code>.
     */
    public String getStrada() {
        return (String) get(6);
    }

    /**
     * Setter for <code>public.ro_companies.numar</code>.
     */
    public void setNumar(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.ro_companies.numar</code>.
     */
    public String getNumar() {
        return (String) get(7);
    }

    /**
     * Setter for <code>public.ro_companies.sector</code>.
     */
    public void setSector(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.ro_companies.sector</code>.
     */
    public String getSector() {
        return (String) get(8);
    }

    /**
     * Setter for <code>public.ro_companies.bloc</code>.
     */
    public void setBloc(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>public.ro_companies.bloc</code>.
     */
    public String getBloc() {
        return (String) get(9);
    }

    /**
     * Setter for <code>public.ro_companies.scara</code>.
     */
    public void setScara(String value) {
        set(10, value);
    }

    /**
     * Getter for <code>public.ro_companies.scara</code>.
     */
    public String getScara() {
        return (String) get(10);
    }

    /**
     * Setter for <code>public.ro_companies.etaj</code>.
     */
    public void setEtaj(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>public.ro_companies.etaj</code>.
     */
    public String getEtaj() {
        return (String) get(11);
    }

    /**
     * Setter for <code>public.ro_companies.apartament</code>.
     */
    public void setApartament(String value) {
        set(12, value);
    }

    /**
     * Getter for <code>public.ro_companies.apartament</code>.
     */
    public String getApartament() {
        return (String) get(12);
    }

    /**
     * Setter for <code>public.ro_companies.cod_postal</code>.
     */
    public void setCodPostal(String value) {
        set(13, value);
    }

    /**
     * Getter for <code>public.ro_companies.cod_postal</code>.
     */
    public String getCodPostal() {
        return (String) get(13);
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public void setTextSearch(Object value) {
        set(14, value);
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public Object getTextSearch() {
        return get(14);
    }

    // -------------------------------------------------------------------------
    // Record15 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row15<LocalDateTime, String, String, String, String, String, String, String, String, String, String, String, String, String, Object> fieldsRow() {
        return (Row15) super.fieldsRow();
    }

    @Override
    public Row15<LocalDateTime, String, String, String, String, String, String, String, String, String, String, String, String, String, Object> valuesRow() {
        return (Row15) super.valuesRow();
    }

    @Override
    public Field<LocalDateTime> field1() {
        return RoCompanies.RO_COMPANIES.CREATE_TIME;
    }

    @Override
    public Field<String> field2() {
        return RoCompanies.RO_COMPANIES.NUME;
    }

    @Override
    public Field<String> field3() {
        return RoCompanies.RO_COMPANIES.CUI;
    }

    @Override
    public Field<String> field4() {
        return RoCompanies.RO_COMPANIES.NR_REG_COM;
    }

    @Override
    public Field<String> field5() {
        return RoCompanies.RO_COMPANIES.JUDET;
    }

    @Override
    public Field<String> field6() {
        return RoCompanies.RO_COMPANIES.LOCALITATE;
    }

    @Override
    public Field<String> field7() {
        return RoCompanies.RO_COMPANIES.STRADA;
    }

    @Override
    public Field<String> field8() {
        return RoCompanies.RO_COMPANIES.NUMAR;
    }

    @Override
    public Field<String> field9() {
        return RoCompanies.RO_COMPANIES.SECTOR;
    }

    @Override
    public Field<String> field10() {
        return RoCompanies.RO_COMPANIES.BLOC;
    }

    @Override
    public Field<String> field11() {
        return RoCompanies.RO_COMPANIES.SCARA;
    }

    @Override
    public Field<String> field12() {
        return RoCompanies.RO_COMPANIES.ETAJ;
    }

    @Override
    public Field<String> field13() {
        return RoCompanies.RO_COMPANIES.APARTAMENT;
    }

    @Override
    public Field<String> field14() {
        return RoCompanies.RO_COMPANIES.COD_POSTAL;
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public Field<Object> field15() {
        return RoCompanies.RO_COMPANIES.TEXT_SEARCH;
    }

    @Override
    public LocalDateTime component1() {
        return getCreateTime();
    }

    @Override
    public String component2() {
        return getNume();
    }

    @Override
    public String component3() {
        return getCui();
    }

    @Override
    public String component4() {
        return getNrRegCom();
    }

    @Override
    public String component5() {
        return getJudet();
    }

    @Override
    public String component6() {
        return getLocalitate();
    }

    @Override
    public String component7() {
        return getStrada();
    }

    @Override
    public String component8() {
        return getNumar();
    }

    @Override
    public String component9() {
        return getSector();
    }

    @Override
    public String component10() {
        return getBloc();
    }

    @Override
    public String component11() {
        return getScara();
    }

    @Override
    public String component12() {
        return getEtaj();
    }

    @Override
    public String component13() {
        return getApartament();
    }

    @Override
    public String component14() {
        return getCodPostal();
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public Object component15() {
        return getTextSearch();
    }

    @Override
    public LocalDateTime value1() {
        return getCreateTime();
    }

    @Override
    public String value2() {
        return getNume();
    }

    @Override
    public String value3() {
        return getCui();
    }

    @Override
    public String value4() {
        return getNrRegCom();
    }

    @Override
    public String value5() {
        return getJudet();
    }

    @Override
    public String value6() {
        return getLocalitate();
    }

    @Override
    public String value7() {
        return getStrada();
    }

    @Override
    public String value8() {
        return getNumar();
    }

    @Override
    public String value9() {
        return getSector();
    }

    @Override
    public String value10() {
        return getBloc();
    }

    @Override
    public String value11() {
        return getScara();
    }

    @Override
    public String value12() {
        return getEtaj();
    }

    @Override
    public String value13() {
        return getApartament();
    }

    @Override
    public String value14() {
        return getCodPostal();
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public Object value15() {
        return getTextSearch();
    }

    @Override
    public RoCompaniesRecord value1(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public RoCompaniesRecord value2(String value) {
        setNume(value);
        return this;
    }

    @Override
    public RoCompaniesRecord value3(String value) {
        setCui(value);
        return this;
    }

    @Override
    public RoCompaniesRecord value4(String value) {
        setNrRegCom(value);
        return this;
    }

    @Override
    public RoCompaniesRecord value5(String value) {
        setJudet(value);
        return this;
    }

    @Override
    public RoCompaniesRecord value6(String value) {
        setLocalitate(value);
        return this;
    }

    @Override
    public RoCompaniesRecord value7(String value) {
        setStrada(value);
        return this;
    }

    @Override
    public RoCompaniesRecord value8(String value) {
        setNumar(value);
        return this;
    }

    @Override
    public RoCompaniesRecord value9(String value) {
        setSector(value);
        return this;
    }

    @Override
    public RoCompaniesRecord value10(String value) {
        setBloc(value);
        return this;
    }

    @Override
    public RoCompaniesRecord value11(String value) {
        setScara(value);
        return this;
    }

    @Override
    public RoCompaniesRecord value12(String value) {
        setEtaj(value);
        return this;
    }

    @Override
    public RoCompaniesRecord value13(String value) {
        setApartament(value);
        return this;
    }

    @Override
    public RoCompaniesRecord value14(String value) {
        setCodPostal(value);
        return this;
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public RoCompaniesRecord value15(Object value) {
        setTextSearch(value);
        return this;
    }

    @Override
    public RoCompaniesRecord values(LocalDateTime value1, String value2, String value3, String value4, String value5, String value6, String value7, String value8, String value9, String value10, String value11, String value12, String value13, String value14, Object value15) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached RoCompaniesRecord
     */
    public RoCompaniesRecord() {
        super(RoCompanies.RO_COMPANIES);
    }

    /**
     * Create a detached, initialised RoCompaniesRecord
     */
    public RoCompaniesRecord(LocalDateTime createTime, String nume, String cui, String nrRegCom, String judet, String localitate, String strada, String numar, String sector, String bloc, String scara, String etaj, String apartament, String codPostal, Object textSearch) {
        super(RoCompanies.RO_COMPANIES);

        setCreateTime(createTime);
        setNume(nume);
        setCui(cui);
        setNrRegCom(nrRegCom);
        setJudet(judet);
        setLocalitate(localitate);
        setStrada(strada);
        setNumar(numar);
        setSector(sector);
        setBloc(bloc);
        setScara(scara);
        setEtaj(etaj);
        setApartament(apartament);
        setCodPostal(codPostal);
        setTextSearch(textSearch);
    }
}
