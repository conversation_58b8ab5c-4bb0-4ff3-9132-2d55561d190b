/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.FirebaseRegistrationToken;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class FirebaseRegistrationTokenRecord extends UpdatableRecordImpl<FirebaseRegistrationTokenRecord> implements Record8<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, UUID, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.firebase_registration_token.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.firebase_registration_token.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.firebase_registration_token.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.firebase_registration_token.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.firebase_registration_token.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.firebase_registration_token.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.firebase_registration_token.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.firebase_registration_token.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.firebase_registration_token.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.firebase_registration_token.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.firebase_registration_token.token</code>.
     */
    public void setToken(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.firebase_registration_token.token</code>.
     */
    public String getToken() {
        return (String) get(5);
    }

    /**
     * Setter for <code>public.firebase_registration_token.user_id</code>.
     */
    public void setUserId(UUID value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.firebase_registration_token.user_id</code>.
     */
    public UUID getUserId() {
        return (UUID) get(6);
    }

    /**
     * Setter for <code>public.firebase_registration_token.employee_id</code>.
     */
    public void setEmployeeId(UUID value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.firebase_registration_token.employee_id</code>.
     */
    public UUID getEmployeeId() {
        return (UUID) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record8 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row8<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, UUID, UUID> fieldsRow() {
        return (Row8) super.fieldsRow();
    }

    @Override
    public Row8<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, UUID, UUID> valuesRow() {
        return (Row8) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return FirebaseRegistrationToken.FIREBASE_REGISTRATION_TOKEN.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return FirebaseRegistrationToken.FIREBASE_REGISTRATION_TOKEN.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return FirebaseRegistrationToken.FIREBASE_REGISTRATION_TOKEN.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return FirebaseRegistrationToken.FIREBASE_REGISTRATION_TOKEN.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return FirebaseRegistrationToken.FIREBASE_REGISTRATION_TOKEN.OWNER_ID;
    }

    @Override
    public Field<String> field6() {
        return FirebaseRegistrationToken.FIREBASE_REGISTRATION_TOKEN.TOKEN;
    }

    @Override
    public Field<UUID> field7() {
        return FirebaseRegistrationToken.FIREBASE_REGISTRATION_TOKEN.USER_ID;
    }

    @Override
    public Field<UUID> field8() {
        return FirebaseRegistrationToken.FIREBASE_REGISTRATION_TOKEN.EMPLOYEE_ID;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public String component6() {
        return getToken();
    }

    @Override
    public UUID component7() {
        return getUserId();
    }

    @Override
    public UUID component8() {
        return getEmployeeId();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public String value6() {
        return getToken();
    }

    @Override
    public UUID value7() {
        return getUserId();
    }

    @Override
    public UUID value8() {
        return getEmployeeId();
    }

    @Override
    public FirebaseRegistrationTokenRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public FirebaseRegistrationTokenRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public FirebaseRegistrationTokenRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public FirebaseRegistrationTokenRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public FirebaseRegistrationTokenRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public FirebaseRegistrationTokenRecord value6(String value) {
        setToken(value);
        return this;
    }

    @Override
    public FirebaseRegistrationTokenRecord value7(UUID value) {
        setUserId(value);
        return this;
    }

    @Override
    public FirebaseRegistrationTokenRecord value8(UUID value) {
        setEmployeeId(value);
        return this;
    }

    @Override
    public FirebaseRegistrationTokenRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, String value6, UUID value7, UUID value8) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached FirebaseRegistrationTokenRecord
     */
    public FirebaseRegistrationTokenRecord() {
        super(FirebaseRegistrationToken.FIREBASE_REGISTRATION_TOKEN);
    }

    /**
     * Create a detached, initialised FirebaseRegistrationTokenRecord
     */
    public FirebaseRegistrationTokenRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, String token, UUID userId, UUID employeeId) {
        super(FirebaseRegistrationToken.FIREBASE_REGISTRATION_TOKEN);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setToken(token);
        setUserId(userId);
        setEmployeeId(employeeId);
    }
}
