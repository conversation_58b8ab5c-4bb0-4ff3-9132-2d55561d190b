/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.FutureSalesViewRecord;
import org.jooq.*;
import org.jooq.Record;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class FutureSalesView extends TableImpl<FutureSalesViewRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.future_sales_view</code>
     */
    public static final FutureSalesView FUTURE_SALES_VIEW = new FutureSalesView();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<FutureSalesViewRecord> getRecordType() {
        return FutureSalesViewRecord.class;
    }

    /**
     * The column <code>public.future_sales_view.id</code>.
     */
    public final TableField<FutureSalesViewRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.future_sales_view.create_time</code>.
     */
    public final TableField<FutureSalesViewRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>public.future_sales_view.owner_id</code>.
     */
    public final TableField<FutureSalesViewRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.future_sales_view.name</code>.
     */
    public final TableField<FutureSalesViewRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR, this, "");

    /**
     * The column <code>public.future_sales_view.quantity</code>.
     */
    public final TableField<FutureSalesViewRecord, Integer> QUANTITY = createField(DSL.name("quantity"), SQLDataType.INTEGER, this, "");

    /**
     * The column <code>public.future_sales_view.exit_price</code>.
     */
    public final TableField<FutureSalesViewRecord, BigDecimal> EXIT_PRICE = createField(DSL.name("exit_price"), SQLDataType.NUMERIC, this, "");

    /**
     * The column <code>public.future_sales_view.product</code>.
     */
    public final TableField<FutureSalesViewRecord, String> PRODUCT = createField(DSL.name("product"), SQLDataType.CLOB, this, "");

    private FutureSalesView(Name alias, Table<FutureSalesViewRecord> aliased) {
        this(alias, aliased, null);
    }

    private FutureSalesView(Name alias, Table<FutureSalesViewRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.view("create view \"future_sales_view\" as  SELECT so.id,\n    so.create_time,\n    so.owner_id,\n        CASE\n            WHEN (so.material_good_id IS NOT NULL) THEN material_good.name\n            WHEN (so.service_id IS NOT NULL) THEN service_template.name\n            ELSE 'ERROR'::character varying\n        END AS name,\n    so.quantity,\n    (((so.price - (so.price * so.discount)) * (so.quantity)::numeric) / (100)::numeric) AS exit_price,\n        CASE\n            WHEN (so.material_good_id IS NOT NULL) THEN 'PRODUCT'::text\n            WHEN (so.service_id IS NOT NULL) THEN 'SERVICE'::text\n            ELSE 'ERROR'::text\n        END AS product\n   FROM ((( SELECT s.id,\n            s.create_time,\n            s.owner_id,\n            ((s.item ->> 'productId'::text))::uuid AS material_good_id,\n            ((s.item ->> 'serviceId'::text))::uuid AS service_id,\n            ((s.item ->> 'quantity'::text))::integer AS quantity,\n            (((s.item -> 'price'::text) ->> 'amount'::text))::numeric AS price,\n            COALESCE(((s.item ->> 'discount'::text))::numeric, (0)::numeric) AS discount\n           FROM ( SELECT sales_order.id,\n                    sales_order.create_time,\n                    sales_order.owner_id,\n                    jsonb_array_elements(sales_order.items) AS item\n                   FROM sales_order\n                  WHERE ((sales_order.status)::text = ANY ((ARRAY['SUBMITTED'::character varying, 'PROCESSING'::character varying, 'PICKING_PACKING'::character varying, 'READY_TO_SHIP'::character varying])::text[]))) s) so\n     LEFT JOIN material_good ON ((material_good.id = so.material_good_id)))\n     LEFT JOIN service_template ON ((service_template.id = so.service_id)));"));
    }

    /**
     * Create an aliased <code>public.future_sales_view</code> table reference
     */
    public FutureSalesView(String alias) {
        this(DSL.name(alias), FUTURE_SALES_VIEW);
    }

    /**
     * Create an aliased <code>public.future_sales_view</code> table reference
     */
    public FutureSalesView(Name alias) {
        this(alias, FUTURE_SALES_VIEW);
    }

    /**
     * Create a <code>public.future_sales_view</code> table reference
     */
    public FutureSalesView() {
        this(DSL.name("future_sales_view"), null);
    }

    public <O extends Record> FutureSalesView(Table<O> child, ForeignKey<O, FutureSalesViewRecord> key) {
        super(child, key, FUTURE_SALES_VIEW);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public FutureSalesView as(String alias) {
        return new FutureSalesView(DSL.name(alias), this);
    }

    @Override
    public FutureSalesView as(Name alias) {
        return new FutureSalesView(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public FutureSalesView rename(String name) {
        return new FutureSalesView(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public FutureSalesView rename(Name name) {
        return new FutureSalesView(name, null);
    }

    // -------------------------------------------------------------------------
    // Row7 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row7<UUID, LocalDateTime, UUID, String, Integer, BigDecimal, String> fieldsRow() {
        return (Row7) super.fieldsRow();
    }
}
