/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.Category;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CategoryRecord extends UpdatableRecordImpl<CategoryRecord> implements Record6<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, JSONB> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.category.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.category.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.category.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.category.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.category.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.category.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.category.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.category.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.category.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.category.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.category.details</code>.
     */
    public void setDetails(JSONB value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.category.details</code>.
     */
    public JSONB getDetails() {
        return (JSONB) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row6<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, JSONB> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    @Override
    public Row6<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, JSONB> valuesRow() {
        return (Row6) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return Category.CATEGORY.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return Category.CATEGORY.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return Category.CATEGORY.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return Category.CATEGORY.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return Category.CATEGORY.OWNER_ID;
    }

    @Override
    public Field<JSONB> field6() {
        return Category.CATEGORY.DETAILS;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public JSONB component6() {
        return getDetails();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public JSONB value6() {
        return getDetails();
    }

    @Override
    public CategoryRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public CategoryRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public CategoryRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public CategoryRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public CategoryRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public CategoryRecord value6(JSONB value) {
        setDetails(value);
        return this;
    }

    @Override
    public CategoryRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, JSONB value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CategoryRecord
     */
    public CategoryRecord() {
        super(Category.CATEGORY);
    }

    /**
     * Create a detached, initialised CategoryRecord
     */
    public CategoryRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, JSONB details) {
        super(Category.CATEGORY);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setDetails(details);
    }
}
