/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.AccountSupplier;

import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record2;
import org.jooq.Row2;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AccountSupplierRecord extends TableRecordImpl<AccountSupplierRecord> implements Record2<UUID, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.account_supplier.supplier_id</code>.
     */
    public void setSupplierId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.account_supplier.supplier_id</code>.
     */
    public UUID getSupplierId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.account_supplier.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.account_supplier.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(1);
    }

    // -------------------------------------------------------------------------
    // Record2 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row2<UUID, UUID> fieldsRow() {
        return (Row2) super.fieldsRow();
    }

    @Override
    public Row2<UUID, UUID> valuesRow() {
        return (Row2) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return AccountSupplier.ACCOUNT_SUPPLIER.SUPPLIER_ID;
    }

    @Override
    public Field<UUID> field2() {
        return AccountSupplier.ACCOUNT_SUPPLIER.OWNER_ID;
    }

    @Override
    public UUID component1() {
        return getSupplierId();
    }

    @Override
    public UUID component2() {
        return getOwnerId();
    }

    @Override
    public UUID value1() {
        return getSupplierId();
    }

    @Override
    public UUID value2() {
        return getOwnerId();
    }

    @Override
    public AccountSupplierRecord value1(UUID value) {
        setSupplierId(value);
        return this;
    }

    @Override
    public AccountSupplierRecord value2(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public AccountSupplierRecord values(UUID value1, UUID value2) {
        value1(value1);
        value2(value2);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached AccountSupplierRecord
     */
    public AccountSupplierRecord() {
        super(AccountSupplier.ACCOUNT_SUPPLIER);
    }

    /**
     * Create a detached, initialised AccountSupplierRecord
     */
    public AccountSupplierRecord(UUID supplierId, UUID ownerId) {
        super(AccountSupplier.ACCOUNT_SUPPLIER);

        setSupplierId(supplierId);
        setOwnerId(ownerId);
    }
}
