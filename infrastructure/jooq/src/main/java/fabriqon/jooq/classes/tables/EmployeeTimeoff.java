/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.EmployeeTimeoffRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row10;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class EmployeeTimeoff extends TableImpl<EmployeeTimeoffRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.employee_timeoff</code>
     */
    public static final EmployeeTimeoff EMPLOYEE_TIMEOFF = new EmployeeTimeoff();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<EmployeeTimeoffRecord> getRecordType() {
        return EmployeeTimeoffRecord.class;
    }

    /**
     * The column <code>public.employee_timeoff.id</code>.
     */
    public final TableField<EmployeeTimeoffRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.employee_timeoff.create_time</code>.
     */
    public final TableField<EmployeeTimeoffRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.employee_timeoff.update_time</code>.
     */
    public final TableField<EmployeeTimeoffRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.employee_timeoff.deleted</code>.
     */
    public final TableField<EmployeeTimeoffRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.employee_timeoff.owner_id</code>.
     */
    public final TableField<EmployeeTimeoffRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.employee_timeoff.user_id</code>.
     */
    public final TableField<EmployeeTimeoffRecord, UUID> USER_ID = createField(DSL.name("user_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.employee_timeoff.start_time</code>.
     */
    public final TableField<EmployeeTimeoffRecord, LocalDateTime> START_TIME = createField(DSL.name("start_time"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>public.employee_timeoff.end_time</code>.
     */
    public final TableField<EmployeeTimeoffRecord, LocalDateTime> END_TIME = createField(DSL.name("end_time"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>public.employee_timeoff.type</code>.
     */
    public final TableField<EmployeeTimeoffRecord, String> TYPE = createField(DSL.name("type"), SQLDataType.VARCHAR(20), this, "");

    /**
     * The column <code>public.employee_timeoff.servicing_order_id</code>.
     */
    public final TableField<EmployeeTimeoffRecord, UUID> SERVICING_ORDER_ID = createField(DSL.name("servicing_order_id"), SQLDataType.UUID, this, "");

    private EmployeeTimeoff(Name alias, Table<EmployeeTimeoffRecord> aliased) {
        this(alias, aliased, null);
    }

    private EmployeeTimeoff(Name alias, Table<EmployeeTimeoffRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.employee_timeoff</code> table reference
     */
    public EmployeeTimeoff(String alias) {
        this(DSL.name(alias), EMPLOYEE_TIMEOFF);
    }

    /**
     * Create an aliased <code>public.employee_timeoff</code> table reference
     */
    public EmployeeTimeoff(Name alias) {
        this(alias, EMPLOYEE_TIMEOFF);
    }

    /**
     * Create a <code>public.employee_timeoff</code> table reference
     */
    public EmployeeTimeoff() {
        this(DSL.name("employee_timeoff"), null);
    }

    public <O extends Record> EmployeeTimeoff(Table<O> child, ForeignKey<O, EmployeeTimeoffRecord> key) {
        super(child, key, EMPLOYEE_TIMEOFF);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public UniqueKey<EmployeeTimeoffRecord> getPrimaryKey() {
        return Keys.EMPLOYEE_TIMEOFF_PKEY;
    }

    @Override
    public List<ForeignKey<EmployeeTimeoffRecord, ?>> getReferences() {
        return Arrays.asList(Keys.EMPLOYEE_TIMEOFF__EMPLOYEE_TIMEOFF_OWNER_ID_FKEY, Keys.EMPLOYEE_TIMEOFF__EMPLOYEE_TIMEOFF_USER_ID_FKEY, Keys.EMPLOYEE_TIMEOFF__EMPLOYEE_TIMEOFF_SERVICING_ORDER_ID_FKEY);
    }

    private transient Account _account;
    private transient Users _users;
    private transient ServicingOrder _servicingOrder;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.EMPLOYEE_TIMEOFF__EMPLOYEE_TIMEOFF_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.users</code> table.
     */
    public Users users() {
        if (_users == null)
            _users = new Users(this, Keys.EMPLOYEE_TIMEOFF__EMPLOYEE_TIMEOFF_USER_ID_FKEY);

        return _users;
    }

    /**
     * Get the implicit join path to the <code>public.servicing_order</code>
     * table.
     */
    public ServicingOrder servicingOrder() {
        if (_servicingOrder == null)
            _servicingOrder = new ServicingOrder(this, Keys.EMPLOYEE_TIMEOFF__EMPLOYEE_TIMEOFF_SERVICING_ORDER_ID_FKEY);

        return _servicingOrder;
    }

    @Override
    public EmployeeTimeoff as(String alias) {
        return new EmployeeTimeoff(DSL.name(alias), this);
    }

    @Override
    public EmployeeTimeoff as(Name alias) {
        return new EmployeeTimeoff(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public EmployeeTimeoff rename(String name) {
        return new EmployeeTimeoff(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public EmployeeTimeoff rename(Name name) {
        return new EmployeeTimeoff(name, null);
    }

    // -------------------------------------------------------------------------
    // Row10 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row10<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, LocalDateTime, LocalDateTime, String, UUID> fieldsRow() {
        return (Row10) super.fieldsRow();
    }
}
