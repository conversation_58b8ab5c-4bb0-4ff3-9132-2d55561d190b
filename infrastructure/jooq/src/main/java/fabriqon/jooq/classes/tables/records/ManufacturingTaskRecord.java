/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.ManufacturingTask;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.Record15;
import org.jooq.Row15;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ManufacturingTaskRecord extends UpdatableRecordImpl<ManufacturingTaskRecord> implements Record15<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String, String, LocalDateTime, LocalDateTime, Integer, JSONB, LocalDateTime, LocalDateTime, Long> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.manufacturing_task.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.manufacturing_task.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.manufacturing_task.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.manufacturing_task.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.manufacturing_task.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.manufacturing_task.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.manufacturing_task.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.manufacturing_task.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.manufacturing_task.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.manufacturing_task.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.manufacturing_task.manufacturing_order_id</code>.
     */
    public void setManufacturingOrderId(UUID value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.manufacturing_task.manufacturing_order_id</code>.
     */
    public UUID getManufacturingOrderId() {
        return (UUID) get(5);
    }

    /**
     * Setter for <code>public.manufacturing_task.status</code>.
     */
    public void setStatus(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.manufacturing_task.status</code>.
     */
    public String getStatus() {
        return (String) get(6);
    }

    /**
     * Setter for <code>public.manufacturing_task.status_reason</code>.
     */
    public void setStatusReason(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.manufacturing_task.status_reason</code>.
     */
    public String getStatusReason() {
        return (String) get(7);
    }

    /**
     * Setter for <code>public.manufacturing_task.estimated_start_time</code>.
     */
    public void setEstimatedStartTime(LocalDateTime value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.manufacturing_task.estimated_start_time</code>.
     */
    public LocalDateTime getEstimatedStartTime() {
        return (LocalDateTime) get(8);
    }

    /**
     * Setter for <code>public.manufacturing_task.estimated_end_time</code>.
     */
    public void setEstimatedEndTime(LocalDateTime value) {
        set(9, value);
    }

    /**
     * Getter for <code>public.manufacturing_task.estimated_end_time</code>.
     */
    public LocalDateTime getEstimatedEndTime() {
        return (LocalDateTime) get(9);
    }

    /**
     * Setter for <code>public.manufacturing_task.duration_in_minutes</code>.
     */
    public void setDurationInMinutes(Integer value) {
        set(10, value);
    }

    /**
     * Getter for <code>public.manufacturing_task.duration_in_minutes</code>.
     */
    public Integer getDurationInMinutes() {
        return (Integer) get(10);
    }

    /**
     * Setter for <code>public.manufacturing_task.details</code>.
     */
    public void setDetails(JSONB value) {
        set(11, value);
    }

    /**
     * Getter for <code>public.manufacturing_task.details</code>.
     */
    public JSONB getDetails() {
        return (JSONB) get(11);
    }

    /**
     * Setter for <code>public.manufacturing_task.start_time</code>.
     */
    public void setStartTime(LocalDateTime value) {
        set(12, value);
    }

    /**
     * Getter for <code>public.manufacturing_task.start_time</code>.
     */
    public LocalDateTime getStartTime() {
        return (LocalDateTime) get(12);
    }

    /**
     * Setter for <code>public.manufacturing_task.end_time</code>.
     */
    public void setEndTime(LocalDateTime value) {
        set(13, value);
    }

    /**
     * Getter for <code>public.manufacturing_task.end_time</code>.
     */
    public LocalDateTime getEndTime() {
        return (LocalDateTime) get(13);
    }

    /**
     * Setter for <code>public.manufacturing_task.ranking</code>.
     */
    public void setRanking(Long value) {
        set(14, value);
    }

    /**
     * Getter for <code>public.manufacturing_task.ranking</code>.
     */
    public Long getRanking() {
        return (Long) get(14);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record15 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row15<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String, String, LocalDateTime, LocalDateTime, Integer, JSONB, LocalDateTime, LocalDateTime, Long> fieldsRow() {
        return (Row15) super.fieldsRow();
    }

    @Override
    public Row15<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String, String, LocalDateTime, LocalDateTime, Integer, JSONB, LocalDateTime, LocalDateTime, Long> valuesRow() {
        return (Row15) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return ManufacturingTask.MANUFACTURING_TASK.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return ManufacturingTask.MANUFACTURING_TASK.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return ManufacturingTask.MANUFACTURING_TASK.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return ManufacturingTask.MANUFACTURING_TASK.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return ManufacturingTask.MANUFACTURING_TASK.OWNER_ID;
    }

    @Override
    public Field<UUID> field6() {
        return ManufacturingTask.MANUFACTURING_TASK.MANUFACTURING_ORDER_ID;
    }

    @Override
    public Field<String> field7() {
        return ManufacturingTask.MANUFACTURING_TASK.STATUS;
    }

    @Override
    public Field<String> field8() {
        return ManufacturingTask.MANUFACTURING_TASK.STATUS_REASON;
    }

    @Override
    public Field<LocalDateTime> field9() {
        return ManufacturingTask.MANUFACTURING_TASK.ESTIMATED_START_TIME;
    }

    @Override
    public Field<LocalDateTime> field10() {
        return ManufacturingTask.MANUFACTURING_TASK.ESTIMATED_END_TIME;
    }

    @Override
    public Field<Integer> field11() {
        return ManufacturingTask.MANUFACTURING_TASK.DURATION_IN_MINUTES;
    }

    @Override
    public Field<JSONB> field12() {
        return ManufacturingTask.MANUFACTURING_TASK.DETAILS;
    }

    @Override
    public Field<LocalDateTime> field13() {
        return ManufacturingTask.MANUFACTURING_TASK.START_TIME;
    }

    @Override
    public Field<LocalDateTime> field14() {
        return ManufacturingTask.MANUFACTURING_TASK.END_TIME;
    }

    @Override
    public Field<Long> field15() {
        return ManufacturingTask.MANUFACTURING_TASK.RANKING;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public UUID component6() {
        return getManufacturingOrderId();
    }

    @Override
    public String component7() {
        return getStatus();
    }

    @Override
    public String component8() {
        return getStatusReason();
    }

    @Override
    public LocalDateTime component9() {
        return getEstimatedStartTime();
    }

    @Override
    public LocalDateTime component10() {
        return getEstimatedEndTime();
    }

    @Override
    public Integer component11() {
        return getDurationInMinutes();
    }

    @Override
    public JSONB component12() {
        return getDetails();
    }

    @Override
    public LocalDateTime component13() {
        return getStartTime();
    }

    @Override
    public LocalDateTime component14() {
        return getEndTime();
    }

    @Override
    public Long component15() {
        return getRanking();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public UUID value6() {
        return getManufacturingOrderId();
    }

    @Override
    public String value7() {
        return getStatus();
    }

    @Override
    public String value8() {
        return getStatusReason();
    }

    @Override
    public LocalDateTime value9() {
        return getEstimatedStartTime();
    }

    @Override
    public LocalDateTime value10() {
        return getEstimatedEndTime();
    }

    @Override
    public Integer value11() {
        return getDurationInMinutes();
    }

    @Override
    public JSONB value12() {
        return getDetails();
    }

    @Override
    public LocalDateTime value13() {
        return getStartTime();
    }

    @Override
    public LocalDateTime value14() {
        return getEndTime();
    }

    @Override
    public Long value15() {
        return getRanking();
    }

    @Override
    public ManufacturingTaskRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public ManufacturingTaskRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public ManufacturingTaskRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public ManufacturingTaskRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public ManufacturingTaskRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public ManufacturingTaskRecord value6(UUID value) {
        setManufacturingOrderId(value);
        return this;
    }

    @Override
    public ManufacturingTaskRecord value7(String value) {
        setStatus(value);
        return this;
    }

    @Override
    public ManufacturingTaskRecord value8(String value) {
        setStatusReason(value);
        return this;
    }

    @Override
    public ManufacturingTaskRecord value9(LocalDateTime value) {
        setEstimatedStartTime(value);
        return this;
    }

    @Override
    public ManufacturingTaskRecord value10(LocalDateTime value) {
        setEstimatedEndTime(value);
        return this;
    }

    @Override
    public ManufacturingTaskRecord value11(Integer value) {
        setDurationInMinutes(value);
        return this;
    }

    @Override
    public ManufacturingTaskRecord value12(JSONB value) {
        setDetails(value);
        return this;
    }

    @Override
    public ManufacturingTaskRecord value13(LocalDateTime value) {
        setStartTime(value);
        return this;
    }

    @Override
    public ManufacturingTaskRecord value14(LocalDateTime value) {
        setEndTime(value);
        return this;
    }

    @Override
    public ManufacturingTaskRecord value15(Long value) {
        setRanking(value);
        return this;
    }

    @Override
    public ManufacturingTaskRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, UUID value6, String value7, String value8, LocalDateTime value9, LocalDateTime value10, Integer value11, JSONB value12, LocalDateTime value13, LocalDateTime value14, Long value15) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ManufacturingTaskRecord
     */
    public ManufacturingTaskRecord() {
        super(ManufacturingTask.MANUFACTURING_TASK);
    }

    /**
     * Create a detached, initialised ManufacturingTaskRecord
     */
    public ManufacturingTaskRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, UUID manufacturingOrderId, String status, String statusReason, LocalDateTime estimatedStartTime, LocalDateTime estimatedEndTime, Integer durationInMinutes, JSONB details, LocalDateTime startTime, LocalDateTime endTime, Long ranking) {
        super(ManufacturingTask.MANUFACTURING_TASK);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setManufacturingOrderId(manufacturingOrderId);
        setStatus(status);
        setStatusReason(statusReason);
        setEstimatedStartTime(estimatedStartTime);
        setEstimatedEndTime(estimatedEndTime);
        setDurationInMinutes(durationInMinutes);
        setDetails(details);
        setStartTime(startTime);
        setEndTime(endTime);
        setRanking(ranking);
    }
}
