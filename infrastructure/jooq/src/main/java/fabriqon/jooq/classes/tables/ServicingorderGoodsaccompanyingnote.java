/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.ServicingorderGoodsaccompanyingnoteRecord;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row2;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ServicingorderGoodsaccompanyingnote extends TableImpl<ServicingorderGoodsaccompanyingnoteRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>public.servicingorder_goodsaccompanyingnote</code>
     */
    public static final ServicingorderGoodsaccompanyingnote SERVICINGORDER_GOODSACCOMPANYINGNOTE = new ServicingorderGoodsaccompanyingnote();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ServicingorderGoodsaccompanyingnoteRecord> getRecordType() {
        return ServicingorderGoodsaccompanyingnoteRecord.class;
    }

    /**
     * The column
     * <code>public.servicingorder_goodsaccompanyingnote.servicing_order_id</code>.
     */
    public final TableField<ServicingorderGoodsaccompanyingnoteRecord, UUID> SERVICING_ORDER_ID = createField(DSL.name("servicing_order_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column
     * <code>public.servicingorder_goodsaccompanyingnote.goods_accompanying_note_id</code>.
     */
    public final TableField<ServicingorderGoodsaccompanyingnoteRecord, UUID> GOODS_ACCOMPANYING_NOTE_ID = createField(DSL.name("goods_accompanying_note_id"), SQLDataType.UUID.nullable(false), this, "");

    private ServicingorderGoodsaccompanyingnote(Name alias, Table<ServicingorderGoodsaccompanyingnoteRecord> aliased) {
        this(alias, aliased, null);
    }

    private ServicingorderGoodsaccompanyingnote(Name alias, Table<ServicingorderGoodsaccompanyingnoteRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased
     * <code>public.servicingorder_goodsaccompanyingnote</code> table reference
     */
    public ServicingorderGoodsaccompanyingnote(String alias) {
        this(DSL.name(alias), SERVICINGORDER_GOODSACCOMPANYINGNOTE);
    }

    /**
     * Create an aliased
     * <code>public.servicingorder_goodsaccompanyingnote</code> table reference
     */
    public ServicingorderGoodsaccompanyingnote(Name alias) {
        this(alias, SERVICINGORDER_GOODSACCOMPANYINGNOTE);
    }

    /**
     * Create a <code>public.servicingorder_goodsaccompanyingnote</code> table
     * reference
     */
    public ServicingorderGoodsaccompanyingnote() {
        this(DSL.name("servicingorder_goodsaccompanyingnote"), null);
    }

    public <O extends Record> ServicingorderGoodsaccompanyingnote(Table<O> child, ForeignKey<O, ServicingorderGoodsaccompanyingnoteRecord> key) {
        super(child, key, SERVICINGORDER_GOODSACCOMPANYINGNOTE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<UniqueKey<ServicingorderGoodsaccompanyingnoteRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.SO_GOODSNOTE_UNIQUE_CONSTRAINTS);
    }

    @Override
    public List<ForeignKey<ServicingorderGoodsaccompanyingnoteRecord, ?>> getReferences() {
        return Arrays.asList(Keys.SERVICINGORDER_GOODSACCOMPANYINGNOTE__SERVICINGORDER_GOODSACCOMPANYINGNOTE_SERVICING_ORDER_ID_FKEY, Keys.SERVICINGORDER_GOODSACCOMPANYINGNOTE__SERVICINGORDER_GOODSACCOMPANYIN_GOODS_ACCOMPANYING_NOTE_ID_FKEY);
    }

    private transient ServicingOrder _servicingOrder;
    private transient GoodsAccompanyingNote _goodsAccompanyingNote;

    /**
     * Get the implicit join path to the <code>public.servicing_order</code>
     * table.
     */
    public ServicingOrder servicingOrder() {
        if (_servicingOrder == null)
            _servicingOrder = new ServicingOrder(this, Keys.SERVICINGORDER_GOODSACCOMPANYINGNOTE__SERVICINGORDER_GOODSACCOMPANYINGNOTE_SERVICING_ORDER_ID_FKEY);

        return _servicingOrder;
    }

    /**
     * Get the implicit join path to the
     * <code>public.goods_accompanying_note</code> table.
     */
    public GoodsAccompanyingNote goodsAccompanyingNote() {
        if (_goodsAccompanyingNote == null)
            _goodsAccompanyingNote = new GoodsAccompanyingNote(this, Keys.SERVICINGORDER_GOODSACCOMPANYINGNOTE__SERVICINGORDER_GOODSACCOMPANYIN_GOODS_ACCOMPANYING_NOTE_ID_FKEY);

        return _goodsAccompanyingNote;
    }

    @Override
    public ServicingorderGoodsaccompanyingnote as(String alias) {
        return new ServicingorderGoodsaccompanyingnote(DSL.name(alias), this);
    }

    @Override
    public ServicingorderGoodsaccompanyingnote as(Name alias) {
        return new ServicingorderGoodsaccompanyingnote(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ServicingorderGoodsaccompanyingnote rename(String name) {
        return new ServicingorderGoodsaccompanyingnote(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ServicingorderGoodsaccompanyingnote rename(Name name) {
        return new ServicingorderGoodsaccompanyingnote(name, null);
    }

    // -------------------------------------------------------------------------
    // Row2 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row2<UUID, UUID> fieldsRow() {
        return (Row2) super.fieldsRow();
    }
}
