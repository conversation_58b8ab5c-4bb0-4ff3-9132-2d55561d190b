/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.Account;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AccountRecord extends UpdatableRecordImpl<AccountRecord> implements Record8<UUID, LocalDateTime, LocalDateTime, Boolean, String, JSONB, JSONB, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.account.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.account.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.account.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.account.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.account.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.account.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.account.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.account.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.account.name</code>.
     */
    public void setName(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.account.name</code>.
     */
    public String getName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>public.account.information</code>.
     */
    public void setInformation(JSONB value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.account.information</code>.
     */
    public JSONB getInformation() {
        return (JSONB) get(5);
    }

    /**
     * Setter for <code>public.account.settings</code>.
     */
    public void setSettings(JSONB value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.account.settings</code>.
     */
    public JSONB getSettings() {
        return (JSONB) get(6);
    }

    /**
     * Setter for <code>public.account.logo</code>.
     */
    public void setLogo(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.account.logo</code>.
     */
    public String getLogo() {
        return (String) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record8 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row8<UUID, LocalDateTime, LocalDateTime, Boolean, String, JSONB, JSONB, String> fieldsRow() {
        return (Row8) super.fieldsRow();
    }

    @Override
    public Row8<UUID, LocalDateTime, LocalDateTime, Boolean, String, JSONB, JSONB, String> valuesRow() {
        return (Row8) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return Account.ACCOUNT.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return Account.ACCOUNT.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return Account.ACCOUNT.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return Account.ACCOUNT.DELETED;
    }

    @Override
    public Field<String> field5() {
        return Account.ACCOUNT.NAME;
    }

    @Override
    public Field<JSONB> field6() {
        return Account.ACCOUNT.INFORMATION;
    }

    @Override
    public Field<JSONB> field7() {
        return Account.ACCOUNT.SETTINGS;
    }

    @Override
    public Field<String> field8() {
        return Account.ACCOUNT.LOGO;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public String component5() {
        return getName();
    }

    @Override
    public JSONB component6() {
        return getInformation();
    }

    @Override
    public JSONB component7() {
        return getSettings();
    }

    @Override
    public String component8() {
        return getLogo();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public String value5() {
        return getName();
    }

    @Override
    public JSONB value6() {
        return getInformation();
    }

    @Override
    public JSONB value7() {
        return getSettings();
    }

    @Override
    public String value8() {
        return getLogo();
    }

    @Override
    public AccountRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public AccountRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public AccountRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public AccountRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public AccountRecord value5(String value) {
        setName(value);
        return this;
    }

    @Override
    public AccountRecord value6(JSONB value) {
        setInformation(value);
        return this;
    }

    @Override
    public AccountRecord value7(JSONB value) {
        setSettings(value);
        return this;
    }

    @Override
    public AccountRecord value8(String value) {
        setLogo(value);
        return this;
    }

    @Override
    public AccountRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, String value5, JSONB value6, JSONB value7, String value8) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached AccountRecord
     */
    public AccountRecord() {
        super(Account.ACCOUNT);
    }

    /**
     * Create a detached, initialised AccountRecord
     */
    public AccountRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, String name, JSONB information, JSONB settings, String logo) {
        super(Account.ACCOUNT);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setName(name);
        setInformation(information);
        setSettings(settings);
        setLogo(logo);
    }
}
