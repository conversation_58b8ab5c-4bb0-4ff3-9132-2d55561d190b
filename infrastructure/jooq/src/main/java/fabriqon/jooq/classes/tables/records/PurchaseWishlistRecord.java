/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.PurchaseWishlist;
import org.jooq.*;
import org.jooq.impl.UpdatableRecordImpl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Currency;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PurchaseWishlistRecord extends UpdatableRecordImpl<PurchaseWishlistRecord> implements Record11<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, BigDecimal, Long, Currency, UUID, JSONB> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.purchase_wishlist.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.purchase_wishlist.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.purchase_wishlist.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.purchase_wishlist.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.purchase_wishlist.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.purchase_wishlist.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.purchase_wishlist.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.purchase_wishlist.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.purchase_wishlist.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.purchase_wishlist.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.purchase_wishlist.material_good_id</code>.
     */
    public void setMaterialGoodId(UUID value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.purchase_wishlist.material_good_id</code>.
     */
    public UUID getMaterialGoodId() {
        return (UUID) get(5);
    }

    /**
     * Setter for <code>public.purchase_wishlist.quantity</code>.
     */
    public void setQuantity(BigDecimal value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.purchase_wishlist.quantity</code>.
     */
    public BigDecimal getQuantity() {
        return (BigDecimal) get(6);
    }

    /**
     * Setter for <code>public.purchase_wishlist.expected_price_amount</code>.
     */
    public void setExpectedPriceAmount(Long value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.purchase_wishlist.expected_price_amount</code>.
     */
    public Long getExpectedPriceAmount() {
        return (Long) get(7);
    }

    /**
     * Setter for <code>public.purchase_wishlist.expected_price_currency</code>.
     */
    public void setExpectedPriceCurrency(Currency value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.purchase_wishlist.expected_price_currency</code>.
     */
    public Currency getExpectedPriceCurrency() {
        return (Currency) get(8);
    }

    /**
     * Setter for <code>public.purchase_wishlist.supplier_id</code>.
     */
    public void setSupplierId(UUID value) {
        set(9, value);
    }

    /**
     * Getter for <code>public.purchase_wishlist.supplier_id</code>.
     */
    public UUID getSupplierId() {
        return (UUID) get(9);
    }

    /**
     * Setter for <code>public.purchase_wishlist.sources</code>.
     */
    public void setSources(JSONB value) {
        set(10, value);
    }

    /**
     * Getter for <code>public.purchase_wishlist.sources</code>.
     */
    public JSONB getSources() {
        return (JSONB) get(10);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record11 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row11<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, BigDecimal, Long, Currency, UUID, JSONB> fieldsRow() {
        return (Row11) super.fieldsRow();
    }

    @Override
    public Row11<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, BigDecimal, Long, Currency, UUID, JSONB> valuesRow() {
        return (Row11) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return PurchaseWishlist.PURCHASE_WISHLIST.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return PurchaseWishlist.PURCHASE_WISHLIST.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return PurchaseWishlist.PURCHASE_WISHLIST.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return PurchaseWishlist.PURCHASE_WISHLIST.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return PurchaseWishlist.PURCHASE_WISHLIST.OWNER_ID;
    }

    @Override
    public Field<UUID> field6() {
        return PurchaseWishlist.PURCHASE_WISHLIST.MATERIAL_GOOD_ID;
    }

    @Override
    public Field<BigDecimal> field7() {
        return PurchaseWishlist.PURCHASE_WISHLIST.QUANTITY;
    }

    @Override
    public Field<Long> field8() {
        return PurchaseWishlist.PURCHASE_WISHLIST.EXPECTED_PRICE_AMOUNT;
    }

    @Override
    public Field<Currency> field9() {
        return PurchaseWishlist.PURCHASE_WISHLIST.EXPECTED_PRICE_CURRENCY;
    }

    @Override
    public Field<UUID> field10() {
        return PurchaseWishlist.PURCHASE_WISHLIST.SUPPLIER_ID;
    }

    @Override
    public Field<JSONB> field11() {
        return PurchaseWishlist.PURCHASE_WISHLIST.SOURCES;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public UUID component6() {
        return getMaterialGoodId();
    }

    @Override
    public BigDecimal component7() {
        return getQuantity();
    }

    @Override
    public Long component8() {
        return getExpectedPriceAmount();
    }

    @Override
    public Currency component9() {
        return getExpectedPriceCurrency();
    }

    @Override
    public UUID component10() {
        return getSupplierId();
    }

    @Override
    public JSONB component11() {
        return getSources();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public UUID value6() {
        return getMaterialGoodId();
    }

    @Override
    public BigDecimal value7() {
        return getQuantity();
    }

    @Override
    public Long value8() {
        return getExpectedPriceAmount();
    }

    @Override
    public Currency value9() {
        return getExpectedPriceCurrency();
    }

    @Override
    public UUID value10() {
        return getSupplierId();
    }

    @Override
    public JSONB value11() {
        return getSources();
    }

    @Override
    public PurchaseWishlistRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public PurchaseWishlistRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public PurchaseWishlistRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public PurchaseWishlistRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public PurchaseWishlistRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public PurchaseWishlistRecord value6(UUID value) {
        setMaterialGoodId(value);
        return this;
    }

    @Override
    public PurchaseWishlistRecord value7(BigDecimal value) {
        setQuantity(value);
        return this;
    }

    @Override
    public PurchaseWishlistRecord value8(Long value) {
        setExpectedPriceAmount(value);
        return this;
    }

    @Override
    public PurchaseWishlistRecord value9(Currency value) {
        setExpectedPriceCurrency(value);
        return this;
    }

    @Override
    public PurchaseWishlistRecord value10(UUID value) {
        setSupplierId(value);
        return this;
    }

    @Override
    public PurchaseWishlistRecord value11(JSONB value) {
        setSources(value);
        return this;
    }

    @Override
    public PurchaseWishlistRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, UUID value6, BigDecimal value7, Long value8, Currency value9, UUID value10, JSONB value11) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached PurchaseWishlistRecord
     */
    public PurchaseWishlistRecord() {
        super(PurchaseWishlist.PURCHASE_WISHLIST);
    }

    /**
     * Create a detached, initialised PurchaseWishlistRecord
     */
    public PurchaseWishlistRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, UUID materialGoodId, BigDecimal quantity, Long expectedPriceAmount, Currency expectedPriceCurrency, UUID supplierId, JSONB sources) {
        super(PurchaseWishlist.PURCHASE_WISHLIST);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setMaterialGoodId(materialGoodId);
        setQuantity(quantity);
        setExpectedPriceAmount(expectedPriceAmount);
        setExpectedPriceCurrency(expectedPriceCurrency);
        setSupplierId(supplierId);
        setSources(sources);
    }
}
