/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.SalesorderFileRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row5;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SalesorderFile extends TableImpl<SalesorderFileRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.salesorder_file</code>
     */
    public static final SalesorderFile SALESORDER_FILE = new SalesorderFile();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SalesorderFileRecord> getRecordType() {
        return SalesorderFileRecord.class;
    }

    /**
     * The column <code>public.salesorder_file.create_time</code>.
     */
    public final TableField<SalesorderFileRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.salesorder_file.owner_id</code>.
     */
    public final TableField<SalesorderFileRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.salesorder_file.sales_order_id</code>.
     */
    public final TableField<SalesorderFileRecord, UUID> SALES_ORDER_ID = createField(DSL.name("sales_order_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.salesorder_file.file_id</code>.
     */
    public final TableField<SalesorderFileRecord, UUID> FILE_ID = createField(DSL.name("file_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.salesorder_file.type</code>.
     */
    public final TableField<SalesorderFileRecord, String> TYPE = createField(DSL.name("type"), SQLDataType.VARCHAR(20), this, "");

    private SalesorderFile(Name alias, Table<SalesorderFileRecord> aliased) {
        this(alias, aliased, null);
    }

    private SalesorderFile(Name alias, Table<SalesorderFileRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.salesorder_file</code> table reference
     */
    public SalesorderFile(String alias) {
        this(DSL.name(alias), SALESORDER_FILE);
    }

    /**
     * Create an aliased <code>public.salesorder_file</code> table reference
     */
    public SalesorderFile(Name alias) {
        this(alias, SALESORDER_FILE);
    }

    /**
     * Create a <code>public.salesorder_file</code> table reference
     */
    public SalesorderFile() {
        this(DSL.name("salesorder_file"), null);
    }

    public <O extends Record> SalesorderFile(Table<O> child, ForeignKey<O, SalesorderFileRecord> key) {
        super(child, key, SALESORDER_FILE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<ForeignKey<SalesorderFileRecord, ?>> getReferences() {
        return Arrays.asList(Keys.SALESORDER_FILE__SALESORDER_FILE_OWNER_ID_FKEY, Keys.SALESORDER_FILE__SALESORDER_FILE_SALES_ORDER_ID_FKEY, Keys.SALESORDER_FILE__SALESORDER_FILE_FILE_ID_FKEY);
    }

    private transient Account _account;
    private transient SalesOrder _salesOrder;
    private transient File _file;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.SALESORDER_FILE__SALESORDER_FILE_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.sales_order</code> table.
     */
    public SalesOrder salesOrder() {
        if (_salesOrder == null)
            _salesOrder = new SalesOrder(this, Keys.SALESORDER_FILE__SALESORDER_FILE_SALES_ORDER_ID_FKEY);

        return _salesOrder;
    }

    /**
     * Get the implicit join path to the <code>public.file</code> table.
     */
    public File file() {
        if (_file == null)
            _file = new File(this, Keys.SALESORDER_FILE__SALESORDER_FILE_FILE_ID_FKEY);

        return _file;
    }

    @Override
    public SalesorderFile as(String alias) {
        return new SalesorderFile(DSL.name(alias), this);
    }

    @Override
    public SalesorderFile as(Name alias) {
        return new SalesorderFile(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SalesorderFile rename(String name) {
        return new SalesorderFile(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public SalesorderFile rename(Name name) {
        return new SalesorderFile(name, null);
    }

    // -------------------------------------------------------------------------
    // Row5 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row5<LocalDateTime, UUID, UUID, UUID, String> fieldsRow() {
        return (Row5) super.fieldsRow();
    }
}
