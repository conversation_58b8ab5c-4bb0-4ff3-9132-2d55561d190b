/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.Company;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CompanyRecord extends UpdatableRecordImpl<CompanyRecord> implements Record9<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, JSONB, Object, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.company.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.company.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.company.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.company.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.company.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.company.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.company.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.company.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.company.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.company.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.company.company_name</code>.
     */
    public void setCompanyName(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.company.company_name</code>.
     */
    public String getCompanyName() {
        return (String) get(5);
    }

    /**
     * Setter for <code>public.company.details</code>.
     */
    public void setDetails(JSONB value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.company.details</code>.
     */
    public JSONB getDetails() {
        return (JSONB) get(6);
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public void setTextSearch(Object value) {
        set(7, value);
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public Object getTextSearch() {
        return get(7);
    }

    /**
     * Setter for <code>public.company.company_type</code>.
     */
    public void setCompanyType(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.company.company_type</code>.
     */
    public String getCompanyType() {
        return (String) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record9 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row9<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, JSONB, Object, String> fieldsRow() {
        return (Row9) super.fieldsRow();
    }

    @Override
    public Row9<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, JSONB, Object, String> valuesRow() {
        return (Row9) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return Company.COMPANY.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return Company.COMPANY.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return Company.COMPANY.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return Company.COMPANY.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return Company.COMPANY.OWNER_ID;
    }

    @Override
    public Field<String> field6() {
        return Company.COMPANY.COMPANY_NAME;
    }

    @Override
    public Field<JSONB> field7() {
        return Company.COMPANY.DETAILS;
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public Field<Object> field8() {
        return Company.COMPANY.TEXT_SEARCH;
    }

    @Override
    public Field<String> field9() {
        return Company.COMPANY.COMPANY_TYPE;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public String component6() {
        return getCompanyName();
    }

    @Override
    public JSONB component7() {
        return getDetails();
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public Object component8() {
        return getTextSearch();
    }

    @Override
    public String component9() {
        return getCompanyType();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public String value6() {
        return getCompanyName();
    }

    @Override
    public JSONB value7() {
        return getDetails();
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public Object value8() {
        return getTextSearch();
    }

    @Override
    public String value9() {
        return getCompanyType();
    }

    @Override
    public CompanyRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public CompanyRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public CompanyRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public CompanyRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public CompanyRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public CompanyRecord value6(String value) {
        setCompanyName(value);
        return this;
    }

    @Override
    public CompanyRecord value7(JSONB value) {
        setDetails(value);
        return this;
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public CompanyRecord value8(Object value) {
        setTextSearch(value);
        return this;
    }

    @Override
    public CompanyRecord value9(String value) {
        setCompanyType(value);
        return this;
    }

    @Override
    public CompanyRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, String value6, JSONB value7, Object value8, String value9) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CompanyRecord
     */
    public CompanyRecord() {
        super(Company.COMPANY);
    }

    /**
     * Create a detached, initialised CompanyRecord
     */
    public CompanyRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, String companyName, JSONB details, Object textSearch, String companyType) {
        super(Company.COMPANY);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setCompanyName(companyName);
        setDetails(details);
        setTextSearch(textSearch);
        setCompanyType(companyType);
    }
}
