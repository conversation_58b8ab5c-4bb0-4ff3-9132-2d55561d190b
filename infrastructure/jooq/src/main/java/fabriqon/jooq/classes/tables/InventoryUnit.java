/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.InventoryUnitRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row6;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class InventoryUnit extends TableImpl<InventoryUnitRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.inventory_unit</code>
     */
    public static final InventoryUnit INVENTORY_UNIT = new InventoryUnit();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<InventoryUnitRecord> getRecordType() {
        return InventoryUnitRecord.class;
    }

    /**
     * The column <code>public.inventory_unit.id</code>.
     */
    public final TableField<InventoryUnitRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.inventory_unit.create_time</code>.
     */
    public final TableField<InventoryUnitRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.inventory_unit.update_time</code>.
     */
    public final TableField<InventoryUnitRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.inventory_unit.deleted</code>.
     */
    public final TableField<InventoryUnitRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.inventory_unit.owner_id</code>.
     */
    public final TableField<InventoryUnitRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.inventory_unit.name</code>.
     */
    public final TableField<InventoryUnitRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(200), this, "");

    private InventoryUnit(Name alias, Table<InventoryUnitRecord> aliased) {
        this(alias, aliased, null);
    }

    private InventoryUnit(Name alias, Table<InventoryUnitRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.inventory_unit</code> table reference
     */
    public InventoryUnit(String alias) {
        this(DSL.name(alias), INVENTORY_UNIT);
    }

    /**
     * Create an aliased <code>public.inventory_unit</code> table reference
     */
    public InventoryUnit(Name alias) {
        this(alias, INVENTORY_UNIT);
    }

    /**
     * Create a <code>public.inventory_unit</code> table reference
     */
    public InventoryUnit() {
        this(DSL.name("inventory_unit"), null);
    }

    public <O extends Record> InventoryUnit(Table<O> child, ForeignKey<O, InventoryUnitRecord> key) {
        super(child, key, INVENTORY_UNIT);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public UniqueKey<InventoryUnitRecord> getPrimaryKey() {
        return Keys.INVENTORY_TYPE_PKEY;
    }

    @Override
    public List<ForeignKey<InventoryUnitRecord, ?>> getReferences() {
        return Arrays.asList(Keys.INVENTORY_UNIT__INVENTORY_TYPE_OWNER_ID_FKEY);
    }

    private transient Account _account;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.INVENTORY_UNIT__INVENTORY_TYPE_OWNER_ID_FKEY);

        return _account;
    }

    @Override
    public InventoryUnit as(String alias) {
        return new InventoryUnit(DSL.name(alias), this);
    }

    @Override
    public InventoryUnit as(Name alias) {
        return new InventoryUnit(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public InventoryUnit rename(String name) {
        return new InventoryUnit(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public InventoryUnit rename(Name name) {
        return new InventoryUnit(name, null);
    }

    // -------------------------------------------------------------------------
    // Row6 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row6<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String> fieldsRow() {
        return (Row6) super.fieldsRow();
    }
}
