/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.AccountSupplierRecord;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row2;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AccountSupplier extends TableImpl<AccountSupplierRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.account_supplier</code>
     */
    public static final AccountSupplier ACCOUNT_SUPPLIER = new AccountSupplier();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AccountSupplierRecord> getRecordType() {
        return AccountSupplierRecord.class;
    }

    /**
     * The column <code>public.account_supplier.supplier_id</code>.
     */
    public final TableField<AccountSupplierRecord, UUID> SUPPLIER_ID = createField(DSL.name("supplier_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.account_supplier.owner_id</code>.
     */
    public final TableField<AccountSupplierRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    private AccountSupplier(Name alias, Table<AccountSupplierRecord> aliased) {
        this(alias, aliased, null);
    }

    private AccountSupplier(Name alias, Table<AccountSupplierRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.account_supplier</code> table reference
     */
    public AccountSupplier(String alias) {
        this(DSL.name(alias), ACCOUNT_SUPPLIER);
    }

    /**
     * Create an aliased <code>public.account_supplier</code> table reference
     */
    public AccountSupplier(Name alias) {
        this(alias, ACCOUNT_SUPPLIER);
    }

    /**
     * Create a <code>public.account_supplier</code> table reference
     */
    public AccountSupplier() {
        this(DSL.name("account_supplier"), null);
    }

    public <O extends Record> AccountSupplier(Table<O> child, ForeignKey<O, AccountSupplierRecord> key) {
        super(child, key, ACCOUNT_SUPPLIER);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<ForeignKey<AccountSupplierRecord, ?>> getReferences() {
        return Arrays.asList(Keys.ACCOUNT_SUPPLIER__ACCOUNT_SUPPLIER_SUPPLIER_ID_FKEY, Keys.ACCOUNT_SUPPLIER__ACCOUNT_SUPPLIER_OWNER_ID_FKEY);
    }

    private transient Company _company;
    private transient Account _account;

    /**
     * Get the implicit join path to the <code>public.company</code> table.
     */
    public Company company() {
        if (_company == null)
            _company = new Company(this, Keys.ACCOUNT_SUPPLIER__ACCOUNT_SUPPLIER_SUPPLIER_ID_FKEY);

        return _company;
    }

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.ACCOUNT_SUPPLIER__ACCOUNT_SUPPLIER_OWNER_ID_FKEY);

        return _account;
    }

    @Override
    public AccountSupplier as(String alias) {
        return new AccountSupplier(DSL.name(alias), this);
    }

    @Override
    public AccountSupplier as(Name alias) {
        return new AccountSupplier(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public AccountSupplier rename(String name) {
        return new AccountSupplier(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public AccountSupplier rename(Name name) {
        return new AccountSupplier(name, null);
    }

    // -------------------------------------------------------------------------
    // Row2 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row2<UUID, UUID> fieldsRow() {
        return (Row2) super.fieldsRow();
    }
}
