/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.ResourceUtilizationRateViewRecord;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row7;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResourceUtilizationRateView extends TableImpl<ResourceUtilizationRateViewRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>public.resource_utilization_rate_view</code>
     */
    public static final ResourceUtilizationRateView RESOURCE_UTILIZATION_RATE_VIEW = new ResourceUtilizationRateView();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ResourceUtilizationRateViewRecord> getRecordType() {
        return ResourceUtilizationRateViewRecord.class;
    }

    /**
     * The column
     * <code>public.resource_utilization_rate_view.employee_id</code>.
     */
    public final TableField<ResourceUtilizationRateViewRecord, UUID> EMPLOYEE_ID = createField(DSL.name("employee_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.resource_utilization_rate_view.owner_id</code>.
     */
    public final TableField<ResourceUtilizationRateViewRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.resource_utilization_rate_view.name</code>.
     */
    public final TableField<ResourceUtilizationRateViewRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(200), this, "");

    /**
     * The column
     * <code>public.resource_utilization_rate_view.day_of_work</code>.
     */
    public final TableField<ResourceUtilizationRateViewRecord, LocalDate> DAY_OF_WORK = createField(DSL.name("day_of_work"), SQLDataType.LOCALDATE, this, "");

    /**
     * The column
     * <code>public.resource_utilization_rate_view.potential_output_seconds</code>.
     */
    public final TableField<ResourceUtilizationRateViewRecord, BigDecimal> POTENTIAL_OUTPUT_SECONDS = createField(DSL.name("potential_output_seconds"), SQLDataType.NUMERIC, this, "");

    /**
     * The column
     * <code>public.resource_utilization_rate_view.actual_output_seconds</code>.
     */
    public final TableField<ResourceUtilizationRateViewRecord, BigDecimal> ACTUAL_OUTPUT_SECONDS = createField(DSL.name("actual_output_seconds"), SQLDataType.NUMERIC, this, "");

    /**
     * The column
     * <code>public.resource_utilization_rate_view.resource_utilization_rate</code>.
     */
    public final TableField<ResourceUtilizationRateViewRecord, BigDecimal> RESOURCE_UTILIZATION_RATE = createField(DSL.name("resource_utilization_rate"), SQLDataType.NUMERIC, this, "");

    private ResourceUtilizationRateView(Name alias, Table<ResourceUtilizationRateViewRecord> aliased) {
        this(alias, aliased, null);
    }

    private ResourceUtilizationRateView(Name alias, Table<ResourceUtilizationRateViewRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.view("create view \"resource_utilization_rate_view\" as  WITH employee_working_hours AS (\n         SELECT e.id AS employee_id,\n            e.owner_id,\n            e.name,\n            (((o.settings -> 'manufacturing'::text) ->> 'workDayStartTime'::text))::time without time zone AS work_start,\n            (((o.settings -> 'manufacturing'::text) ->> 'workDayEndTime'::text))::time without time zone AS work_end,\n            ((o.settings -> 'manufacturing'::text) -> 'workingDays'::text) AS working_days,\n            ((o.settings -> 'general'::text) ->> 'defaultTimeZone'::text) AS time_zone,\n            EXTRACT(epoch FROM ((((o.settings -> 'manufacturing'::text) ->> 'workDayEndTime'::text))::time without time zone - (((o.settings -> 'manufacturing'::text) ->> 'workDayStartTime'::text))::time without time zone)) AS potential_working_seconds_per_day\n           FROM (users e\n             JOIN account o ON ((e.owner_id = o.id)))\n          GROUP BY e.id, e.owner_id, e.name, o.settings\n        ), employee_timeoff_excluded AS (\n         SELECT et.user_id,\n            et.owner_id,\n            (et.start_time)::date AS day_of_timeoff,\n            sum(EXTRACT(epoch FROM (et.end_time - et.start_time))) AS total_time_off_seconds\n           FROM employee_timeoff et\n          GROUP BY et.user_id, et.owner_id, ((et.start_time)::date)\n        ), task_work_per_day AS (\n         SELECT me.user_id,\n            me.owner_id,\n            gs.day_of_work,\n            sum(\n                CASE\n                    WHEN (gs.day_of_work = (mt.start_time)::date) THEN GREATEST(EXTRACT(epoch FROM (LEAST((((o.settings -> 'manufacturing'::text) ->> 'workDayEndTime'::text))::time without time zone,\n                    CASE\n                        WHEN ((mt.end_time)::date = (mt.start_time)::date) THEN (mt.end_time)::time without time zone\n                        ELSE (((o.settings -> 'manufacturing'::text) ->> 'workDayEndTime'::text))::time without time zone\n                    END) - GREATEST((mt.start_time)::time without time zone, (((o.settings -> 'manufacturing'::text) ->> 'workDayStartTime'::text))::time without time zone))), (0)::numeric)\n                    WHEN (gs.day_of_work = (mt.end_time)::date) THEN GREATEST(EXTRACT(epoch FROM (LEAST((mt.end_time)::time without time zone, (((o.settings -> 'manufacturing'::text) ->> 'workDayEndTime'::text))::time without time zone) - (((o.settings -> 'manufacturing'::text) ->> 'workDayStartTime'::text))::time without time zone)), (0)::numeric)\n                    ELSE EXTRACT(epoch FROM ((((o.settings -> 'manufacturing'::text) ->> 'workDayEndTime'::text))::time without time zone - (((o.settings -> 'manufacturing'::text) ->> 'workDayStartTime'::text))::time without time zone))\n                END) AS actual_working_seconds\n           FROM (((manufacturingtask_employee me\n             JOIN manufacturing_task mt ON ((me.manufacturing_task_id = mt.id)))\n             JOIN account o ON ((me.owner_id = o.id)))\n             CROSS JOIN LATERAL generate_series(((mt.start_time)::date)::timestamp with time zone, ((mt.end_time)::date)::timestamp with time zone, '1 day'::interval) gs(day_of_work))\n          WHERE ((mt.status)::text = 'DONE'::text)\n          GROUP BY me.user_id, me.owner_id, gs.day_of_work, o.settings\n        ), calendar_days AS (\n         SELECT (d.d)::date AS day_of_work\n           FROM generate_series(((( SELECT min(manufacturing_task.start_time) AS min\n                   FROM manufacturing_task\n                  WHERE ((manufacturing_task.status)::text = 'DONE'::text)))::date)::timestamp with time zone, ((( SELECT max(manufacturing_task.end_time) AS max\n                   FROM manufacturing_task\n                  WHERE ((manufacturing_task.status)::text = 'DONE'::text)))::date)::timestamp with time zone, '1 day'::interval) d(d)\n        )\n SELECT ewh.employee_id,\n    ewh.owner_id,\n    ewh.name,\n    cd.day_of_work,\n    GREATEST((COALESCE(ewh.potential_working_seconds_per_day, (0)::numeric) - COALESCE(eto.total_time_off_seconds, (0)::numeric)), (0)::numeric) AS potential_output_seconds,\n    LEAST(COALESCE(twd.actual_working_seconds, (0)::numeric), GREATEST((COALESCE(ewh.potential_working_seconds_per_day, (0)::numeric) - COALESCE(eto.total_time_off_seconds, (0)::numeric)), (0)::numeric)) AS actual_output_seconds,\n        CASE\n            WHEN (GREATEST((COALESCE(ewh.potential_working_seconds_per_day, (0)::numeric) - COALESCE(eto.total_time_off_seconds, (0)::numeric)), (0)::numeric) > (0)::numeric) THEN round((LEAST(COALESCE(twd.actual_working_seconds, (0)::numeric), GREATEST((COALESCE(ewh.potential_working_seconds_per_day, (0)::numeric) - COALESCE(eto.total_time_off_seconds, (0)::numeric)), (0)::numeric)) / GREATEST((COALESCE(ewh.potential_working_seconds_per_day, (0)::numeric) - COALESCE(eto.total_time_off_seconds, (0)::numeric)), (1)::numeric)), 4)\n            ELSE (0)::numeric\n        END AS resource_utilization_rate\n   FROM (((calendar_days cd\n     JOIN employee_working_hours ewh ON ((\n        CASE\n            WHEN (EXTRACT(dow FROM cd.day_of_work) = (0)::numeric) THEN (7)::numeric\n            ELSE EXTRACT(dow FROM cd.day_of_work)\n        END IN ( SELECT (jsonb_array_elements_text(ewh.working_days))::integer AS jsonb_array_elements_text))))\n     LEFT JOIN employee_timeoff_excluded eto ON (((ewh.employee_id = eto.user_id) AND (ewh.owner_id = eto.owner_id) AND (cd.day_of_work = eto.day_of_timeoff))))\n     LEFT JOIN task_work_per_day twd ON (((ewh.employee_id = twd.user_id) AND (ewh.owner_id = twd.owner_id) AND (cd.day_of_work = twd.day_of_work))))\n  ORDER BY cd.day_of_work, ewh.employee_id;"));
    }

    /**
     * Create an aliased <code>public.resource_utilization_rate_view</code>
     * table reference
     */
    public ResourceUtilizationRateView(String alias) {
        this(DSL.name(alias), RESOURCE_UTILIZATION_RATE_VIEW);
    }

    /**
     * Create an aliased <code>public.resource_utilization_rate_view</code>
     * table reference
     */
    public ResourceUtilizationRateView(Name alias) {
        this(alias, RESOURCE_UTILIZATION_RATE_VIEW);
    }

    /**
     * Create a <code>public.resource_utilization_rate_view</code> table
     * reference
     */
    public ResourceUtilizationRateView() {
        this(DSL.name("resource_utilization_rate_view"), null);
    }

    public <O extends Record> ResourceUtilizationRateView(Table<O> child, ForeignKey<O, ResourceUtilizationRateViewRecord> key) {
        super(child, key, RESOURCE_UTILIZATION_RATE_VIEW);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public ResourceUtilizationRateView as(String alias) {
        return new ResourceUtilizationRateView(DSL.name(alias), this);
    }

    @Override
    public ResourceUtilizationRateView as(Name alias) {
        return new ResourceUtilizationRateView(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ResourceUtilizationRateView rename(String name) {
        return new ResourceUtilizationRateView(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ResourceUtilizationRateView rename(Name name) {
        return new ResourceUtilizationRateView(name, null);
    }

    // -------------------------------------------------------------------------
    // Row7 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row7<UUID, UUID, String, LocalDate, BigDecimal, BigDecimal, BigDecimal> fieldsRow() {
        return (Row7) super.fieldsRow();
    }
}
