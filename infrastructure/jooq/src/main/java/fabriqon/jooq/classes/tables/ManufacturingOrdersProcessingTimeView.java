/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.ManufacturingOrdersProcessingTimeViewRecord;
import org.jooq.*;
import org.jooq.Record;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ManufacturingOrdersProcessingTimeView extends TableImpl<ManufacturingOrdersProcessingTimeViewRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>public.manufacturing_orders_processing_time_view</code>
     */
    public static final ManufacturingOrdersProcessingTimeView MANUFACTURING_ORDERS_PROCESSING_TIME_VIEW = new ManufacturingOrdersProcessingTimeView();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ManufacturingOrdersProcessingTimeViewRecord> getRecordType() {
        return ManufacturingOrdersProcessingTimeViewRecord.class;
    }

    /**
     * The column
     * <code>public.manufacturing_orders_processing_time_view.owner_id</code>.
     */
    public final TableField<ManufacturingOrdersProcessingTimeViewRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID, this, "");

    /**
     * The column
     * <code>public.manufacturing_orders_processing_time_view.manufacturing_order_id</code>.
     */
    public final TableField<ManufacturingOrdersProcessingTimeViewRecord, UUID> MANUFACTURING_ORDER_ID = createField(DSL.name("manufacturing_order_id"), SQLDataType.UUID, this, "");

    /**
     * The column
     * <code>public.manufacturing_orders_processing_time_view.task_start_time</code>.
     */
    public final TableField<ManufacturingOrdersProcessingTimeViewRecord, LocalDateTime> TASK_START_TIME = createField(DSL.name("task_start_time"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column
     * <code>public.manufacturing_orders_processing_time_view.task_end_time</code>.
     */
    public final TableField<ManufacturingOrdersProcessingTimeViewRecord, LocalDateTime> TASK_END_TIME = createField(DSL.name("task_end_time"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column
     * <code>public.manufacturing_orders_processing_time_view.total_working_seconds</code>.
     */
    public final TableField<ManufacturingOrdersProcessingTimeViewRecord, BigDecimal> TOTAL_WORKING_SECONDS = createField(DSL.name("total_working_seconds"), SQLDataType.NUMERIC, this, "");

    private ManufacturingOrdersProcessingTimeView(Name alias, Table<ManufacturingOrdersProcessingTimeViewRecord> aliased) {
        this(alias, aliased, null);
    }

    private ManufacturingOrdersProcessingTimeView(Name alias, Table<ManufacturingOrdersProcessingTimeViewRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.view("create view \"manufacturing_orders_processing_time_view\" as  WITH manufacturing_order_times AS (\n         SELECT mo.owner_id,\n            mo.id AS manufacturing_order_id,\n            mo.create_time,\n            min(mt.start_time) AS task_start_time,\n            max(mt.end_time) AS task_end_time,\n            ((a.settings -> 'manufacturing'::text) ->> 'workDayStartTime'::text) AS work_start,\n            ((a.settings -> 'manufacturing'::text) ->> 'workDayEndTime'::text) AS work_end,\n            ((a.settings -> 'manufacturing'::text) -> 'workingDays'::text) AS working_days\n           FROM ((manufacturing_order mo\n             JOIN account a ON ((mo.owner_id = a.id)))\n             LEFT JOIN manufacturing_task mt ON ((mt.manufacturing_order_id = mo.id)))\n          WHERE ((mo.status)::text = ANY ((ARRAY['MANUFACTURED'::character varying, 'CONSUMPTION_RECORDED'::character varying, 'ACCOUNTED'::character varying])::text[]))\n          GROUP BY mo.owner_id, mo.id, a.settings\n        ), working_days_and_hours AS (\n         SELECT mot.owner_id,\n            mot.manufacturing_order_id,\n            (d.d)::date AS day_of_processing,\n            (mot.work_start)::time without time zone AS work_start,\n            (mot.work_end)::time without time zone AS work_end,\n            mot.task_start_time,\n            mot.task_end_time\n           FROM manufacturing_order_times mot,\n            LATERAL generate_series(((mot.task_start_time)::date)::timestamp with time zone, ((mot.task_end_time)::date)::timestamp with time zone, '1 day'::interval) d(d)\n          WHERE (EXTRACT(dow FROM d.d) IN ( SELECT (((jsonb_array_elements_text(mot.working_days))::integer + 6) % 7)))\n        )\n SELECT working_days_and_hours.owner_id,\n    working_days_and_hours.manufacturing_order_id,\n    working_days_and_hours.task_start_time,\n    working_days_and_hours.task_end_time,\n    GREATEST(sum(\n        CASE\n            WHEN (working_days_and_hours.day_of_processing = (working_days_and_hours.task_start_time)::date) THEN EXTRACT(epoch FROM (LEAST((working_days_and_hours.day_of_processing + working_days_and_hours.work_end), working_days_and_hours.task_end_time) - GREATEST(working_days_and_hours.task_start_time, (working_days_and_hours.day_of_processing + working_days_and_hours.work_start))))\n            WHEN (working_days_and_hours.day_of_processing = (working_days_and_hours.task_end_time)::date) THEN EXTRACT(epoch FROM (LEAST(working_days_and_hours.task_end_time, (working_days_and_hours.day_of_processing + working_days_and_hours.work_end)) - (working_days_and_hours.day_of_processing + working_days_and_hours.work_start)))\n            ELSE EXTRACT(epoch FROM (working_days_and_hours.work_end - working_days_and_hours.work_start))\n        END), (0)::numeric) AS total_working_seconds\n   FROM working_days_and_hours\n  GROUP BY working_days_and_hours.owner_id, working_days_and_hours.manufacturing_order_id, working_days_and_hours.task_start_time, working_days_and_hours.task_end_time;"));
    }

    /**
     * Create an aliased
     * <code>public.manufacturing_orders_processing_time_view</code> table
     * reference
     */
    public ManufacturingOrdersProcessingTimeView(String alias) {
        this(DSL.name(alias), MANUFACTURING_ORDERS_PROCESSING_TIME_VIEW);
    }

    /**
     * Create an aliased
     * <code>public.manufacturing_orders_processing_time_view</code> table
     * reference
     */
    public ManufacturingOrdersProcessingTimeView(Name alias) {
        this(alias, MANUFACTURING_ORDERS_PROCESSING_TIME_VIEW);
    }

    /**
     * Create a <code>public.manufacturing_orders_processing_time_view</code>
     * table reference
     */
    public ManufacturingOrdersProcessingTimeView() {
        this(DSL.name("manufacturing_orders_processing_time_view"), null);
    }

    public <O extends Record> ManufacturingOrdersProcessingTimeView(Table<O> child, ForeignKey<O, ManufacturingOrdersProcessingTimeViewRecord> key) {
        super(child, key, MANUFACTURING_ORDERS_PROCESSING_TIME_VIEW);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public ManufacturingOrdersProcessingTimeView as(String alias) {
        return new ManufacturingOrdersProcessingTimeView(DSL.name(alias), this);
    }

    @Override
    public ManufacturingOrdersProcessingTimeView as(Name alias) {
        return new ManufacturingOrdersProcessingTimeView(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ManufacturingOrdersProcessingTimeView rename(String name) {
        return new ManufacturingOrdersProcessingTimeView(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ManufacturingOrdersProcessingTimeView rename(Name name) {
        return new ManufacturingOrdersProcessingTimeView(name, null);
    }

    // -------------------------------------------------------------------------
    // Row5 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row5<UUID, UUID, LocalDateTime, LocalDateTime, BigDecimal> fieldsRow() {
        return (Row5) super.fieldsRow();
    }
}
