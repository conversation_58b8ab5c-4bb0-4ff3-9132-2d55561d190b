/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.ManufacturingTaskRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row15;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ManufacturingTask extends TableImpl<ManufacturingTaskRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.manufacturing_task</code>
     */
    public static final ManufacturingTask MANUFACTURING_TASK = new ManufacturingTask();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ManufacturingTaskRecord> getRecordType() {
        return ManufacturingTaskRecord.class;
    }

    /**
     * The column <code>public.manufacturing_task.id</code>.
     */
    public final TableField<ManufacturingTaskRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.manufacturing_task.create_time</code>.
     */
    public final TableField<ManufacturingTaskRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.manufacturing_task.update_time</code>.
     */
    public final TableField<ManufacturingTaskRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.manufacturing_task.deleted</code>.
     */
    public final TableField<ManufacturingTaskRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.manufacturing_task.owner_id</code>.
     */
    public final TableField<ManufacturingTaskRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.manufacturing_task.manufacturing_order_id</code>.
     */
    public final TableField<ManufacturingTaskRecord, UUID> MANUFACTURING_ORDER_ID = createField(DSL.name("manufacturing_order_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.manufacturing_task.status</code>.
     */
    public final TableField<ManufacturingTaskRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(50), this, "");

    /**
     * The column <code>public.manufacturing_task.status_reason</code>.
     */
    public final TableField<ManufacturingTaskRecord, String> STATUS_REASON = createField(DSL.name("status_reason"), SQLDataType.VARCHAR(50), this, "");

    /**
     * The column <code>public.manufacturing_task.estimated_start_time</code>.
     */
    public final TableField<ManufacturingTaskRecord, LocalDateTime> ESTIMATED_START_TIME = createField(DSL.name("estimated_start_time"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>public.manufacturing_task.estimated_end_time</code>.
     */
    public final TableField<ManufacturingTaskRecord, LocalDateTime> ESTIMATED_END_TIME = createField(DSL.name("estimated_end_time"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>public.manufacturing_task.duration_in_minutes</code>.
     */
    public final TableField<ManufacturingTaskRecord, Integer> DURATION_IN_MINUTES = createField(DSL.name("duration_in_minutes"), SQLDataType.INTEGER, this, "");

    /**
     * The column <code>public.manufacturing_task.details</code>.
     */
    public final TableField<ManufacturingTaskRecord, JSONB> DETAILS = createField(DSL.name("details"), SQLDataType.JSONB, this, "");

    /**
     * The column <code>public.manufacturing_task.start_time</code>.
     */
    public final TableField<ManufacturingTaskRecord, LocalDateTime> START_TIME = createField(DSL.name("start_time"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>public.manufacturing_task.end_time</code>.
     */
    public final TableField<ManufacturingTaskRecord, LocalDateTime> END_TIME = createField(DSL.name("end_time"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>public.manufacturing_task.ranking</code>.
     */
    public final TableField<ManufacturingTaskRecord, Long> RANKING = createField(DSL.name("ranking"), SQLDataType.BIGINT, this, "");

    private ManufacturingTask(Name alias, Table<ManufacturingTaskRecord> aliased) {
        this(alias, aliased, null);
    }

    private ManufacturingTask(Name alias, Table<ManufacturingTaskRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.manufacturing_task</code> table reference
     */
    public ManufacturingTask(String alias) {
        this(DSL.name(alias), MANUFACTURING_TASK);
    }

    /**
     * Create an aliased <code>public.manufacturing_task</code> table reference
     */
    public ManufacturingTask(Name alias) {
        this(alias, MANUFACTURING_TASK);
    }

    /**
     * Create a <code>public.manufacturing_task</code> table reference
     */
    public ManufacturingTask() {
        this(DSL.name("manufacturing_task"), null);
    }

    public <O extends Record> ManufacturingTask(Table<O> child, ForeignKey<O, ManufacturingTaskRecord> key) {
        super(child, key, MANUFACTURING_TASK);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public UniqueKey<ManufacturingTaskRecord> getPrimaryKey() {
        return Keys.MANUFACTURING_TASK_PKEY;
    }

    @Override
    public List<ForeignKey<ManufacturingTaskRecord, ?>> getReferences() {
        return Arrays.asList(Keys.MANUFACTURING_TASK__MANUFACTURING_TASK_OWNER_ID_FKEY, Keys.MANUFACTURING_TASK__MANUFACTURING_TASK_MANUFACTURING_ORDER_ID_FKEY);
    }

    private transient Account _account;
    private transient ManufacturingOrder _manufacturingOrder;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.MANUFACTURING_TASK__MANUFACTURING_TASK_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.manufacturing_order</code>
     * table.
     */
    public ManufacturingOrder manufacturingOrder() {
        if (_manufacturingOrder == null)
            _manufacturingOrder = new ManufacturingOrder(this, Keys.MANUFACTURING_TASK__MANUFACTURING_TASK_MANUFACTURING_ORDER_ID_FKEY);

        return _manufacturingOrder;
    }

    @Override
    public ManufacturingTask as(String alias) {
        return new ManufacturingTask(DSL.name(alias), this);
    }

    @Override
    public ManufacturingTask as(Name alias) {
        return new ManufacturingTask(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ManufacturingTask rename(String name) {
        return new ManufacturingTask(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ManufacturingTask rename(Name name) {
        return new ManufacturingTask(name, null);
    }

    // -------------------------------------------------------------------------
    // Row15 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row15<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String, String, LocalDateTime, LocalDateTime, Integer, JSONB, LocalDateTime, LocalDateTime, Long> fieldsRow() {
        return (Row15) super.fieldsRow();
    }
}
