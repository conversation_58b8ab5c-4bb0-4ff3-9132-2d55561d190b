/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.SalesOrder;
import org.jooq.*;
import org.jooq.impl.UpdatableRecordImpl;

import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SalesOrderRecord extends UpdatableRecordImpl<SalesOrderRecord> implements Record19<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String, Integer, LocalDateTime, String, JSONB, JSONB, String, Object, LocalDateTime, LocalDateTime, JSONB, String, JSONB> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.sales_order.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.sales_order.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.sales_order.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.sales_order.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.sales_order.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.sales_order.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.sales_order.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.sales_order.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.sales_order.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.sales_order.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.sales_order.customer_id</code>.
     */
    public void setCustomerId(UUID value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.sales_order.customer_id</code>.
     */
    public UUID getCustomerId() {
        return (UUID) get(5);
    }

    /**
     * Setter for <code>public.sales_order.number</code>.
     */
    public void setNumber(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.sales_order.number</code>.
     */
    public String getNumber() {
        return (String) get(6);
    }

    /**
     * Setter for <code>public.sales_order.ranking</code>.
     */
    public void setRanking(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.sales_order.ranking</code>.
     */
    public Integer getRanking() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>public.sales_order.delivery_deadline</code>.
     */
    public void setDeliveryDeadline(LocalDateTime value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.sales_order.delivery_deadline</code>.
     */
    public LocalDateTime getDeliveryDeadline() {
        return (LocalDateTime) get(8);
    }

    /**
     * Setter for <code>public.sales_order.status</code>.
     */
    public void setStatus(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>public.sales_order.status</code>.
     */
    public String getStatus() {
        return (String) get(9);
    }

    /**
     * Setter for <code>public.sales_order.items</code>.
     */
    public void setItems(JSONB value) {
        set(10, value);
    }

    /**
     * Getter for <code>public.sales_order.items</code>.
     */
    public JSONB getItems() {
        return (JSONB) get(10);
    }

    /**
     * Setter for <code>public.sales_order.shipping_address</code>.
     */
    public void setShippingAddress(JSONB value) {
        set(11, value);
    }

    /**
     * Getter for <code>public.sales_order.shipping_address</code>.
     */
    public JSONB getShippingAddress() {
        return (JSONB) get(11);
    }

    /**
     * Setter for <code>public.sales_order.notes</code>.
     */
    public void setNotes(String value) {
        set(12, value);
    }

    /**
     * Getter for <code>public.sales_order.notes</code>.
     */
    public String getNotes() {
        return (String) get(12);
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public void setTextSearch(Object value) {
        set(13, value);
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public Object getTextSearch() {
        return get(13);
    }

    /**
     * Setter for <code>public.sales_order.offer_date</code>.
     */
    public void setOfferDate(LocalDateTime value) {
        set(14, value);
    }

    /**
     * Getter for <code>public.sales_order.offer_date</code>.
     */
    public LocalDateTime getOfferDate() {
        return (LocalDateTime) get(14);
    }

    /**
     * Setter for <code>public.sales_order.offer_expiration</code>.
     */
    public void setOfferExpiration(LocalDateTime value) {
        set(15, value);
    }

    /**
     * Getter for <code>public.sales_order.offer_expiration</code>.
     */
    public LocalDateTime getOfferExpiration() {
        return (LocalDateTime) get(15);
    }

    /**
     * Setter for <code>public.sales_order.rendering_details</code>.
     */
    public void setRenderingDetails(JSONB value) {
        set(16, value);
    }

    /**
     * Getter for <code>public.sales_order.rendering_details</code>.
     */
    public JSONB getRenderingDetails() {
        return (JSONB) get(16);
    }

    /**
     * Setter for <code>public.sales_order.customer_notes</code>.
     */
    public void setCustomerNotes(String value) {
        set(17, value);
    }

    /**
     * Getter for <code>public.sales_order.customer_notes</code>.
     */
    public String getCustomerNotes() {
        return (String) get(17);
    }

    /**
     * Setter for <code>public.sales_order.global_discount</code>.
     */
    public void setGlobalDiscount(JSONB value) {
        set(18, value);
    }

    /**
     * Getter for <code>public.sales_order.global_discount</code>.
     */
    public JSONB getGlobalDiscount() {
        return (JSONB) get(18);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record19 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row19<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String, Integer, LocalDateTime, String, JSONB, JSONB, String, Object, LocalDateTime, LocalDateTime, JSONB, String, JSONB> fieldsRow() {
        return (Row19) super.fieldsRow();
    }

    @Override
    public Row19<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String, Integer, LocalDateTime, String, JSONB, JSONB, String, Object, LocalDateTime, LocalDateTime, JSONB, String, JSONB> valuesRow() {
        return (Row19) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return SalesOrder.SALES_ORDER.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return SalesOrder.SALES_ORDER.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return SalesOrder.SALES_ORDER.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return SalesOrder.SALES_ORDER.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return SalesOrder.SALES_ORDER.OWNER_ID;
    }

    @Override
    public Field<UUID> field6() {
        return SalesOrder.SALES_ORDER.CUSTOMER_ID;
    }

    @Override
    public Field<String> field7() {
        return SalesOrder.SALES_ORDER.NUMBER;
    }

    @Override
    public Field<Integer> field8() {
        return SalesOrder.SALES_ORDER.RANKING;
    }

    @Override
    public Field<LocalDateTime> field9() {
        return SalesOrder.SALES_ORDER.DELIVERY_DEADLINE;
    }

    @Override
    public Field<String> field10() {
        return SalesOrder.SALES_ORDER.STATUS;
    }

    @Override
    public Field<JSONB> field11() {
        return SalesOrder.SALES_ORDER.ITEMS;
    }

    @Override
    public Field<JSONB> field12() {
        return SalesOrder.SALES_ORDER.SHIPPING_ADDRESS;
    }

    @Override
    public Field<String> field13() {
        return SalesOrder.SALES_ORDER.NOTES;
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public Field<Object> field14() {
        return SalesOrder.SALES_ORDER.TEXT_SEARCH;
    }

    @Override
    public Field<LocalDateTime> field15() {
        return SalesOrder.SALES_ORDER.OFFER_DATE;
    }

    @Override
    public Field<LocalDateTime> field16() {
        return SalesOrder.SALES_ORDER.OFFER_EXPIRATION;
    }

    @Override
    public Field<JSONB> field17() {
        return SalesOrder.SALES_ORDER.RENDERING_DETAILS;
    }

    @Override
    public Field<String> field18() {
        return SalesOrder.SALES_ORDER.CUSTOMER_NOTES;
    }

    @Override
    public Field<JSONB> field19() {
        return SalesOrder.SALES_ORDER.GLOBAL_DISCOUNT;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public UUID component6() {
        return getCustomerId();
    }

    @Override
    public String component7() {
        return getNumber();
    }

    @Override
    public Integer component8() {
        return getRanking();
    }

    @Override
    public LocalDateTime component9() {
        return getDeliveryDeadline();
    }

    @Override
    public String component10() {
        return getStatus();
    }

    @Override
    public JSONB component11() {
        return getItems();
    }

    @Override
    public JSONB component12() {
        return getShippingAddress();
    }

    @Override
    public String component13() {
        return getNotes();
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public Object component14() {
        return getTextSearch();
    }

    @Override
    public LocalDateTime component15() {
        return getOfferDate();
    }

    @Override
    public LocalDateTime component16() {
        return getOfferExpiration();
    }

    @Override
    public JSONB component17() {
        return getRenderingDetails();
    }

    @Override
    public String component18() {
        return getCustomerNotes();
    }

    @Override
    public JSONB component19() {
        return getGlobalDiscount();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public UUID value6() {
        return getCustomerId();
    }

    @Override
    public String value7() {
        return getNumber();
    }

    @Override
    public Integer value8() {
        return getRanking();
    }

    @Override
    public LocalDateTime value9() {
        return getDeliveryDeadline();
    }

    @Override
    public String value10() {
        return getStatus();
    }

    @Override
    public JSONB value11() {
        return getItems();
    }

    @Override
    public JSONB value12() {
        return getShippingAddress();
    }

    @Override
    public String value13() {
        return getNotes();
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public Object value14() {
        return getTextSearch();
    }

    @Override
    public LocalDateTime value15() {
        return getOfferDate();
    }

    @Override
    public LocalDateTime value16() {
        return getOfferExpiration();
    }

    @Override
    public JSONB value17() {
        return getRenderingDetails();
    }

    @Override
    public String value18() {
        return getCustomerNotes();
    }

    @Override
    public JSONB value19() {
        return getGlobalDiscount();
    }

    @Override
    public SalesOrderRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public SalesOrderRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public SalesOrderRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public SalesOrderRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public SalesOrderRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public SalesOrderRecord value6(UUID value) {
        setCustomerId(value);
        return this;
    }

    @Override
    public SalesOrderRecord value7(String value) {
        setNumber(value);
        return this;
    }

    @Override
    public SalesOrderRecord value8(Integer value) {
        setRanking(value);
        return this;
    }

    @Override
    public SalesOrderRecord value9(LocalDateTime value) {
        setDeliveryDeadline(value);
        return this;
    }

    @Override
    public SalesOrderRecord value10(String value) {
        setStatus(value);
        return this;
    }

    @Override
    public SalesOrderRecord value11(JSONB value) {
        setItems(value);
        return this;
    }

    @Override
    public SalesOrderRecord value12(JSONB value) {
        setShippingAddress(value);
        return this;
    }

    @Override
    public SalesOrderRecord value13(String value) {
        setNotes(value);
        return this;
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public SalesOrderRecord value14(Object value) {
        setTextSearch(value);
        return this;
    }

    @Override
    public SalesOrderRecord value15(LocalDateTime value) {
        setOfferDate(value);
        return this;
    }

    @Override
    public SalesOrderRecord value16(LocalDateTime value) {
        setOfferExpiration(value);
        return this;
    }

    @Override
    public SalesOrderRecord value17(JSONB value) {
        setRenderingDetails(value);
        return this;
    }

    @Override
    public SalesOrderRecord value18(String value) {
        setCustomerNotes(value);
        return this;
    }

    @Override
    public SalesOrderRecord value19(JSONB value) {
        setGlobalDiscount(value);
        return this;
    }

    @Override
    public SalesOrderRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, UUID value6, String value7, Integer value8, LocalDateTime value9, String value10, JSONB value11, JSONB value12, String value13, Object value14, LocalDateTime value15, LocalDateTime value16, JSONB value17, String value18, JSONB value19) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        value17(value17);
        value18(value18);
        value19(value19);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SalesOrderRecord
     */
    public SalesOrderRecord() {
        super(SalesOrder.SALES_ORDER);
    }

    /**
     * Create a detached, initialised SalesOrderRecord
     */
    public SalesOrderRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, UUID customerId, String number, Integer ranking, LocalDateTime deliveryDeadline, String status, JSONB items, JSONB shippingAddress, String notes, Object textSearch, LocalDateTime offerDate, LocalDateTime offerExpiration, JSONB renderingDetails, String customerNotes, JSONB globalDiscount) {
        super(SalesOrder.SALES_ORDER);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setCustomerId(customerId);
        setNumber(number);
        setRanking(ranking);
        setDeliveryDeadline(deliveryDeadline);
        setStatus(status);
        setItems(items);
        setShippingAddress(shippingAddress);
        setNotes(notes);
        setTextSearch(textSearch);
        setOfferDate(offerDate);
        setOfferExpiration(offerExpiration);
        setRenderingDetails(renderingDetails);
        setCustomerNotes(customerNotes);
        setGlobalDiscount(globalDiscount);
    }
}
