/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.FirebaseRegistrationTokenRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row8;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class FirebaseRegistrationToken extends TableImpl<FirebaseRegistrationTokenRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.firebase_registration_token</code>
     */
    public static final FirebaseRegistrationToken FIREBASE_REGISTRATION_TOKEN = new FirebaseRegistrationToken();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<FirebaseRegistrationTokenRecord> getRecordType() {
        return FirebaseRegistrationTokenRecord.class;
    }

    /**
     * The column <code>public.firebase_registration_token.id</code>.
     */
    public final TableField<FirebaseRegistrationTokenRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.firebase_registration_token.create_time</code>.
     */
    public final TableField<FirebaseRegistrationTokenRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.firebase_registration_token.update_time</code>.
     */
    public final TableField<FirebaseRegistrationTokenRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.firebase_registration_token.deleted</code>.
     */
    public final TableField<FirebaseRegistrationTokenRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.firebase_registration_token.owner_id</code>.
     */
    public final TableField<FirebaseRegistrationTokenRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.firebase_registration_token.token</code>.
     */
    public final TableField<FirebaseRegistrationTokenRecord, String> TOKEN = createField(DSL.name("token"), SQLDataType.VARCHAR(200).nullable(false), this, "");

    /**
     * The column <code>public.firebase_registration_token.user_id</code>.
     */
    public final TableField<FirebaseRegistrationTokenRecord, UUID> USER_ID = createField(DSL.name("user_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.firebase_registration_token.employee_id</code>.
     */
    public final TableField<FirebaseRegistrationTokenRecord, UUID> EMPLOYEE_ID = createField(DSL.name("employee_id"), SQLDataType.UUID, this, "");

    private FirebaseRegistrationToken(Name alias, Table<FirebaseRegistrationTokenRecord> aliased) {
        this(alias, aliased, null);
    }

    private FirebaseRegistrationToken(Name alias, Table<FirebaseRegistrationTokenRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.firebase_registration_token</code> table
     * reference
     */
    public FirebaseRegistrationToken(String alias) {
        this(DSL.name(alias), FIREBASE_REGISTRATION_TOKEN);
    }

    /**
     * Create an aliased <code>public.firebase_registration_token</code> table
     * reference
     */
    public FirebaseRegistrationToken(Name alias) {
        this(alias, FIREBASE_REGISTRATION_TOKEN);
    }

    /**
     * Create a <code>public.firebase_registration_token</code> table reference
     */
    public FirebaseRegistrationToken() {
        this(DSL.name("firebase_registration_token"), null);
    }

    public <O extends Record> FirebaseRegistrationToken(Table<O> child, ForeignKey<O, FirebaseRegistrationTokenRecord> key) {
        super(child, key, FIREBASE_REGISTRATION_TOKEN);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public UniqueKey<FirebaseRegistrationTokenRecord> getPrimaryKey() {
        return Keys.FIREBASE_REGISTRATION_TOKEN_PKEY;
    }

    @Override
    public List<UniqueKey<FirebaseRegistrationTokenRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.FIREBASE_REGISTRATION_TOKEN_USER_ID_KEY, Keys.FIREBASE_REGISTRATION_TOKEN_EMPLOYEE_ID_KEY);
    }

    @Override
    public List<ForeignKey<FirebaseRegistrationTokenRecord, ?>> getReferences() {
        return Arrays.asList(Keys.FIREBASE_REGISTRATION_TOKEN__FIREBASE_REGISTRATION_TOKEN_OWNER_ID_FKEY, Keys.FIREBASE_REGISTRATION_TOKEN__FIREBASE_REGISTRATION_TOKEN_USER_ID_FKEY);
    }

    private transient Account _account;
    private transient Users _users;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.FIREBASE_REGISTRATION_TOKEN__FIREBASE_REGISTRATION_TOKEN_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.users</code> table.
     */
    public Users users() {
        if (_users == null)
            _users = new Users(this, Keys.FIREBASE_REGISTRATION_TOKEN__FIREBASE_REGISTRATION_TOKEN_USER_ID_FKEY);

        return _users;
    }

    @Override
    public FirebaseRegistrationToken as(String alias) {
        return new FirebaseRegistrationToken(DSL.name(alias), this);
    }

    @Override
    public FirebaseRegistrationToken as(Name alias) {
        return new FirebaseRegistrationToken(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public FirebaseRegistrationToken rename(String name) {
        return new FirebaseRegistrationToken(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public FirebaseRegistrationToken rename(Name name) {
        return new FirebaseRegistrationToken(name, null);
    }

    // -------------------------------------------------------------------------
    // Row8 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row8<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, UUID, UUID> fieldsRow() {
        return (Row8) super.fieldsRow();
    }
}
