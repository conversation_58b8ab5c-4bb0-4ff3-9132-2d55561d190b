/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.NotificationReadBy;

import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record2;
import org.jooq.Row2;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class NotificationReadByRecord extends TableRecordImpl<NotificationReadByRecord> implements Record2<UUID, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.notification_read_by.notification_id</code>.
     */
    public void setNotificationId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.notification_read_by.notification_id</code>.
     */
    public UUID getNotificationId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.notification_read_by.user_id</code>.
     */
    public void setUserId(UUID value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.notification_read_by.user_id</code>.
     */
    public UUID getUserId() {
        return (UUID) get(1);
    }

    // -------------------------------------------------------------------------
    // Record2 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row2<UUID, UUID> fieldsRow() {
        return (Row2) super.fieldsRow();
    }

    @Override
    public Row2<UUID, UUID> valuesRow() {
        return (Row2) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return NotificationReadBy.NOTIFICATION_READ_BY.NOTIFICATION_ID;
    }

    @Override
    public Field<UUID> field2() {
        return NotificationReadBy.NOTIFICATION_READ_BY.USER_ID;
    }

    @Override
    public UUID component1() {
        return getNotificationId();
    }

    @Override
    public UUID component2() {
        return getUserId();
    }

    @Override
    public UUID value1() {
        return getNotificationId();
    }

    @Override
    public UUID value2() {
        return getUserId();
    }

    @Override
    public NotificationReadByRecord value1(UUID value) {
        setNotificationId(value);
        return this;
    }

    @Override
    public NotificationReadByRecord value2(UUID value) {
        setUserId(value);
        return this;
    }

    @Override
    public NotificationReadByRecord values(UUID value1, UUID value2) {
        value1(value1);
        value2(value2);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached NotificationReadByRecord
     */
    public NotificationReadByRecord() {
        super(NotificationReadBy.NOTIFICATION_READ_BY);
    }

    /**
     * Create a detached, initialised NotificationReadByRecord
     */
    public NotificationReadByRecord(UUID notificationId, UUID userId) {
        super(NotificationReadBy.NOTIFICATION_READ_BY);

        setNotificationId(notificationId);
        setUserId(userId);
    }
}
