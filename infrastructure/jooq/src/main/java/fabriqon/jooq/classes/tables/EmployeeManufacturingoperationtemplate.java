/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.EmployeeManufacturingoperationtemplateRecord;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row4;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class EmployeeManufacturingoperationtemplate extends TableImpl<EmployeeManufacturingoperationtemplateRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>public.employee_manufacturingoperationtemplate</code>
     */
    public static final EmployeeManufacturingoperationtemplate EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE = new EmployeeManufacturingoperationtemplate();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<EmployeeManufacturingoperationtemplateRecord> getRecordType() {
        return EmployeeManufacturingoperationtemplateRecord.class;
    }

    /**
     * The column
     * <code>public.employee_manufacturingoperationtemplate.owner_id</code>.
     */
    public final TableField<EmployeeManufacturingoperationtemplateRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column
     * <code>public.employee_manufacturingoperationtemplate.user_id</code>.
     */
    public final TableField<EmployeeManufacturingoperationtemplateRecord, UUID> USER_ID = createField(DSL.name("user_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column
     * <code>public.employee_manufacturingoperationtemplate.manufacturing_operation_template_id</code>.
     */
    public final TableField<EmployeeManufacturingoperationtemplateRecord, UUID> MANUFACTURING_OPERATION_TEMPLATE_ID = createField(DSL.name("manufacturing_operation_template_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column
     * <code>public.employee_manufacturingoperationtemplate.preferential</code>.
     */
    public final TableField<EmployeeManufacturingoperationtemplateRecord, Boolean> PREFERENTIAL = createField(DSL.name("preferential"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    private EmployeeManufacturingoperationtemplate(Name alias, Table<EmployeeManufacturingoperationtemplateRecord> aliased) {
        this(alias, aliased, null);
    }

    private EmployeeManufacturingoperationtemplate(Name alias, Table<EmployeeManufacturingoperationtemplateRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased
     * <code>public.employee_manufacturingoperationtemplate</code> table
     * reference
     */
    public EmployeeManufacturingoperationtemplate(String alias) {
        this(DSL.name(alias), EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE);
    }

    /**
     * Create an aliased
     * <code>public.employee_manufacturingoperationtemplate</code> table
     * reference
     */
    public EmployeeManufacturingoperationtemplate(Name alias) {
        this(alias, EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE);
    }

    /**
     * Create a <code>public.employee_manufacturingoperationtemplate</code>
     * table reference
     */
    public EmployeeManufacturingoperationtemplate() {
        this(DSL.name("employee_manufacturingoperationtemplate"), null);
    }

    public <O extends Record> EmployeeManufacturingoperationtemplate(Table<O> child, ForeignKey<O, EmployeeManufacturingoperationtemplateRecord> key) {
        super(child, key, EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<ForeignKey<EmployeeManufacturingoperationtemplateRecord, ?>> getReferences() {
        return Arrays.asList(Keys.EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE__EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE_OWNER_ID_FKEY, Keys.EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE__EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE_USER_ID_FKEY, Keys.EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE__EMPLOYEE_MANUFACTURINGOPERATI_MANUFACTURING_OPERATION_TEMP_FKEY);
    }

    private transient Account _account;
    private transient Users _users;
    private transient ManufacturingOperationTemplate _manufacturingOperationTemplate;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE__EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.users</code> table.
     */
    public Users users() {
        if (_users == null)
            _users = new Users(this, Keys.EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE__EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE_USER_ID_FKEY);

        return _users;
    }

    /**
     * Get the implicit join path to the
     * <code>public.manufacturing_operation_template</code> table.
     */
    public ManufacturingOperationTemplate manufacturingOperationTemplate() {
        if (_manufacturingOperationTemplate == null)
            _manufacturingOperationTemplate = new ManufacturingOperationTemplate(this, Keys.EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE__EMPLOYEE_MANUFACTURINGOPERATI_MANUFACTURING_OPERATION_TEMP_FKEY);

        return _manufacturingOperationTemplate;
    }

    @Override
    public EmployeeManufacturingoperationtemplate as(String alias) {
        return new EmployeeManufacturingoperationtemplate(DSL.name(alias), this);
    }

    @Override
    public EmployeeManufacturingoperationtemplate as(Name alias) {
        return new EmployeeManufacturingoperationtemplate(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public EmployeeManufacturingoperationtemplate rename(String name) {
        return new EmployeeManufacturingoperationtemplate(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public EmployeeManufacturingoperationtemplate rename(Name name) {
        return new EmployeeManufacturingoperationtemplate(name, null);
    }

    // -------------------------------------------------------------------------
    // Row4 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row4<UUID, UUID, UUID, Boolean> fieldsRow() {
        return (Row4) super.fieldsRow();
    }
}
