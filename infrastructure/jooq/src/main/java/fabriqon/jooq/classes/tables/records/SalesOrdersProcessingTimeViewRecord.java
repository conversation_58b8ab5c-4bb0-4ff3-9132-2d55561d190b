/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.SalesOrdersProcessingTimeView;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SalesOrdersProcessingTimeViewRecord extends TableRecordImpl<SalesOrdersProcessingTimeViewRecord> implements Record5<UUID, UUID, LocalDateTime, LocalDateTime, BigDecimal> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for
     * <code>public.sales_orders_processing_time_view.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for
     * <code>public.sales_orders_processing_time_view.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(0);
    }

    /**
     * Setter for
     * <code>public.sales_orders_processing_time_view.sales_order_id</code>.
     */
    public void setSalesOrderId(UUID value) {
        set(1, value);
    }

    /**
     * Getter for
     * <code>public.sales_orders_processing_time_view.sales_order_id</code>.
     */
    public UUID getSalesOrderId() {
        return (UUID) get(1);
    }

    /**
     * Setter for
     * <code>public.sales_orders_processing_time_view.start_time_local</code>.
     */
    public void setStartTimeLocal(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for
     * <code>public.sales_orders_processing_time_view.start_time_local</code>.
     */
    public LocalDateTime getStartTimeLocal() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for
     * <code>public.sales_orders_processing_time_view.delivered_time_local</code>.
     */
    public void setDeliveredTimeLocal(LocalDateTime value) {
        set(3, value);
    }

    /**
     * Getter for
     * <code>public.sales_orders_processing_time_view.delivered_time_local</code>.
     */
    public LocalDateTime getDeliveredTimeLocal() {
        return (LocalDateTime) get(3);
    }

    /**
     * Setter for
     * <code>public.sales_orders_processing_time_view.total_working_seconds</code>.
     */
    public void setTotalWorkingSeconds(BigDecimal value) {
        set(4, value);
    }

    /**
     * Getter for
     * <code>public.sales_orders_processing_time_view.total_working_seconds</code>.
     */
    public BigDecimal getTotalWorkingSeconds() {
        return (BigDecimal) get(4);
    }

    // -------------------------------------------------------------------------
    // Record5 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row5<UUID, UUID, LocalDateTime, LocalDateTime, BigDecimal> fieldsRow() {
        return (Row5) super.fieldsRow();
    }

    @Override
    public Row5<UUID, UUID, LocalDateTime, LocalDateTime, BigDecimal> valuesRow() {
        return (Row5) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return SalesOrdersProcessingTimeView.SALES_ORDERS_PROCESSING_TIME_VIEW.OWNER_ID;
    }

    @Override
    public Field<UUID> field2() {
        return SalesOrdersProcessingTimeView.SALES_ORDERS_PROCESSING_TIME_VIEW.SALES_ORDER_ID;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return SalesOrdersProcessingTimeView.SALES_ORDERS_PROCESSING_TIME_VIEW.START_TIME_LOCAL;
    }

    @Override
    public Field<LocalDateTime> field4() {
        return SalesOrdersProcessingTimeView.SALES_ORDERS_PROCESSING_TIME_VIEW.DELIVERED_TIME_LOCAL;
    }

    @Override
    public Field<BigDecimal> field5() {
        return SalesOrdersProcessingTimeView.SALES_ORDERS_PROCESSING_TIME_VIEW.TOTAL_WORKING_SECONDS;
    }

    @Override
    public UUID component1() {
        return getOwnerId();
    }

    @Override
    public UUID component2() {
        return getSalesOrderId();
    }

    @Override
    public LocalDateTime component3() {
        return getStartTimeLocal();
    }

    @Override
    public LocalDateTime component4() {
        return getDeliveredTimeLocal();
    }

    @Override
    public BigDecimal component5() {
        return getTotalWorkingSeconds();
    }

    @Override
    public UUID value1() {
        return getOwnerId();
    }

    @Override
    public UUID value2() {
        return getSalesOrderId();
    }

    @Override
    public LocalDateTime value3() {
        return getStartTimeLocal();
    }

    @Override
    public LocalDateTime value4() {
        return getDeliveredTimeLocal();
    }

    @Override
    public BigDecimal value5() {
        return getTotalWorkingSeconds();
    }

    @Override
    public SalesOrdersProcessingTimeViewRecord value1(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public SalesOrdersProcessingTimeViewRecord value2(UUID value) {
        setSalesOrderId(value);
        return this;
    }

    @Override
    public SalesOrdersProcessingTimeViewRecord value3(LocalDateTime value) {
        setStartTimeLocal(value);
        return this;
    }

    @Override
    public SalesOrdersProcessingTimeViewRecord value4(LocalDateTime value) {
        setDeliveredTimeLocal(value);
        return this;
    }

    @Override
    public SalesOrdersProcessingTimeViewRecord value5(BigDecimal value) {
        setTotalWorkingSeconds(value);
        return this;
    }

    @Override
    public SalesOrdersProcessingTimeViewRecord values(UUID value1, UUID value2, LocalDateTime value3, LocalDateTime value4, BigDecimal value5) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SalesOrdersProcessingTimeViewRecord
     */
    public SalesOrdersProcessingTimeViewRecord() {
        super(SalesOrdersProcessingTimeView.SALES_ORDERS_PROCESSING_TIME_VIEW);
    }

    /**
     * Create a detached, initialised SalesOrdersProcessingTimeViewRecord
     */
    public SalesOrdersProcessingTimeViewRecord(UUID ownerId, UUID salesOrderId, LocalDateTime startTimeLocal, LocalDateTime deliveredTimeLocal, BigDecimal totalWorkingSeconds) {
        super(SalesOrdersProcessingTimeView.SALES_ORDERS_PROCESSING_TIME_VIEW);

        setOwnerId(ownerId);
        setSalesOrderId(salesOrderId);
        setStartTimeLocal(startTimeLocal);
        setDeliveredTimeLocal(deliveredTimeLocal);
        setTotalWorkingSeconds(totalWorkingSeconds);
    }
}
