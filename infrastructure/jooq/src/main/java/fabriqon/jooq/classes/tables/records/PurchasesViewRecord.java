/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.PurchasesView;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PurchasesViewRecord extends TableRecordImpl<PurchasesViewRecord> implements Record6<UUID, LocalDateTime, UUID, BigDecimal, BigDecimal, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.purchases_view.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.purchases_view.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.purchases_view.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.purchases_view.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.purchases_view.reception_receipt_id</code>.
     */
    public void setReceptionReceiptId(UUID value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.purchases_view.reception_receipt_id</code>.
     */
    public UUID getReceptionReceiptId() {
        return (UUID) get(2);
    }

    /**
     * Setter for <code>public.purchases_view.reception_price</code>.
     */
    public void setReceptionPrice(BigDecimal value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.purchases_view.reception_price</code>.
     */
    public BigDecimal getReceptionPrice() {
        return (BigDecimal) get(3);
    }

    /**
     * Setter for <code>public.purchases_view.quantity</code>.
     */
    public void setQuantity(BigDecimal value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.purchases_view.quantity</code>.
     */
    public BigDecimal getQuantity() {
        return (BigDecimal) get(4);
    }

    /**
     * Setter for <code>public.purchases_view.supplier</code>.
     */
    public void setSupplier(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.purchases_view.supplier</code>.
     */
    public String getSupplier() {
        return (String) get(5);
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row6<UUID, LocalDateTime, UUID, BigDecimal, BigDecimal, String> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    @Override
    public Row6<UUID, LocalDateTime, UUID, BigDecimal, BigDecimal, String> valuesRow() {
        return (Row6) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return PurchasesView.PURCHASES_VIEW.OWNER_ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return PurchasesView.PURCHASES_VIEW.CREATE_TIME;
    }

    @Override
    public Field<UUID> field3() {
        return PurchasesView.PURCHASES_VIEW.RECEPTION_RECEIPT_ID;
    }

    @Override
    public Field<BigDecimal> field4() {
        return PurchasesView.PURCHASES_VIEW.RECEPTION_PRICE;
    }

    @Override
    public Field<BigDecimal> field5() {
        return PurchasesView.PURCHASES_VIEW.QUANTITY;
    }

    @Override
    public Field<String> field6() {
        return PurchasesView.PURCHASES_VIEW.SUPPLIER;
    }

    @Override
    public UUID component1() {
        return getOwnerId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public UUID component3() {
        return getReceptionReceiptId();
    }

    @Override
    public BigDecimal component4() {
        return getReceptionPrice();
    }

    @Override
    public BigDecimal component5() {
        return getQuantity();
    }

    @Override
    public String component6() {
        return getSupplier();
    }

    @Override
    public UUID value1() {
        return getOwnerId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public UUID value3() {
        return getReceptionReceiptId();
    }

    @Override
    public BigDecimal value4() {
        return getReceptionPrice();
    }

    @Override
    public BigDecimal value5() {
        return getQuantity();
    }

    @Override
    public String value6() {
        return getSupplier();
    }

    @Override
    public PurchasesViewRecord value1(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public PurchasesViewRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public PurchasesViewRecord value3(UUID value) {
        setReceptionReceiptId(value);
        return this;
    }

    @Override
    public PurchasesViewRecord value4(BigDecimal value) {
        setReceptionPrice(value);
        return this;
    }

    @Override
    public PurchasesViewRecord value5(BigDecimal value) {
        setQuantity(value);
        return this;
    }

    @Override
    public PurchasesViewRecord value6(String value) {
        setSupplier(value);
        return this;
    }

    @Override
    public PurchasesViewRecord values(UUID value1, LocalDateTime value2, UUID value3, BigDecimal value4, BigDecimal value5, String value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached PurchasesViewRecord
     */
    public PurchasesViewRecord() {
        super(PurchasesView.PURCHASES_VIEW);
    }

    /**
     * Create a detached, initialised PurchasesViewRecord
     */
    public PurchasesViewRecord(UUID ownerId, LocalDateTime createTime, UUID receptionReceiptId, BigDecimal receptionPrice, BigDecimal quantity, String supplier) {
        super(PurchasesView.PURCHASES_VIEW);

        setOwnerId(ownerId);
        setCreateTime(createTime);
        setReceptionReceiptId(receptionReceiptId);
        setReceptionPrice(receptionPrice);
        setQuantity(quantity);
        setSupplier(supplier);
    }
}
