/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.File;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class FileRecord extends UpdatableRecordImpl<FileRecord> implements Record5<LocalDateTime, UUID, UUID, String, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.file.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.file.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(0);
    }

    /**
     * Setter for <code>public.file.id</code>.
     */
    public void setId(UUID value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.file.id</code>.
     */
    public UUID getId() {
        return (UUID) get(1);
    }

    /**
     * Setter for <code>public.file.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.file.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(2);
    }

    /**
     * Setter for <code>public.file.file_name</code>.
     */
    public void setFileName(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.file.file_name</code>.
     */
    public String getFileName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>public.file.file_size</code>.
     */
    public void setFileSize(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.file.file_size</code>.
     */
    public Integer getFileSize() {
        return (Integer) get(4);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record5 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row5<LocalDateTime, UUID, UUID, String, Integer> fieldsRow() {
        return (Row5) super.fieldsRow();
    }

    @Override
    public Row5<LocalDateTime, UUID, UUID, String, Integer> valuesRow() {
        return (Row5) super.valuesRow();
    }

    @Override
    public Field<LocalDateTime> field1() {
        return File.FILE.CREATE_TIME;
    }

    @Override
    public Field<UUID> field2() {
        return File.FILE.ID;
    }

    @Override
    public Field<UUID> field3() {
        return File.FILE.OWNER_ID;
    }

    @Override
    public Field<String> field4() {
        return File.FILE.FILE_NAME;
    }

    @Override
    public Field<Integer> field5() {
        return File.FILE.FILE_SIZE;
    }

    @Override
    public LocalDateTime component1() {
        return getCreateTime();
    }

    @Override
    public UUID component2() {
        return getId();
    }

    @Override
    public UUID component3() {
        return getOwnerId();
    }

    @Override
    public String component4() {
        return getFileName();
    }

    @Override
    public Integer component5() {
        return getFileSize();
    }

    @Override
    public LocalDateTime value1() {
        return getCreateTime();
    }

    @Override
    public UUID value2() {
        return getId();
    }

    @Override
    public UUID value3() {
        return getOwnerId();
    }

    @Override
    public String value4() {
        return getFileName();
    }

    @Override
    public Integer value5() {
        return getFileSize();
    }

    @Override
    public FileRecord value1(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public FileRecord value2(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public FileRecord value3(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public FileRecord value4(String value) {
        setFileName(value);
        return this;
    }

    @Override
    public FileRecord value5(Integer value) {
        setFileSize(value);
        return this;
    }

    @Override
    public FileRecord values(LocalDateTime value1, UUID value2, UUID value3, String value4, Integer value5) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached FileRecord
     */
    public FileRecord() {
        super(File.FILE);
    }

    /**
     * Create a detached, initialised FileRecord
     */
    public FileRecord(LocalDateTime createTime, UUID id, UUID ownerId, String fileName, Integer fileSize) {
        super(File.FILE);

        setCreateTime(createTime);
        setId(id);
        setOwnerId(ownerId);
        setFileName(fileName);
        setFileSize(fileSize);
    }
}
