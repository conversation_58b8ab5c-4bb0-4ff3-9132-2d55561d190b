/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.InventoryRecord;
import fabriqon.jooq.converters.CurrencyConverter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Currency;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row20;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Inventory extends TableImpl<InventoryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.inventory</code>
     */
    public static final Inventory INVENTORY = new Inventory();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<InventoryRecord> getRecordType() {
        return InventoryRecord.class;
    }

    /**
     * The column <code>public.inventory.id</code>.
     */
    public final TableField<InventoryRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.inventory.create_time</code>.
     */
    public final TableField<InventoryRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.inventory.update_time</code>.
     */
    public final TableField<InventoryRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.inventory.deleted</code>.
     */
    public final TableField<InventoryRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.inventory.owner_id</code>.
     */
    public final TableField<InventoryRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.inventory.material_good_id</code>.
     */
    public final TableField<InventoryRecord, UUID> MATERIAL_GOOD_ID = createField(DSL.name("material_good_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.inventory.inventory_adjustment_order_id</code>.
     */
    public final TableField<InventoryRecord, UUID> INVENTORY_ADJUSTMENT_ORDER_ID = createField(DSL.name("inventory_adjustment_order_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.inventory.sales_order_id</code>.
     */
    public final TableField<InventoryRecord, UUID> SALES_ORDER_ID = createField(DSL.name("sales_order_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.inventory.manufacturing_order_id</code>.
     */
    public final TableField<InventoryRecord, UUID> MANUFACTURING_ORDER_ID = createField(DSL.name("manufacturing_order_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.inventory.supplier_id</code>.
     */
    public final TableField<InventoryRecord, UUID> SUPPLIER_ID = createField(DSL.name("supplier_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.inventory.purchase_date</code>.
     */
    public final TableField<InventoryRecord, LocalDateTime> PURCHASE_DATE = createField(DSL.name("purchase_date"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>public.inventory.expiry_date</code>.
     */
    public final TableField<InventoryRecord, LocalDateTime> EXPIRY_DATE = createField(DSL.name("expiry_date"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>public.inventory.reception_price_amount</code>.
     */
    public final TableField<InventoryRecord, Long> RECEPTION_PRICE_AMOUNT = createField(DSL.name("reception_price_amount"), SQLDataType.BIGINT, this, "");

    /**
     * The column <code>public.inventory.reception_price_currency</code>.
     */
    public final TableField<InventoryRecord, Currency> RECEPTION_PRICE_CURRENCY = createField(DSL.name("reception_price_currency"), SQLDataType.VARCHAR(3), this, "", new CurrencyConverter());

    /**
     * The column <code>public.inventory.quantity</code>.
     */
    public final TableField<InventoryRecord, BigDecimal> QUANTITY = createField(DSL.name("quantity"), SQLDataType.NUMERIC(12, 4), this, "");

    /**
     * The column <code>public.inventory.unit_id</code>.
     */
    public final TableField<InventoryRecord, UUID> UNIT_ID = createField(DSL.name("unit_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.inventory.reception_receipt_id</code>.
     */
    public final TableField<InventoryRecord, UUID> RECEPTION_RECEIPT_ID = createField(DSL.name("reception_receipt_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.inventory.exit_price_amount</code>.
     */
    public final TableField<InventoryRecord, Long> EXIT_PRICE_AMOUNT = createField(DSL.name("exit_price_amount"), SQLDataType.BIGINT, this, "");

    /**
     * The column <code>public.inventory.exit_price_currency</code>.
     */
    public final TableField<InventoryRecord, Currency> EXIT_PRICE_CURRENCY = createField(DSL.name("exit_price_currency"), SQLDataType.VARCHAR(3), this, "", new CurrencyConverter());

    /**
     * The column <code>public.inventory.servicing_order_id</code>.
     */
    public final TableField<InventoryRecord, UUID> SERVICING_ORDER_ID = createField(DSL.name("servicing_order_id"), SQLDataType.UUID, this, "");

    private Inventory(Name alias, Table<InventoryRecord> aliased) {
        this(alias, aliased, null);
    }

    private Inventory(Name alias, Table<InventoryRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.inventory</code> table reference
     */
    public Inventory(String alias) {
        this(DSL.name(alias), INVENTORY);
    }

    /**
     * Create an aliased <code>public.inventory</code> table reference
     */
    public Inventory(Name alias) {
        this(alias, INVENTORY);
    }

    /**
     * Create a <code>public.inventory</code> table reference
     */
    public Inventory() {
        this(DSL.name("inventory"), null);
    }

    public <O extends Record> Inventory(Table<O> child, ForeignKey<O, InventoryRecord> key) {
        super(child, key, INVENTORY);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public UniqueKey<InventoryRecord> getPrimaryKey() {
        return Keys.INVENTORY_PKEY;
    }

    @Override
    public List<ForeignKey<InventoryRecord, ?>> getReferences() {
        return Arrays.asList(Keys.INVENTORY__INVENTORY_OWNER_ID_FKEY, Keys.INVENTORY__INVENTORY_MATERIAL_GOOD_ID_FKEY, Keys.INVENTORY__INVENTORY_INVENTORY_ADJUSTMENT_ORDER_ID_FKEY, Keys.INVENTORY__INVENTORY_SALES_ORDER_ID_FKEY, Keys.INVENTORY__INVENTORY_MANUFACTURING_ORDER_ID_FKEY, Keys.INVENTORY__INVENTORY_SUPPLIER_ID_FKEY, Keys.INVENTORY__INVENTORY_TYPE_ID_FKEY, Keys.INVENTORY__INVENTORY_RECEPTION_RECEIPT_ID_FKEY, Keys.INVENTORY__INVENTORY_SERVICING_ORDER_ID_FKEY);
    }

    private transient Account _account;
    private transient MaterialGood _materialGood;
    private transient InventoryAdjustmentOrder _inventoryAdjustmentOrder;
    private transient SalesOrder _salesOrder;
    private transient ManufacturingOrder _manufacturingOrder;
    private transient Company _company;
    private transient InventoryUnit _inventoryUnit;
    private transient ReceptionReceipt _receptionReceipt;
    private transient ServicingOrder _servicingOrder;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.INVENTORY__INVENTORY_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.material_good</code>
     * table.
     */
    public MaterialGood materialGood() {
        if (_materialGood == null)
            _materialGood = new MaterialGood(this, Keys.INVENTORY__INVENTORY_MATERIAL_GOOD_ID_FKEY);

        return _materialGood;
    }

    /**
     * Get the implicit join path to the
     * <code>public.inventory_adjustment_order</code> table.
     */
    public InventoryAdjustmentOrder inventoryAdjustmentOrder() {
        if (_inventoryAdjustmentOrder == null)
            _inventoryAdjustmentOrder = new InventoryAdjustmentOrder(this, Keys.INVENTORY__INVENTORY_INVENTORY_ADJUSTMENT_ORDER_ID_FKEY);

        return _inventoryAdjustmentOrder;
    }

    /**
     * Get the implicit join path to the <code>public.sales_order</code> table.
     */
    public SalesOrder salesOrder() {
        if (_salesOrder == null)
            _salesOrder = new SalesOrder(this, Keys.INVENTORY__INVENTORY_SALES_ORDER_ID_FKEY);

        return _salesOrder;
    }

    /**
     * Get the implicit join path to the <code>public.manufacturing_order</code>
     * table.
     */
    public ManufacturingOrder manufacturingOrder() {
        if (_manufacturingOrder == null)
            _manufacturingOrder = new ManufacturingOrder(this, Keys.INVENTORY__INVENTORY_MANUFACTURING_ORDER_ID_FKEY);

        return _manufacturingOrder;
    }

    /**
     * Get the implicit join path to the <code>public.company</code> table.
     */
    public Company company() {
        if (_company == null)
            _company = new Company(this, Keys.INVENTORY__INVENTORY_SUPPLIER_ID_FKEY);

        return _company;
    }

    /**
     * Get the implicit join path to the <code>public.inventory_unit</code>
     * table.
     */
    public InventoryUnit inventoryUnit() {
        if (_inventoryUnit == null)
            _inventoryUnit = new InventoryUnit(this, Keys.INVENTORY__INVENTORY_TYPE_ID_FKEY);

        return _inventoryUnit;
    }

    /**
     * Get the implicit join path to the <code>public.reception_receipt</code>
     * table.
     */
    public ReceptionReceipt receptionReceipt() {
        if (_receptionReceipt == null)
            _receptionReceipt = new ReceptionReceipt(this, Keys.INVENTORY__INVENTORY_RECEPTION_RECEIPT_ID_FKEY);

        return _receptionReceipt;
    }

    /**
     * Get the implicit join path to the <code>public.servicing_order</code>
     * table.
     */
    public ServicingOrder servicingOrder() {
        if (_servicingOrder == null)
            _servicingOrder = new ServicingOrder(this, Keys.INVENTORY__INVENTORY_SERVICING_ORDER_ID_FKEY);

        return _servicingOrder;
    }

    @Override
    public Inventory as(String alias) {
        return new Inventory(DSL.name(alias), this);
    }

    @Override
    public Inventory as(Name alias) {
        return new Inventory(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Inventory rename(String name) {
        return new Inventory(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public Inventory rename(Name name) {
        return new Inventory(name, null);
    }

    // -------------------------------------------------------------------------
    // Row20 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row20<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, UUID, UUID, UUID, UUID, LocalDateTime, LocalDateTime, Long, Currency, BigDecimal, UUID, UUID, Long, Currency, UUID> fieldsRow() {
        return (Row20) super.fieldsRow();
    }
}
