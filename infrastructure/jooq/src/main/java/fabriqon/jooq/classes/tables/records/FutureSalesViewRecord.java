/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.FutureSalesView;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class FutureSalesViewRecord extends TableRecordImpl<FutureSalesViewRecord> implements Record7<UUID, LocalDateTime, UUID, String, Integer, BigDecimal, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.future_sales_view.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.future_sales_view.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.future_sales_view.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.future_sales_view.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.future_sales_view.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.future_sales_view.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(2);
    }

    /**
     * Setter for <code>public.future_sales_view.name</code>.
     */
    public void setName(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.future_sales_view.name</code>.
     */
    public String getName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>public.future_sales_view.quantity</code>.
     */
    public void setQuantity(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.future_sales_view.quantity</code>.
     */
    public Integer getQuantity() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>public.future_sales_view.exit_price</code>.
     */
    public void setExitPrice(BigDecimal value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.future_sales_view.exit_price</code>.
     */
    public BigDecimal getExitPrice() {
        return (BigDecimal) get(5);
    }

    /**
     * Setter for <code>public.future_sales_view.product</code>.
     */
    public void setProduct(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.future_sales_view.product</code>.
     */
    public String getProduct() {
        return (String) get(6);
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row7<UUID, LocalDateTime, UUID, String, Integer, BigDecimal, String> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    @Override
    public Row7<UUID, LocalDateTime, UUID, String, Integer, BigDecimal, String> valuesRow() {
        return (Row7) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return FutureSalesView.FUTURE_SALES_VIEW.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return FutureSalesView.FUTURE_SALES_VIEW.CREATE_TIME;
    }

    @Override
    public Field<UUID> field3() {
        return FutureSalesView.FUTURE_SALES_VIEW.OWNER_ID;
    }

    @Override
    public Field<String> field4() {
        return FutureSalesView.FUTURE_SALES_VIEW.NAME;
    }

    @Override
    public Field<Integer> field5() {
        return FutureSalesView.FUTURE_SALES_VIEW.QUANTITY;
    }

    @Override
    public Field<BigDecimal> field6() {
        return FutureSalesView.FUTURE_SALES_VIEW.EXIT_PRICE;
    }

    @Override
    public Field<String> field7() {
        return FutureSalesView.FUTURE_SALES_VIEW.PRODUCT;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public UUID component3() {
        return getOwnerId();
    }

    @Override
    public String component4() {
        return getName();
    }

    @Override
    public Integer component5() {
        return getQuantity();
    }

    @Override
    public BigDecimal component6() {
        return getExitPrice();
    }

    @Override
    public String component7() {
        return getProduct();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public UUID value3() {
        return getOwnerId();
    }

    @Override
    public String value4() {
        return getName();
    }

    @Override
    public Integer value5() {
        return getQuantity();
    }

    @Override
    public BigDecimal value6() {
        return getExitPrice();
    }

    @Override
    public String value7() {
        return getProduct();
    }

    @Override
    public FutureSalesViewRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public FutureSalesViewRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public FutureSalesViewRecord value3(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public FutureSalesViewRecord value4(String value) {
        setName(value);
        return this;
    }

    @Override
    public FutureSalesViewRecord value5(Integer value) {
        setQuantity(value);
        return this;
    }

    @Override
    public FutureSalesViewRecord value6(BigDecimal value) {
        setExitPrice(value);
        return this;
    }

    @Override
    public FutureSalesViewRecord value7(String value) {
        setProduct(value);
        return this;
    }

    @Override
    public FutureSalesViewRecord values(UUID value1, LocalDateTime value2, UUID value3, String value4, Integer value5, BigDecimal value6, String value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached FutureSalesViewRecord
     */
    public FutureSalesViewRecord() {
        super(FutureSalesView.FUTURE_SALES_VIEW);
    }

    /**
     * Create a detached, initialised FutureSalesViewRecord
     */
    public FutureSalesViewRecord(UUID id, LocalDateTime createTime, UUID ownerId, String name, Integer quantity, BigDecimal exitPrice, String product) {
        super(FutureSalesView.FUTURE_SALES_VIEW);

        setId(id);
        setCreateTime(createTime);
        setOwnerId(ownerId);
        setName(name);
        setQuantity(quantity);
        setExitPrice(exitPrice);
        setProduct(product);
    }
}
