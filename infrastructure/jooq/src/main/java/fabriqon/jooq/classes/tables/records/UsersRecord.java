/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.Users;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.Record10;
import org.jooq.Row10;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class UsersRecord extends UpdatableRecordImpl<UsersRecord> implements Record10<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, String, JSONB, Object, Boolean> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.users.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.users.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.users.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.users.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.users.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.users.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.users.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.users.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.users.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.users.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.users.external_id</code>.
     */
    public void setExternalId(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.users.external_id</code>.
     */
    public String getExternalId() {
        return (String) get(5);
    }

    /**
     * Setter for <code>public.users.name</code>.
     */
    public void setName(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.users.name</code>.
     */
    public String getName() {
        return (String) get(6);
    }

    /**
     * Setter for <code>public.users.details</code>.
     */
    public void setDetails(JSONB value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.users.details</code>.
     */
    public JSONB getDetails() {
        return (JSONB) get(7);
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public void setTextSearch(Object value) {
        set(8, value);
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public Object getTextSearch() {
        return get(8);
    }

    /**
     * Setter for <code>public.users.hidden</code>.
     */
    public void setHidden(Boolean value) {
        set(9, value);
    }

    /**
     * Getter for <code>public.users.hidden</code>.
     */
    public Boolean getHidden() {
        return (Boolean) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record10 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row10<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, String, JSONB, Object, Boolean> fieldsRow() {
        return (Row10) super.fieldsRow();
    }

    @Override
    public Row10<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, String, JSONB, Object, Boolean> valuesRow() {
        return (Row10) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return Users.USERS.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return Users.USERS.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return Users.USERS.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return Users.USERS.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return Users.USERS.OWNER_ID;
    }

    @Override
    public Field<String> field6() {
        return Users.USERS.EXTERNAL_ID;
    }

    @Override
    public Field<String> field7() {
        return Users.USERS.NAME;
    }

    @Override
    public Field<JSONB> field8() {
        return Users.USERS.DETAILS;
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public Field<Object> field9() {
        return Users.USERS.TEXT_SEARCH;
    }

    @Override
    public Field<Boolean> field10() {
        return Users.USERS.HIDDEN;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public String component6() {
        return getExternalId();
    }

    @Override
    public String component7() {
        return getName();
    }

    @Override
    public JSONB component8() {
        return getDetails();
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public Object component9() {
        return getTextSearch();
    }

    @Override
    public Boolean component10() {
        return getHidden();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public String value6() {
        return getExternalId();
    }

    @Override
    public String value7() {
        return getName();
    }

    @Override
    public JSONB value8() {
        return getDetails();
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public Object value9() {
        return getTextSearch();
    }

    @Override
    public Boolean value10() {
        return getHidden();
    }

    @Override
    public UsersRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public UsersRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public UsersRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public UsersRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public UsersRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public UsersRecord value6(String value) {
        setExternalId(value);
        return this;
    }

    @Override
    public UsersRecord value7(String value) {
        setName(value);
        return this;
    }

    @Override
    public UsersRecord value8(JSONB value) {
        setDetails(value);
        return this;
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public UsersRecord value9(Object value) {
        setTextSearch(value);
        return this;
    }

    @Override
    public UsersRecord value10(Boolean value) {
        setHidden(value);
        return this;
    }

    @Override
    public UsersRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, String value6, String value7, JSONB value8, Object value9, Boolean value10) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UsersRecord
     */
    public UsersRecord() {
        super(Users.USERS);
    }

    /**
     * Create a detached, initialised UsersRecord
     */
    public UsersRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, String externalId, String name, JSONB details, Object textSearch, Boolean hidden) {
        super(Users.USERS);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setExternalId(externalId);
        setName(name);
        setDetails(details);
        setTextSearch(textSearch);
        setHidden(hidden);
    }
}
