/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.ServicingorderFileRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row4;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ServicingorderFile extends TableImpl<ServicingorderFileRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.servicingorder_file</code>
     */
    public static final ServicingorderFile SERVICINGORDER_FILE = new ServicingorderFile();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ServicingorderFileRecord> getRecordType() {
        return ServicingorderFileRecord.class;
    }

    /**
     * The column <code>public.servicingorder_file.create_time</code>.
     */
    public final TableField<ServicingorderFileRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.servicingorder_file.owner_id</code>.
     */
    public final TableField<ServicingorderFileRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.servicingorder_file.servicing_order_id</code>.
     */
    public final TableField<ServicingorderFileRecord, UUID> SERVICING_ORDER_ID = createField(DSL.name("servicing_order_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.servicingorder_file.file_id</code>.
     */
    public final TableField<ServicingorderFileRecord, UUID> FILE_ID = createField(DSL.name("file_id"), SQLDataType.UUID.nullable(false), this, "");

    private ServicingorderFile(Name alias, Table<ServicingorderFileRecord> aliased) {
        this(alias, aliased, null);
    }

    private ServicingorderFile(Name alias, Table<ServicingorderFileRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.servicingorder_file</code> table reference
     */
    public ServicingorderFile(String alias) {
        this(DSL.name(alias), SERVICINGORDER_FILE);
    }

    /**
     * Create an aliased <code>public.servicingorder_file</code> table reference
     */
    public ServicingorderFile(Name alias) {
        this(alias, SERVICINGORDER_FILE);
    }

    /**
     * Create a <code>public.servicingorder_file</code> table reference
     */
    public ServicingorderFile() {
        this(DSL.name("servicingorder_file"), null);
    }

    public <O extends Record> ServicingorderFile(Table<O> child, ForeignKey<O, ServicingorderFileRecord> key) {
        super(child, key, SERVICINGORDER_FILE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<ForeignKey<ServicingorderFileRecord, ?>> getReferences() {
        return Arrays.asList(Keys.SERVICINGORDER_FILE__SERVICINGORDER_FILE_OWNER_ID_FKEY, Keys.SERVICINGORDER_FILE__SERVICINGORDER_FILE_SERVICING_ORDER_ID_FKEY, Keys.SERVICINGORDER_FILE__SERVICINGORDER_FILE_FILE_ID_FKEY);
    }

    private transient Account _account;
    private transient ServicingOrder _servicingOrder;
    private transient File _file;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.SERVICINGORDER_FILE__SERVICINGORDER_FILE_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.servicing_order</code>
     * table.
     */
    public ServicingOrder servicingOrder() {
        if (_servicingOrder == null)
            _servicingOrder = new ServicingOrder(this, Keys.SERVICINGORDER_FILE__SERVICINGORDER_FILE_SERVICING_ORDER_ID_FKEY);

        return _servicingOrder;
    }

    /**
     * Get the implicit join path to the <code>public.file</code> table.
     */
    public File file() {
        if (_file == null)
            _file = new File(this, Keys.SERVICINGORDER_FILE__SERVICINGORDER_FILE_FILE_ID_FKEY);

        return _file;
    }

    @Override
    public ServicingorderFile as(String alias) {
        return new ServicingorderFile(DSL.name(alias), this);
    }

    @Override
    public ServicingorderFile as(Name alias) {
        return new ServicingorderFile(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ServicingorderFile rename(String name) {
        return new ServicingorderFile(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ServicingorderFile rename(Name name) {
        return new ServicingorderFile(name, null);
    }

    // -------------------------------------------------------------------------
    // Row4 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row4<LocalDateTime, UUID, UUID, UUID> fieldsRow() {
        return (Row4) super.fieldsRow();
    }
}
