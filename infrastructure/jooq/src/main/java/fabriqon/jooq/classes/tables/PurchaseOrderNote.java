/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.PurchaseOrderNoteRecord;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row2;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PurchaseOrderNote extends TableImpl<PurchaseOrderNoteRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.purchase_order_note</code>
     */
    public static final PurchaseOrderNote PURCHASE_ORDER_NOTE = new PurchaseOrderNote();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<PurchaseOrderNoteRecord> getRecordType() {
        return PurchaseOrderNoteRecord.class;
    }

    /**
     * The column <code>public.purchase_order_note.purchase_order_id</code>.
     */
    public final TableField<PurchaseOrderNoteRecord, UUID> PURCHASE_ORDER_ID = createField(DSL.name("purchase_order_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.purchase_order_note.note_id</code>.
     */
    public final TableField<PurchaseOrderNoteRecord, UUID> NOTE_ID = createField(DSL.name("note_id"), SQLDataType.UUID.nullable(false), this, "");

    private PurchaseOrderNote(Name alias, Table<PurchaseOrderNoteRecord> aliased) {
        this(alias, aliased, null);
    }

    private PurchaseOrderNote(Name alias, Table<PurchaseOrderNoteRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.purchase_order_note</code> table reference
     */
    public PurchaseOrderNote(String alias) {
        this(DSL.name(alias), PURCHASE_ORDER_NOTE);
    }

    /**
     * Create an aliased <code>public.purchase_order_note</code> table reference
     */
    public PurchaseOrderNote(Name alias) {
        this(alias, PURCHASE_ORDER_NOTE);
    }

    /**
     * Create a <code>public.purchase_order_note</code> table reference
     */
    public PurchaseOrderNote() {
        this(DSL.name("purchase_order_note"), null);
    }

    public <O extends Record> PurchaseOrderNote(Table<O> child, ForeignKey<O, PurchaseOrderNoteRecord> key) {
        super(child, key, PURCHASE_ORDER_NOTE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<ForeignKey<PurchaseOrderNoteRecord, ?>> getReferences() {
        return Arrays.asList(Keys.PURCHASE_ORDER_NOTE__PURCHASE_ORDER_NOTE_PURCHASE_ORDER_ID_FKEY, Keys.PURCHASE_ORDER_NOTE__PURCHASE_ORDER_NOTE_NOTE_ID_FKEY);
    }

    private transient PurchaseOrder _purchaseOrder;
    private transient Note _note;

    /**
     * Get the implicit join path to the <code>public.purchase_order</code>
     * table.
     */
    public PurchaseOrder purchaseOrder() {
        if (_purchaseOrder == null)
            _purchaseOrder = new PurchaseOrder(this, Keys.PURCHASE_ORDER_NOTE__PURCHASE_ORDER_NOTE_PURCHASE_ORDER_ID_FKEY);

        return _purchaseOrder;
    }

    /**
     * Get the implicit join path to the <code>public.note</code> table.
     */
    public Note note() {
        if (_note == null)
            _note = new Note(this, Keys.PURCHASE_ORDER_NOTE__PURCHASE_ORDER_NOTE_NOTE_ID_FKEY);

        return _note;
    }

    @Override
    public PurchaseOrderNote as(String alias) {
        return new PurchaseOrderNote(DSL.name(alias), this);
    }

    @Override
    public PurchaseOrderNote as(Name alias) {
        return new PurchaseOrderNote(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public PurchaseOrderNote rename(String name) {
        return new PurchaseOrderNote(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public PurchaseOrderNote rename(Name name) {
        return new PurchaseOrderNote(name, null);
    }

    // -------------------------------------------------------------------------
    // Row2 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row2<UUID, UUID> fieldsRow() {
        return (Row2) super.fieldsRow();
    }
}
