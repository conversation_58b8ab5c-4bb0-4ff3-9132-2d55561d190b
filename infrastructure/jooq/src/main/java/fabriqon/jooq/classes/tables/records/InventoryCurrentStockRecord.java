/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.InventoryCurrentStock;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class InventoryCurrentStockRecord extends TableRecordImpl<InventoryCurrentStockRecord> implements Record7<LocalDateTime, UUID, UUID, BigDecimal, String, Long, Long> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.inventory_current_stock.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.inventory_current_stock.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(0);
    }

    /**
     * Setter for <code>public.inventory_current_stock.material_good_id</code>.
     */
    public void setMaterialGoodId(UUID value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.inventory_current_stock.material_good_id</code>.
     */
    public UUID getMaterialGoodId() {
        return (UUID) get(1);
    }

    /**
     * Setter for <code>public.inventory_current_stock.unit_id</code>.
     */
    public void setUnitId(UUID value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.inventory_current_stock.unit_id</code>.
     */
    public UUID getUnitId() {
        return (UUID) get(2);
    }

    /**
     * Setter for <code>public.inventory_current_stock.quantity</code>.
     */
    public void setQuantity(BigDecimal value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.inventory_current_stock.quantity</code>.
     */
    public BigDecimal getQuantity() {
        return (BigDecimal) get(3);
    }

    /**
     * Setter for <code>public.inventory_current_stock.currency</code>.
     */
    public void setCurrency(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.inventory_current_stock.currency</code>.
     */
    public String getCurrency() {
        return (String) get(4);
    }

    /**
     * Setter for <code>public.inventory_current_stock.cost</code>.
     */
    public void setCost(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.inventory_current_stock.cost</code>.
     */
    public Long getCost() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>public.inventory_current_stock.total_value</code>.
     */
    public void setTotalValue(Long value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.inventory_current_stock.total_value</code>.
     */
    public Long getTotalValue() {
        return (Long) get(6);
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row7<LocalDateTime, UUID, UUID, BigDecimal, String, Long, Long> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    @Override
    public Row7<LocalDateTime, UUID, UUID, BigDecimal, String, Long, Long> valuesRow() {
        return (Row7) super.valuesRow();
    }

    @Override
    public Field<LocalDateTime> field1() {
        return InventoryCurrentStock.INVENTORY_CURRENT_STOCK.UPDATE_TIME;
    }

    @Override
    public Field<UUID> field2() {
        return InventoryCurrentStock.INVENTORY_CURRENT_STOCK.MATERIAL_GOOD_ID;
    }

    @Override
    public Field<UUID> field3() {
        return InventoryCurrentStock.INVENTORY_CURRENT_STOCK.UNIT_ID;
    }

    @Override
    public Field<BigDecimal> field4() {
        return InventoryCurrentStock.INVENTORY_CURRENT_STOCK.QUANTITY;
    }

    @Override
    public Field<String> field5() {
        return InventoryCurrentStock.INVENTORY_CURRENT_STOCK.CURRENCY;
    }

    @Override
    public Field<Long> field6() {
        return InventoryCurrentStock.INVENTORY_CURRENT_STOCK.COST;
    }

    @Override
    public Field<Long> field7() {
        return InventoryCurrentStock.INVENTORY_CURRENT_STOCK.TOTAL_VALUE;
    }

    @Override
    public LocalDateTime component1() {
        return getUpdateTime();
    }

    @Override
    public UUID component2() {
        return getMaterialGoodId();
    }

    @Override
    public UUID component3() {
        return getUnitId();
    }

    @Override
    public BigDecimal component4() {
        return getQuantity();
    }

    @Override
    public String component5() {
        return getCurrency();
    }

    @Override
    public Long component6() {
        return getCost();
    }

    @Override
    public Long component7() {
        return getTotalValue();
    }

    @Override
    public LocalDateTime value1() {
        return getUpdateTime();
    }

    @Override
    public UUID value2() {
        return getMaterialGoodId();
    }

    @Override
    public UUID value3() {
        return getUnitId();
    }

    @Override
    public BigDecimal value4() {
        return getQuantity();
    }

    @Override
    public String value5() {
        return getCurrency();
    }

    @Override
    public Long value6() {
        return getCost();
    }

    @Override
    public Long value7() {
        return getTotalValue();
    }

    @Override
    public InventoryCurrentStockRecord value1(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public InventoryCurrentStockRecord value2(UUID value) {
        setMaterialGoodId(value);
        return this;
    }

    @Override
    public InventoryCurrentStockRecord value3(UUID value) {
        setUnitId(value);
        return this;
    }

    @Override
    public InventoryCurrentStockRecord value4(BigDecimal value) {
        setQuantity(value);
        return this;
    }

    @Override
    public InventoryCurrentStockRecord value5(String value) {
        setCurrency(value);
        return this;
    }

    @Override
    public InventoryCurrentStockRecord value6(Long value) {
        setCost(value);
        return this;
    }

    @Override
    public InventoryCurrentStockRecord value7(Long value) {
        setTotalValue(value);
        return this;
    }

    @Override
    public InventoryCurrentStockRecord values(LocalDateTime value1, UUID value2, UUID value3, BigDecimal value4, String value5, Long value6, Long value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached InventoryCurrentStockRecord
     */
    public InventoryCurrentStockRecord() {
        super(InventoryCurrentStock.INVENTORY_CURRENT_STOCK);
    }

    /**
     * Create a detached, initialised InventoryCurrentStockRecord
     */
    public InventoryCurrentStockRecord(LocalDateTime updateTime, UUID materialGoodId, UUID unitId, BigDecimal quantity, String currency, Long cost, Long totalValue) {
        super(InventoryCurrentStock.INVENTORY_CURRENT_STOCK);

        setUpdateTime(updateTime);
        setMaterialGoodId(materialGoodId);
        setUnitId(unitId);
        setQuantity(quantity);
        setCurrency(currency);
        setCost(cost);
        setTotalValue(totalValue);
    }
}
