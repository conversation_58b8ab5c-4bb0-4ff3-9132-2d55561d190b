/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.ManufacturingWorkstation;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ManufacturingWorkstationRecord extends UpdatableRecordImpl<ManufacturingWorkstationRecord> implements Record7<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, JSONB> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.manufacturing_workstation.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.manufacturing_workstation.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.manufacturing_workstation.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.manufacturing_workstation.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.manufacturing_workstation.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.manufacturing_workstation.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.manufacturing_workstation.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.manufacturing_workstation.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.manufacturing_workstation.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.manufacturing_workstation.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.manufacturing_workstation.name</code>.
     */
    public void setName(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.manufacturing_workstation.name</code>.
     */
    public String getName() {
        return (String) get(5);
    }

    /**
     * Setter for <code>public.manufacturing_workstation.details</code>.
     */
    public void setDetails(JSONB value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.manufacturing_workstation.details</code>.
     */
    public JSONB getDetails() {
        return (JSONB) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row7<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, JSONB> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    @Override
    public Row7<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, JSONB> valuesRow() {
        return (Row7) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return ManufacturingWorkstation.MANUFACTURING_WORKSTATION.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return ManufacturingWorkstation.MANUFACTURING_WORKSTATION.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return ManufacturingWorkstation.MANUFACTURING_WORKSTATION.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return ManufacturingWorkstation.MANUFACTURING_WORKSTATION.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return ManufacturingWorkstation.MANUFACTURING_WORKSTATION.OWNER_ID;
    }

    @Override
    public Field<String> field6() {
        return ManufacturingWorkstation.MANUFACTURING_WORKSTATION.NAME;
    }

    @Override
    public Field<JSONB> field7() {
        return ManufacturingWorkstation.MANUFACTURING_WORKSTATION.DETAILS;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public String component6() {
        return getName();
    }

    @Override
    public JSONB component7() {
        return getDetails();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public String value6() {
        return getName();
    }

    @Override
    public JSONB value7() {
        return getDetails();
    }

    @Override
    public ManufacturingWorkstationRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public ManufacturingWorkstationRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public ManufacturingWorkstationRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public ManufacturingWorkstationRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public ManufacturingWorkstationRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public ManufacturingWorkstationRecord value6(String value) {
        setName(value);
        return this;
    }

    @Override
    public ManufacturingWorkstationRecord value7(JSONB value) {
        setDetails(value);
        return this;
    }

    @Override
    public ManufacturingWorkstationRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, String value6, JSONB value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ManufacturingWorkstationRecord
     */
    public ManufacturingWorkstationRecord() {
        super(ManufacturingWorkstation.MANUFACTURING_WORKSTATION);
    }

    /**
     * Create a detached, initialised ManufacturingWorkstationRecord
     */
    public ManufacturingWorkstationRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, String name, JSONB details) {
        super(ManufacturingWorkstation.MANUFACTURING_WORKSTATION);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setName(name);
        setDetails(details);
    }
}
