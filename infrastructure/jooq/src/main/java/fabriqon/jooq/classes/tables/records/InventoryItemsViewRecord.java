/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.InventoryItemsView;

import java.math.BigDecimal;
import java.util.Currency;
import java.util.UUID;

import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class InventoryItemsViewRecord extends TableRecordImpl<InventoryItemsViewRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.inventory_items_view.material_good_id</code>.
     */
    public void setMaterialGoodId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.material_good_id</code>.
     */
    public UUID getMaterialGoodId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.inventory_items_view.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(1);
    }

    /**
     * Setter for <code>public.inventory_items_view.name</code>.
     */
    public void setName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.name</code>.
     */
    public String getName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>public.inventory_items_view.code</code>.
     */
    public void setCode(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.code</code>.
     */
    public String getCode() {
        return (String) get(3);
    }

    /**
     * Setter for <code>public.inventory_items_view.external_code</code>.
     */
    public void setExternalCode(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.external_code</code>.
     */
    public String getExternalCode() {
        return (String) get(4);
    }

    /**
     * Setter for <code>public.inventory_items_view.description</code>.
     */
    public void setDescription(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.description</code>.
     */
    public String getDescription() {
        return (String) get(5);
    }

    /**
     * Setter for <code>public.inventory_items_view.critical_on_hand</code>.
     */
    public void setCriticalOnHand(BigDecimal value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.critical_on_hand</code>.
     */
    public BigDecimal getCriticalOnHand() {
        return (BigDecimal) get(6);
    }

    /**
     * Setter for <code>public.inventory_items_view.produced</code>.
     */
    public void setProduced(Boolean value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.produced</code>.
     */
    public Boolean getProduced() {
        return (Boolean) get(7);
    }

    /**
     * Setter for <code>public.inventory_items_view.unit_of_production</code>.
     */
    public void setUnitOfProduction(BigDecimal value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.unit_of_production</code>.
     */
    public BigDecimal getUnitOfProduction() {
        return (BigDecimal) get(8);
    }

    /**
     * Setter for <code>public.inventory_items_view.measurement_unit</code>.
     */
    public void setMeasurementUnit(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.measurement_unit</code>.
     */
    public String getMeasurementUnit() {
        return (String) get(9);
    }

    /**
     * Setter for <code>public.inventory_items_view.sell_price_amount</code>.
     */
    public void setSellPriceAmount(Long value) {
        set(10, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.sell_price_amount</code>.
     */
    public Long getSellPriceAmount() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>public.inventory_items_view.sell_price_currency</code>.
     */
    public void setSellPriceCurrency(Currency value) {
        set(11, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.sell_price_currency</code>.
     */
    public Currency getSellPriceCurrency() {
        return (Currency) get(11);
    }

    /**
     * Setter for <code>public.inventory_items_view.category_id</code>.
     */
    public void setCategoryId(UUID value) {
        set(12, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.category_id</code>.
     */
    public UUID getCategoryId() {
        return (UUID) get(12);
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public void setTextSearch(Object value) {
        set(13, value);
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public Object getTextSearch() {
        return get(13);
    }

    /**
     * Setter for <code>public.inventory_items_view.category_name</code>.
     */
    public void setCategoryName(String value) {
        set(14, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.category_name</code>.
     */
    public String getCategoryName() {
        return (String) get(14);
    }

    /**
     * Setter for <code>public.inventory_items_view.inventory_unit_id</code>.
     */
    public void setInventoryUnitId(UUID value) {
        set(15, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.inventory_unit_id</code>.
     */
    public UUID getInventoryUnitId() {
        return (UUID) get(15);
    }

    /**
     * Setter for <code>public.inventory_items_view.inventory_unit_name</code>.
     */
    public void setInventoryUnitName(String value) {
        set(16, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.inventory_unit_name</code>.
     */
    public String getInventoryUnitName() {
        return (String) get(16);
    }

    /**
     * Setter for <code>public.inventory_items_view.currency</code>.
     */
    public void setCurrency(String value) {
        set(17, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.currency</code>.
     */
    public String getCurrency() {
        return (String) get(17);
    }

    /**
     * Setter for <code>public.inventory_items_view.quantity</code>.
     */
    public void setQuantity(BigDecimal value) {
        set(18, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.quantity</code>.
     */
    public BigDecimal getQuantity() {
        return (BigDecimal) get(18);
    }

    /**
     * Setter for <code>public.inventory_items_view.cost</code>.
     */
    public void setCost(Long value) {
        set(19, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.cost</code>.
     */
    public Long getCost() {
        return (Long) get(19);
    }

    /**
     * Setter for <code>public.inventory_items_view.total_value</code>.
     */
    public void setTotalValue(Long value) {
        set(20, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.total_value</code>.
     */
    public Long getTotalValue() {
        return (Long) get(20);
    }

    /**
     * Setter for <code>public.inventory_items_view.committed_quantity</code>.
     */
    public void setCommittedQuantity(BigDecimal value) {
        set(21, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.committed_quantity</code>.
     */
    public BigDecimal getCommittedQuantity() {
        return (BigDecimal) get(21);
    }

    /**
     * Setter for <code>public.inventory_items_view.incoming_quantity</code>.
     */
    public void setIncomingQuantity(BigDecimal value) {
        set(22, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.incoming_quantity</code>.
     */
    public BigDecimal getIncomingQuantity() {
        return (BigDecimal) get(22);
    }

    /**
     * Setter for <code>public.inventory_items_view.supplier_id</code>.
     */
    public void setSupplierId(UUID value) {
        set(23, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.supplier_id</code>.
     */
    public UUID getSupplierId() {
        return (UUID) get(23);
    }

    /**
     * Setter for <code>public.inventory_items_view.supplier_name</code>.
     */
    public void setSupplierName(String value) {
        set(24, value);
    }

    /**
     * Getter for <code>public.inventory_items_view.supplier_name</code>.
     */
    public String getSupplierName() {
        return (String) get(24);
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached InventoryItemsViewRecord
     */
    public InventoryItemsViewRecord() {
        super(InventoryItemsView.INVENTORY_ITEMS_VIEW);
    }

    /**
     * Create a detached, initialised InventoryItemsViewRecord
     */
    public InventoryItemsViewRecord(UUID materialGoodId, UUID ownerId, String name, String code, String externalCode, String description, BigDecimal criticalOnHand, Boolean produced, BigDecimal unitOfProduction, String measurementUnit, Long sellPriceAmount, Currency sellPriceCurrency, UUID categoryId, Object textSearch, String categoryName, UUID inventoryUnitId, String inventoryUnitName, String currency, BigDecimal quantity, Long cost, Long totalValue, BigDecimal committedQuantity, BigDecimal incomingQuantity, UUID supplierId, String supplierName) {
        super(InventoryItemsView.INVENTORY_ITEMS_VIEW);

        setMaterialGoodId(materialGoodId);
        setOwnerId(ownerId);
        setName(name);
        setCode(code);
        setExternalCode(externalCode);
        setDescription(description);
        setCriticalOnHand(criticalOnHand);
        setProduced(produced);
        setUnitOfProduction(unitOfProduction);
        setMeasurementUnit(measurementUnit);
        setSellPriceAmount(sellPriceAmount);
        setSellPriceCurrency(sellPriceCurrency);
        setCategoryId(categoryId);
        setTextSearch(textSearch);
        setCategoryName(categoryName);
        setInventoryUnitId(inventoryUnitId);
        setInventoryUnitName(inventoryUnitName);
        setCurrency(currency);
        setQuantity(quantity);
        setCost(cost);
        setTotalValue(totalValue);
        setCommittedQuantity(committedQuantity);
        setIncomingQuantity(incomingQuantity);
        setSupplierId(supplierId);
        setSupplierName(supplierName);
    }
}
