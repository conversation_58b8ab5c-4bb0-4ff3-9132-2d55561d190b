/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.ReservedInventoryRecord;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row14;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ReservedInventory extends TableImpl<ReservedInventoryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.reserved_inventory</code>
     */
    public static final ReservedInventory RESERVED_INVENTORY = new ReservedInventory();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ReservedInventoryRecord> getRecordType() {
        return ReservedInventoryRecord.class;
    }

    /**
     * The column <code>public.reserved_inventory.id</code>.
     */
    public final TableField<ReservedInventoryRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.reserved_inventory.create_time</code>.
     */
    public final TableField<ReservedInventoryRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.reserved_inventory.update_time</code>.
     */
    public final TableField<ReservedInventoryRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.reserved_inventory.deleted</code>.
     */
    public final TableField<ReservedInventoryRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.reserved_inventory.owner_id</code>.
     */
    public final TableField<ReservedInventoryRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.reserved_inventory.material_good_id</code>.
     */
    public final TableField<ReservedInventoryRecord, UUID> MATERIAL_GOOD_ID = createField(DSL.name("material_good_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.reserved_inventory.location_id</code>.
     */
    public final TableField<ReservedInventoryRecord, UUID> LOCATION_ID = createField(DSL.name("location_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.reserved_inventory.sales_order_id</code>.
     */
    public final TableField<ReservedInventoryRecord, UUID> SALES_ORDER_ID = createField(DSL.name("sales_order_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.reserved_inventory.manufacturing_order_id</code>.
     */
    public final TableField<ReservedInventoryRecord, UUID> MANUFACTURING_ORDER_ID = createField(DSL.name("manufacturing_order_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.reserved_inventory.quantity</code>.
     */
    public final TableField<ReservedInventoryRecord, BigDecimal> QUANTITY = createField(DSL.name("quantity"), SQLDataType.NUMERIC(12, 4), this, "");

    /**
     * The column <code>public.reserved_inventory.inventory_unit_id</code>.
     */
    public final TableField<ReservedInventoryRecord, UUID> INVENTORY_UNIT_ID = createField(DSL.name("inventory_unit_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.reserved_inventory.mandatory_for_order</code>.
     */
    public final TableField<ReservedInventoryRecord, Boolean> MANDATORY_FOR_ORDER = createField(DSL.name("mandatory_for_order"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.reserved_inventory.from_order</code>.
     */
    public final TableField<ReservedInventoryRecord, UUID> FROM_ORDER = createField(DSL.name("from_order"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.reserved_inventory.servicing_order_id</code>.
     */
    public final TableField<ReservedInventoryRecord, UUID> SERVICING_ORDER_ID = createField(DSL.name("servicing_order_id"), SQLDataType.UUID, this, "");

    private ReservedInventory(Name alias, Table<ReservedInventoryRecord> aliased) {
        this(alias, aliased, null);
    }

    private ReservedInventory(Name alias, Table<ReservedInventoryRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.reserved_inventory</code> table reference
     */
    public ReservedInventory(String alias) {
        this(DSL.name(alias), RESERVED_INVENTORY);
    }

    /**
     * Create an aliased <code>public.reserved_inventory</code> table reference
     */
    public ReservedInventory(Name alias) {
        this(alias, RESERVED_INVENTORY);
    }

    /**
     * Create a <code>public.reserved_inventory</code> table reference
     */
    public ReservedInventory() {
        this(DSL.name("reserved_inventory"), null);
    }

    public <O extends Record> ReservedInventory(Table<O> child, ForeignKey<O, ReservedInventoryRecord> key) {
        super(child, key, RESERVED_INVENTORY);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public UniqueKey<ReservedInventoryRecord> getPrimaryKey() {
        return Keys.RESERVED_INVENTORY_PKEY;
    }

    @Override
    public List<ForeignKey<ReservedInventoryRecord, ?>> getReferences() {
        return Arrays.asList(Keys.RESERVED_INVENTORY__RESERVED_INVENTORY_OWNER_ID_FKEY, Keys.RESERVED_INVENTORY__RESERVED_INVENTORY_MATERIAL_GOOD_ID_FKEY, Keys.RESERVED_INVENTORY__RESERVED_INVENTORY_LOCATION_ID_FKEY, Keys.RESERVED_INVENTORY__RESERVED_INVENTORY_SALES_ORDER_ID_FKEY, Keys.RESERVED_INVENTORY__RESERVED_INVENTORY_MANUFACTURING_ORDER_ID_FKEY, Keys.RESERVED_INVENTORY__RESERVED_INVENTORY_INVENTORY_TYPE_ID_FKEY, Keys.RESERVED_INVENTORY__RESERVED_INVENTORY_SERVICING_ORDER_ID_FKEY);
    }

    private transient Account _account;
    private transient MaterialGood _materialGood;
    private transient Location _location;
    private transient SalesOrder _salesOrder;
    private transient ManufacturingOrder _manufacturingOrder;
    private transient InventoryUnit _inventoryUnit;
    private transient ServicingOrder _servicingOrder;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.RESERVED_INVENTORY__RESERVED_INVENTORY_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.material_good</code>
     * table.
     */
    public MaterialGood materialGood() {
        if (_materialGood == null)
            _materialGood = new MaterialGood(this, Keys.RESERVED_INVENTORY__RESERVED_INVENTORY_MATERIAL_GOOD_ID_FKEY);

        return _materialGood;
    }

    /**
     * Get the implicit join path to the <code>public.location</code> table.
     */
    public Location location() {
        if (_location == null)
            _location = new Location(this, Keys.RESERVED_INVENTORY__RESERVED_INVENTORY_LOCATION_ID_FKEY);

        return _location;
    }

    /**
     * Get the implicit join path to the <code>public.sales_order</code> table.
     */
    public SalesOrder salesOrder() {
        if (_salesOrder == null)
            _salesOrder = new SalesOrder(this, Keys.RESERVED_INVENTORY__RESERVED_INVENTORY_SALES_ORDER_ID_FKEY);

        return _salesOrder;
    }

    /**
     * Get the implicit join path to the <code>public.manufacturing_order</code>
     * table.
     */
    public ManufacturingOrder manufacturingOrder() {
        if (_manufacturingOrder == null)
            _manufacturingOrder = new ManufacturingOrder(this, Keys.RESERVED_INVENTORY__RESERVED_INVENTORY_MANUFACTURING_ORDER_ID_FKEY);

        return _manufacturingOrder;
    }

    /**
     * Get the implicit join path to the <code>public.inventory_unit</code>
     * table.
     */
    public InventoryUnit inventoryUnit() {
        if (_inventoryUnit == null)
            _inventoryUnit = new InventoryUnit(this, Keys.RESERVED_INVENTORY__RESERVED_INVENTORY_INVENTORY_TYPE_ID_FKEY);

        return _inventoryUnit;
    }

    /**
     * Get the implicit join path to the <code>public.servicing_order</code>
     * table.
     */
    public ServicingOrder servicingOrder() {
        if (_servicingOrder == null)
            _servicingOrder = new ServicingOrder(this, Keys.RESERVED_INVENTORY__RESERVED_INVENTORY_SERVICING_ORDER_ID_FKEY);

        return _servicingOrder;
    }

    @Override
    public ReservedInventory as(String alias) {
        return new ReservedInventory(DSL.name(alias), this);
    }

    @Override
    public ReservedInventory as(Name alias) {
        return new ReservedInventory(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ReservedInventory rename(String name) {
        return new ReservedInventory(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ReservedInventory rename(Name name) {
        return new ReservedInventory(name, null);
    }

    // -------------------------------------------------------------------------
    // Row14 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row14<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, UUID, UUID, UUID, BigDecimal, UUID, Boolean, UUID, UUID> fieldsRow() {
        return (Row14) super.fieldsRow();
    }
}
