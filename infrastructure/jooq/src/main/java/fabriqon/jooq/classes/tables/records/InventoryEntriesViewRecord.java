/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.InventoryEntriesView;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record12;
import org.jooq.Row12;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class InventoryEntriesViewRecord extends TableRecordImpl<InventoryEntriesViewRecord> implements Record12<UUID, LocalDateTime, UUID, UUID, UUID, UUID, BigDecimal, BigDecimal, BigDecimal, String, String, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.inventory_entries_view.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.inventory_entries_view.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.inventory_entries_view.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.inventory_entries_view.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.inventory_entries_view.sales_order_id</code>.
     */
    public void setSalesOrderId(UUID value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.inventory_entries_view.sales_order_id</code>.
     */
    public UUID getSalesOrderId() {
        return (UUID) get(2);
    }

    /**
     * Setter for
     * <code>public.inventory_entries_view.manufacturing_order_id</code>.
     */
    public void setManufacturingOrderId(UUID value) {
        set(3, value);
    }

    /**
     * Getter for
     * <code>public.inventory_entries_view.manufacturing_order_id</code>.
     */
    public UUID getManufacturingOrderId() {
        return (UUID) get(3);
    }

    /**
     * Setter for
     * <code>public.inventory_entries_view.reception_receipt_id</code>.
     */
    public void setReceptionReceiptId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for
     * <code>public.inventory_entries_view.reception_receipt_id</code>.
     */
    public UUID getReceptionReceiptId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.inventory_entries_view.supplier_id</code>.
     */
    public void setSupplierId(UUID value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.inventory_entries_view.supplier_id</code>.
     */
    public UUID getSupplierId() {
        return (UUID) get(5);
    }

    /**
     * Setter for <code>public.inventory_entries_view.reception_price</code>.
     */
    public void setReceptionPrice(BigDecimal value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.inventory_entries_view.reception_price</code>.
     */
    public BigDecimal getReceptionPrice() {
        return (BigDecimal) get(6);
    }

    /**
     * Setter for <code>public.inventory_entries_view.exit_price</code>.
     */
    public void setExitPrice(BigDecimal value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.inventory_entries_view.exit_price</code>.
     */
    public BigDecimal getExitPrice() {
        return (BigDecimal) get(7);
    }

    /**
     * Setter for <code>public.inventory_entries_view.quantity</code>.
     */
    public void setQuantity(BigDecimal value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.inventory_entries_view.quantity</code>.
     */
    public BigDecimal getQuantity() {
        return (BigDecimal) get(8);
    }

    /**
     * Setter for <code>public.inventory_entries_view.item_name</code>.
     */
    public void setItemName(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>public.inventory_entries_view.item_name</code>.
     */
    public String getItemName() {
        return (String) get(9);
    }

    /**
     * Setter for <code>public.inventory_entries_view.category</code>.
     */
    public void setCategory(String value) {
        set(10, value);
    }

    /**
     * Getter for <code>public.inventory_entries_view.category</code>.
     */
    public String getCategory() {
        return (String) get(10);
    }

    /**
     * Setter for <code>public.inventory_entries_view.product</code>.
     */
    public void setProduct(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>public.inventory_entries_view.product</code>.
     */
    public String getProduct() {
        return (String) get(11);
    }

    // -------------------------------------------------------------------------
    // Record12 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row12<UUID, LocalDateTime, UUID, UUID, UUID, UUID, BigDecimal, BigDecimal, BigDecimal, String, String, String> fieldsRow() {
        return (Row12) super.fieldsRow();
    }

    @Override
    public Row12<UUID, LocalDateTime, UUID, UUID, UUID, UUID, BigDecimal, BigDecimal, BigDecimal, String, String, String> valuesRow() {
        return (Row12) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return InventoryEntriesView.INVENTORY_ENTRIES_VIEW.OWNER_ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return InventoryEntriesView.INVENTORY_ENTRIES_VIEW.CREATE_TIME;
    }

    @Override
    public Field<UUID> field3() {
        return InventoryEntriesView.INVENTORY_ENTRIES_VIEW.SALES_ORDER_ID;
    }

    @Override
    public Field<UUID> field4() {
        return InventoryEntriesView.INVENTORY_ENTRIES_VIEW.MANUFACTURING_ORDER_ID;
    }

    @Override
    public Field<UUID> field5() {
        return InventoryEntriesView.INVENTORY_ENTRIES_VIEW.RECEPTION_RECEIPT_ID;
    }

    @Override
    public Field<UUID> field6() {
        return InventoryEntriesView.INVENTORY_ENTRIES_VIEW.SUPPLIER_ID;
    }

    @Override
    public Field<BigDecimal> field7() {
        return InventoryEntriesView.INVENTORY_ENTRIES_VIEW.RECEPTION_PRICE;
    }

    @Override
    public Field<BigDecimal> field8() {
        return InventoryEntriesView.INVENTORY_ENTRIES_VIEW.EXIT_PRICE;
    }

    @Override
    public Field<BigDecimal> field9() {
        return InventoryEntriesView.INVENTORY_ENTRIES_VIEW.QUANTITY;
    }

    @Override
    public Field<String> field10() {
        return InventoryEntriesView.INVENTORY_ENTRIES_VIEW.ITEM_NAME;
    }

    @Override
    public Field<String> field11() {
        return InventoryEntriesView.INVENTORY_ENTRIES_VIEW.CATEGORY;
    }

    @Override
    public Field<String> field12() {
        return InventoryEntriesView.INVENTORY_ENTRIES_VIEW.PRODUCT;
    }

    @Override
    public UUID component1() {
        return getOwnerId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public UUID component3() {
        return getSalesOrderId();
    }

    @Override
    public UUID component4() {
        return getManufacturingOrderId();
    }

    @Override
    public UUID component5() {
        return getReceptionReceiptId();
    }

    @Override
    public UUID component6() {
        return getSupplierId();
    }

    @Override
    public BigDecimal component7() {
        return getReceptionPrice();
    }

    @Override
    public BigDecimal component8() {
        return getExitPrice();
    }

    @Override
    public BigDecimal component9() {
        return getQuantity();
    }

    @Override
    public String component10() {
        return getItemName();
    }

    @Override
    public String component11() {
        return getCategory();
    }

    @Override
    public String component12() {
        return getProduct();
    }

    @Override
    public UUID value1() {
        return getOwnerId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public UUID value3() {
        return getSalesOrderId();
    }

    @Override
    public UUID value4() {
        return getManufacturingOrderId();
    }

    @Override
    public UUID value5() {
        return getReceptionReceiptId();
    }

    @Override
    public UUID value6() {
        return getSupplierId();
    }

    @Override
    public BigDecimal value7() {
        return getReceptionPrice();
    }

    @Override
    public BigDecimal value8() {
        return getExitPrice();
    }

    @Override
    public BigDecimal value9() {
        return getQuantity();
    }

    @Override
    public String value10() {
        return getItemName();
    }

    @Override
    public String value11() {
        return getCategory();
    }

    @Override
    public String value12() {
        return getProduct();
    }

    @Override
    public InventoryEntriesViewRecord value1(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public InventoryEntriesViewRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public InventoryEntriesViewRecord value3(UUID value) {
        setSalesOrderId(value);
        return this;
    }

    @Override
    public InventoryEntriesViewRecord value4(UUID value) {
        setManufacturingOrderId(value);
        return this;
    }

    @Override
    public InventoryEntriesViewRecord value5(UUID value) {
        setReceptionReceiptId(value);
        return this;
    }

    @Override
    public InventoryEntriesViewRecord value6(UUID value) {
        setSupplierId(value);
        return this;
    }

    @Override
    public InventoryEntriesViewRecord value7(BigDecimal value) {
        setReceptionPrice(value);
        return this;
    }

    @Override
    public InventoryEntriesViewRecord value8(BigDecimal value) {
        setExitPrice(value);
        return this;
    }

    @Override
    public InventoryEntriesViewRecord value9(BigDecimal value) {
        setQuantity(value);
        return this;
    }

    @Override
    public InventoryEntriesViewRecord value10(String value) {
        setItemName(value);
        return this;
    }

    @Override
    public InventoryEntriesViewRecord value11(String value) {
        setCategory(value);
        return this;
    }

    @Override
    public InventoryEntriesViewRecord value12(String value) {
        setProduct(value);
        return this;
    }

    @Override
    public InventoryEntriesViewRecord values(UUID value1, LocalDateTime value2, UUID value3, UUID value4, UUID value5, UUID value6, BigDecimal value7, BigDecimal value8, BigDecimal value9, String value10, String value11, String value12) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached InventoryEntriesViewRecord
     */
    public InventoryEntriesViewRecord() {
        super(InventoryEntriesView.INVENTORY_ENTRIES_VIEW);
    }

    /**
     * Create a detached, initialised InventoryEntriesViewRecord
     */
    public InventoryEntriesViewRecord(UUID ownerId, LocalDateTime createTime, UUID salesOrderId, UUID manufacturingOrderId, UUID receptionReceiptId, UUID supplierId, BigDecimal receptionPrice, BigDecimal exitPrice, BigDecimal quantity, String itemName, String category, String product) {
        super(InventoryEntriesView.INVENTORY_ENTRIES_VIEW);

        setOwnerId(ownerId);
        setCreateTime(createTime);
        setSalesOrderId(salesOrderId);
        setManufacturingOrderId(manufacturingOrderId);
        setReceptionReceiptId(receptionReceiptId);
        setSupplierId(supplierId);
        setReceptionPrice(receptionPrice);
        setExitPrice(exitPrice);
        setQuantity(quantity);
        setItemName(itemName);
        setCategory(category);
        setProduct(product);
    }
}
