/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.InventoryItemsViewRecord;
import fabriqon.jooq.converters.CurrencyConverter;
import org.jooq.*;
import org.jooq.Record;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import java.math.BigDecimal;
import java.util.Currency;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class InventoryItemsView extends TableImpl<InventoryItemsViewRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.inventory_items_view</code>
     */
    public static final InventoryItemsView INVENTORY_ITEMS_VIEW = new InventoryItemsView();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<InventoryItemsViewRecord> getRecordType() {
        return InventoryItemsViewRecord.class;
    }

    /**
     * The column <code>public.inventory_items_view.material_good_id</code>.
     */
    public final TableField<InventoryItemsViewRecord, UUID> MATERIAL_GOOD_ID = createField(DSL.name("material_good_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.inventory_items_view.owner_id</code>.
     */
    public final TableField<InventoryItemsViewRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.inventory_items_view.name</code>.
     */
    public final TableField<InventoryItemsViewRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(200), this, "");

    /**
     * The column <code>public.inventory_items_view.code</code>.
     */
    public final TableField<InventoryItemsViewRecord, String> CODE = createField(DSL.name("code"), SQLDataType.VARCHAR(20), this, "");

    /**
     * The column <code>public.inventory_items_view.external_code</code>.
     */
    public final TableField<InventoryItemsViewRecord, String> EXTERNAL_CODE = createField(DSL.name("external_code"), SQLDataType.VARCHAR(20), this, "");

    /**
     * The column <code>public.inventory_items_view.description</code>.
     */
    public final TableField<InventoryItemsViewRecord, String> DESCRIPTION = createField(DSL.name("description"), SQLDataType.VARCHAR(500), this, "");

    /**
     * The column <code>public.inventory_items_view.critical_on_hand</code>.
     */
    public final TableField<InventoryItemsViewRecord, BigDecimal> CRITICAL_ON_HAND = createField(DSL.name("critical_on_hand"), SQLDataType.NUMERIC, this, "");

    /**
     * The column <code>public.inventory_items_view.produced</code>.
     */
    public final TableField<InventoryItemsViewRecord, Boolean> PRODUCED = createField(DSL.name("produced"), SQLDataType.BOOLEAN, this, "");

    /**
     * The column <code>public.inventory_items_view.unit_of_production</code>.
     */
    public final TableField<InventoryItemsViewRecord, BigDecimal> UNIT_OF_PRODUCTION = createField(DSL.name("unit_of_production"), SQLDataType.NUMERIC, this, "");

    /**
     * The column <code>public.inventory_items_view.measurement_unit</code>.
     */
    public final TableField<InventoryItemsViewRecord, String> MEASUREMENT_UNIT = createField(DSL.name("measurement_unit"), SQLDataType.CLOB, this, "");

    /**
     * The column <code>public.inventory_items_view.sell_price_amount</code>.
     */
    public final TableField<InventoryItemsViewRecord, Long> SELL_PRICE_AMOUNT = createField(DSL.name("sell_price_amount"), SQLDataType.BIGINT, this, "");

    /**
     * The column <code>public.inventory_items_view.sell_price_currency</code>.
     */
    public final TableField<InventoryItemsViewRecord, Currency> SELL_PRICE_CURRENCY = createField(DSL.name("sell_price_currency"), SQLDataType.VARCHAR(3), this, "", new CurrencyConverter());

    /**
     * The column <code>public.inventory_items_view.category_id</code>.
     */
    public final TableField<InventoryItemsViewRecord, UUID> CATEGORY_ID = createField(DSL.name("category_id"), SQLDataType.UUID, this, "");

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public final TableField<InventoryItemsViewRecord, Object> TEXT_SEARCH = createField(DSL.name("text_search"), org.jooq.impl.DefaultDataType.getDefaultDataType("\"pg_catalog\".\"tsvector\""), this, "");

    /**
     * The column <code>public.inventory_items_view.category_name</code>.
     */
    public final TableField<InventoryItemsViewRecord, String> CATEGORY_NAME = createField(DSL.name("category_name"), SQLDataType.CLOB, this, "");

    /**
     * The column <code>public.inventory_items_view.inventory_unit_id</code>.
     */
    public final TableField<InventoryItemsViewRecord, UUID> INVENTORY_UNIT_ID = createField(DSL.name("inventory_unit_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.inventory_items_view.inventory_unit_name</code>.
     */
    public final TableField<InventoryItemsViewRecord, String> INVENTORY_UNIT_NAME = createField(DSL.name("inventory_unit_name"), SQLDataType.VARCHAR(200), this, "");

    /**
     * The column <code>public.inventory_items_view.currency</code>.
     */
    public final TableField<InventoryItemsViewRecord, String> CURRENCY = createField(DSL.name("currency"), SQLDataType.VARCHAR(3), this, "");

    /**
     * The column <code>public.inventory_items_view.quantity</code>.
     */
    public final TableField<InventoryItemsViewRecord, BigDecimal> QUANTITY = createField(DSL.name("quantity"), SQLDataType.NUMERIC, this, "");

    /**
     * The column <code>public.inventory_items_view.cost</code>.
     */
    public final TableField<InventoryItemsViewRecord, Long> COST = createField(DSL.name("cost"), SQLDataType.BIGINT, this, "");

    /**
     * The column <code>public.inventory_items_view.total_value</code>.
     */
    public final TableField<InventoryItemsViewRecord, Long> TOTAL_VALUE = createField(DSL.name("total_value"), SQLDataType.BIGINT, this, "");

    /**
     * The column <code>public.inventory_items_view.committed_quantity</code>.
     */
    public final TableField<InventoryItemsViewRecord, BigDecimal> COMMITTED_QUANTITY = createField(DSL.name("committed_quantity"), SQLDataType.NUMERIC, this, "");

    /**
     * The column <code>public.inventory_items_view.incoming_quantity</code>.
     */
    public final TableField<InventoryItemsViewRecord, BigDecimal> INCOMING_QUANTITY = createField(DSL.name("incoming_quantity"), SQLDataType.NUMERIC, this, "");

    /**
     * The column <code>public.inventory_items_view.supplier_id</code>.
     */
    public final TableField<InventoryItemsViewRecord, UUID> SUPPLIER_ID = createField(DSL.name("supplier_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.inventory_items_view.supplier_name</code>.
     */
    public final TableField<InventoryItemsViewRecord, String> SUPPLIER_NAME = createField(DSL.name("supplier_name"), SQLDataType.VARCHAR(200), this, "");

    private InventoryItemsView(Name alias, Table<InventoryItemsViewRecord> aliased) {
        this(alias, aliased, null);
    }

    private InventoryItemsView(Name alias, Table<InventoryItemsViewRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.view("create view \"inventory_items_view\" as  WITH last_supplier AS (\n         SELECT inventory.material_good_id,\n            inventory.supplier_id,\n            company.company_name AS supplier_name,\n            row_number() OVER (PARTITION BY inventory.material_good_id ORDER BY inventory.create_time DESC) AS rn\n           FROM (inventory\n             LEFT JOIN company ON ((inventory.supplier_id = company.id)))\n          WHERE (inventory.supplier_id IS NOT NULL)\n        ), committed_stock AS (\n         SELECT combined.owner_id,\n            combined.material_good_id,\n            sum(combined.committed_quantity) AS committed_quantity\n           FROM ( SELECT so.owner_id,\n                    ((item.value ->> 'productId'::text))::uuid AS material_good_id,\n                    sum(((item.value ->> 'quantity'::text))::numeric) AS committed_quantity\n                   FROM sales_order so,\n                    LATERAL jsonb_array_elements(so.items) item(value)\n                  WHERE ((so.deleted = false) AND ((so.status)::text = ANY ((ARRAY['SUBMITTED'::character varying, 'PROCESSING'::character varying, 'PICKING_PACKING'::character varying, 'READY_TO_SHIP'::character varying])::text[])))\n                  GROUP BY so.owner_id, ((item.value ->> 'productId'::text))::uuid\n                UNION ALL\n                 SELECT mo.owner_id,\n                    (material_id.value)::uuid AS material_good_id,\n                    sum((((material.value ->> 'quantity'::text))::numeric * mo.quantity)) AS committed_quantity\n                   FROM manufacturing_order mo,\n                    LATERAL jsonb_array_elements(mo.manufacturing_operations) operation(value),\n                    LATERAL jsonb_array_elements((operation.value -> 'materials'::text)) material(value),\n                    LATERAL jsonb_array_elements_text((material.value -> 'materialIds'::text)) material_id(value)\n                  WHERE ((mo.deleted = false) AND ((mo.status)::text = ANY ((ARRAY['SUBMITTED'::character varying, 'MANUFACTURING'::character varying, 'MANUFACTURED'::character varying])::text[])))\n                  GROUP BY mo.owner_id, (material_id.value)::uuid) combined\n          GROUP BY combined.owner_id, combined.material_good_id\n        ), incoming_stock AS (\n         SELECT combined.owner_id,\n            combined.material_good_id,\n            sum(combined.incoming_quantity) AS incoming_quantity\n           FROM ( SELECT po.owner_id,\n                    ((item.value ->> 'materialGoodId'::text))::uuid AS material_good_id,\n                    sum(((item.value ->> 'quantity'::text))::numeric) AS incoming_quantity\n                   FROM purchase_order po,\n                    LATERAL jsonb_array_elements(po.items) item(value)\n                  WHERE ((po.deleted = false) AND ((po.status)::text = ANY ((ARRAY['SUBMITTED'::character varying, 'SENT_FOR_QUOTE'::character varying, 'SENT'::character varying])::text[])))\n                  GROUP BY po.owner_id, ((item.value ->> 'materialGoodId'::text))::uuid\n                UNION ALL\n                 SELECT mo.owner_id,\n                    mo.product_id AS material_good_id,\n                    sum(mo.quantity) AS incoming_quantity\n                   FROM manufacturing_order mo\n                  WHERE ((mo.deleted = false) AND ((mo.status)::text = ANY ((ARRAY['SUBMITTED'::character varying, 'MANUFACTURING'::character varying])::text[])) AND (mo.product_id IS NOT NULL))\n                  GROUP BY mo.owner_id, mo.product_id) combined\n          GROUP BY combined.owner_id, combined.material_good_id\n        )\n SELECT mg.id AS material_good_id,\n    mg.owner_id,\n    mg.name,\n    mg.code,\n    mg.external_code,\n    mg.description,\n    ((mg.details ->> 'criticalOnHand'::text))::numeric AS critical_on_hand,\n    ((mg.details ->> 'produced'::text))::boolean AS produced,\n    ((mg.details ->> 'unitOfProduction'::text))::numeric AS unit_of_production,\n    (mg.details ->> 'measurementUnit'::text) AS measurement_unit,\n    mg.sell_price_amount,\n    mg.sell_price_currency,\n    mg.category_id,\n    mg.text_search,\n    (c.details ->> 'name'::text) AS category_name,\n    iu.id AS inventory_unit_id,\n    iu.name AS inventory_unit_name,\n    ics.currency,\n    COALESCE(ics.quantity, (0)::numeric) AS quantity,\n    COALESCE(ics.cost, (0)::bigint) AS cost,\n    COALESCE(ics.total_value, (0)::bigint) AS total_value,\n    COALESCE(cs.committed_quantity, (0)::numeric) AS committed_quantity,\n    COALESCE(incoming_s.incoming_quantity, (0)::numeric) AS incoming_quantity,\n    ls.supplier_id,\n    ls.supplier_name\n   FROM ((((((material_good mg\n     LEFT JOIN category c ON ((mg.category_id = c.id)))\n     LEFT JOIN inventory_unit iu ON (((mg.owner_id = iu.owner_id) AND (iu.deleted = false))))\n     FULL JOIN inventory_current_stock ics ON (((iu.id = ics.unit_id) AND (mg.id = ics.material_good_id))))\n     LEFT JOIN last_supplier ls ON (((mg.id = ls.material_good_id) AND (ls.rn = 1))))\n     LEFT JOIN committed_stock cs ON ((mg.id = cs.material_good_id)))\n     LEFT JOIN incoming_stock incoming_s ON ((mg.id = incoming_s.material_good_id)))\n  WHERE (mg.deleted IS FALSE);"));
    }

    /**
     * Create an aliased <code>public.inventory_items_view</code> table
     * reference
     */
    public InventoryItemsView(String alias) {
        this(DSL.name(alias), INVENTORY_ITEMS_VIEW);
    }

    /**
     * Create an aliased <code>public.inventory_items_view</code> table
     * reference
     */
    public InventoryItemsView(Name alias) {
        this(alias, INVENTORY_ITEMS_VIEW);
    }

    /**
     * Create a <code>public.inventory_items_view</code> table reference
     */
    public InventoryItemsView() {
        this(DSL.name("inventory_items_view"), null);
    }

    public <O extends Record> InventoryItemsView(Table<O> child, ForeignKey<O, InventoryItemsViewRecord> key) {
        super(child, key, INVENTORY_ITEMS_VIEW);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public InventoryItemsView as(String alias) {
        return new InventoryItemsView(DSL.name(alias), this);
    }

    @Override
    public InventoryItemsView as(Name alias) {
        return new InventoryItemsView(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public InventoryItemsView rename(String name) {
        return new InventoryItemsView(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public InventoryItemsView rename(Name name) {
        return new InventoryItemsView(name, null);
    }
}
