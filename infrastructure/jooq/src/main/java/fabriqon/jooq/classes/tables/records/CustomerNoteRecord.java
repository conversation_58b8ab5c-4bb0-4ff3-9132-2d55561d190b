/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.CustomerNote;

import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record2;
import org.jooq.Row2;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CustomerNoteRecord extends TableRecordImpl<CustomerNoteRecord> implements Record2<UUID, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.customer_note.company_id</code>.
     */
    public void setCompanyId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.customer_note.company_id</code>.
     */
    public UUID getCompanyId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.customer_note.note_id</code>.
     */
    public void setNoteId(UUID value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.customer_note.note_id</code>.
     */
    public UUID getNoteId() {
        return (UUID) get(1);
    }

    // -------------------------------------------------------------------------
    // Record2 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row2<UUID, UUID> fieldsRow() {
        return (Row2) super.fieldsRow();
    }

    @Override
    public Row2<UUID, UUID> valuesRow() {
        return (Row2) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return CustomerNote.CUSTOMER_NOTE.COMPANY_ID;
    }

    @Override
    public Field<UUID> field2() {
        return CustomerNote.CUSTOMER_NOTE.NOTE_ID;
    }

    @Override
    public UUID component1() {
        return getCompanyId();
    }

    @Override
    public UUID component2() {
        return getNoteId();
    }

    @Override
    public UUID value1() {
        return getCompanyId();
    }

    @Override
    public UUID value2() {
        return getNoteId();
    }

    @Override
    public CustomerNoteRecord value1(UUID value) {
        setCompanyId(value);
        return this;
    }

    @Override
    public CustomerNoteRecord value2(UUID value) {
        setNoteId(value);
        return this;
    }

    @Override
    public CustomerNoteRecord values(UUID value1, UUID value2) {
        value1(value1);
        value2(value2);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CustomerNoteRecord
     */
    public CustomerNoteRecord() {
        super(CustomerNote.CUSTOMER_NOTE);
    }

    /**
     * Create a detached, initialised CustomerNoteRecord
     */
    public CustomerNoteRecord(UUID companyId, UUID noteId) {
        super(CustomerNote.CUSTOMER_NOTE);

        setCompanyId(companyId);
        setNoteId(noteId);
    }
}
