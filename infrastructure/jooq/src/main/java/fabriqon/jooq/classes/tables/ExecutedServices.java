/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.ExecutedServicesRecord;
import fabriqon.jooq.converters.CurrencyConverter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Currency;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row11;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ExecutedServices extends TableImpl<ExecutedServicesRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.executed_services</code>
     */
    public static final ExecutedServices EXECUTED_SERVICES = new ExecutedServices();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ExecutedServicesRecord> getRecordType() {
        return ExecutedServicesRecord.class;
    }

    /**
     * The column <code>public.executed_services.create_time</code>.
     */
    public final TableField<ExecutedServicesRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.executed_services.update_time</code>.
     */
    public final TableField<ExecutedServicesRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.executed_services.deleted</code>.
     */
    public final TableField<ExecutedServicesRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.executed_services.owner_id</code>.
     */
    public final TableField<ExecutedServicesRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.executed_services.sales_order_id</code>.
     */
    public final TableField<ExecutedServicesRecord, UUID> SALES_ORDER_ID = createField(DSL.name("sales_order_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.executed_services.servicing_order_id</code>.
     */
    public final TableField<ExecutedServicesRecord, UUID> SERVICING_ORDER_ID = createField(DSL.name("servicing_order_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.executed_services.service_id</code>.
     */
    public final TableField<ExecutedServicesRecord, UUID> SERVICE_ID = createField(DSL.name("service_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.executed_services.quantity</code>.
     */
    public final TableField<ExecutedServicesRecord, BigDecimal> QUANTITY = createField(DSL.name("quantity"), SQLDataType.NUMERIC.nullable(false), this, "");

    /**
     * The column <code>public.executed_services.cost_amount</code>.
     */
    public final TableField<ExecutedServicesRecord, Long> COST_AMOUNT = createField(DSL.name("cost_amount"), SQLDataType.BIGINT, this, "");

    /**
     * The column <code>public.executed_services.sale_amount</code>.
     */
    public final TableField<ExecutedServicesRecord, Long> SALE_AMOUNT = createField(DSL.name("sale_amount"), SQLDataType.BIGINT, this, "");

    /**
     * The column <code>public.executed_services.service_currency</code>.
     */
    public final TableField<ExecutedServicesRecord, Currency> SERVICE_CURRENCY = createField(DSL.name("service_currency"), SQLDataType.VARCHAR(3).nullable(false), this, "", new CurrencyConverter());

    private ExecutedServices(Name alias, Table<ExecutedServicesRecord> aliased) {
        this(alias, aliased, null);
    }

    private ExecutedServices(Name alias, Table<ExecutedServicesRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.executed_services</code> table reference
     */
    public ExecutedServices(String alias) {
        this(DSL.name(alias), EXECUTED_SERVICES);
    }

    /**
     * Create an aliased <code>public.executed_services</code> table reference
     */
    public ExecutedServices(Name alias) {
        this(alias, EXECUTED_SERVICES);
    }

    /**
     * Create a <code>public.executed_services</code> table reference
     */
    public ExecutedServices() {
        this(DSL.name("executed_services"), null);
    }

    public <O extends Record> ExecutedServices(Table<O> child, ForeignKey<O, ExecutedServicesRecord> key) {
        super(child, key, EXECUTED_SERVICES);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public UniqueKey<ExecutedServicesRecord> getPrimaryKey() {
        return Keys.EXECUTED_SERVICES_PKEY;
    }

    @Override
    public List<ForeignKey<ExecutedServicesRecord, ?>> getReferences() {
        return Arrays.asList(Keys.EXECUTED_SERVICES__EXECUTED_SERVICES_OWNER_ID_FKEY, Keys.EXECUTED_SERVICES__EXECUTED_SERVICES_SALES_ORDER_ID_FKEY, Keys.EXECUTED_SERVICES__EXECUTED_SERVICES_SERVICING_ORDER_ID_FKEY, Keys.EXECUTED_SERVICES__EXECUTED_SERVICES_SERVICE_ID_FKEY);
    }

    private transient Account _account;
    private transient SalesOrder _salesOrder;
    private transient ServicingOrder _servicingOrder;
    private transient ServiceTemplate _serviceTemplate;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.EXECUTED_SERVICES__EXECUTED_SERVICES_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.sales_order</code> table.
     */
    public SalesOrder salesOrder() {
        if (_salesOrder == null)
            _salesOrder = new SalesOrder(this, Keys.EXECUTED_SERVICES__EXECUTED_SERVICES_SALES_ORDER_ID_FKEY);

        return _salesOrder;
    }

    /**
     * Get the implicit join path to the <code>public.servicing_order</code>
     * table.
     */
    public ServicingOrder servicingOrder() {
        if (_servicingOrder == null)
            _servicingOrder = new ServicingOrder(this, Keys.EXECUTED_SERVICES__EXECUTED_SERVICES_SERVICING_ORDER_ID_FKEY);

        return _servicingOrder;
    }

    /**
     * Get the implicit join path to the <code>public.service_template</code>
     * table.
     */
    public ServiceTemplate serviceTemplate() {
        if (_serviceTemplate == null)
            _serviceTemplate = new ServiceTemplate(this, Keys.EXECUTED_SERVICES__EXECUTED_SERVICES_SERVICE_ID_FKEY);

        return _serviceTemplate;
    }

    @Override
    public ExecutedServices as(String alias) {
        return new ExecutedServices(DSL.name(alias), this);
    }

    @Override
    public ExecutedServices as(Name alias) {
        return new ExecutedServices(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ExecutedServices rename(String name) {
        return new ExecutedServices(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ExecutedServices rename(Name name) {
        return new ExecutedServices(name, null);
    }

    // -------------------------------------------------------------------------
    // Row11 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row11<LocalDateTime, LocalDateTime, Boolean, UUID, UUID, UUID, UUID, BigDecimal, Long, Long, Currency> fieldsRow() {
        return (Row11) super.fieldsRow();
    }
}
