/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.SalesOrdersProcessingTimeViewRecord;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row5;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SalesOrdersProcessingTimeView extends TableImpl<SalesOrdersProcessingTimeViewRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>public.sales_orders_processing_time_view</code>
     */
    public static final SalesOrdersProcessingTimeView SALES_ORDERS_PROCESSING_TIME_VIEW = new SalesOrdersProcessingTimeView();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SalesOrdersProcessingTimeViewRecord> getRecordType() {
        return SalesOrdersProcessingTimeViewRecord.class;
    }

    /**
     * The column
     * <code>public.sales_orders_processing_time_view.owner_id</code>.
     */
    public final TableField<SalesOrdersProcessingTimeViewRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID, this, "");

    /**
     * The column
     * <code>public.sales_orders_processing_time_view.sales_order_id</code>.
     */
    public final TableField<SalesOrdersProcessingTimeViewRecord, UUID> SALES_ORDER_ID = createField(DSL.name("sales_order_id"), SQLDataType.UUID, this, "");

    /**
     * The column
     * <code>public.sales_orders_processing_time_view.start_time_local</code>.
     */
    public final TableField<SalesOrdersProcessingTimeViewRecord, LocalDateTime> START_TIME_LOCAL = createField(DSL.name("start_time_local"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column
     * <code>public.sales_orders_processing_time_view.delivered_time_local</code>.
     */
    public final TableField<SalesOrdersProcessingTimeViewRecord, LocalDateTime> DELIVERED_TIME_LOCAL = createField(DSL.name("delivered_time_local"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column
     * <code>public.sales_orders_processing_time_view.total_working_seconds</code>.
     */
    public final TableField<SalesOrdersProcessingTimeViewRecord, BigDecimal> TOTAL_WORKING_SECONDS = createField(DSL.name("total_working_seconds"), SQLDataType.NUMERIC, this, "");

    private SalesOrdersProcessingTimeView(Name alias, Table<SalesOrdersProcessingTimeViewRecord> aliased) {
        this(alias, aliased, null);
    }

    private SalesOrdersProcessingTimeView(Name alias, Table<SalesOrdersProcessingTimeViewRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.view("create view \"sales_orders_processing_time_view\" as  WITH order_processing_times AS (\n         SELECT se.owner_id,\n            se.target_entity_id,\n            COALESCE(min(\n                CASE\n                    WHEN ((se.transition)::text = 'SUBMITTED'::text) THEN se.create_time\n                    ELSE NULL::timestamp without time zone\n                END), min(\n                CASE\n                    WHEN ((se.transition)::text = 'CREATE'::text) THEN se.create_time\n                    ELSE NULL::timestamp without time zone\n                END)) AS start_time,\n            min(\n                CASE\n                    WHEN ((se.transition)::text = 'DELIVERED'::text) THEN se.create_time\n                    ELSE NULL::timestamp without time zone\n                END) AS delivered_time,\n            count(\n                CASE\n                    WHEN ((se.transition)::text = 'CANCELED'::text) THEN 1\n                    ELSE NULL::integer\n                END) AS cancel_count,\n            ((a.settings -> 'manufacturing'::text) ->> 'workDayStartTime'::text) AS work_start,\n            ((a.settings -> 'manufacturing'::text) ->> 'workDayEndTime'::text) AS work_end,\n            ((a.settings -> 'manufacturing'::text) -> 'workingDays'::text) AS working_days,\n            ((a.settings -> 'general'::text) ->> 'defaultTimeZone'::text) AS time_zone\n           FROM (system_event se\n             JOIN account a ON ((se.owner_id = a.id)))\n          WHERE ((se.transition)::text = ANY (ARRAY[('CREATE'::character varying)::text, ('SUBMITTED'::character varying)::text, ('DELIVERED'::character varying)::text, ('CANCELED'::character varying)::text]))\n          GROUP BY se.owner_id, se.target_entity_id, a.settings\n         HAVING ((COALESCE(min(\n                CASE\n                    WHEN ((se.transition)::text = 'SUBMITTED'::text) THEN se.create_time\n                    ELSE NULL::timestamp without time zone\n                END), min(\n                CASE\n                    WHEN ((se.transition)::text = 'CREATE'::text) THEN se.create_time\n                    ELSE NULL::timestamp without time zone\n                END)) IS NOT NULL) AND (min(\n                CASE\n                    WHEN ((se.transition)::text = 'DELIVERED'::text) THEN se.create_time\n                    ELSE NULL::timestamp without time zone\n                END) IS NOT NULL) AND (count(\n                CASE\n                    WHEN ((se.transition)::text = 'CANCELED'::text) THEN 1\n                    ELSE NULL::integer\n                END) = 0))\n        ), working_days_and_hours AS (\n         SELECT o.owner_id,\n            o.target_entity_id AS sales_order_id,\n            (d.d)::date AS day_of_processing,\n            (o.work_start)::time without time zone AS work_start,\n            (o.work_end)::time without time zone AS work_end,\n            ((o.start_time AT TIME ZONE 'UTC'::text) AT TIME ZONE o.time_zone) AS start_time_local,\n            ((o.delivered_time AT TIME ZONE 'UTC'::text) AT TIME ZONE o.time_zone) AS delivered_time_local,\n            o.time_zone\n           FROM order_processing_times o,\n            LATERAL generate_series(((o.start_time)::date)::timestamp with time zone, ((o.delivered_time)::date)::timestamp with time zone, '1 day'::interval) d(d)\n          WHERE (EXTRACT(dow FROM d.d) IN ( SELECT (((jsonb_array_elements_text(o.working_days))::integer + 6) % 7)))\n        )\n SELECT working_days_and_hours.owner_id,\n    working_days_and_hours.sales_order_id,\n    working_days_and_hours.start_time_local,\n    working_days_and_hours.delivered_time_local,\n    GREATEST(sum(\n        CASE\n            WHEN (working_days_and_hours.day_of_processing = (working_days_and_hours.start_time_local)::date) THEN EXTRACT(epoch FROM (LEAST((working_days_and_hours.day_of_processing + working_days_and_hours.work_end), working_days_and_hours.delivered_time_local) - GREATEST(working_days_and_hours.start_time_local, (working_days_and_hours.day_of_processing + working_days_and_hours.work_start))))\n            WHEN (working_days_and_hours.day_of_processing = (working_days_and_hours.delivered_time_local)::date) THEN EXTRACT(epoch FROM (LEAST(working_days_and_hours.delivered_time_local, (working_days_and_hours.day_of_processing + working_days_and_hours.work_end)) - (working_days_and_hours.day_of_processing + working_days_and_hours.work_start)))\n            ELSE EXTRACT(epoch FROM (working_days_and_hours.work_end - working_days_and_hours.work_start))\n        END), (0)::numeric) AS total_working_seconds\n   FROM working_days_and_hours\n  GROUP BY working_days_and_hours.owner_id, working_days_and_hours.sales_order_id, working_days_and_hours.start_time_local, working_days_and_hours.delivered_time_local;"));
    }

    /**
     * Create an aliased <code>public.sales_orders_processing_time_view</code>
     * table reference
     */
    public SalesOrdersProcessingTimeView(String alias) {
        this(DSL.name(alias), SALES_ORDERS_PROCESSING_TIME_VIEW);
    }

    /**
     * Create an aliased <code>public.sales_orders_processing_time_view</code>
     * table reference
     */
    public SalesOrdersProcessingTimeView(Name alias) {
        this(alias, SALES_ORDERS_PROCESSING_TIME_VIEW);
    }

    /**
     * Create a <code>public.sales_orders_processing_time_view</code> table
     * reference
     */
    public SalesOrdersProcessingTimeView() {
        this(DSL.name("sales_orders_processing_time_view"), null);
    }

    public <O extends Record> SalesOrdersProcessingTimeView(Table<O> child, ForeignKey<O, SalesOrdersProcessingTimeViewRecord> key) {
        super(child, key, SALES_ORDERS_PROCESSING_TIME_VIEW);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public SalesOrdersProcessingTimeView as(String alias) {
        return new SalesOrdersProcessingTimeView(DSL.name(alias), this);
    }

    @Override
    public SalesOrdersProcessingTimeView as(Name alias) {
        return new SalesOrdersProcessingTimeView(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SalesOrdersProcessingTimeView rename(String name) {
        return new SalesOrdersProcessingTimeView(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public SalesOrdersProcessingTimeView rename(Name name) {
        return new SalesOrdersProcessingTimeView(name, null);
    }

    // -------------------------------------------------------------------------
    // Row5 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row5<UUID, UUID, LocalDateTime, LocalDateTime, BigDecimal> fieldsRow() {
        return (Row5) super.fieldsRow();
    }
}
