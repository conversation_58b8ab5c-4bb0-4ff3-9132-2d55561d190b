/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.DevicePairingToken;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record4;
import org.jooq.Row4;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DevicePairingTokenRecord extends TableRecordImpl<DevicePairingTokenRecord> implements Record4<LocalDateTime, UUID, String, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.device_pairing_token.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.device_pairing_token.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(0);
    }

    /**
     * Setter for <code>public.device_pairing_token.user_id</code>.
     */
    public void setUserId(UUID value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.device_pairing_token.user_id</code>.
     */
    public UUID getUserId() {
        return (UUID) get(1);
    }

    /**
     * Setter for <code>public.device_pairing_token.token</code>.
     */
    public void setToken(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.device_pairing_token.token</code>.
     */
    public String getToken() {
        return (String) get(2);
    }

    /**
     * Setter for <code>public.device_pairing_token.token_type</code>.
     */
    public void setTokenType(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.device_pairing_token.token_type</code>.
     */
    public String getTokenType() {
        return (String) get(3);
    }

    // -------------------------------------------------------------------------
    // Record4 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row4<LocalDateTime, UUID, String, String> fieldsRow() {
        return (Row4) super.fieldsRow();
    }

    @Override
    public Row4<LocalDateTime, UUID, String, String> valuesRow() {
        return (Row4) super.valuesRow();
    }

    @Override
    public Field<LocalDateTime> field1() {
        return DevicePairingToken.DEVICE_PAIRING_TOKEN.CREATE_TIME;
    }

    @Override
    public Field<UUID> field2() {
        return DevicePairingToken.DEVICE_PAIRING_TOKEN.USER_ID;
    }

    @Override
    public Field<String> field3() {
        return DevicePairingToken.DEVICE_PAIRING_TOKEN.TOKEN;
    }

    @Override
    public Field<String> field4() {
        return DevicePairingToken.DEVICE_PAIRING_TOKEN.TOKEN_TYPE;
    }

    @Override
    public LocalDateTime component1() {
        return getCreateTime();
    }

    @Override
    public UUID component2() {
        return getUserId();
    }

    @Override
    public String component3() {
        return getToken();
    }

    @Override
    public String component4() {
        return getTokenType();
    }

    @Override
    public LocalDateTime value1() {
        return getCreateTime();
    }

    @Override
    public UUID value2() {
        return getUserId();
    }

    @Override
    public String value3() {
        return getToken();
    }

    @Override
    public String value4() {
        return getTokenType();
    }

    @Override
    public DevicePairingTokenRecord value1(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public DevicePairingTokenRecord value2(UUID value) {
        setUserId(value);
        return this;
    }

    @Override
    public DevicePairingTokenRecord value3(String value) {
        setToken(value);
        return this;
    }

    @Override
    public DevicePairingTokenRecord value4(String value) {
        setTokenType(value);
        return this;
    }

    @Override
    public DevicePairingTokenRecord values(LocalDateTime value1, UUID value2, String value3, String value4) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached DevicePairingTokenRecord
     */
    public DevicePairingTokenRecord() {
        super(DevicePairingToken.DEVICE_PAIRING_TOKEN);
    }

    /**
     * Create a detached, initialised DevicePairingTokenRecord
     */
    public DevicePairingTokenRecord(LocalDateTime createTime, UUID userId, String token, String tokenType) {
        super(DevicePairingToken.DEVICE_PAIRING_TOKEN);

        setCreateTime(createTime);
        setUserId(userId);
        setToken(token);
        setTokenType(tokenType);
    }
}
