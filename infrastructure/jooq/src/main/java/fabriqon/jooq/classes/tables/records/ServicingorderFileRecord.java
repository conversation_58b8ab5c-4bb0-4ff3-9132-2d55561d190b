/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.ServicingorderFile;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record4;
import org.jooq.Row4;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ServicingorderFileRecord extends TableRecordImpl<ServicingorderFileRecord> implements Record4<LocalDateTime, UUID, UUID, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.servicingorder_file.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.servicingorder_file.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(0);
    }

    /**
     * Setter for <code>public.servicingorder_file.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.servicingorder_file.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(1);
    }

    /**
     * Setter for <code>public.servicingorder_file.servicing_order_id</code>.
     */
    public void setServicingOrderId(UUID value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.servicingorder_file.servicing_order_id</code>.
     */
    public UUID getServicingOrderId() {
        return (UUID) get(2);
    }

    /**
     * Setter for <code>public.servicingorder_file.file_id</code>.
     */
    public void setFileId(UUID value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.servicingorder_file.file_id</code>.
     */
    public UUID getFileId() {
        return (UUID) get(3);
    }

    // -------------------------------------------------------------------------
    // Record4 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row4<LocalDateTime, UUID, UUID, UUID> fieldsRow() {
        return (Row4) super.fieldsRow();
    }

    @Override
    public Row4<LocalDateTime, UUID, UUID, UUID> valuesRow() {
        return (Row4) super.valuesRow();
    }

    @Override
    public Field<LocalDateTime> field1() {
        return ServicingorderFile.SERVICINGORDER_FILE.CREATE_TIME;
    }

    @Override
    public Field<UUID> field2() {
        return ServicingorderFile.SERVICINGORDER_FILE.OWNER_ID;
    }

    @Override
    public Field<UUID> field3() {
        return ServicingorderFile.SERVICINGORDER_FILE.SERVICING_ORDER_ID;
    }

    @Override
    public Field<UUID> field4() {
        return ServicingorderFile.SERVICINGORDER_FILE.FILE_ID;
    }

    @Override
    public LocalDateTime component1() {
        return getCreateTime();
    }

    @Override
    public UUID component2() {
        return getOwnerId();
    }

    @Override
    public UUID component3() {
        return getServicingOrderId();
    }

    @Override
    public UUID component4() {
        return getFileId();
    }

    @Override
    public LocalDateTime value1() {
        return getCreateTime();
    }

    @Override
    public UUID value2() {
        return getOwnerId();
    }

    @Override
    public UUID value3() {
        return getServicingOrderId();
    }

    @Override
    public UUID value4() {
        return getFileId();
    }

    @Override
    public ServicingorderFileRecord value1(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public ServicingorderFileRecord value2(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public ServicingorderFileRecord value3(UUID value) {
        setServicingOrderId(value);
        return this;
    }

    @Override
    public ServicingorderFileRecord value4(UUID value) {
        setFileId(value);
        return this;
    }

    @Override
    public ServicingorderFileRecord values(LocalDateTime value1, UUID value2, UUID value3, UUID value4) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ServicingorderFileRecord
     */
    public ServicingorderFileRecord() {
        super(ServicingorderFile.SERVICINGORDER_FILE);
    }

    /**
     * Create a detached, initialised ServicingorderFileRecord
     */
    public ServicingorderFileRecord(LocalDateTime createTime, UUID ownerId, UUID servicingOrderId, UUID fileId) {
        super(ServicingorderFile.SERVICINGORDER_FILE);

        setCreateTime(createTime);
        setOwnerId(ownerId);
        setServicingOrderId(servicingOrderId);
        setFileId(fileId);
    }
}
