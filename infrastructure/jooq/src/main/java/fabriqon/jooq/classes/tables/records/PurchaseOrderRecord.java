/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.PurchaseOrder;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.Record14;
import org.jooq.Row14;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PurchaseOrderRecord extends UpdatableRecordImpl<PurchaseOrderRecord> implements Record14<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String, String, LocalDateTime, JSONB, LocalDateTime, Object, JSONB, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.purchase_order.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.purchase_order.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.purchase_order.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.purchase_order.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.purchase_order.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.purchase_order.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.purchase_order.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.purchase_order.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.purchase_order.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.purchase_order.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.purchase_order.supplier_id</code>.
     */
    public void setSupplierId(UUID value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.purchase_order.supplier_id</code>.
     */
    public UUID getSupplierId() {
        return (UUID) get(5);
    }

    /**
     * Setter for <code>public.purchase_order.number</code>.
     */
    public void setNumber(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.purchase_order.number</code>.
     */
    public String getNumber() {
        return (String) get(6);
    }

    /**
     * Setter for <code>public.purchase_order.status</code>.
     */
    public void setStatus(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.purchase_order.status</code>.
     */
    public String getStatus() {
        return (String) get(7);
    }

    /**
     * Setter for <code>public.purchase_order.delivered_at</code>.
     */
    public void setDeliveredAt(LocalDateTime value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.purchase_order.delivered_at</code>.
     */
    public LocalDateTime getDeliveredAt() {
        return (LocalDateTime) get(8);
    }

    /**
     * Setter for <code>public.purchase_order.items</code>.
     */
    public void setItems(JSONB value) {
        set(9, value);
    }

    /**
     * Getter for <code>public.purchase_order.items</code>.
     */
    public JSONB getItems() {
        return (JSONB) get(9);
    }

    /**
     * Setter for <code>public.purchase_order.expected_by</code>.
     */
    public void setExpectedBy(LocalDateTime value) {
        set(10, value);
    }

    /**
     * Getter for <code>public.purchase_order.expected_by</code>.
     */
    public LocalDateTime getExpectedBy() {
        return (LocalDateTime) get(10);
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public void setTextSearch(Object value) {
        set(11, value);
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public Object getTextSearch() {
        return get(11);
    }

    /**
     * Setter for <code>public.purchase_order.rendering_details</code>.
     */
    public void setRenderingDetails(JSONB value) {
        set(12, value);
    }

    /**
     * Getter for <code>public.purchase_order.rendering_details</code>.
     */
    public JSONB getRenderingDetails() {
        return (JSONB) get(12);
    }

    /**
     * Setter for <code>public.purchase_order.managed_by</code>.
     */
    public void setManagedBy(UUID value) {
        set(13, value);
    }

    /**
     * Getter for <code>public.purchase_order.managed_by</code>.
     */
    public UUID getManagedBy() {
        return (UUID) get(13);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record14 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row14<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String, String, LocalDateTime, JSONB, LocalDateTime, Object, JSONB, UUID> fieldsRow() {
        return (Row14) super.fieldsRow();
    }

    @Override
    public Row14<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String, String, LocalDateTime, JSONB, LocalDateTime, Object, JSONB, UUID> valuesRow() {
        return (Row14) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return PurchaseOrder.PURCHASE_ORDER.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return PurchaseOrder.PURCHASE_ORDER.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return PurchaseOrder.PURCHASE_ORDER.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return PurchaseOrder.PURCHASE_ORDER.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return PurchaseOrder.PURCHASE_ORDER.OWNER_ID;
    }

    @Override
    public Field<UUID> field6() {
        return PurchaseOrder.PURCHASE_ORDER.SUPPLIER_ID;
    }

    @Override
    public Field<String> field7() {
        return PurchaseOrder.PURCHASE_ORDER.NUMBER;
    }

    @Override
    public Field<String> field8() {
        return PurchaseOrder.PURCHASE_ORDER.STATUS;
    }

    @Override
    public Field<LocalDateTime> field9() {
        return PurchaseOrder.PURCHASE_ORDER.DELIVERED_AT;
    }

    @Override
    public Field<JSONB> field10() {
        return PurchaseOrder.PURCHASE_ORDER.ITEMS;
    }

    @Override
    public Field<LocalDateTime> field11() {
        return PurchaseOrder.PURCHASE_ORDER.EXPECTED_BY;
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public Field<Object> field12() {
        return PurchaseOrder.PURCHASE_ORDER.TEXT_SEARCH;
    }

    @Override
    public Field<JSONB> field13() {
        return PurchaseOrder.PURCHASE_ORDER.RENDERING_DETAILS;
    }

    @Override
    public Field<UUID> field14() {
        return PurchaseOrder.PURCHASE_ORDER.MANAGED_BY;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public UUID component6() {
        return getSupplierId();
    }

    @Override
    public String component7() {
        return getNumber();
    }

    @Override
    public String component8() {
        return getStatus();
    }

    @Override
    public LocalDateTime component9() {
        return getDeliveredAt();
    }

    @Override
    public JSONB component10() {
        return getItems();
    }

    @Override
    public LocalDateTime component11() {
        return getExpectedBy();
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public Object component12() {
        return getTextSearch();
    }

    @Override
    public JSONB component13() {
        return getRenderingDetails();
    }

    @Override
    public UUID component14() {
        return getManagedBy();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public UUID value6() {
        return getSupplierId();
    }

    @Override
    public String value7() {
        return getNumber();
    }

    @Override
    public String value8() {
        return getStatus();
    }

    @Override
    public LocalDateTime value9() {
        return getDeliveredAt();
    }

    @Override
    public JSONB value10() {
        return getItems();
    }

    @Override
    public LocalDateTime value11() {
        return getExpectedBy();
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public Object value12() {
        return getTextSearch();
    }

    @Override
    public JSONB value13() {
        return getRenderingDetails();
    }

    @Override
    public UUID value14() {
        return getManagedBy();
    }

    @Override
    public PurchaseOrderRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public PurchaseOrderRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public PurchaseOrderRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public PurchaseOrderRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public PurchaseOrderRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public PurchaseOrderRecord value6(UUID value) {
        setSupplierId(value);
        return this;
    }

    @Override
    public PurchaseOrderRecord value7(String value) {
        setNumber(value);
        return this;
    }

    @Override
    public PurchaseOrderRecord value8(String value) {
        setStatus(value);
        return this;
    }

    @Override
    public PurchaseOrderRecord value9(LocalDateTime value) {
        setDeliveredAt(value);
        return this;
    }

    @Override
    public PurchaseOrderRecord value10(JSONB value) {
        setItems(value);
        return this;
    }

    @Override
    public PurchaseOrderRecord value11(LocalDateTime value) {
        setExpectedBy(value);
        return this;
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public PurchaseOrderRecord value12(Object value) {
        setTextSearch(value);
        return this;
    }

    @Override
    public PurchaseOrderRecord value13(JSONB value) {
        setRenderingDetails(value);
        return this;
    }

    @Override
    public PurchaseOrderRecord value14(UUID value) {
        setManagedBy(value);
        return this;
    }

    @Override
    public PurchaseOrderRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, UUID value6, String value7, String value8, LocalDateTime value9, JSONB value10, LocalDateTime value11, Object value12, JSONB value13, UUID value14) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached PurchaseOrderRecord
     */
    public PurchaseOrderRecord() {
        super(PurchaseOrder.PURCHASE_ORDER);
    }

    /**
     * Create a detached, initialised PurchaseOrderRecord
     */
    public PurchaseOrderRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, UUID supplierId, String number, String status, LocalDateTime deliveredAt, JSONB items, LocalDateTime expectedBy, Object textSearch, JSONB renderingDetails, UUID managedBy) {
        super(PurchaseOrder.PURCHASE_ORDER);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setSupplierId(supplierId);
        setNumber(number);
        setStatus(status);
        setDeliveredAt(deliveredAt);
        setItems(items);
        setExpectedBy(expectedBy);
        setTextSearch(textSearch);
        setRenderingDetails(renderingDetails);
        setManagedBy(managedBy);
    }
}
