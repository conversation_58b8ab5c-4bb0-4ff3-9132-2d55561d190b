/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes;


import fabriqon.jooq.classes.tables.*;
import org.jooq.Index;
import org.jooq.OrderField;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;


/**
 * A class modelling indexes of tables in public.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Indexes {

    // -------------------------------------------------------------------------
    // INDEX definitions
    // -------------------------------------------------------------------------

    public static final Index COMPANY_TEXT_SEARCH_IDX = Internal.createIndex(DSL.name("company_text_search_idx"), Company.COMPANY, new OrderField[] { Company.COMPANY.TEXT_SEARCH }, false);
    public static final Index MANUFACTURING_ORDER_TEXT_SEARCH_IDX = Internal.createIndex(DSL.name("manufacturing_order_text_search_idx"), ManufacturingOrder.MANUFACTURING_ORDER, new OrderField[] { ManufacturingOrder.MANUFACTURING_ORDER.TEXT_SEARCH }, false);
    public static final Index MATERIAL_GOOD_TEXT_SEARCH_IDX = Internal.createIndex(DSL.name("material_good_text_search_idx"), MaterialGood.MATERIAL_GOOD, new OrderField[] { MaterialGood.MATERIAL_GOOD.TEXT_SEARCH }, false);
    public static final Index PURCHASE_ORDER_TEXT_SEARCH_IDX = Internal.createIndex(DSL.name("purchase_order_text_search_idx"), PurchaseOrder.PURCHASE_ORDER, new OrderField[] { PurchaseOrder.PURCHASE_ORDER.TEXT_SEARCH }, false);
    public static final Index RO_COMPANIES_TEXT_SEARCH_IDX = Internal.createIndex(DSL.name("ro_companies_text_search_idx"), RoCompanies.RO_COMPANIES, new OrderField[] { RoCompanies.RO_COMPANIES.TEXT_SEARCH }, false);
    public static final Index SALES_ORDER_TEXT_SEARCH_IDX = Internal.createIndex(DSL.name("sales_order_text_search_idx"), SalesOrder.SALES_ORDER, new OrderField[] { SalesOrder.SALES_ORDER.TEXT_SEARCH }, false);
    public static final Index SERVICING_ORDER_TEXT_SEARCH_IDX = Internal.createIndex(DSL.name("servicing_order_text_search_idx"), ServicingOrder.SERVICING_ORDER, new OrderField[] { ServicingOrder.SERVICING_ORDER.TEXT_SEARCH }, false);
    public static final Index UNIQUE_SO_MO_ITEM_SERVICE = Internal.createIndex(DSL.name("unique_so_mo_item_service"), ManufacturingOrder.MANUFACTURING_ORDER, new OrderField[] { ManufacturingOrder.MANUFACTURING_ORDER.SALES_ORDER_ID, ManufacturingOrder.MANUFACTURING_ORDER.SERVICE_ID }, true);
    public static final Index USERS_TEXT_SEARCH_IDX = Internal.createIndex(DSL.name("users_text_search_idx"), Users.USERS, new OrderField[] { Users.USERS.TEXT_SEARCH }, false);
}
