/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.ManufacturingorderFileRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row4;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ManufacturingorderFile extends TableImpl<ManufacturingorderFileRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.manufacturingorder_file</code>
     */
    public static final ManufacturingorderFile MANUFACTURINGORDER_FILE = new ManufacturingorderFile();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ManufacturingorderFileRecord> getRecordType() {
        return ManufacturingorderFileRecord.class;
    }

    /**
     * The column <code>public.manufacturingorder_file.create_time</code>.
     */
    public final TableField<ManufacturingorderFileRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.manufacturingorder_file.owner_id</code>.
     */
    public final TableField<ManufacturingorderFileRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column
     * <code>public.manufacturingorder_file.manufacturing_order_id</code>.
     */
    public final TableField<ManufacturingorderFileRecord, UUID> MANUFACTURING_ORDER_ID = createField(DSL.name("manufacturing_order_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.manufacturingorder_file.file_id</code>.
     */
    public final TableField<ManufacturingorderFileRecord, UUID> FILE_ID = createField(DSL.name("file_id"), SQLDataType.UUID.nullable(false), this, "");

    private ManufacturingorderFile(Name alias, Table<ManufacturingorderFileRecord> aliased) {
        this(alias, aliased, null);
    }

    private ManufacturingorderFile(Name alias, Table<ManufacturingorderFileRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.manufacturingorder_file</code> table
     * reference
     */
    public ManufacturingorderFile(String alias) {
        this(DSL.name(alias), MANUFACTURINGORDER_FILE);
    }

    /**
     * Create an aliased <code>public.manufacturingorder_file</code> table
     * reference
     */
    public ManufacturingorderFile(Name alias) {
        this(alias, MANUFACTURINGORDER_FILE);
    }

    /**
     * Create a <code>public.manufacturingorder_file</code> table reference
     */
    public ManufacturingorderFile() {
        this(DSL.name("manufacturingorder_file"), null);
    }

    public <O extends Record> ManufacturingorderFile(Table<O> child, ForeignKey<O, ManufacturingorderFileRecord> key) {
        super(child, key, MANUFACTURINGORDER_FILE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<ForeignKey<ManufacturingorderFileRecord, ?>> getReferences() {
        return Arrays.asList(Keys.MANUFACTURINGORDER_FILE__MANUFACTURINGORDER_FILE_OWNER_ID_FKEY, Keys.MANUFACTURINGORDER_FILE__MANUFACTURINGORDER_FILE_MANUFACTURING_ORDER_ID_FKEY, Keys.MANUFACTURINGORDER_FILE__MANUFACTURINGORDER_FILE_FILE_ID_FKEY);
    }

    private transient Account _account;
    private transient ManufacturingOrder _manufacturingOrder;
    private transient File _file;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.MANUFACTURINGORDER_FILE__MANUFACTURINGORDER_FILE_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.manufacturing_order</code>
     * table.
     */
    public ManufacturingOrder manufacturingOrder() {
        if (_manufacturingOrder == null)
            _manufacturingOrder = new ManufacturingOrder(this, Keys.MANUFACTURINGORDER_FILE__MANUFACTURINGORDER_FILE_MANUFACTURING_ORDER_ID_FKEY);

        return _manufacturingOrder;
    }

    /**
     * Get the implicit join path to the <code>public.file</code> table.
     */
    public File file() {
        if (_file == null)
            _file = new File(this, Keys.MANUFACTURINGORDER_FILE__MANUFACTURINGORDER_FILE_FILE_ID_FKEY);

        return _file;
    }

    @Override
    public ManufacturingorderFile as(String alias) {
        return new ManufacturingorderFile(DSL.name(alias), this);
    }

    @Override
    public ManufacturingorderFile as(Name alias) {
        return new ManufacturingorderFile(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ManufacturingorderFile rename(String name) {
        return new ManufacturingorderFile(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ManufacturingorderFile rename(Name name) {
        return new ManufacturingorderFile(name, null);
    }

    // -------------------------------------------------------------------------
    // Row4 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row4<LocalDateTime, UUID, UUID, UUID> fieldsRow() {
        return (Row4) super.fieldsRow();
    }
}
