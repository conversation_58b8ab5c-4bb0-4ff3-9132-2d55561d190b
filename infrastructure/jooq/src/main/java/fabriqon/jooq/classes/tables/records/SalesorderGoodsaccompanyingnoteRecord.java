/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.SalesorderGoodsaccompanyingnote;

import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record2;
import org.jooq.Row2;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SalesorderGoodsaccompanyingnoteRecord extends TableRecordImpl<SalesorderGoodsaccompanyingnoteRecord> implements Record2<UUID, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for
     * <code>public.salesorder_goodsaccompanyingnote.sales_order_id</code>.
     */
    public void setSalesOrderId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for
     * <code>public.salesorder_goodsaccompanyingnote.sales_order_id</code>.
     */
    public UUID getSalesOrderId() {
        return (UUID) get(0);
    }

    /**
     * Setter for
     * <code>public.salesorder_goodsaccompanyingnote.goods_accompanying_note_id</code>.
     */
    public void setGoodsAccompanyingNoteId(UUID value) {
        set(1, value);
    }

    /**
     * Getter for
     * <code>public.salesorder_goodsaccompanyingnote.goods_accompanying_note_id</code>.
     */
    public UUID getGoodsAccompanyingNoteId() {
        return (UUID) get(1);
    }

    // -------------------------------------------------------------------------
    // Record2 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row2<UUID, UUID> fieldsRow() {
        return (Row2) super.fieldsRow();
    }

    @Override
    public Row2<UUID, UUID> valuesRow() {
        return (Row2) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return SalesorderGoodsaccompanyingnote.SALESORDER_GOODSACCOMPANYINGNOTE.SALES_ORDER_ID;
    }

    @Override
    public Field<UUID> field2() {
        return SalesorderGoodsaccompanyingnote.SALESORDER_GOODSACCOMPANYINGNOTE.GOODS_ACCOMPANYING_NOTE_ID;
    }

    @Override
    public UUID component1() {
        return getSalesOrderId();
    }

    @Override
    public UUID component2() {
        return getGoodsAccompanyingNoteId();
    }

    @Override
    public UUID value1() {
        return getSalesOrderId();
    }

    @Override
    public UUID value2() {
        return getGoodsAccompanyingNoteId();
    }

    @Override
    public SalesorderGoodsaccompanyingnoteRecord value1(UUID value) {
        setSalesOrderId(value);
        return this;
    }

    @Override
    public SalesorderGoodsaccompanyingnoteRecord value2(UUID value) {
        setGoodsAccompanyingNoteId(value);
        return this;
    }

    @Override
    public SalesorderGoodsaccompanyingnoteRecord values(UUID value1, UUID value2) {
        value1(value1);
        value2(value2);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SalesorderGoodsaccompanyingnoteRecord
     */
    public SalesorderGoodsaccompanyingnoteRecord() {
        super(SalesorderGoodsaccompanyingnote.SALESORDER_GOODSACCOMPANYINGNOTE);
    }

    /**
     * Create a detached, initialised SalesorderGoodsaccompanyingnoteRecord
     */
    public SalesorderGoodsaccompanyingnoteRecord(UUID salesOrderId, UUID goodsAccompanyingNoteId) {
        super(SalesorderGoodsaccompanyingnote.SALESORDER_GOODSACCOMPANYINGNOTE);

        setSalesOrderId(salesOrderId);
        setGoodsAccompanyingNoteId(goodsAccompanyingNoteId);
    }
}
