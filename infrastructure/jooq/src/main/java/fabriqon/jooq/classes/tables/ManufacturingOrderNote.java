/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.ManufacturingOrderNoteRecord;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row2;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ManufacturingOrderNote extends TableImpl<ManufacturingOrderNoteRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.manufacturing_order_note</code>
     */
    public static final ManufacturingOrderNote MANUFACTURING_ORDER_NOTE = new ManufacturingOrderNote();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ManufacturingOrderNoteRecord> getRecordType() {
        return ManufacturingOrderNoteRecord.class;
    }

    /**
     * The column
     * <code>public.manufacturing_order_note.manufacturing_order_id</code>.
     */
    public final TableField<ManufacturingOrderNoteRecord, UUID> MANUFACTURING_ORDER_ID = createField(DSL.name("manufacturing_order_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.manufacturing_order_note.note_id</code>.
     */
    public final TableField<ManufacturingOrderNoteRecord, UUID> NOTE_ID = createField(DSL.name("note_id"), SQLDataType.UUID.nullable(false), this, "");

    private ManufacturingOrderNote(Name alias, Table<ManufacturingOrderNoteRecord> aliased) {
        this(alias, aliased, null);
    }

    private ManufacturingOrderNote(Name alias, Table<ManufacturingOrderNoteRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.manufacturing_order_note</code> table
     * reference
     */
    public ManufacturingOrderNote(String alias) {
        this(DSL.name(alias), MANUFACTURING_ORDER_NOTE);
    }

    /**
     * Create an aliased <code>public.manufacturing_order_note</code> table
     * reference
     */
    public ManufacturingOrderNote(Name alias) {
        this(alias, MANUFACTURING_ORDER_NOTE);
    }

    /**
     * Create a <code>public.manufacturing_order_note</code> table reference
     */
    public ManufacturingOrderNote() {
        this(DSL.name("manufacturing_order_note"), null);
    }

    public <O extends Record> ManufacturingOrderNote(Table<O> child, ForeignKey<O, ManufacturingOrderNoteRecord> key) {
        super(child, key, MANUFACTURING_ORDER_NOTE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<ForeignKey<ManufacturingOrderNoteRecord, ?>> getReferences() {
        return Arrays.asList(Keys.MANUFACTURING_ORDER_NOTE__MANUFACTURING_ORDER_NOTE_MANUFACTURING_ORDER_ID_FKEY, Keys.MANUFACTURING_ORDER_NOTE__MANUFACTURING_ORDER_NOTE_NOTE_ID_FKEY);
    }

    private transient ManufacturingOrder _manufacturingOrder;
    private transient Note _note;

    /**
     * Get the implicit join path to the <code>public.manufacturing_order</code>
     * table.
     */
    public ManufacturingOrder manufacturingOrder() {
        if (_manufacturingOrder == null)
            _manufacturingOrder = new ManufacturingOrder(this, Keys.MANUFACTURING_ORDER_NOTE__MANUFACTURING_ORDER_NOTE_MANUFACTURING_ORDER_ID_FKEY);

        return _manufacturingOrder;
    }

    /**
     * Get the implicit join path to the <code>public.note</code> table.
     */
    public Note note() {
        if (_note == null)
            _note = new Note(this, Keys.MANUFACTURING_ORDER_NOTE__MANUFACTURING_ORDER_NOTE_NOTE_ID_FKEY);

        return _note;
    }

    @Override
    public ManufacturingOrderNote as(String alias) {
        return new ManufacturingOrderNote(DSL.name(alias), this);
    }

    @Override
    public ManufacturingOrderNote as(Name alias) {
        return new ManufacturingOrderNote(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ManufacturingOrderNote rename(String name) {
        return new ManufacturingOrderNote(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ManufacturingOrderNote rename(Name name) {
        return new ManufacturingOrderNote(name, null);
    }

    // -------------------------------------------------------------------------
    // Row2 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row2<UUID, UUID> fieldsRow() {
        return (Row2) super.fieldsRow();
    }
}
