/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.Sequence;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SequenceRecord extends UpdatableRecordImpl<SequenceRecord> implements Record8<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, String, Long> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.sequence.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.sequence.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.sequence.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.sequence.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.sequence.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.sequence.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.sequence.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.sequence.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.sequence.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.sequence.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.sequence.section</code>.
     */
    public void setSection(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.sequence.section</code>.
     */
    public String getSection() {
        return (String) get(5);
    }

    /**
     * Setter for <code>public.sequence.pattern</code>.
     */
    public void setPattern(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.sequence.pattern</code>.
     */
    public String getPattern() {
        return (String) get(6);
    }

    /**
     * Setter for <code>public.sequence.value</code>.
     */
    public void setValue(Long value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.sequence.value</code>.
     */
    public Long getValue() {
        return (Long) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record8 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row8<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, String, Long> fieldsRow() {
        return (Row8) super.fieldsRow();
    }

    @Override
    public Row8<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, String, Long> valuesRow() {
        return (Row8) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return Sequence.SEQUENCE.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return Sequence.SEQUENCE.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return Sequence.SEQUENCE.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return Sequence.SEQUENCE.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return Sequence.SEQUENCE.OWNER_ID;
    }

    @Override
    public Field<String> field6() {
        return Sequence.SEQUENCE.SECTION;
    }

    @Override
    public Field<String> field7() {
        return Sequence.SEQUENCE.PATTERN;
    }

    @Override
    public Field<Long> field8() {
        return Sequence.SEQUENCE.VALUE;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public String component6() {
        return getSection();
    }

    @Override
    public String component7() {
        return getPattern();
    }

    @Override
    public Long component8() {
        return getValue();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public String value6() {
        return getSection();
    }

    @Override
    public String value7() {
        return getPattern();
    }

    @Override
    public Long value8() {
        return getValue();
    }

    @Override
    public SequenceRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public SequenceRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public SequenceRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public SequenceRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public SequenceRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public SequenceRecord value6(String value) {
        setSection(value);
        return this;
    }

    @Override
    public SequenceRecord value7(String value) {
        setPattern(value);
        return this;
    }

    @Override
    public SequenceRecord value8(Long value) {
        setValue(value);
        return this;
    }

    @Override
    public SequenceRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, String value6, String value7, Long value8) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SequenceRecord
     */
    public SequenceRecord() {
        super(Sequence.SEQUENCE);
    }

    /**
     * Create a detached, initialised SequenceRecord
     */
    public SequenceRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, String section, String pattern, Long value) {
        super(Sequence.SEQUENCE);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setSection(section);
        setPattern(pattern);
        setValue(value);
    }
}
