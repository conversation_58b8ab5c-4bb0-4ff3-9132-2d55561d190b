/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.InventoryEntriesViewRecord;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row12;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class InventoryEntriesView extends TableImpl<InventoryEntriesViewRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.inventory_entries_view</code>
     */
    public static final InventoryEntriesView INVENTORY_ENTRIES_VIEW = new InventoryEntriesView();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<InventoryEntriesViewRecord> getRecordType() {
        return InventoryEntriesViewRecord.class;
    }

    /**
     * The column <code>public.inventory_entries_view.owner_id</code>.
     */
    public final TableField<InventoryEntriesViewRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.inventory_entries_view.create_time</code>.
     */
    public final TableField<InventoryEntriesViewRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>public.inventory_entries_view.sales_order_id</code>.
     */
    public final TableField<InventoryEntriesViewRecord, UUID> SALES_ORDER_ID = createField(DSL.name("sales_order_id"), SQLDataType.UUID, this, "");

    /**
     * The column
     * <code>public.inventory_entries_view.manufacturing_order_id</code>.
     */
    public final TableField<InventoryEntriesViewRecord, UUID> MANUFACTURING_ORDER_ID = createField(DSL.name("manufacturing_order_id"), SQLDataType.UUID, this, "");

    /**
     * The column
     * <code>public.inventory_entries_view.reception_receipt_id</code>.
     */
    public final TableField<InventoryEntriesViewRecord, UUID> RECEPTION_RECEIPT_ID = createField(DSL.name("reception_receipt_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.inventory_entries_view.supplier_id</code>.
     */
    public final TableField<InventoryEntriesViewRecord, UUID> SUPPLIER_ID = createField(DSL.name("supplier_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.inventory_entries_view.reception_price</code>.
     */
    public final TableField<InventoryEntriesViewRecord, BigDecimal> RECEPTION_PRICE = createField(DSL.name("reception_price"), SQLDataType.NUMERIC, this, "");

    /**
     * The column <code>public.inventory_entries_view.exit_price</code>.
     */
    public final TableField<InventoryEntriesViewRecord, BigDecimal> EXIT_PRICE = createField(DSL.name("exit_price"), SQLDataType.NUMERIC, this, "");

    /**
     * The column <code>public.inventory_entries_view.quantity</code>.
     */
    public final TableField<InventoryEntriesViewRecord, BigDecimal> QUANTITY = createField(DSL.name("quantity"), SQLDataType.NUMERIC, this, "");

    /**
     * The column <code>public.inventory_entries_view.item_name</code>.
     */
    public final TableField<InventoryEntriesViewRecord, String> ITEM_NAME = createField(DSL.name("item_name"), SQLDataType.VARCHAR(200), this, "");

    /**
     * The column <code>public.inventory_entries_view.category</code>.
     */
    public final TableField<InventoryEntriesViewRecord, String> CATEGORY = createField(DSL.name("category"), SQLDataType.CLOB, this, "");

    /**
     * The column <code>public.inventory_entries_view.product</code>.
     */
    public final TableField<InventoryEntriesViewRecord, String> PRODUCT = createField(DSL.name("product"), SQLDataType.CLOB, this, "");

    private InventoryEntriesView(Name alias, Table<InventoryEntriesViewRecord> aliased) {
        this(alias, aliased, null);
    }

    private InventoryEntriesView(Name alias, Table<InventoryEntriesViewRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.view("create view \"inventory_entries_view\" as  SELECT inventory.owner_id,\n    inventory.create_time,\n    inventory.sales_order_id,\n    inventory.manufacturing_order_id,\n    inventory.reception_receipt_id,\n    inventory.supplier_id,\n    (((inventory.reception_price_amount)::numeric * abs(inventory.quantity)) / (100)::numeric) AS reception_price,\n    (((inventory.exit_price_amount)::numeric * abs(inventory.quantity)) / (100)::numeric) AS exit_price,\n    inventory.quantity,\n    material_good.name AS item_name,\n    (category.details ->> 'name'::text) AS category,\n    'PRODUCT'::text AS product\n   FROM ((inventory\n     JOIN material_good ON ((inventory.material_good_id = material_good.id)))\n     JOIN category ON ((material_good.category_id = category.id)))\nUNION ALL\n SELECT executed_services.owner_id,\n    executed_services.create_time,\n    executed_services.sales_order_id,\n    executed_services.servicing_order_id AS manufacturing_order_id,\n    NULL::uuid AS reception_receipt_id,\n    NULL::uuid AS supplier_id,\n    (((executed_services.cost_amount)::numeric * executed_services.quantity) / (100)::numeric) AS reception_price,\n    (((executed_services.sale_amount)::numeric * executed_services.quantity) / (100)::numeric) AS exit_price,\n    executed_services.quantity,\n    service_template.name AS item_name,\n    'SERVICE'::text AS category,\n    'SERVICE'::text AS product\n   FROM (executed_services\n     JOIN service_template ON ((executed_services.service_id = service_template.id)));"));
    }

    /**
     * Create an aliased <code>public.inventory_entries_view</code> table
     * reference
     */
    public InventoryEntriesView(String alias) {
        this(DSL.name(alias), INVENTORY_ENTRIES_VIEW);
    }

    /**
     * Create an aliased <code>public.inventory_entries_view</code> table
     * reference
     */
    public InventoryEntriesView(Name alias) {
        this(alias, INVENTORY_ENTRIES_VIEW);
    }

    /**
     * Create a <code>public.inventory_entries_view</code> table reference
     */
    public InventoryEntriesView() {
        this(DSL.name("inventory_entries_view"), null);
    }

    public <O extends Record> InventoryEntriesView(Table<O> child, ForeignKey<O, InventoryEntriesViewRecord> key) {
        super(child, key, INVENTORY_ENTRIES_VIEW);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public InventoryEntriesView as(String alias) {
        return new InventoryEntriesView(DSL.name(alias), this);
    }

    @Override
    public InventoryEntriesView as(Name alias) {
        return new InventoryEntriesView(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public InventoryEntriesView rename(String name) {
        return new InventoryEntriesView(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public InventoryEntriesView rename(Name name) {
        return new InventoryEntriesView(name, null);
    }

    // -------------------------------------------------------------------------
    // Row12 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row12<UUID, LocalDateTime, UUID, UUID, UUID, UUID, BigDecimal, BigDecimal, BigDecimal, String, String, String> fieldsRow() {
        return (Row12) super.fieldsRow();
    }
}
