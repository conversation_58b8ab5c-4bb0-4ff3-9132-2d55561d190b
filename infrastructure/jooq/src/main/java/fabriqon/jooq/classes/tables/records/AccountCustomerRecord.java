/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.AccountCustomer;

import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record2;
import org.jooq.Row2;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AccountCustomerRecord extends TableRecordImpl<AccountCustomerRecord> implements Record2<UUID, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.account_customer.customer_id</code>.
     */
    public void setCustomerId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.account_customer.customer_id</code>.
     */
    public UUID getCustomerId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.account_customer.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.account_customer.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(1);
    }

    // -------------------------------------------------------------------------
    // Record2 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row2<UUID, UUID> fieldsRow() {
        return (Row2) super.fieldsRow();
    }

    @Override
    public Row2<UUID, UUID> valuesRow() {
        return (Row2) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return AccountCustomer.ACCOUNT_CUSTOMER.CUSTOMER_ID;
    }

    @Override
    public Field<UUID> field2() {
        return AccountCustomer.ACCOUNT_CUSTOMER.OWNER_ID;
    }

    @Override
    public UUID component1() {
        return getCustomerId();
    }

    @Override
    public UUID component2() {
        return getOwnerId();
    }

    @Override
    public UUID value1() {
        return getCustomerId();
    }

    @Override
    public UUID value2() {
        return getOwnerId();
    }

    @Override
    public AccountCustomerRecord value1(UUID value) {
        setCustomerId(value);
        return this;
    }

    @Override
    public AccountCustomerRecord value2(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public AccountCustomerRecord values(UUID value1, UUID value2) {
        value1(value1);
        value2(value2);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached AccountCustomerRecord
     */
    public AccountCustomerRecord() {
        super(AccountCustomer.ACCOUNT_CUSTOMER);
    }

    /**
     * Create a detached, initialised AccountCustomerRecord
     */
    public AccountCustomerRecord(UUID customerId, UUID ownerId) {
        super(AccountCustomer.ACCOUNT_CUSTOMER);

        setCustomerId(customerId);
        setOwnerId(ownerId);
    }
}
