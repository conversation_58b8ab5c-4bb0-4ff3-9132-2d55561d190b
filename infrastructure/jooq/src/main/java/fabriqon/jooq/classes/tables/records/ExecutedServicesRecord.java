/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.ExecutedServices;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Currency;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record11;
import org.jooq.Record4;
import org.jooq.Row11;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ExecutedServicesRecord extends UpdatableRecordImpl<ExecutedServicesRecord> implements Record11<LocalDateTime, LocalDateTime, Boolean, UUID, UUID, UUID, UUID, BigDecimal, Long, Long, Currency> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.executed_services.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.executed_services.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(0);
    }

    /**
     * Setter for <code>public.executed_services.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.executed_services.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.executed_services.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.executed_services.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(2);
    }

    /**
     * Setter for <code>public.executed_services.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.executed_services.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(3);
    }

    /**
     * Setter for <code>public.executed_services.sales_order_id</code>.
     */
    public void setSalesOrderId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.executed_services.sales_order_id</code>.
     */
    public UUID getSalesOrderId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.executed_services.servicing_order_id</code>.
     */
    public void setServicingOrderId(UUID value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.executed_services.servicing_order_id</code>.
     */
    public UUID getServicingOrderId() {
        return (UUID) get(5);
    }

    /**
     * Setter for <code>public.executed_services.service_id</code>.
     */
    public void setServiceId(UUID value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.executed_services.service_id</code>.
     */
    public UUID getServiceId() {
        return (UUID) get(6);
    }

    /**
     * Setter for <code>public.executed_services.quantity</code>.
     */
    public void setQuantity(BigDecimal value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.executed_services.quantity</code>.
     */
    public BigDecimal getQuantity() {
        return (BigDecimal) get(7);
    }

    /**
     * Setter for <code>public.executed_services.cost_amount</code>.
     */
    public void setCostAmount(Long value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.executed_services.cost_amount</code>.
     */
    public Long getCostAmount() {
        return (Long) get(8);
    }

    /**
     * Setter for <code>public.executed_services.sale_amount</code>.
     */
    public void setSaleAmount(Long value) {
        set(9, value);
    }

    /**
     * Getter for <code>public.executed_services.sale_amount</code>.
     */
    public Long getSaleAmount() {
        return (Long) get(9);
    }

    /**
     * Setter for <code>public.executed_services.service_currency</code>.
     */
    public void setServiceCurrency(Currency value) {
        set(10, value);
    }

    /**
     * Getter for <code>public.executed_services.service_currency</code>.
     */
    public Currency getServiceCurrency() {
        return (Currency) get(10);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record4<UUID, UUID, UUID, UUID> key() {
        return (Record4) super.key();
    }

    // -------------------------------------------------------------------------
    // Record11 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row11<LocalDateTime, LocalDateTime, Boolean, UUID, UUID, UUID, UUID, BigDecimal, Long, Long, Currency> fieldsRow() {
        return (Row11) super.fieldsRow();
    }

    @Override
    public Row11<LocalDateTime, LocalDateTime, Boolean, UUID, UUID, UUID, UUID, BigDecimal, Long, Long, Currency> valuesRow() {
        return (Row11) super.valuesRow();
    }

    @Override
    public Field<LocalDateTime> field1() {
        return ExecutedServices.EXECUTED_SERVICES.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return ExecutedServices.EXECUTED_SERVICES.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field3() {
        return ExecutedServices.EXECUTED_SERVICES.DELETED;
    }

    @Override
    public Field<UUID> field4() {
        return ExecutedServices.EXECUTED_SERVICES.OWNER_ID;
    }

    @Override
    public Field<UUID> field5() {
        return ExecutedServices.EXECUTED_SERVICES.SALES_ORDER_ID;
    }

    @Override
    public Field<UUID> field6() {
        return ExecutedServices.EXECUTED_SERVICES.SERVICING_ORDER_ID;
    }

    @Override
    public Field<UUID> field7() {
        return ExecutedServices.EXECUTED_SERVICES.SERVICE_ID;
    }

    @Override
    public Field<BigDecimal> field8() {
        return ExecutedServices.EXECUTED_SERVICES.QUANTITY;
    }

    @Override
    public Field<Long> field9() {
        return ExecutedServices.EXECUTED_SERVICES.COST_AMOUNT;
    }

    @Override
    public Field<Long> field10() {
        return ExecutedServices.EXECUTED_SERVICES.SALE_AMOUNT;
    }

    @Override
    public Field<Currency> field11() {
        return ExecutedServices.EXECUTED_SERVICES.SERVICE_CURRENCY;
    }

    @Override
    public LocalDateTime component1() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component2() {
        return getUpdateTime();
    }

    @Override
    public Boolean component3() {
        return getDeleted();
    }

    @Override
    public UUID component4() {
        return getOwnerId();
    }

    @Override
    public UUID component5() {
        return getSalesOrderId();
    }

    @Override
    public UUID component6() {
        return getServicingOrderId();
    }

    @Override
    public UUID component7() {
        return getServiceId();
    }

    @Override
    public BigDecimal component8() {
        return getQuantity();
    }

    @Override
    public Long component9() {
        return getCostAmount();
    }

    @Override
    public Long component10() {
        return getSaleAmount();
    }

    @Override
    public Currency component11() {
        return getServiceCurrency();
    }

    @Override
    public LocalDateTime value1() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value2() {
        return getUpdateTime();
    }

    @Override
    public Boolean value3() {
        return getDeleted();
    }

    @Override
    public UUID value4() {
        return getOwnerId();
    }

    @Override
    public UUID value5() {
        return getSalesOrderId();
    }

    @Override
    public UUID value6() {
        return getServicingOrderId();
    }

    @Override
    public UUID value7() {
        return getServiceId();
    }

    @Override
    public BigDecimal value8() {
        return getQuantity();
    }

    @Override
    public Long value9() {
        return getCostAmount();
    }

    @Override
    public Long value10() {
        return getSaleAmount();
    }

    @Override
    public Currency value11() {
        return getServiceCurrency();
    }

    @Override
    public ExecutedServicesRecord value1(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public ExecutedServicesRecord value2(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public ExecutedServicesRecord value3(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public ExecutedServicesRecord value4(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public ExecutedServicesRecord value5(UUID value) {
        setSalesOrderId(value);
        return this;
    }

    @Override
    public ExecutedServicesRecord value6(UUID value) {
        setServicingOrderId(value);
        return this;
    }

    @Override
    public ExecutedServicesRecord value7(UUID value) {
        setServiceId(value);
        return this;
    }

    @Override
    public ExecutedServicesRecord value8(BigDecimal value) {
        setQuantity(value);
        return this;
    }

    @Override
    public ExecutedServicesRecord value9(Long value) {
        setCostAmount(value);
        return this;
    }

    @Override
    public ExecutedServicesRecord value10(Long value) {
        setSaleAmount(value);
        return this;
    }

    @Override
    public ExecutedServicesRecord value11(Currency value) {
        setServiceCurrency(value);
        return this;
    }

    @Override
    public ExecutedServicesRecord values(LocalDateTime value1, LocalDateTime value2, Boolean value3, UUID value4, UUID value5, UUID value6, UUID value7, BigDecimal value8, Long value9, Long value10, Currency value11) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ExecutedServicesRecord
     */
    public ExecutedServicesRecord() {
        super(ExecutedServices.EXECUTED_SERVICES);
    }

    /**
     * Create a detached, initialised ExecutedServicesRecord
     */
    public ExecutedServicesRecord(LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, UUID salesOrderId, UUID servicingOrderId, UUID serviceId, BigDecimal quantity, Long costAmount, Long saleAmount, Currency serviceCurrency) {
        super(ExecutedServices.EXECUTED_SERVICES);

        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setSalesOrderId(salesOrderId);
        setServicingOrderId(servicingOrderId);
        setServiceId(serviceId);
        setQuantity(quantity);
        setCostAmount(costAmount);
        setSaleAmount(saleAmount);
        setServiceCurrency(serviceCurrency);
    }
}
