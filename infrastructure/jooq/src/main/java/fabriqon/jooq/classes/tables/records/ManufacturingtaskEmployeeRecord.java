/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.ManufacturingtaskEmployee;

import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record3;
import org.jooq.Row3;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ManufacturingtaskEmployeeRecord extends TableRecordImpl<ManufacturingtaskEmployeeRecord> implements Record3<UUID, UUID, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.manufacturingtask_employee.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.manufacturingtask_employee.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.manufacturingtask_employee.user_id</code>.
     */
    public void setUserId(UUID value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.manufacturingtask_employee.user_id</code>.
     */
    public UUID getUserId() {
        return (UUID) get(1);
    }

    /**
     * Setter for
     * <code>public.manufacturingtask_employee.manufacturing_task_id</code>.
     */
    public void setManufacturingTaskId(UUID value) {
        set(2, value);
    }

    /**
     * Getter for
     * <code>public.manufacturingtask_employee.manufacturing_task_id</code>.
     */
    public UUID getManufacturingTaskId() {
        return (UUID) get(2);
    }

    // -------------------------------------------------------------------------
    // Record3 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row3<UUID, UUID, UUID> fieldsRow() {
        return (Row3) super.fieldsRow();
    }

    @Override
    public Row3<UUID, UUID, UUID> valuesRow() {
        return (Row3) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return ManufacturingtaskEmployee.MANUFACTURINGTASK_EMPLOYEE.OWNER_ID;
    }

    @Override
    public Field<UUID> field2() {
        return ManufacturingtaskEmployee.MANUFACTURINGTASK_EMPLOYEE.USER_ID;
    }

    @Override
    public Field<UUID> field3() {
        return ManufacturingtaskEmployee.MANUFACTURINGTASK_EMPLOYEE.MANUFACTURING_TASK_ID;
    }

    @Override
    public UUID component1() {
        return getOwnerId();
    }

    @Override
    public UUID component2() {
        return getUserId();
    }

    @Override
    public UUID component3() {
        return getManufacturingTaskId();
    }

    @Override
    public UUID value1() {
        return getOwnerId();
    }

    @Override
    public UUID value2() {
        return getUserId();
    }

    @Override
    public UUID value3() {
        return getManufacturingTaskId();
    }

    @Override
    public ManufacturingtaskEmployeeRecord value1(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public ManufacturingtaskEmployeeRecord value2(UUID value) {
        setUserId(value);
        return this;
    }

    @Override
    public ManufacturingtaskEmployeeRecord value3(UUID value) {
        setManufacturingTaskId(value);
        return this;
    }

    @Override
    public ManufacturingtaskEmployeeRecord values(UUID value1, UUID value2, UUID value3) {
        value1(value1);
        value2(value2);
        value3(value3);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ManufacturingtaskEmployeeRecord
     */
    public ManufacturingtaskEmployeeRecord() {
        super(ManufacturingtaskEmployee.MANUFACTURINGTASK_EMPLOYEE);
    }

    /**
     * Create a detached, initialised ManufacturingtaskEmployeeRecord
     */
    public ManufacturingtaskEmployeeRecord(UUID ownerId, UUID userId, UUID manufacturingTaskId) {
        super(ManufacturingtaskEmployee.MANUFACTURINGTASK_EMPLOYEE);

        setOwnerId(ownerId);
        setUserId(userId);
        setManufacturingTaskId(manufacturingTaskId);
    }
}
