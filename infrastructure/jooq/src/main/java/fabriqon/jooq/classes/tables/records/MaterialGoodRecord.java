/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.MaterialGood;

import java.time.LocalDateTime;
import java.util.Currency;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.Record15;
import org.jooq.Row15;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MaterialGoodRecord extends UpdatableRecordImpl<MaterialGoodRecord> implements Record15<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, String, UUID, UUID, String, Long, Currency, Object, String, JSONB> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.material_good.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.material_good.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.material_good.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.material_good.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.material_good.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.material_good.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.material_good.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.material_good.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.material_good.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.material_good.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.material_good.code</code>.
     */
    public void setCode(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.material_good.code</code>.
     */
    public String getCode() {
        return (String) get(5);
    }

    /**
     * Setter for <code>public.material_good.name</code>.
     */
    public void setName(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.material_good.name</code>.
     */
    public String getName() {
        return (String) get(6);
    }

    /**
     * Setter for <code>public.material_good.category_id</code>.
     */
    public void setCategoryId(UUID value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.material_good.category_id</code>.
     */
    public UUID getCategoryId() {
        return (UUID) get(7);
    }

    /**
     * Setter for <code>public.material_good.parent_id</code>.
     */
    public void setParentId(UUID value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.material_good.parent_id</code>.
     */
    public UUID getParentId() {
        return (UUID) get(8);
    }

    /**
     * Setter for <code>public.material_good.description</code>.
     */
    public void setDescription(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>public.material_good.description</code>.
     */
    public String getDescription() {
        return (String) get(9);
    }

    /**
     * Setter for <code>public.material_good.sell_price_amount</code>.
     */
    public void setSellPriceAmount(Long value) {
        set(10, value);
    }

    /**
     * Getter for <code>public.material_good.sell_price_amount</code>.
     */
    public Long getSellPriceAmount() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>public.material_good.sell_price_currency</code>.
     */
    public void setSellPriceCurrency(Currency value) {
        set(11, value);
    }

    /**
     * Getter for <code>public.material_good.sell_price_currency</code>.
     */
    public Currency getSellPriceCurrency() {
        return (Currency) get(11);
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public void setTextSearch(Object value) {
        set(12, value);
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public Object getTextSearch() {
        return get(12);
    }

    /**
     * Setter for <code>public.material_good.external_code</code>.
     */
    public void setExternalCode(String value) {
        set(13, value);
    }

    /**
     * Getter for <code>public.material_good.external_code</code>.
     */
    public String getExternalCode() {
        return (String) get(13);
    }

    /**
     * Setter for <code>public.material_good.details</code>.
     */
    public void setDetails(JSONB value) {
        set(14, value);
    }

    /**
     * Getter for <code>public.material_good.details</code>.
     */
    public JSONB getDetails() {
        return (JSONB) get(14);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record15 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row15<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, String, UUID, UUID, String, Long, Currency, Object, String, JSONB> fieldsRow() {
        return (Row15) super.fieldsRow();
    }

    @Override
    public Row15<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, String, UUID, UUID, String, Long, Currency, Object, String, JSONB> valuesRow() {
        return (Row15) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return MaterialGood.MATERIAL_GOOD.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return MaterialGood.MATERIAL_GOOD.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return MaterialGood.MATERIAL_GOOD.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return MaterialGood.MATERIAL_GOOD.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return MaterialGood.MATERIAL_GOOD.OWNER_ID;
    }

    @Override
    public Field<String> field6() {
        return MaterialGood.MATERIAL_GOOD.CODE;
    }

    @Override
    public Field<String> field7() {
        return MaterialGood.MATERIAL_GOOD.NAME;
    }

    @Override
    public Field<UUID> field8() {
        return MaterialGood.MATERIAL_GOOD.CATEGORY_ID;
    }

    @Override
    public Field<UUID> field9() {
        return MaterialGood.MATERIAL_GOOD.PARENT_ID;
    }

    @Override
    public Field<String> field10() {
        return MaterialGood.MATERIAL_GOOD.DESCRIPTION;
    }

    @Override
    public Field<Long> field11() {
        return MaterialGood.MATERIAL_GOOD.SELL_PRICE_AMOUNT;
    }

    @Override
    public Field<Currency> field12() {
        return MaterialGood.MATERIAL_GOOD.SELL_PRICE_CURRENCY;
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public Field<Object> field13() {
        return MaterialGood.MATERIAL_GOOD.TEXT_SEARCH;
    }

    @Override
    public Field<String> field14() {
        return MaterialGood.MATERIAL_GOOD.EXTERNAL_CODE;
    }

    @Override
    public Field<JSONB> field15() {
        return MaterialGood.MATERIAL_GOOD.DETAILS;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public String component6() {
        return getCode();
    }

    @Override
    public String component7() {
        return getName();
    }

    @Override
    public UUID component8() {
        return getCategoryId();
    }

    @Override
    public UUID component9() {
        return getParentId();
    }

    @Override
    public String component10() {
        return getDescription();
    }

    @Override
    public Long component11() {
        return getSellPriceAmount();
    }

    @Override
    public Currency component12() {
        return getSellPriceCurrency();
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public Object component13() {
        return getTextSearch();
    }

    @Override
    public String component14() {
        return getExternalCode();
    }

    @Override
    public JSONB component15() {
        return getDetails();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public String value6() {
        return getCode();
    }

    @Override
    public String value7() {
        return getName();
    }

    @Override
    public UUID value8() {
        return getCategoryId();
    }

    @Override
    public UUID value9() {
        return getParentId();
    }

    @Override
    public String value10() {
        return getDescription();
    }

    @Override
    public Long value11() {
        return getSellPriceAmount();
    }

    @Override
    public Currency value12() {
        return getSellPriceCurrency();
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public Object value13() {
        return getTextSearch();
    }

    @Override
    public String value14() {
        return getExternalCode();
    }

    @Override
    public JSONB value15() {
        return getDetails();
    }

    @Override
    public MaterialGoodRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public MaterialGoodRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public MaterialGoodRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public MaterialGoodRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public MaterialGoodRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public MaterialGoodRecord value6(String value) {
        setCode(value);
        return this;
    }

    @Override
    public MaterialGoodRecord value7(String value) {
        setName(value);
        return this;
    }

    @Override
    public MaterialGoodRecord value8(UUID value) {
        setCategoryId(value);
        return this;
    }

    @Override
    public MaterialGoodRecord value9(UUID value) {
        setParentId(value);
        return this;
    }

    @Override
    public MaterialGoodRecord value10(String value) {
        setDescription(value);
        return this;
    }

    @Override
    public MaterialGoodRecord value11(Long value) {
        setSellPriceAmount(value);
        return this;
    }

    @Override
    public MaterialGoodRecord value12(Currency value) {
        setSellPriceCurrency(value);
        return this;
    }

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    @Override
    public MaterialGoodRecord value13(Object value) {
        setTextSearch(value);
        return this;
    }

    @Override
    public MaterialGoodRecord value14(String value) {
        setExternalCode(value);
        return this;
    }

    @Override
    public MaterialGoodRecord value15(JSONB value) {
        setDetails(value);
        return this;
    }

    @Override
    public MaterialGoodRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, String value6, String value7, UUID value8, UUID value9, String value10, Long value11, Currency value12, Object value13, String value14, JSONB value15) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached MaterialGoodRecord
     */
    public MaterialGoodRecord() {
        super(MaterialGood.MATERIAL_GOOD);
    }

    /**
     * Create a detached, initialised MaterialGoodRecord
     */
    public MaterialGoodRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, String code, String name, UUID categoryId, UUID parentId, String description, Long sellPriceAmount, Currency sellPriceCurrency, Object textSearch, String externalCode, JSONB details) {
        super(MaterialGood.MATERIAL_GOOD);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setCode(code);
        setName(name);
        setCategoryId(categoryId);
        setParentId(parentId);
        setDescription(description);
        setSellPriceAmount(sellPriceAmount);
        setSellPriceCurrency(sellPriceCurrency);
        setTextSearch(textSearch);
        setExternalCode(externalCode);
        setDetails(details);
    }
}
