/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.ManufacturingWorkstationRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row7;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ManufacturingWorkstation extends TableImpl<ManufacturingWorkstationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.manufacturing_workstation</code>
     */
    public static final ManufacturingWorkstation MANUFACTURING_WORKSTATION = new ManufacturingWorkstation();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ManufacturingWorkstationRecord> getRecordType() {
        return ManufacturingWorkstationRecord.class;
    }

    /**
     * The column <code>public.manufacturing_workstation.id</code>.
     */
    public final TableField<ManufacturingWorkstationRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.manufacturing_workstation.create_time</code>.
     */
    public final TableField<ManufacturingWorkstationRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.manufacturing_workstation.update_time</code>.
     */
    public final TableField<ManufacturingWorkstationRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.manufacturing_workstation.deleted</code>.
     */
    public final TableField<ManufacturingWorkstationRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.manufacturing_workstation.owner_id</code>.
     */
    public final TableField<ManufacturingWorkstationRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.manufacturing_workstation.name</code>.
     */
    public final TableField<ManufacturingWorkstationRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(200), this, "");

    /**
     * The column <code>public.manufacturing_workstation.details</code>.
     */
    public final TableField<ManufacturingWorkstationRecord, JSONB> DETAILS = createField(DSL.name("details"), SQLDataType.JSONB, this, "");

    private ManufacturingWorkstation(Name alias, Table<ManufacturingWorkstationRecord> aliased) {
        this(alias, aliased, null);
    }

    private ManufacturingWorkstation(Name alias, Table<ManufacturingWorkstationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.manufacturing_workstation</code> table
     * reference
     */
    public ManufacturingWorkstation(String alias) {
        this(DSL.name(alias), MANUFACTURING_WORKSTATION);
    }

    /**
     * Create an aliased <code>public.manufacturing_workstation</code> table
     * reference
     */
    public ManufacturingWorkstation(Name alias) {
        this(alias, MANUFACTURING_WORKSTATION);
    }

    /**
     * Create a <code>public.manufacturing_workstation</code> table reference
     */
    public ManufacturingWorkstation() {
        this(DSL.name("manufacturing_workstation"), null);
    }

    public <O extends Record> ManufacturingWorkstation(Table<O> child, ForeignKey<O, ManufacturingWorkstationRecord> key) {
        super(child, key, MANUFACTURING_WORKSTATION);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public UniqueKey<ManufacturingWorkstationRecord> getPrimaryKey() {
        return Keys.MANUFACTURING_WORKSTATION_PKEY;
    }

    @Override
    public List<ForeignKey<ManufacturingWorkstationRecord, ?>> getReferences() {
        return Arrays.asList(Keys.MANUFACTURING_WORKSTATION__MANUFACTURING_WORKSTATION_OWNER_ID_FKEY);
    }

    private transient Account _account;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.MANUFACTURING_WORKSTATION__MANUFACTURING_WORKSTATION_OWNER_ID_FKEY);

        return _account;
    }

    @Override
    public ManufacturingWorkstation as(String alias) {
        return new ManufacturingWorkstation(DSL.name(alias), this);
    }

    @Override
    public ManufacturingWorkstation as(Name alias) {
        return new ManufacturingWorkstation(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ManufacturingWorkstation rename(String name) {
        return new ManufacturingWorkstation(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ManufacturingWorkstation rename(Name name) {
        return new ManufacturingWorkstation(name, null);
    }

    // -------------------------------------------------------------------------
    // Row7 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row7<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, JSONB> fieldsRow() {
        return (Row7) super.fieldsRow();
    }
}
