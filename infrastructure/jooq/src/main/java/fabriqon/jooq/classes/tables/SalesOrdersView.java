/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.SalesOrdersViewRecord;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row7;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SalesOrdersView extends TableImpl<SalesOrdersViewRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.sales_orders_view</code>
     */
    public static final SalesOrdersView SALES_ORDERS_VIEW = new SalesOrdersView();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SalesOrdersViewRecord> getRecordType() {
        return SalesOrdersViewRecord.class;
    }

    /**
     * The column <code>public.sales_orders_view.owner_id</code>.
     */
    public final TableField<SalesOrdersViewRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.sales_orders_view.create_time</code>.
     */
    public final TableField<SalesOrdersViewRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>public.sales_orders_view.sales_order_id</code>.
     */
    public final TableField<SalesOrdersViewRecord, UUID> SALES_ORDER_ID = createField(DSL.name("sales_order_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.sales_orders_view.exit_price</code>.
     */
    public final TableField<SalesOrdersViewRecord, BigDecimal> EXIT_PRICE = createField(DSL.name("exit_price"), SQLDataType.NUMERIC, this, "");

    /**
     * The column <code>public.sales_orders_view.quantity</code>.
     */
    public final TableField<SalesOrdersViewRecord, BigDecimal> QUANTITY = createField(DSL.name("quantity"), SQLDataType.NUMERIC, this, "");

    /**
     * The column <code>public.sales_orders_view.customer</code>.
     */
    public final TableField<SalesOrdersViewRecord, String> CUSTOMER = createField(DSL.name("customer"), SQLDataType.VARCHAR(200), this, "");

    /**
     * The column <code>public.sales_orders_view.product</code>.
     */
    public final TableField<SalesOrdersViewRecord, String> PRODUCT = createField(DSL.name("product"), SQLDataType.CLOB, this, "");

    private SalesOrdersView(Name alias, Table<SalesOrdersViewRecord> aliased) {
        this(alias, aliased, null);
    }

    private SalesOrdersView(Name alias, Table<SalesOrdersViewRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.view("create view \"sales_orders_view\" as  SELECT inventory.owner_id,\n    inventory.create_time,\n    inventory.sales_order_id,\n    (((inventory.exit_price_amount)::numeric * abs(inventory.quantity)) / (100)::numeric) AS exit_price,\n    inventory.quantity,\n    company.company_name AS customer,\n    'PRODUCT'::text AS product\n   FROM ((inventory\n     JOIN sales_order ON ((inventory.sales_order_id = sales_order.id)))\n     JOIN company ON ((sales_order.customer_id = company.id)))\n  WHERE (inventory.sales_order_id IS NOT NULL)\nUNION ALL\n SELECT executed_services.owner_id,\n    executed_services.create_time,\n    executed_services.sales_order_id,\n    (((executed_services.sale_amount)::numeric * executed_services.quantity) / (100)::numeric) AS exit_price,\n    executed_services.quantity,\n    company.company_name AS customer,\n    'SERVICE'::text AS product\n   FROM ((executed_services\n     JOIN sales_order ON ((executed_services.sales_order_id = sales_order.id)))\n     JOIN company ON ((sales_order.customer_id = company.id)));"));
    }

    /**
     * Create an aliased <code>public.sales_orders_view</code> table reference
     */
    public SalesOrdersView(String alias) {
        this(DSL.name(alias), SALES_ORDERS_VIEW);
    }

    /**
     * Create an aliased <code>public.sales_orders_view</code> table reference
     */
    public SalesOrdersView(Name alias) {
        this(alias, SALES_ORDERS_VIEW);
    }

    /**
     * Create a <code>public.sales_orders_view</code> table reference
     */
    public SalesOrdersView() {
        this(DSL.name("sales_orders_view"), null);
    }

    public <O extends Record> SalesOrdersView(Table<O> child, ForeignKey<O, SalesOrdersViewRecord> key) {
        super(child, key, SALES_ORDERS_VIEW);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public SalesOrdersView as(String alias) {
        return new SalesOrdersView(DSL.name(alias), this);
    }

    @Override
    public SalesOrdersView as(Name alias) {
        return new SalesOrdersView(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SalesOrdersView rename(String name) {
        return new SalesOrdersView(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public SalesOrdersView rename(Name name) {
        return new SalesOrdersView(name, null);
    }

    // -------------------------------------------------------------------------
    // Row7 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row7<UUID, LocalDateTime, UUID, BigDecimal, BigDecimal, String, String> fieldsRow() {
        return (Row7) super.fieldsRow();
    }
}
