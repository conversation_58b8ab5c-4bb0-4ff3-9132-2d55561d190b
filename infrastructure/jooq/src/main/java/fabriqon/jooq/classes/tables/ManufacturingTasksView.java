/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.ManufacturingTasksViewRecord;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row7;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ManufacturingTasksView extends TableImpl<ManufacturingTasksViewRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.manufacturing_tasks_view</code>
     */
    public static final ManufacturingTasksView MANUFACTURING_TASKS_VIEW = new ManufacturingTasksView();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ManufacturingTasksViewRecord> getRecordType() {
        return ManufacturingTasksViewRecord.class;
    }

    /**
     * The column <code>public.manufacturing_tasks_view.owner_id</code>.
     */
    public final TableField<ManufacturingTasksViewRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.manufacturing_tasks_view.execution_time</code>.
     */
    public final TableField<ManufacturingTasksViewRecord, LocalDateTime> EXECUTION_TIME = createField(DSL.name("execution_time"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>public.manufacturing_tasks_view.task_name</code>.
     */
    public final TableField<ManufacturingTasksViewRecord, JSONB> TASK_NAME = createField(DSL.name("task_name"), SQLDataType.JSONB, this, "");

    /**
     * The column <code>public.manufacturing_tasks_view.employee_name</code>.
     */
    public final TableField<ManufacturingTasksViewRecord, String> EMPLOYEE_NAME = createField(DSL.name("employee_name"), SQLDataType.VARCHAR(200), this, "");

    /**
     * The column <code>public.manufacturing_tasks_view.quantity</code>.
     */
    public final TableField<ManufacturingTasksViewRecord, BigDecimal> QUANTITY = createField(DSL.name("quantity"), SQLDataType.NUMERIC(12, 4), this, "");

    /**
     * The column <code>public.manufacturing_tasks_view.product_name</code>.
     */
    public final TableField<ManufacturingTasksViewRecord, String> PRODUCT_NAME = createField(DSL.name("product_name"), SQLDataType.VARCHAR(200), this, "");

    /**
     * The column <code>public.manufacturing_tasks_view.product</code>.
     */
    public final TableField<ManufacturingTasksViewRecord, String> PRODUCT = createField(DSL.name("product"), SQLDataType.CLOB, this, "");

    private ManufacturingTasksView(Name alias, Table<ManufacturingTasksViewRecord> aliased) {
        this(alias, aliased, null);
    }

    private ManufacturingTasksView(Name alias, Table<ManufacturingTasksViewRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.view("create view \"manufacturing_tasks_view\" as  SELECT manufacturing_task.owner_id,\n    manufacturing_task.end_time AS execution_time,\n    (manufacturing_task.details -> 'name'::text) AS task_name,\n    users.name AS employee_name,\n    manufacturing_order.quantity,\n    material_good.name AS product_name,\n    'PRODUCT'::text AS product\n   FROM ((((manufacturing_task\n     JOIN manufacturingtask_employee ON ((manufacturing_task.id = manufacturingtask_employee.manufacturing_task_id)))\n     JOIN users ON ((manufacturingtask_employee.user_id = users.id)))\n     JOIN manufacturing_order ON ((manufacturing_task.manufacturing_order_id = manufacturing_order.id)))\n     JOIN material_good ON ((manufacturing_order.product_id = material_good.id)))\n  WHERE ((manufacturing_task.status)::text = 'DONE'::text)\nUNION ALL\n SELECT manufacturing_task.owner_id,\n    manufacturing_task.end_time AS execution_time,\n    (manufacturing_task.details -> 'name'::text) AS task_name,\n    users.name AS employee_name,\n    manufacturing_order.quantity,\n    service_template.name AS product_name,\n    'SERVICE'::text AS product\n   FROM ((((manufacturing_task\n     JOIN manufacturingtask_employee ON ((manufacturing_task.id = manufacturingtask_employee.manufacturing_task_id)))\n     JOIN users ON ((manufacturingtask_employee.user_id = users.id)))\n     JOIN manufacturing_order ON ((manufacturing_task.manufacturing_order_id = manufacturing_order.id)))\n     JOIN service_template ON ((manufacturing_order.service_id = service_template.id)))\n  WHERE ((manufacturing_task.status)::text = 'DONE'::text);"));
    }

    /**
     * Create an aliased <code>public.manufacturing_tasks_view</code> table
     * reference
     */
    public ManufacturingTasksView(String alias) {
        this(DSL.name(alias), MANUFACTURING_TASKS_VIEW);
    }

    /**
     * Create an aliased <code>public.manufacturing_tasks_view</code> table
     * reference
     */
    public ManufacturingTasksView(Name alias) {
        this(alias, MANUFACTURING_TASKS_VIEW);
    }

    /**
     * Create a <code>public.manufacturing_tasks_view</code> table reference
     */
    public ManufacturingTasksView() {
        this(DSL.name("manufacturing_tasks_view"), null);
    }

    public <O extends Record> ManufacturingTasksView(Table<O> child, ForeignKey<O, ManufacturingTasksViewRecord> key) {
        super(child, key, MANUFACTURING_TASKS_VIEW);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public ManufacturingTasksView as(String alias) {
        return new ManufacturingTasksView(DSL.name(alias), this);
    }

    @Override
    public ManufacturingTasksView as(Name alias) {
        return new ManufacturingTasksView(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ManufacturingTasksView rename(String name) {
        return new ManufacturingTasksView(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ManufacturingTasksView rename(Name name) {
        return new ManufacturingTasksView(name, null);
    }

    // -------------------------------------------------------------------------
    // Row7 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row7<UUID, LocalDateTime, JSONB, String, BigDecimal, String, String> fieldsRow() {
        return (Row7) super.fieldsRow();
    }
}
