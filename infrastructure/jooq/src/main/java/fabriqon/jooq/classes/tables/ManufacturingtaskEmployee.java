/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.ManufacturingtaskEmployeeRecord;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row3;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ManufacturingtaskEmployee extends TableImpl<ManufacturingtaskEmployeeRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.manufacturingtask_employee</code>
     */
    public static final ManufacturingtaskEmployee MANUFACTURINGTASK_EMPLOYEE = new ManufacturingtaskEmployee();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ManufacturingtaskEmployeeRecord> getRecordType() {
        return ManufacturingtaskEmployeeRecord.class;
    }

    /**
     * The column <code>public.manufacturingtask_employee.owner_id</code>.
     */
    public final TableField<ManufacturingtaskEmployeeRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.manufacturingtask_employee.user_id</code>.
     */
    public final TableField<ManufacturingtaskEmployeeRecord, UUID> USER_ID = createField(DSL.name("user_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column
     * <code>public.manufacturingtask_employee.manufacturing_task_id</code>.
     */
    public final TableField<ManufacturingtaskEmployeeRecord, UUID> MANUFACTURING_TASK_ID = createField(DSL.name("manufacturing_task_id"), SQLDataType.UUID.nullable(false), this, "");

    private ManufacturingtaskEmployee(Name alias, Table<ManufacturingtaskEmployeeRecord> aliased) {
        this(alias, aliased, null);
    }

    private ManufacturingtaskEmployee(Name alias, Table<ManufacturingtaskEmployeeRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.manufacturingtask_employee</code> table
     * reference
     */
    public ManufacturingtaskEmployee(String alias) {
        this(DSL.name(alias), MANUFACTURINGTASK_EMPLOYEE);
    }

    /**
     * Create an aliased <code>public.manufacturingtask_employee</code> table
     * reference
     */
    public ManufacturingtaskEmployee(Name alias) {
        this(alias, MANUFACTURINGTASK_EMPLOYEE);
    }

    /**
     * Create a <code>public.manufacturingtask_employee</code> table reference
     */
    public ManufacturingtaskEmployee() {
        this(DSL.name("manufacturingtask_employee"), null);
    }

    public <O extends Record> ManufacturingtaskEmployee(Table<O> child, ForeignKey<O, ManufacturingtaskEmployeeRecord> key) {
        super(child, key, MANUFACTURINGTASK_EMPLOYEE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<ForeignKey<ManufacturingtaskEmployeeRecord, ?>> getReferences() {
        return Arrays.asList(Keys.MANUFACTURINGTASK_EMPLOYEE__MANUFACTURINGTASK_EMPLOYEE_OWNER_ID_FKEY, Keys.MANUFACTURINGTASK_EMPLOYEE__MANUFACTURINGTASK_EMPLOYEE_USER_ID_FKEY, Keys.MANUFACTURINGTASK_EMPLOYEE__MANUFACTURINGTASK_EMPLOYEE_MANUFACTURING_TASK_ID_FKEY);
    }

    private transient Account _account;
    private transient Users _users;
    private transient ManufacturingTask _manufacturingTask;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.MANUFACTURINGTASK_EMPLOYEE__MANUFACTURINGTASK_EMPLOYEE_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.users</code> table.
     */
    public Users users() {
        if (_users == null)
            _users = new Users(this, Keys.MANUFACTURINGTASK_EMPLOYEE__MANUFACTURINGTASK_EMPLOYEE_USER_ID_FKEY);

        return _users;
    }

    /**
     * Get the implicit join path to the <code>public.manufacturing_task</code>
     * table.
     */
    public ManufacturingTask manufacturingTask() {
        if (_manufacturingTask == null)
            _manufacturingTask = new ManufacturingTask(this, Keys.MANUFACTURINGTASK_EMPLOYEE__MANUFACTURINGTASK_EMPLOYEE_MANUFACTURING_TASK_ID_FKEY);

        return _manufacturingTask;
    }

    @Override
    public ManufacturingtaskEmployee as(String alias) {
        return new ManufacturingtaskEmployee(DSL.name(alias), this);
    }

    @Override
    public ManufacturingtaskEmployee as(Name alias) {
        return new ManufacturingtaskEmployee(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ManufacturingtaskEmployee rename(String name) {
        return new ManufacturingtaskEmployee(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ManufacturingtaskEmployee rename(Name name) {
        return new ManufacturingtaskEmployee(name, null);
    }

    // -------------------------------------------------------------------------
    // Row3 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row3<UUID, UUID, UUID> fieldsRow() {
        return (Row3) super.fieldsRow();
    }
}
