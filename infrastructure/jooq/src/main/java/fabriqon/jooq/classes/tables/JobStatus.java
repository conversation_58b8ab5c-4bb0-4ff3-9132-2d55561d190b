/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.JobStatusRecord;

import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row3;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class JobStatus extends TableImpl<JobStatusRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.job_status</code>
     */
    public static final JobStatus JOB_STATUS = new JobStatus();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<JobStatusRecord> getRecordType() {
        return JobStatusRecord.class;
    }

    /**
     * The column <code>public.job_status.create_time</code>.
     */
    public final TableField<JobStatusRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.job_status.name</code>.
     */
    public final TableField<JobStatusRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(100).nullable(false), this, "");

    /**
     * The column <code>public.job_status.status</code>.
     */
    public final TableField<JobStatusRecord, Boolean> STATUS = createField(DSL.name("status"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    private JobStatus(Name alias, Table<JobStatusRecord> aliased) {
        this(alias, aliased, null);
    }

    private JobStatus(Name alias, Table<JobStatusRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.job_status</code> table reference
     */
    public JobStatus(String alias) {
        this(DSL.name(alias), JOB_STATUS);
    }

    /**
     * Create an aliased <code>public.job_status</code> table reference
     */
    public JobStatus(Name alias) {
        this(alias, JOB_STATUS);
    }

    /**
     * Create a <code>public.job_status</code> table reference
     */
    public JobStatus() {
        this(DSL.name("job_status"), null);
    }

    public <O extends Record> JobStatus(Table<O> child, ForeignKey<O, JobStatusRecord> key) {
        super(child, key, JOB_STATUS);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public JobStatus as(String alias) {
        return new JobStatus(DSL.name(alias), this);
    }

    @Override
    public JobStatus as(Name alias) {
        return new JobStatus(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public JobStatus rename(String name) {
        return new JobStatus(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public JobStatus rename(Name name) {
        return new JobStatus(name, null);
    }

    // -------------------------------------------------------------------------
    // Row3 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row3<LocalDateTime, String, Boolean> fieldsRow() {
        return (Row3) super.fieldsRow();
    }
}
