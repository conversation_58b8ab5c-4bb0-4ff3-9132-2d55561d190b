/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Indexes;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.RoCompaniesRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row15;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class RoCompanies extends TableImpl<RoCompaniesRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.ro_companies</code>
     */
    public static final RoCompanies RO_COMPANIES = new RoCompanies();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<RoCompaniesRecord> getRecordType() {
        return RoCompaniesRecord.class;
    }

    /**
     * The column <code>public.ro_companies.create_time</code>.
     */
    public final TableField<RoCompaniesRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.ro_companies.nume</code>.
     */
    public final TableField<RoCompaniesRecord, String> NUME = createField(DSL.name("nume"), SQLDataType.VARCHAR(300), this, "");

    /**
     * The column <code>public.ro_companies.cui</code>.
     */
    public final TableField<RoCompaniesRecord, String> CUI = createField(DSL.name("cui"), SQLDataType.VARCHAR(30), this, "");

    /**
     * The column <code>public.ro_companies.nr_reg_com</code>.
     */
    public final TableField<RoCompaniesRecord, String> NR_REG_COM = createField(DSL.name("nr_reg_com"), SQLDataType.VARCHAR(30), this, "");

    /**
     * The column <code>public.ro_companies.judet</code>.
     */
    public final TableField<RoCompaniesRecord, String> JUDET = createField(DSL.name("judet"), SQLDataType.VARCHAR(30), this, "");

    /**
     * The column <code>public.ro_companies.localitate</code>.
     */
    public final TableField<RoCompaniesRecord, String> LOCALITATE = createField(DSL.name("localitate"), SQLDataType.VARCHAR(100), this, "");

    /**
     * The column <code>public.ro_companies.strada</code>.
     */
    public final TableField<RoCompaniesRecord, String> STRADA = createField(DSL.name("strada"), SQLDataType.VARCHAR(100), this, "");

    /**
     * The column <code>public.ro_companies.numar</code>.
     */
    public final TableField<RoCompaniesRecord, String> NUMAR = createField(DSL.name("numar"), SQLDataType.VARCHAR(30), this, "");

    /**
     * The column <code>public.ro_companies.sector</code>.
     */
    public final TableField<RoCompaniesRecord, String> SECTOR = createField(DSL.name("sector"), SQLDataType.VARCHAR(30), this, "");

    /**
     * The column <code>public.ro_companies.bloc</code>.
     */
    public final TableField<RoCompaniesRecord, String> BLOC = createField(DSL.name("bloc"), SQLDataType.VARCHAR(100), this, "");

    /**
     * The column <code>public.ro_companies.scara</code>.
     */
    public final TableField<RoCompaniesRecord, String> SCARA = createField(DSL.name("scara"), SQLDataType.VARCHAR(30), this, "");

    /**
     * The column <code>public.ro_companies.etaj</code>.
     */
    public final TableField<RoCompaniesRecord, String> ETAJ = createField(DSL.name("etaj"), SQLDataType.VARCHAR(30), this, "");

    /**
     * The column <code>public.ro_companies.apartament</code>.
     */
    public final TableField<RoCompaniesRecord, String> APARTAMENT = createField(DSL.name("apartament"), SQLDataType.VARCHAR(30), this, "");

    /**
     * The column <code>public.ro_companies.cod_postal</code>.
     */
    public final TableField<RoCompaniesRecord, String> COD_POSTAL = createField(DSL.name("cod_postal"), SQLDataType.VARCHAR(10), this, "");

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public final TableField<RoCompaniesRecord, Object> TEXT_SEARCH = createField(DSL.name("text_search"), org.jooq.impl.DefaultDataType.getDefaultDataType("\"pg_catalog\".\"tsvector\""), this, "");

    private RoCompanies(Name alias, Table<RoCompaniesRecord> aliased) {
        this(alias, aliased, null);
    }

    private RoCompanies(Name alias, Table<RoCompaniesRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.ro_companies</code> table reference
     */
    public RoCompanies(String alias) {
        this(DSL.name(alias), RO_COMPANIES);
    }

    /**
     * Create an aliased <code>public.ro_companies</code> table reference
     */
    public RoCompanies(Name alias) {
        this(alias, RO_COMPANIES);
    }

    /**
     * Create a <code>public.ro_companies</code> table reference
     */
    public RoCompanies() {
        this(DSL.name("ro_companies"), null);
    }

    public <O extends Record> RoCompanies(Table<O> child, ForeignKey<O, RoCompaniesRecord> key) {
        super(child, key, RO_COMPANIES);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.asList(Indexes.RO_COMPANIES_TEXT_SEARCH_IDX);
    }

    @Override
    public RoCompanies as(String alias) {
        return new RoCompanies(DSL.name(alias), this);
    }

    @Override
    public RoCompanies as(Name alias) {
        return new RoCompanies(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public RoCompanies rename(String name) {
        return new RoCompanies(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public RoCompanies rename(Name name) {
        return new RoCompanies(name, null);
    }

    // -------------------------------------------------------------------------
    // Row15 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row15<LocalDateTime, String, String, String, String, String, String, String, String, String, String, String, String, String, Object> fieldsRow() {
        return (Row15) super.fieldsRow();
    }
}
