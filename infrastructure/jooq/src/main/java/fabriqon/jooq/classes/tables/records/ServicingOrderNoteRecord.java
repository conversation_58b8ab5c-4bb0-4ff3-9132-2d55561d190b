/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.ServicingOrderNote;

import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record2;
import org.jooq.Row2;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ServicingOrderNoteRecord extends TableRecordImpl<ServicingOrderNoteRecord> implements Record2<UUID, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.servicing_order_note.servicing_order_id</code>.
     */
    public void setServicingOrderId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.servicing_order_note.servicing_order_id</code>.
     */
    public UUID getServicingOrderId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.servicing_order_note.note_id</code>.
     */
    public void setNoteId(UUID value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.servicing_order_note.note_id</code>.
     */
    public UUID getNoteId() {
        return (UUID) get(1);
    }

    // -------------------------------------------------------------------------
    // Record2 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row2<UUID, UUID> fieldsRow() {
        return (Row2) super.fieldsRow();
    }

    @Override
    public Row2<UUID, UUID> valuesRow() {
        return (Row2) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return ServicingOrderNote.SERVICING_ORDER_NOTE.SERVICING_ORDER_ID;
    }

    @Override
    public Field<UUID> field2() {
        return ServicingOrderNote.SERVICING_ORDER_NOTE.NOTE_ID;
    }

    @Override
    public UUID component1() {
        return getServicingOrderId();
    }

    @Override
    public UUID component2() {
        return getNoteId();
    }

    @Override
    public UUID value1() {
        return getServicingOrderId();
    }

    @Override
    public UUID value2() {
        return getNoteId();
    }

    @Override
    public ServicingOrderNoteRecord value1(UUID value) {
        setServicingOrderId(value);
        return this;
    }

    @Override
    public ServicingOrderNoteRecord value2(UUID value) {
        setNoteId(value);
        return this;
    }

    @Override
    public ServicingOrderNoteRecord values(UUID value1, UUID value2) {
        value1(value1);
        value2(value2);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ServicingOrderNoteRecord
     */
    public ServicingOrderNoteRecord() {
        super(ServicingOrderNote.SERVICING_ORDER_NOTE);
    }

    /**
     * Create a detached, initialised ServicingOrderNoteRecord
     */
    public ServicingOrderNoteRecord(UUID servicingOrderId, UUID noteId) {
        super(ServicingOrderNote.SERVICING_ORDER_NOTE);

        setServicingOrderId(servicingOrderId);
        setNoteId(noteId);
    }
}
