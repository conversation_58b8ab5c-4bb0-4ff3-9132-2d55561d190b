/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.ReceptionreceiptFileRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row4;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ReceptionreceiptFile extends TableImpl<ReceptionreceiptFileRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.receptionreceipt_file</code>
     */
    public static final ReceptionreceiptFile RECEPTIONRECEIPT_FILE = new ReceptionreceiptFile();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ReceptionreceiptFileRecord> getRecordType() {
        return ReceptionreceiptFileRecord.class;
    }

    /**
     * The column <code>public.receptionreceipt_file.create_time</code>.
     */
    public final TableField<ReceptionreceiptFileRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.receptionreceipt_file.owner_id</code>.
     */
    public final TableField<ReceptionreceiptFileRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column
     * <code>public.receptionreceipt_file.reception_receipt_id</code>.
     */
    public final TableField<ReceptionreceiptFileRecord, UUID> RECEPTION_RECEIPT_ID = createField(DSL.name("reception_receipt_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.receptionreceipt_file.file_id</code>.
     */
    public final TableField<ReceptionreceiptFileRecord, UUID> FILE_ID = createField(DSL.name("file_id"), SQLDataType.UUID.nullable(false), this, "");

    private ReceptionreceiptFile(Name alias, Table<ReceptionreceiptFileRecord> aliased) {
        this(alias, aliased, null);
    }

    private ReceptionreceiptFile(Name alias, Table<ReceptionreceiptFileRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.receptionreceipt_file</code> table
     * reference
     */
    public ReceptionreceiptFile(String alias) {
        this(DSL.name(alias), RECEPTIONRECEIPT_FILE);
    }

    /**
     * Create an aliased <code>public.receptionreceipt_file</code> table
     * reference
     */
    public ReceptionreceiptFile(Name alias) {
        this(alias, RECEPTIONRECEIPT_FILE);
    }

    /**
     * Create a <code>public.receptionreceipt_file</code> table reference
     */
    public ReceptionreceiptFile() {
        this(DSL.name("receptionreceipt_file"), null);
    }

    public <O extends Record> ReceptionreceiptFile(Table<O> child, ForeignKey<O, ReceptionreceiptFileRecord> key) {
        super(child, key, RECEPTIONRECEIPT_FILE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<ForeignKey<ReceptionreceiptFileRecord, ?>> getReferences() {
        return Arrays.asList(Keys.RECEPTIONRECEIPT_FILE__RECEPTIONRECEIPT_FILE_OWNER_ID_FKEY, Keys.RECEPTIONRECEIPT_FILE__RECEPTIONRECEIPT_FILE_RECEPTION_RECEIPT_ID_FKEY, Keys.RECEPTIONRECEIPT_FILE__RECEPTIONRECEIPT_FILE_FILE_ID_FKEY);
    }

    private transient Account _account;
    private transient ReceptionReceipt _receptionReceipt;
    private transient File _file;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.RECEPTIONRECEIPT_FILE__RECEPTIONRECEIPT_FILE_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.reception_receipt</code>
     * table.
     */
    public ReceptionReceipt receptionReceipt() {
        if (_receptionReceipt == null)
            _receptionReceipt = new ReceptionReceipt(this, Keys.RECEPTIONRECEIPT_FILE__RECEPTIONRECEIPT_FILE_RECEPTION_RECEIPT_ID_FKEY);

        return _receptionReceipt;
    }

    /**
     * Get the implicit join path to the <code>public.file</code> table.
     */
    public File file() {
        if (_file == null)
            _file = new File(this, Keys.RECEPTIONRECEIPT_FILE__RECEPTIONRECEIPT_FILE_FILE_ID_FKEY);

        return _file;
    }

    @Override
    public ReceptionreceiptFile as(String alias) {
        return new ReceptionreceiptFile(DSL.name(alias), this);
    }

    @Override
    public ReceptionreceiptFile as(Name alias) {
        return new ReceptionreceiptFile(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ReceptionreceiptFile rename(String name) {
        return new ReceptionreceiptFile(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ReceptionreceiptFile rename(Name name) {
        return new ReceptionreceiptFile(name, null);
    }

    // -------------------------------------------------------------------------
    // Row4 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row4<LocalDateTime, UUID, UUID, UUID> fieldsRow() {
        return (Row4) super.fieldsRow();
    }
}
