/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Indexes;
import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.MaterialGoodRecord;
import fabriqon.jooq.converters.CurrencyConverter;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Currency;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Index;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row15;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MaterialGood extends TableImpl<MaterialGoodRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.material_good</code>
     */
    public static final MaterialGood MATERIAL_GOOD = new MaterialGood();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<MaterialGoodRecord> getRecordType() {
        return MaterialGoodRecord.class;
    }

    /**
     * The column <code>public.material_good.id</code>.
     */
    public final TableField<MaterialGoodRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.material_good.create_time</code>.
     */
    public final TableField<MaterialGoodRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.material_good.update_time</code>.
     */
    public final TableField<MaterialGoodRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.material_good.deleted</code>.
     */
    public final TableField<MaterialGoodRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.material_good.owner_id</code>.
     */
    public final TableField<MaterialGoodRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.material_good.code</code>.
     */
    public final TableField<MaterialGoodRecord, String> CODE = createField(DSL.name("code"), SQLDataType.VARCHAR(20), this, "");

    /**
     * The column <code>public.material_good.name</code>.
     */
    public final TableField<MaterialGoodRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(200).nullable(false), this, "");

    /**
     * The column <code>public.material_good.category_id</code>.
     */
    public final TableField<MaterialGoodRecord, UUID> CATEGORY_ID = createField(DSL.name("category_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.material_good.parent_id</code>.
     */
    public final TableField<MaterialGoodRecord, UUID> PARENT_ID = createField(DSL.name("parent_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.material_good.description</code>.
     */
    public final TableField<MaterialGoodRecord, String> DESCRIPTION = createField(DSL.name("description"), SQLDataType.VARCHAR(500), this, "");

    /**
     * The column <code>public.material_good.sell_price_amount</code>.
     */
    public final TableField<MaterialGoodRecord, Long> SELL_PRICE_AMOUNT = createField(DSL.name("sell_price_amount"), SQLDataType.BIGINT, this, "");

    /**
     * The column <code>public.material_good.sell_price_currency</code>.
     */
    public final TableField<MaterialGoodRecord, Currency> SELL_PRICE_CURRENCY = createField(DSL.name("sell_price_currency"), SQLDataType.VARCHAR(3), this, "", new CurrencyConverter());

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public final TableField<MaterialGoodRecord, Object> TEXT_SEARCH = createField(DSL.name("text_search"), org.jooq.impl.DefaultDataType.getDefaultDataType("\"pg_catalog\".\"tsvector\""), this, "");

    /**
     * The column <code>public.material_good.external_code</code>.
     */
    public final TableField<MaterialGoodRecord, String> EXTERNAL_CODE = createField(DSL.name("external_code"), SQLDataType.VARCHAR(20), this, "");

    /**
     * The column <code>public.material_good.details</code>.
     */
    public final TableField<MaterialGoodRecord, JSONB> DETAILS = createField(DSL.name("details"), SQLDataType.JSONB, this, "");

    private MaterialGood(Name alias, Table<MaterialGoodRecord> aliased) {
        this(alias, aliased, null);
    }

    private MaterialGood(Name alias, Table<MaterialGoodRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.material_good</code> table reference
     */
    public MaterialGood(String alias) {
        this(DSL.name(alias), MATERIAL_GOOD);
    }

    /**
     * Create an aliased <code>public.material_good</code> table reference
     */
    public MaterialGood(Name alias) {
        this(alias, MATERIAL_GOOD);
    }

    /**
     * Create a <code>public.material_good</code> table reference
     */
    public MaterialGood() {
        this(DSL.name("material_good"), null);
    }

    public <O extends Record> MaterialGood(Table<O> child, ForeignKey<O, MaterialGoodRecord> key) {
        super(child, key, MATERIAL_GOOD);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.asList(Indexes.MATERIAL_GOOD_TEXT_SEARCH_IDX);
    }

    @Override
    public UniqueKey<MaterialGoodRecord> getPrimaryKey() {
        return Keys.MATERIAL_GOOD_PKEY;
    }

    @Override
    public List<ForeignKey<MaterialGoodRecord, ?>> getReferences() {
        return Arrays.asList(Keys.MATERIAL_GOOD__MATERIAL_GOOD_OWNER_ID_FKEY, Keys.MATERIAL_GOOD__MATERIAL_GOOD_CATEGORY_ID_FKEY);
    }

    private transient Account _account;
    private transient Category _category;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.MATERIAL_GOOD__MATERIAL_GOOD_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.category</code> table.
     */
    public Category category() {
        if (_category == null)
            _category = new Category(this, Keys.MATERIAL_GOOD__MATERIAL_GOOD_CATEGORY_ID_FKEY);

        return _category;
    }

    @Override
    public MaterialGood as(String alias) {
        return new MaterialGood(DSL.name(alias), this);
    }

    @Override
    public MaterialGood as(Name alias) {
        return new MaterialGood(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public MaterialGood rename(String name) {
        return new MaterialGood(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public MaterialGood rename(Name name) {
        return new MaterialGood(name, null);
    }

    // -------------------------------------------------------------------------
    // Row15 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row15<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, String, UUID, UUID, String, Long, Currency, Object, String, JSONB> fieldsRow() {
        return (Row15) super.fieldsRow();
    }
}
