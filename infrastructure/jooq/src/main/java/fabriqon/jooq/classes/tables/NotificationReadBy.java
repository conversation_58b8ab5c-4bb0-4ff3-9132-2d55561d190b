/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.NotificationReadByRecord;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row2;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class NotificationReadBy extends TableImpl<NotificationReadByRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.notification_read_by</code>
     */
    public static final NotificationReadBy NOTIFICATION_READ_BY = new NotificationReadBy();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<NotificationReadByRecord> getRecordType() {
        return NotificationReadByRecord.class;
    }

    /**
     * The column <code>public.notification_read_by.notification_id</code>.
     */
    public final TableField<NotificationReadByRecord, UUID> NOTIFICATION_ID = createField(DSL.name("notification_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.notification_read_by.user_id</code>.
     */
    public final TableField<NotificationReadByRecord, UUID> USER_ID = createField(DSL.name("user_id"), SQLDataType.UUID.nullable(false), this, "");

    private NotificationReadBy(Name alias, Table<NotificationReadByRecord> aliased) {
        this(alias, aliased, null);
    }

    private NotificationReadBy(Name alias, Table<NotificationReadByRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.notification_read_by</code> table
     * reference
     */
    public NotificationReadBy(String alias) {
        this(DSL.name(alias), NOTIFICATION_READ_BY);
    }

    /**
     * Create an aliased <code>public.notification_read_by</code> table
     * reference
     */
    public NotificationReadBy(Name alias) {
        this(alias, NOTIFICATION_READ_BY);
    }

    /**
     * Create a <code>public.notification_read_by</code> table reference
     */
    public NotificationReadBy() {
        this(DSL.name("notification_read_by"), null);
    }

    public <O extends Record> NotificationReadBy(Table<O> child, ForeignKey<O, NotificationReadByRecord> key) {
        super(child, key, NOTIFICATION_READ_BY);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<UniqueKey<NotificationReadByRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.NOTIFICATION_READ_BY_NOTIFICATION_ID_USER_ID_KEY);
    }

    @Override
    public List<ForeignKey<NotificationReadByRecord, ?>> getReferences() {
        return Arrays.asList(Keys.NOTIFICATION_READ_BY__NOTIFICATION_READ_BY_NOTIFICATION_ID_FKEY, Keys.NOTIFICATION_READ_BY__NOTIFICATION_READ_BY_USER_ID_FKEY);
    }

    private transient Notification _notification;
    private transient Users _users;

    /**
     * Get the implicit join path to the <code>public.notification</code> table.
     */
    public Notification notification() {
        if (_notification == null)
            _notification = new Notification(this, Keys.NOTIFICATION_READ_BY__NOTIFICATION_READ_BY_NOTIFICATION_ID_FKEY);

        return _notification;
    }

    /**
     * Get the implicit join path to the <code>public.users</code> table.
     */
    public Users users() {
        if (_users == null)
            _users = new Users(this, Keys.NOTIFICATION_READ_BY__NOTIFICATION_READ_BY_USER_ID_FKEY);

        return _users;
    }

    @Override
    public NotificationReadBy as(String alias) {
        return new NotificationReadBy(DSL.name(alias), this);
    }

    @Override
    public NotificationReadBy as(Name alias) {
        return new NotificationReadBy(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public NotificationReadBy rename(String name) {
        return new NotificationReadBy(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public NotificationReadBy rename(Name name) {
        return new NotificationReadBy(name, null);
    }

    // -------------------------------------------------------------------------
    // Row2 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row2<UUID, UUID> fieldsRow() {
        return (Row2) super.fieldsRow();
    }
}
