/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.ResourceUtilizationRateView;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResourceUtilizationRateViewRecord extends TableRecordImpl<ResourceUtilizationRateViewRecord> implements Record7<UUID, UUID, String, LocalDate, BigDecimal, BigDecimal, BigDecimal> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for
     * <code>public.resource_utilization_rate_view.employee_id</code>.
     */
    public void setEmployeeId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for
     * <code>public.resource_utilization_rate_view.employee_id</code>.
     */
    public UUID getEmployeeId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.resource_utilization_rate_view.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.resource_utilization_rate_view.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(1);
    }

    /**
     * Setter for <code>public.resource_utilization_rate_view.name</code>.
     */
    public void setName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.resource_utilization_rate_view.name</code>.
     */
    public String getName() {
        return (String) get(2);
    }

    /**
     * Setter for
     * <code>public.resource_utilization_rate_view.day_of_work</code>.
     */
    public void setDayOfWork(LocalDate value) {
        set(3, value);
    }

    /**
     * Getter for
     * <code>public.resource_utilization_rate_view.day_of_work</code>.
     */
    public LocalDate getDayOfWork() {
        return (LocalDate) get(3);
    }

    /**
     * Setter for
     * <code>public.resource_utilization_rate_view.potential_output_seconds</code>.
     */
    public void setPotentialOutputSeconds(BigDecimal value) {
        set(4, value);
    }

    /**
     * Getter for
     * <code>public.resource_utilization_rate_view.potential_output_seconds</code>.
     */
    public BigDecimal getPotentialOutputSeconds() {
        return (BigDecimal) get(4);
    }

    /**
     * Setter for
     * <code>public.resource_utilization_rate_view.actual_output_seconds</code>.
     */
    public void setActualOutputSeconds(BigDecimal value) {
        set(5, value);
    }

    /**
     * Getter for
     * <code>public.resource_utilization_rate_view.actual_output_seconds</code>.
     */
    public BigDecimal getActualOutputSeconds() {
        return (BigDecimal) get(5);
    }

    /**
     * Setter for
     * <code>public.resource_utilization_rate_view.resource_utilization_rate</code>.
     */
    public void setResourceUtilizationRate(BigDecimal value) {
        set(6, value);
    }

    /**
     * Getter for
     * <code>public.resource_utilization_rate_view.resource_utilization_rate</code>.
     */
    public BigDecimal getResourceUtilizationRate() {
        return (BigDecimal) get(6);
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row7<UUID, UUID, String, LocalDate, BigDecimal, BigDecimal, BigDecimal> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    @Override
    public Row7<UUID, UUID, String, LocalDate, BigDecimal, BigDecimal, BigDecimal> valuesRow() {
        return (Row7) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return ResourceUtilizationRateView.RESOURCE_UTILIZATION_RATE_VIEW.EMPLOYEE_ID;
    }

    @Override
    public Field<UUID> field2() {
        return ResourceUtilizationRateView.RESOURCE_UTILIZATION_RATE_VIEW.OWNER_ID;
    }

    @Override
    public Field<String> field3() {
        return ResourceUtilizationRateView.RESOURCE_UTILIZATION_RATE_VIEW.NAME;
    }

    @Override
    public Field<LocalDate> field4() {
        return ResourceUtilizationRateView.RESOURCE_UTILIZATION_RATE_VIEW.DAY_OF_WORK;
    }

    @Override
    public Field<BigDecimal> field5() {
        return ResourceUtilizationRateView.RESOURCE_UTILIZATION_RATE_VIEW.POTENTIAL_OUTPUT_SECONDS;
    }

    @Override
    public Field<BigDecimal> field6() {
        return ResourceUtilizationRateView.RESOURCE_UTILIZATION_RATE_VIEW.ACTUAL_OUTPUT_SECONDS;
    }

    @Override
    public Field<BigDecimal> field7() {
        return ResourceUtilizationRateView.RESOURCE_UTILIZATION_RATE_VIEW.RESOURCE_UTILIZATION_RATE;
    }

    @Override
    public UUID component1() {
        return getEmployeeId();
    }

    @Override
    public UUID component2() {
        return getOwnerId();
    }

    @Override
    public String component3() {
        return getName();
    }

    @Override
    public LocalDate component4() {
        return getDayOfWork();
    }

    @Override
    public BigDecimal component5() {
        return getPotentialOutputSeconds();
    }

    @Override
    public BigDecimal component6() {
        return getActualOutputSeconds();
    }

    @Override
    public BigDecimal component7() {
        return getResourceUtilizationRate();
    }

    @Override
    public UUID value1() {
        return getEmployeeId();
    }

    @Override
    public UUID value2() {
        return getOwnerId();
    }

    @Override
    public String value3() {
        return getName();
    }

    @Override
    public LocalDate value4() {
        return getDayOfWork();
    }

    @Override
    public BigDecimal value5() {
        return getPotentialOutputSeconds();
    }

    @Override
    public BigDecimal value6() {
        return getActualOutputSeconds();
    }

    @Override
    public BigDecimal value7() {
        return getResourceUtilizationRate();
    }

    @Override
    public ResourceUtilizationRateViewRecord value1(UUID value) {
        setEmployeeId(value);
        return this;
    }

    @Override
    public ResourceUtilizationRateViewRecord value2(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public ResourceUtilizationRateViewRecord value3(String value) {
        setName(value);
        return this;
    }

    @Override
    public ResourceUtilizationRateViewRecord value4(LocalDate value) {
        setDayOfWork(value);
        return this;
    }

    @Override
    public ResourceUtilizationRateViewRecord value5(BigDecimal value) {
        setPotentialOutputSeconds(value);
        return this;
    }

    @Override
    public ResourceUtilizationRateViewRecord value6(BigDecimal value) {
        setActualOutputSeconds(value);
        return this;
    }

    @Override
    public ResourceUtilizationRateViewRecord value7(BigDecimal value) {
        setResourceUtilizationRate(value);
        return this;
    }

    @Override
    public ResourceUtilizationRateViewRecord values(UUID value1, UUID value2, String value3, LocalDate value4, BigDecimal value5, BigDecimal value6, BigDecimal value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ResourceUtilizationRateViewRecord
     */
    public ResourceUtilizationRateViewRecord() {
        super(ResourceUtilizationRateView.RESOURCE_UTILIZATION_RATE_VIEW);
    }

    /**
     * Create a detached, initialised ResourceUtilizationRateViewRecord
     */
    public ResourceUtilizationRateViewRecord(UUID employeeId, UUID ownerId, String name, LocalDate dayOfWork, BigDecimal potentialOutputSeconds, BigDecimal actualOutputSeconds, BigDecimal resourceUtilizationRate) {
        super(ResourceUtilizationRateView.RESOURCE_UTILIZATION_RATE_VIEW);

        setEmployeeId(employeeId);
        setOwnerId(ownerId);
        setName(name);
        setDayOfWork(dayOfWork);
        setPotentialOutputSeconds(potentialOutputSeconds);
        setActualOutputSeconds(actualOutputSeconds);
        setResourceUtilizationRate(resourceUtilizationRate);
    }
}
