/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.InventoryUnit;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class InventoryUnitRecord extends UpdatableRecordImpl<InventoryUnitRecord> implements Record6<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.inventory_unit.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.inventory_unit.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.inventory_unit.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.inventory_unit.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.inventory_unit.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.inventory_unit.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.inventory_unit.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.inventory_unit.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.inventory_unit.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.inventory_unit.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.inventory_unit.name</code>.
     */
    public void setName(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.inventory_unit.name</code>.
     */
    public String getName() {
        return (String) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row6<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    @Override
    public Row6<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String> valuesRow() {
        return (Row6) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return InventoryUnit.INVENTORY_UNIT.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return InventoryUnit.INVENTORY_UNIT.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return InventoryUnit.INVENTORY_UNIT.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return InventoryUnit.INVENTORY_UNIT.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return InventoryUnit.INVENTORY_UNIT.OWNER_ID;
    }

    @Override
    public Field<String> field6() {
        return InventoryUnit.INVENTORY_UNIT.NAME;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public String component6() {
        return getName();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public String value6() {
        return getName();
    }

    @Override
    public InventoryUnitRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public InventoryUnitRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public InventoryUnitRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public InventoryUnitRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public InventoryUnitRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public InventoryUnitRecord value6(String value) {
        setName(value);
        return this;
    }

    @Override
    public InventoryUnitRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, String value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached InventoryUnitRecord
     */
    public InventoryUnitRecord() {
        super(InventoryUnit.INVENTORY_UNIT);
    }

    /**
     * Create a detached, initialised InventoryUnitRecord
     */
    public InventoryUnitRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, String name) {
        super(InventoryUnit.INVENTORY_UNIT);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setName(name);
    }
}
