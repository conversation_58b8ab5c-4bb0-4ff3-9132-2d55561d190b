/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Indexes;
import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.PurchaseOrderRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Index;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row14;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PurchaseOrder extends TableImpl<PurchaseOrderRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.purchase_order</code>
     */
    public static final PurchaseOrder PURCHASE_ORDER = new PurchaseOrder();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<PurchaseOrderRecord> getRecordType() {
        return PurchaseOrderRecord.class;
    }

    /**
     * The column <code>public.purchase_order.id</code>.
     */
    public final TableField<PurchaseOrderRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.purchase_order.create_time</code>.
     */
    public final TableField<PurchaseOrderRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.purchase_order.update_time</code>.
     */
    public final TableField<PurchaseOrderRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.purchase_order.deleted</code>.
     */
    public final TableField<PurchaseOrderRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.purchase_order.owner_id</code>.
     */
    public final TableField<PurchaseOrderRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.purchase_order.supplier_id</code>.
     */
    public final TableField<PurchaseOrderRecord, UUID> SUPPLIER_ID = createField(DSL.name("supplier_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.purchase_order.number</code>.
     */
    public final TableField<PurchaseOrderRecord, String> NUMBER = createField(DSL.name("number"), SQLDataType.VARCHAR(20).nullable(false), this, "");

    /**
     * The column <code>public.purchase_order.status</code>.
     */
    public final TableField<PurchaseOrderRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(32).nullable(false), this, "");

    /**
     * The column <code>public.purchase_order.delivered_at</code>.
     */
    public final TableField<PurchaseOrderRecord, LocalDateTime> DELIVERED_AT = createField(DSL.name("delivered_at"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>public.purchase_order.items</code>.
     */
    public final TableField<PurchaseOrderRecord, JSONB> ITEMS = createField(DSL.name("items"), SQLDataType.JSONB, this, "");

    /**
     * The column <code>public.purchase_order.expected_by</code>.
     */
    public final TableField<PurchaseOrderRecord, LocalDateTime> EXPECTED_BY = createField(DSL.name("expected_by"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * @deprecated Unknown data type. If this is a qualified, user-defined type,
     * it may have been excluded from code generation. If this is a built-in
     * type, you can define an explicit {@link org.jooq.Binding} to specify how
     * this type should be handled. Deprecation can be turned off using
     * {@literal <deprecationOnUnknownTypes/>} in your code generator
     * configuration.
     */
    @Deprecated
    public final TableField<PurchaseOrderRecord, Object> TEXT_SEARCH = createField(DSL.name("text_search"), org.jooq.impl.DefaultDataType.getDefaultDataType("\"pg_catalog\".\"tsvector\""), this, "");

    /**
     * The column <code>public.purchase_order.rendering_details</code>.
     */
    public final TableField<PurchaseOrderRecord, JSONB> RENDERING_DETAILS = createField(DSL.name("rendering_details"), SQLDataType.JSONB, this, "");

    /**
     * The column <code>public.purchase_order.managed_by</code>.
     */
    public final TableField<PurchaseOrderRecord, UUID> MANAGED_BY = createField(DSL.name("managed_by"), SQLDataType.UUID, this, "");

    private PurchaseOrder(Name alias, Table<PurchaseOrderRecord> aliased) {
        this(alias, aliased, null);
    }

    private PurchaseOrder(Name alias, Table<PurchaseOrderRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.purchase_order</code> table reference
     */
    public PurchaseOrder(String alias) {
        this(DSL.name(alias), PURCHASE_ORDER);
    }

    /**
     * Create an aliased <code>public.purchase_order</code> table reference
     */
    public PurchaseOrder(Name alias) {
        this(alias, PURCHASE_ORDER);
    }

    /**
     * Create a <code>public.purchase_order</code> table reference
     */
    public PurchaseOrder() {
        this(DSL.name("purchase_order"), null);
    }

    public <O extends Record> PurchaseOrder(Table<O> child, ForeignKey<O, PurchaseOrderRecord> key) {
        super(child, key, PURCHASE_ORDER);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.asList(Indexes.PURCHASE_ORDER_TEXT_SEARCH_IDX);
    }

    @Override
    public UniqueKey<PurchaseOrderRecord> getPrimaryKey() {
        return Keys.PURCHASE_ORDER_PKEY;
    }

    @Override
    public List<ForeignKey<PurchaseOrderRecord, ?>> getReferences() {
        return Arrays.asList(Keys.PURCHASE_ORDER__PURCHASE_ORDER_OWNER_ID_FKEY, Keys.PURCHASE_ORDER__PURCHASE_ORDER_SUPPLIER_ID_FKEY, Keys.PURCHASE_ORDER__PURCHASE_ORDER_MANAGED_BY_FKEY);
    }

    private transient Account _account;
    private transient Company _company;
    private transient Users _users;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.PURCHASE_ORDER__PURCHASE_ORDER_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.company</code> table.
     */
    public Company company() {
        if (_company == null)
            _company = new Company(this, Keys.PURCHASE_ORDER__PURCHASE_ORDER_SUPPLIER_ID_FKEY);

        return _company;
    }

    /**
     * Get the implicit join path to the <code>public.users</code> table.
     */
    public Users users() {
        if (_users == null)
            _users = new Users(this, Keys.PURCHASE_ORDER__PURCHASE_ORDER_MANAGED_BY_FKEY);

        return _users;
    }

    @Override
    public PurchaseOrder as(String alias) {
        return new PurchaseOrder(DSL.name(alias), this);
    }

    @Override
    public PurchaseOrder as(Name alias) {
        return new PurchaseOrder(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public PurchaseOrder rename(String name) {
        return new PurchaseOrder(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public PurchaseOrder rename(Name name) {
        return new PurchaseOrder(name, null);
    }

    // -------------------------------------------------------------------------
    // Row14 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row14<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String, String, LocalDateTime, JSONB, LocalDateTime, Object, JSONB, UUID> fieldsRow() {
        return (Row14) super.fieldsRow();
    }
}
