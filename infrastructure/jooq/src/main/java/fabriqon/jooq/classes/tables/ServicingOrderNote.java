/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.ServicingOrderNoteRecord;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row2;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ServicingOrderNote extends TableImpl<ServicingOrderNoteRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.servicing_order_note</code>
     */
    public static final ServicingOrderNote SERVICING_ORDER_NOTE = new ServicingOrderNote();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ServicingOrderNoteRecord> getRecordType() {
        return ServicingOrderNoteRecord.class;
    }

    /**
     * The column <code>public.servicing_order_note.servicing_order_id</code>.
     */
    public final TableField<ServicingOrderNoteRecord, UUID> SERVICING_ORDER_ID = createField(DSL.name("servicing_order_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.servicing_order_note.note_id</code>.
     */
    public final TableField<ServicingOrderNoteRecord, UUID> NOTE_ID = createField(DSL.name("note_id"), SQLDataType.UUID.nullable(false), this, "");

    private ServicingOrderNote(Name alias, Table<ServicingOrderNoteRecord> aliased) {
        this(alias, aliased, null);
    }

    private ServicingOrderNote(Name alias, Table<ServicingOrderNoteRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.servicing_order_note</code> table
     * reference
     */
    public ServicingOrderNote(String alias) {
        this(DSL.name(alias), SERVICING_ORDER_NOTE);
    }

    /**
     * Create an aliased <code>public.servicing_order_note</code> table
     * reference
     */
    public ServicingOrderNote(Name alias) {
        this(alias, SERVICING_ORDER_NOTE);
    }

    /**
     * Create a <code>public.servicing_order_note</code> table reference
     */
    public ServicingOrderNote() {
        this(DSL.name("servicing_order_note"), null);
    }

    public <O extends Record> ServicingOrderNote(Table<O> child, ForeignKey<O, ServicingOrderNoteRecord> key) {
        super(child, key, SERVICING_ORDER_NOTE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<ForeignKey<ServicingOrderNoteRecord, ?>> getReferences() {
        return Arrays.asList(Keys.SERVICING_ORDER_NOTE__SERVICING_ORDER_NOTE_SERVICING_ORDER_ID_FKEY, Keys.SERVICING_ORDER_NOTE__SERVICING_ORDER_NOTE_NOTE_ID_FKEY);
    }

    private transient ServicingOrder _servicingOrder;
    private transient Note _note;

    /**
     * Get the implicit join path to the <code>public.servicing_order</code>
     * table.
     */
    public ServicingOrder servicingOrder() {
        if (_servicingOrder == null)
            _servicingOrder = new ServicingOrder(this, Keys.SERVICING_ORDER_NOTE__SERVICING_ORDER_NOTE_SERVICING_ORDER_ID_FKEY);

        return _servicingOrder;
    }

    /**
     * Get the implicit join path to the <code>public.note</code> table.
     */
    public Note note() {
        if (_note == null)
            _note = new Note(this, Keys.SERVICING_ORDER_NOTE__SERVICING_ORDER_NOTE_NOTE_ID_FKEY);

        return _note;
    }

    @Override
    public ServicingOrderNote as(String alias) {
        return new ServicingOrderNote(DSL.name(alias), this);
    }

    @Override
    public ServicingOrderNote as(Name alias) {
        return new ServicingOrderNote(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ServicingOrderNote rename(String name) {
        return new ServicingOrderNote(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ServicingOrderNote rename(Name name) {
        return new ServicingOrderNote(name, null);
    }

    // -------------------------------------------------------------------------
    // Row2 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row2<UUID, UUID> fieldsRow() {
        return (Row2) super.fieldsRow();
    }
}
