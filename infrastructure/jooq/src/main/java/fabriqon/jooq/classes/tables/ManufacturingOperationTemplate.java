/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.ManufacturingOperationTemplateRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row7;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ManufacturingOperationTemplate extends TableImpl<ManufacturingOperationTemplateRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>public.manufacturing_operation_template</code>
     */
    public static final ManufacturingOperationTemplate MANUFACTURING_OPERATION_TEMPLATE = new ManufacturingOperationTemplate();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ManufacturingOperationTemplateRecord> getRecordType() {
        return ManufacturingOperationTemplateRecord.class;
    }

    /**
     * The column <code>public.manufacturing_operation_template.id</code>.
     */
    public final TableField<ManufacturingOperationTemplateRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column
     * <code>public.manufacturing_operation_template.create_time</code>.
     */
    public final TableField<ManufacturingOperationTemplateRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column
     * <code>public.manufacturing_operation_template.update_time</code>.
     */
    public final TableField<ManufacturingOperationTemplateRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.manufacturing_operation_template.deleted</code>.
     */
    public final TableField<ManufacturingOperationTemplateRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.manufacturing_operation_template.owner_id</code>.
     */
    public final TableField<ManufacturingOperationTemplateRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.manufacturing_operation_template.name</code>.
     */
    public final TableField<ManufacturingOperationTemplateRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(200), this, "");

    /**
     * The column <code>public.manufacturing_operation_template.details</code>.
     */
    public final TableField<ManufacturingOperationTemplateRecord, JSONB> DETAILS = createField(DSL.name("details"), SQLDataType.JSONB, this, "");

    private ManufacturingOperationTemplate(Name alias, Table<ManufacturingOperationTemplateRecord> aliased) {
        this(alias, aliased, null);
    }

    private ManufacturingOperationTemplate(Name alias, Table<ManufacturingOperationTemplateRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.manufacturing_operation_template</code>
     * table reference
     */
    public ManufacturingOperationTemplate(String alias) {
        this(DSL.name(alias), MANUFACTURING_OPERATION_TEMPLATE);
    }

    /**
     * Create an aliased <code>public.manufacturing_operation_template</code>
     * table reference
     */
    public ManufacturingOperationTemplate(Name alias) {
        this(alias, MANUFACTURING_OPERATION_TEMPLATE);
    }

    /**
     * Create a <code>public.manufacturing_operation_template</code> table
     * reference
     */
    public ManufacturingOperationTemplate() {
        this(DSL.name("manufacturing_operation_template"), null);
    }

    public <O extends Record> ManufacturingOperationTemplate(Table<O> child, ForeignKey<O, ManufacturingOperationTemplateRecord> key) {
        super(child, key, MANUFACTURING_OPERATION_TEMPLATE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public UniqueKey<ManufacturingOperationTemplateRecord> getPrimaryKey() {
        return Keys.MANUFACTURING_OPERATION_TEMPLATE_PKEY;
    }

    @Override
    public List<ForeignKey<ManufacturingOperationTemplateRecord, ?>> getReferences() {
        return Arrays.asList(Keys.MANUFACTURING_OPERATION_TEMPLATE__MANUFACTURING_OPERATION_TEMPLATE_OWNER_ID_FKEY);
    }

    private transient Account _account;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.MANUFACTURING_OPERATION_TEMPLATE__MANUFACTURING_OPERATION_TEMPLATE_OWNER_ID_FKEY);

        return _account;
    }

    @Override
    public ManufacturingOperationTemplate as(String alias) {
        return new ManufacturingOperationTemplate(DSL.name(alias), this);
    }

    @Override
    public ManufacturingOperationTemplate as(Name alias) {
        return new ManufacturingOperationTemplate(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ManufacturingOperationTemplate rename(String name) {
        return new ManufacturingOperationTemplate(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ManufacturingOperationTemplate rename(Name name) {
        return new ManufacturingOperationTemplate(name, null);
    }

    // -------------------------------------------------------------------------
    // Row7 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row7<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, JSONB> fieldsRow() {
        return (Row7) super.fieldsRow();
    }
}
