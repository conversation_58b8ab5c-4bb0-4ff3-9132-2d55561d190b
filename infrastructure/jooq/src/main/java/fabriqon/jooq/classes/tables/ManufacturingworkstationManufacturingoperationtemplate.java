/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.ManufacturingworkstationManufacturingoperationtemplateRecord;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row3;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ManufacturingworkstationManufacturingoperationtemplate extends TableImpl<ManufacturingworkstationManufacturingoperationtemplateRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>public.manufacturingworkstation_manufacturingoperationtemplate</code>
     */
    public static final ManufacturingworkstationManufacturingoperationtemplate MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE = new ManufacturingworkstationManufacturingoperationtemplate();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ManufacturingworkstationManufacturingoperationtemplateRecord> getRecordType() {
        return ManufacturingworkstationManufacturingoperationtemplateRecord.class;
    }

    /**
     * The column
     * <code>public.manufacturingworkstation_manufacturingoperationtemplate.owner_id</code>.
     */
    public final TableField<ManufacturingworkstationManufacturingoperationtemplateRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column
     * <code>public.manufacturingworkstation_manufacturingoperationtemplate.manufacturing_workstation_id</code>.
     */
    public final TableField<ManufacturingworkstationManufacturingoperationtemplateRecord, UUID> MANUFACTURING_WORKSTATION_ID = createField(DSL.name("manufacturing_workstation_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column
     * <code>public.manufacturingworkstation_manufacturingoperationtemplate.manufacturing_operation_template_id</code>.
     */
    public final TableField<ManufacturingworkstationManufacturingoperationtemplateRecord, UUID> MANUFACTURING_OPERATION_TEMPLATE_ID = createField(DSL.name("manufacturing_operation_template_id"), SQLDataType.UUID.nullable(false), this, "");

    private ManufacturingworkstationManufacturingoperationtemplate(Name alias, Table<ManufacturingworkstationManufacturingoperationtemplateRecord> aliased) {
        this(alias, aliased, null);
    }

    private ManufacturingworkstationManufacturingoperationtemplate(Name alias, Table<ManufacturingworkstationManufacturingoperationtemplateRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased
     * <code>public.manufacturingworkstation_manufacturingoperationtemplate</code>
     * table reference
     */
    public ManufacturingworkstationManufacturingoperationtemplate(String alias) {
        this(DSL.name(alias), MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE);
    }

    /**
     * Create an aliased
     * <code>public.manufacturingworkstation_manufacturingoperationtemplate</code>
     * table reference
     */
    public ManufacturingworkstationManufacturingoperationtemplate(Name alias) {
        this(alias, MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE);
    }

    /**
     * Create a
     * <code>public.manufacturingworkstation_manufacturingoperationtemplate</code>
     * table reference
     */
    public ManufacturingworkstationManufacturingoperationtemplate() {
        this(DSL.name("manufacturingworkstation_manufacturingoperationtemplate"), null);
    }

    public <O extends Record> ManufacturingworkstationManufacturingoperationtemplate(Table<O> child, ForeignKey<O, ManufacturingworkstationManufacturingoperationtemplateRecord> key) {
        super(child, key, MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<ForeignKey<ManufacturingworkstationManufacturingoperationtemplateRecord, ?>> getReferences() {
        return Arrays.asList(Keys.MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE__MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTE_OWNER_ID_FKEY, Keys.MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE__MANUFACTURINGWORKSTATION_MANU_MANUFACTURING_WORKSTATION_ID_FKEY, Keys.MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE__MANUFACTURINGWORKSTATION_MANU_MANUFACTURING_OPERATION_TEMP_FKEY);
    }

    private transient Account _account;
    private transient ManufacturingWorkstation _manufacturingWorkstation;
    private transient ManufacturingOperationTemplate _manufacturingOperationTemplate;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE__MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTE_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the
     * <code>public.manufacturing_workstation</code> table.
     */
    public ManufacturingWorkstation manufacturingWorkstation() {
        if (_manufacturingWorkstation == null)
            _manufacturingWorkstation = new ManufacturingWorkstation(this, Keys.MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE__MANUFACTURINGWORKSTATION_MANU_MANUFACTURING_WORKSTATION_ID_FKEY);

        return _manufacturingWorkstation;
    }

    /**
     * Get the implicit join path to the
     * <code>public.manufacturing_operation_template</code> table.
     */
    public ManufacturingOperationTemplate manufacturingOperationTemplate() {
        if (_manufacturingOperationTemplate == null)
            _manufacturingOperationTemplate = new ManufacturingOperationTemplate(this, Keys.MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE__MANUFACTURINGWORKSTATION_MANU_MANUFACTURING_OPERATION_TEMP_FKEY);

        return _manufacturingOperationTemplate;
    }

    @Override
    public ManufacturingworkstationManufacturingoperationtemplate as(String alias) {
        return new ManufacturingworkstationManufacturingoperationtemplate(DSL.name(alias), this);
    }

    @Override
    public ManufacturingworkstationManufacturingoperationtemplate as(Name alias) {
        return new ManufacturingworkstationManufacturingoperationtemplate(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ManufacturingworkstationManufacturingoperationtemplate rename(String name) {
        return new ManufacturingworkstationManufacturingoperationtemplate(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ManufacturingworkstationManufacturingoperationtemplate rename(Name name) {
        return new ManufacturingworkstationManufacturingoperationtemplate(name, null);
    }

    // -------------------------------------------------------------------------
    // Row3 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row3<UUID, UUID, UUID> fieldsRow() {
        return (Row3) super.fieldsRow();
    }
}
