/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.Inventory;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Currency;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record20;
import org.jooq.Row20;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class InventoryRecord extends UpdatableRecordImpl<InventoryRecord> implements Record20<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, UUID, UUID, UUID, UUID, LocalDateTime, LocalDateTime, Long, Currency, BigDecimal, UUID, UUID, Long, Currency, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.inventory.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.inventory.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.inventory.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.inventory.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.inventory.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.inventory.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.inventory.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.inventory.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.inventory.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.inventory.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.inventory.material_good_id</code>.
     */
    public void setMaterialGoodId(UUID value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.inventory.material_good_id</code>.
     */
    public UUID getMaterialGoodId() {
        return (UUID) get(5);
    }

    /**
     * Setter for <code>public.inventory.inventory_adjustment_order_id</code>.
     */
    public void setInventoryAdjustmentOrderId(UUID value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.inventory.inventory_adjustment_order_id</code>.
     */
    public UUID getInventoryAdjustmentOrderId() {
        return (UUID) get(6);
    }

    /**
     * Setter for <code>public.inventory.sales_order_id</code>.
     */
    public void setSalesOrderId(UUID value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.inventory.sales_order_id</code>.
     */
    public UUID getSalesOrderId() {
        return (UUID) get(7);
    }

    /**
     * Setter for <code>public.inventory.manufacturing_order_id</code>.
     */
    public void setManufacturingOrderId(UUID value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.inventory.manufacturing_order_id</code>.
     */
    public UUID getManufacturingOrderId() {
        return (UUID) get(8);
    }

    /**
     * Setter for <code>public.inventory.supplier_id</code>.
     */
    public void setSupplierId(UUID value) {
        set(9, value);
    }

    /**
     * Getter for <code>public.inventory.supplier_id</code>.
     */
    public UUID getSupplierId() {
        return (UUID) get(9);
    }

    /**
     * Setter for <code>public.inventory.purchase_date</code>.
     */
    public void setPurchaseDate(LocalDateTime value) {
        set(10, value);
    }

    /**
     * Getter for <code>public.inventory.purchase_date</code>.
     */
    public LocalDateTime getPurchaseDate() {
        return (LocalDateTime) get(10);
    }

    /**
     * Setter for <code>public.inventory.expiry_date</code>.
     */
    public void setExpiryDate(LocalDateTime value) {
        set(11, value);
    }

    /**
     * Getter for <code>public.inventory.expiry_date</code>.
     */
    public LocalDateTime getExpiryDate() {
        return (LocalDateTime) get(11);
    }

    /**
     * Setter for <code>public.inventory.reception_price_amount</code>.
     */
    public void setReceptionPriceAmount(Long value) {
        set(12, value);
    }

    /**
     * Getter for <code>public.inventory.reception_price_amount</code>.
     */
    public Long getReceptionPriceAmount() {
        return (Long) get(12);
    }

    /**
     * Setter for <code>public.inventory.reception_price_currency</code>.
     */
    public void setReceptionPriceCurrency(Currency value) {
        set(13, value);
    }

    /**
     * Getter for <code>public.inventory.reception_price_currency</code>.
     */
    public Currency getReceptionPriceCurrency() {
        return (Currency) get(13);
    }

    /**
     * Setter for <code>public.inventory.quantity</code>.
     */
    public void setQuantity(BigDecimal value) {
        set(14, value);
    }

    /**
     * Getter for <code>public.inventory.quantity</code>.
     */
    public BigDecimal getQuantity() {
        return (BigDecimal) get(14);
    }

    /**
     * Setter for <code>public.inventory.unit_id</code>.
     */
    public void setUnitId(UUID value) {
        set(15, value);
    }

    /**
     * Getter for <code>public.inventory.unit_id</code>.
     */
    public UUID getUnitId() {
        return (UUID) get(15);
    }

    /**
     * Setter for <code>public.inventory.reception_receipt_id</code>.
     */
    public void setReceptionReceiptId(UUID value) {
        set(16, value);
    }

    /**
     * Getter for <code>public.inventory.reception_receipt_id</code>.
     */
    public UUID getReceptionReceiptId() {
        return (UUID) get(16);
    }

    /**
     * Setter for <code>public.inventory.exit_price_amount</code>.
     */
    public void setExitPriceAmount(Long value) {
        set(17, value);
    }

    /**
     * Getter for <code>public.inventory.exit_price_amount</code>.
     */
    public Long getExitPriceAmount() {
        return (Long) get(17);
    }

    /**
     * Setter for <code>public.inventory.exit_price_currency</code>.
     */
    public void setExitPriceCurrency(Currency value) {
        set(18, value);
    }

    /**
     * Getter for <code>public.inventory.exit_price_currency</code>.
     */
    public Currency getExitPriceCurrency() {
        return (Currency) get(18);
    }

    /**
     * Setter for <code>public.inventory.servicing_order_id</code>.
     */
    public void setServicingOrderId(UUID value) {
        set(19, value);
    }

    /**
     * Getter for <code>public.inventory.servicing_order_id</code>.
     */
    public UUID getServicingOrderId() {
        return (UUID) get(19);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record20 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row20<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, UUID, UUID, UUID, UUID, LocalDateTime, LocalDateTime, Long, Currency, BigDecimal, UUID, UUID, Long, Currency, UUID> fieldsRow() {
        return (Row20) super.fieldsRow();
    }

    @Override
    public Row20<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, UUID, UUID, UUID, UUID, LocalDateTime, LocalDateTime, Long, Currency, BigDecimal, UUID, UUID, Long, Currency, UUID> valuesRow() {
        return (Row20) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return Inventory.INVENTORY.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return Inventory.INVENTORY.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return Inventory.INVENTORY.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return Inventory.INVENTORY.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return Inventory.INVENTORY.OWNER_ID;
    }

    @Override
    public Field<UUID> field6() {
        return Inventory.INVENTORY.MATERIAL_GOOD_ID;
    }

    @Override
    public Field<UUID> field7() {
        return Inventory.INVENTORY.INVENTORY_ADJUSTMENT_ORDER_ID;
    }

    @Override
    public Field<UUID> field8() {
        return Inventory.INVENTORY.SALES_ORDER_ID;
    }

    @Override
    public Field<UUID> field9() {
        return Inventory.INVENTORY.MANUFACTURING_ORDER_ID;
    }

    @Override
    public Field<UUID> field10() {
        return Inventory.INVENTORY.SUPPLIER_ID;
    }

    @Override
    public Field<LocalDateTime> field11() {
        return Inventory.INVENTORY.PURCHASE_DATE;
    }

    @Override
    public Field<LocalDateTime> field12() {
        return Inventory.INVENTORY.EXPIRY_DATE;
    }

    @Override
    public Field<Long> field13() {
        return Inventory.INVENTORY.RECEPTION_PRICE_AMOUNT;
    }

    @Override
    public Field<Currency> field14() {
        return Inventory.INVENTORY.RECEPTION_PRICE_CURRENCY;
    }

    @Override
    public Field<BigDecimal> field15() {
        return Inventory.INVENTORY.QUANTITY;
    }

    @Override
    public Field<UUID> field16() {
        return Inventory.INVENTORY.UNIT_ID;
    }

    @Override
    public Field<UUID> field17() {
        return Inventory.INVENTORY.RECEPTION_RECEIPT_ID;
    }

    @Override
    public Field<Long> field18() {
        return Inventory.INVENTORY.EXIT_PRICE_AMOUNT;
    }

    @Override
    public Field<Currency> field19() {
        return Inventory.INVENTORY.EXIT_PRICE_CURRENCY;
    }

    @Override
    public Field<UUID> field20() {
        return Inventory.INVENTORY.SERVICING_ORDER_ID;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public UUID component6() {
        return getMaterialGoodId();
    }

    @Override
    public UUID component7() {
        return getInventoryAdjustmentOrderId();
    }

    @Override
    public UUID component8() {
        return getSalesOrderId();
    }

    @Override
    public UUID component9() {
        return getManufacturingOrderId();
    }

    @Override
    public UUID component10() {
        return getSupplierId();
    }

    @Override
    public LocalDateTime component11() {
        return getPurchaseDate();
    }

    @Override
    public LocalDateTime component12() {
        return getExpiryDate();
    }

    @Override
    public Long component13() {
        return getReceptionPriceAmount();
    }

    @Override
    public Currency component14() {
        return getReceptionPriceCurrency();
    }

    @Override
    public BigDecimal component15() {
        return getQuantity();
    }

    @Override
    public UUID component16() {
        return getUnitId();
    }

    @Override
    public UUID component17() {
        return getReceptionReceiptId();
    }

    @Override
    public Long component18() {
        return getExitPriceAmount();
    }

    @Override
    public Currency component19() {
        return getExitPriceCurrency();
    }

    @Override
    public UUID component20() {
        return getServicingOrderId();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public UUID value6() {
        return getMaterialGoodId();
    }

    @Override
    public UUID value7() {
        return getInventoryAdjustmentOrderId();
    }

    @Override
    public UUID value8() {
        return getSalesOrderId();
    }

    @Override
    public UUID value9() {
        return getManufacturingOrderId();
    }

    @Override
    public UUID value10() {
        return getSupplierId();
    }

    @Override
    public LocalDateTime value11() {
        return getPurchaseDate();
    }

    @Override
    public LocalDateTime value12() {
        return getExpiryDate();
    }

    @Override
    public Long value13() {
        return getReceptionPriceAmount();
    }

    @Override
    public Currency value14() {
        return getReceptionPriceCurrency();
    }

    @Override
    public BigDecimal value15() {
        return getQuantity();
    }

    @Override
    public UUID value16() {
        return getUnitId();
    }

    @Override
    public UUID value17() {
        return getReceptionReceiptId();
    }

    @Override
    public Long value18() {
        return getExitPriceAmount();
    }

    @Override
    public Currency value19() {
        return getExitPriceCurrency();
    }

    @Override
    public UUID value20() {
        return getServicingOrderId();
    }

    @Override
    public InventoryRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public InventoryRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public InventoryRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public InventoryRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public InventoryRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public InventoryRecord value6(UUID value) {
        setMaterialGoodId(value);
        return this;
    }

    @Override
    public InventoryRecord value7(UUID value) {
        setInventoryAdjustmentOrderId(value);
        return this;
    }

    @Override
    public InventoryRecord value8(UUID value) {
        setSalesOrderId(value);
        return this;
    }

    @Override
    public InventoryRecord value9(UUID value) {
        setManufacturingOrderId(value);
        return this;
    }

    @Override
    public InventoryRecord value10(UUID value) {
        setSupplierId(value);
        return this;
    }

    @Override
    public InventoryRecord value11(LocalDateTime value) {
        setPurchaseDate(value);
        return this;
    }

    @Override
    public InventoryRecord value12(LocalDateTime value) {
        setExpiryDate(value);
        return this;
    }

    @Override
    public InventoryRecord value13(Long value) {
        setReceptionPriceAmount(value);
        return this;
    }

    @Override
    public InventoryRecord value14(Currency value) {
        setReceptionPriceCurrency(value);
        return this;
    }

    @Override
    public InventoryRecord value15(BigDecimal value) {
        setQuantity(value);
        return this;
    }

    @Override
    public InventoryRecord value16(UUID value) {
        setUnitId(value);
        return this;
    }

    @Override
    public InventoryRecord value17(UUID value) {
        setReceptionReceiptId(value);
        return this;
    }

    @Override
    public InventoryRecord value18(Long value) {
        setExitPriceAmount(value);
        return this;
    }

    @Override
    public InventoryRecord value19(Currency value) {
        setExitPriceCurrency(value);
        return this;
    }

    @Override
    public InventoryRecord value20(UUID value) {
        setServicingOrderId(value);
        return this;
    }

    @Override
    public InventoryRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, UUID value6, UUID value7, UUID value8, UUID value9, UUID value10, LocalDateTime value11, LocalDateTime value12, Long value13, Currency value14, BigDecimal value15, UUID value16, UUID value17, Long value18, Currency value19, UUID value20) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        value17(value17);
        value18(value18);
        value19(value19);
        value20(value20);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached InventoryRecord
     */
    public InventoryRecord() {
        super(Inventory.INVENTORY);
    }

    /**
     * Create a detached, initialised InventoryRecord
     */
    public InventoryRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, UUID materialGoodId, UUID inventoryAdjustmentOrderId, UUID salesOrderId, UUID manufacturingOrderId, UUID supplierId, LocalDateTime purchaseDate, LocalDateTime expiryDate, Long receptionPriceAmount, Currency receptionPriceCurrency, BigDecimal quantity, UUID unitId, UUID receptionReceiptId, Long exitPriceAmount, Currency exitPriceCurrency, UUID servicingOrderId) {
        super(Inventory.INVENTORY);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setMaterialGoodId(materialGoodId);
        setInventoryAdjustmentOrderId(inventoryAdjustmentOrderId);
        setSalesOrderId(salesOrderId);
        setManufacturingOrderId(manufacturingOrderId);
        setSupplierId(supplierId);
        setPurchaseDate(purchaseDate);
        setExpiryDate(expiryDate);
        setReceptionPriceAmount(receptionPriceAmount);
        setReceptionPriceCurrency(receptionPriceCurrency);
        setQuantity(quantity);
        setUnitId(unitId);
        setReceptionReceiptId(receptionReceiptId);
        setExitPriceAmount(exitPriceAmount);
        setExitPriceCurrency(exitPriceCurrency);
        setServicingOrderId(servicingOrderId);
    }
}
