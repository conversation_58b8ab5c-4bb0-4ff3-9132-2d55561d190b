/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.AccountCustomerRecord;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row2;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AccountCustomer extends TableImpl<AccountCustomerRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.account_customer</code>
     */
    public static final AccountCustomer ACCOUNT_CUSTOMER = new AccountCustomer();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AccountCustomerRecord> getRecordType() {
        return AccountCustomerRecord.class;
    }

    /**
     * The column <code>public.account_customer.customer_id</code>.
     */
    public final TableField<AccountCustomerRecord, UUID> CUSTOMER_ID = createField(DSL.name("customer_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.account_customer.owner_id</code>.
     */
    public final TableField<AccountCustomerRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    private AccountCustomer(Name alias, Table<AccountCustomerRecord> aliased) {
        this(alias, aliased, null);
    }

    private AccountCustomer(Name alias, Table<AccountCustomerRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.account_customer</code> table reference
     */
    public AccountCustomer(String alias) {
        this(DSL.name(alias), ACCOUNT_CUSTOMER);
    }

    /**
     * Create an aliased <code>public.account_customer</code> table reference
     */
    public AccountCustomer(Name alias) {
        this(alias, ACCOUNT_CUSTOMER);
    }

    /**
     * Create a <code>public.account_customer</code> table reference
     */
    public AccountCustomer() {
        this(DSL.name("account_customer"), null);
    }

    public <O extends Record> AccountCustomer(Table<O> child, ForeignKey<O, AccountCustomerRecord> key) {
        super(child, key, ACCOUNT_CUSTOMER);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<ForeignKey<AccountCustomerRecord, ?>> getReferences() {
        return Arrays.asList(Keys.ACCOUNT_CUSTOMER__ACCOUNT_CUSTOMER_CUSTOMER_ID_FKEY, Keys.ACCOUNT_CUSTOMER__ACCOUNT_CUSTOMER_OWNER_ID_FKEY);
    }

    private transient Company _company;
    private transient Account _account;

    /**
     * Get the implicit join path to the <code>public.company</code> table.
     */
    public Company company() {
        if (_company == null)
            _company = new Company(this, Keys.ACCOUNT_CUSTOMER__ACCOUNT_CUSTOMER_CUSTOMER_ID_FKEY);

        return _company;
    }

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.ACCOUNT_CUSTOMER__ACCOUNT_CUSTOMER_OWNER_ID_FKEY);

        return _account;
    }

    @Override
    public AccountCustomer as(String alias) {
        return new AccountCustomer(DSL.name(alias), this);
    }

    @Override
    public AccountCustomer as(Name alias) {
        return new AccountCustomer(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public AccountCustomer rename(String name) {
        return new AccountCustomer(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public AccountCustomer rename(Name name) {
        return new AccountCustomer(name, null);
    }

    // -------------------------------------------------------------------------
    // Row2 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row2<UUID, UUID> fieldsRow() {
        return (Row2) super.fieldsRow();
    }
}
