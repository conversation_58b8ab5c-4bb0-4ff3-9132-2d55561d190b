/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.SequenceRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row8;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Sequence extends TableImpl<SequenceRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.sequence</code>
     */
    public static final Sequence SEQUENCE = new Sequence();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SequenceRecord> getRecordType() {
        return SequenceRecord.class;
    }

    /**
     * The column <code>public.sequence.id</code>.
     */
    public final TableField<SequenceRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.sequence.create_time</code>.
     */
    public final TableField<SequenceRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.sequence.update_time</code>.
     */
    public final TableField<SequenceRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.sequence.deleted</code>.
     */
    public final TableField<SequenceRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.sequence.owner_id</code>.
     */
    public final TableField<SequenceRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.sequence.section</code>.
     */
    public final TableField<SequenceRecord, String> SECTION = createField(DSL.name("section"), SQLDataType.VARCHAR(32), this, "");

    /**
     * The column <code>public.sequence.pattern</code>.
     */
    public final TableField<SequenceRecord, String> PATTERN = createField(DSL.name("pattern"), SQLDataType.VARCHAR(32), this, "");

    /**
     * The column <code>public.sequence.value</code>.
     */
    public final TableField<SequenceRecord, Long> VALUE = createField(DSL.name("value"), SQLDataType.BIGINT, this, "");

    private Sequence(Name alias, Table<SequenceRecord> aliased) {
        this(alias, aliased, null);
    }

    private Sequence(Name alias, Table<SequenceRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.sequence</code> table reference
     */
    public Sequence(String alias) {
        this(DSL.name(alias), SEQUENCE);
    }

    /**
     * Create an aliased <code>public.sequence</code> table reference
     */
    public Sequence(Name alias) {
        this(alias, SEQUENCE);
    }

    /**
     * Create a <code>public.sequence</code> table reference
     */
    public Sequence() {
        this(DSL.name("sequence"), null);
    }

    public <O extends Record> Sequence(Table<O> child, ForeignKey<O, SequenceRecord> key) {
        super(child, key, SEQUENCE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public UniqueKey<SequenceRecord> getPrimaryKey() {
        return Keys.SEQUENCE_PKEY;
    }

    @Override
    public List<ForeignKey<SequenceRecord, ?>> getReferences() {
        return Arrays.asList(Keys.SEQUENCE__SEQUENCE_OWNER_ID_FKEY);
    }

    private transient Account _account;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.SEQUENCE__SEQUENCE_OWNER_ID_FKEY);

        return _account;
    }

    @Override
    public Sequence as(String alias) {
        return new Sequence(DSL.name(alias), this);
    }

    @Override
    public Sequence as(Name alias) {
        return new Sequence(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Sequence rename(String name) {
        return new Sequence(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public Sequence rename(Name name) {
        return new Sequence(name, null);
    }

    // -------------------------------------------------------------------------
    // Row8 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row8<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, String, Long> fieldsRow() {
        return (Row8) super.fieldsRow();
    }
}
