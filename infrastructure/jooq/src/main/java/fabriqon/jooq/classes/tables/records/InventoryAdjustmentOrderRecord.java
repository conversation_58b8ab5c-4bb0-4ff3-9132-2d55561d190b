/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.InventoryAdjustmentOrder;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class InventoryAdjustmentOrderRecord extends UpdatableRecordImpl<InventoryAdjustmentOrderRecord> implements Record8<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, JSONB, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.inventory_adjustment_order.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.inventory_adjustment_order.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.inventory_adjustment_order.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.inventory_adjustment_order.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.inventory_adjustment_order.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.inventory_adjustment_order.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.inventory_adjustment_order.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.inventory_adjustment_order.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.inventory_adjustment_order.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.inventory_adjustment_order.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.inventory_adjustment_order.number</code>.
     */
    public void setNumber(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.inventory_adjustment_order.number</code>.
     */
    public String getNumber() {
        return (String) get(5);
    }

    /**
     * Setter for <code>public.inventory_adjustment_order.details</code>.
     */
    public void setDetails(JSONB value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.inventory_adjustment_order.details</code>.
     */
    public JSONB getDetails() {
        return (JSONB) get(6);
    }

    /**
     * Setter for <code>public.inventory_adjustment_order.type</code>.
     */
    public void setType(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.inventory_adjustment_order.type</code>.
     */
    public String getType() {
        return (String) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record8 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row8<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, JSONB, String> fieldsRow() {
        return (Row8) super.fieldsRow();
    }

    @Override
    public Row8<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, String, JSONB, String> valuesRow() {
        return (Row8) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return InventoryAdjustmentOrder.INVENTORY_ADJUSTMENT_ORDER.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return InventoryAdjustmentOrder.INVENTORY_ADJUSTMENT_ORDER.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return InventoryAdjustmentOrder.INVENTORY_ADJUSTMENT_ORDER.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return InventoryAdjustmentOrder.INVENTORY_ADJUSTMENT_ORDER.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return InventoryAdjustmentOrder.INVENTORY_ADJUSTMENT_ORDER.OWNER_ID;
    }

    @Override
    public Field<String> field6() {
        return InventoryAdjustmentOrder.INVENTORY_ADJUSTMENT_ORDER.NUMBER;
    }

    @Override
    public Field<JSONB> field7() {
        return InventoryAdjustmentOrder.INVENTORY_ADJUSTMENT_ORDER.DETAILS;
    }

    @Override
    public Field<String> field8() {
        return InventoryAdjustmentOrder.INVENTORY_ADJUSTMENT_ORDER.TYPE;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public String component6() {
        return getNumber();
    }

    @Override
    public JSONB component7() {
        return getDetails();
    }

    @Override
    public String component8() {
        return getType();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public String value6() {
        return getNumber();
    }

    @Override
    public JSONB value7() {
        return getDetails();
    }

    @Override
    public String value8() {
        return getType();
    }

    @Override
    public InventoryAdjustmentOrderRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public InventoryAdjustmentOrderRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public InventoryAdjustmentOrderRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public InventoryAdjustmentOrderRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public InventoryAdjustmentOrderRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public InventoryAdjustmentOrderRecord value6(String value) {
        setNumber(value);
        return this;
    }

    @Override
    public InventoryAdjustmentOrderRecord value7(JSONB value) {
        setDetails(value);
        return this;
    }

    @Override
    public InventoryAdjustmentOrderRecord value8(String value) {
        setType(value);
        return this;
    }

    @Override
    public InventoryAdjustmentOrderRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, String value6, JSONB value7, String value8) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached InventoryAdjustmentOrderRecord
     */
    public InventoryAdjustmentOrderRecord() {
        super(InventoryAdjustmentOrder.INVENTORY_ADJUSTMENT_ORDER);
    }

    /**
     * Create a detached, initialised InventoryAdjustmentOrderRecord
     */
    public InventoryAdjustmentOrderRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, String number, JSONB details, String type) {
        super(InventoryAdjustmentOrder.INVENTORY_ADJUSTMENT_ORDER);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setNumber(number);
        setDetails(details);
        setType(type);
    }
}
