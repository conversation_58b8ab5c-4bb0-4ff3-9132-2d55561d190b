/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.SystemEventRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row7;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SystemEvent extends TableImpl<SystemEventRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.system_event</code>
     */
    public static final SystemEvent SYSTEM_EVENT = new SystemEvent();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SystemEventRecord> getRecordType() {
        return SystemEventRecord.class;
    }

    /**
     * The column <code>public.system_event.id</code>.
     */
    public final TableField<SystemEventRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.system_event.create_time</code>.
     */
    public final TableField<SystemEventRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.system_event.owner_id</code>.
     */
    public final TableField<SystemEventRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.system_event.user_id</code>.
     */
    public final TableField<SystemEventRecord, UUID> USER_ID = createField(DSL.name("user_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.system_event.target_entity_id</code>.
     */
    public final TableField<SystemEventRecord, UUID> TARGET_ENTITY_ID = createField(DSL.name("target_entity_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.system_event.section</code>.
     */
    public final TableField<SystemEventRecord, String> SECTION = createField(DSL.name("section"), SQLDataType.VARCHAR(200).nullable(false), this, "");

    /**
     * The column <code>public.system_event.transition</code>.
     */
    public final TableField<SystemEventRecord, String> TRANSITION = createField(DSL.name("transition"), SQLDataType.VARCHAR(200).nullable(false), this, "");

    private SystemEvent(Name alias, Table<SystemEventRecord> aliased) {
        this(alias, aliased, null);
    }

    private SystemEvent(Name alias, Table<SystemEventRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.system_event</code> table reference
     */
    public SystemEvent(String alias) {
        this(DSL.name(alias), SYSTEM_EVENT);
    }

    /**
     * Create an aliased <code>public.system_event</code> table reference
     */
    public SystemEvent(Name alias) {
        this(alias, SYSTEM_EVENT);
    }

    /**
     * Create a <code>public.system_event</code> table reference
     */
    public SystemEvent() {
        this(DSL.name("system_event"), null);
    }

    public <O extends Record> SystemEvent(Table<O> child, ForeignKey<O, SystemEventRecord> key) {
        super(child, key, SYSTEM_EVENT);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public UniqueKey<SystemEventRecord> getPrimaryKey() {
        return Keys.SYSTEM_EVENT_PKEY;
    }

    @Override
    public List<ForeignKey<SystemEventRecord, ?>> getReferences() {
        return Arrays.asList(Keys.SYSTEM_EVENT__SYSTEM_EVENT_OWNER_ID_FKEY, Keys.SYSTEM_EVENT__SYSTEM_EVENT_USER_ID_FKEY);
    }

    private transient Account _account;
    private transient Users _users;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.SYSTEM_EVENT__SYSTEM_EVENT_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.users</code> table.
     */
    public Users users() {
        if (_users == null)
            _users = new Users(this, Keys.SYSTEM_EVENT__SYSTEM_EVENT_USER_ID_FKEY);

        return _users;
    }

    @Override
    public SystemEvent as(String alias) {
        return new SystemEvent(DSL.name(alias), this);
    }

    @Override
    public SystemEvent as(Name alias) {
        return new SystemEvent(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SystemEvent rename(String name) {
        return new SystemEvent(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public SystemEvent rename(Name name) {
        return new SystemEvent(name, null);
    }

    // -------------------------------------------------------------------------
    // Row7 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row7<UUID, LocalDateTime, UUID, UUID, UUID, String, String> fieldsRow() {
        return (Row7) super.fieldsRow();
    }
}
