/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.PurchaseWishlistRecord;
import fabriqon.jooq.converters.CurrencyConverter;
import org.jooq.*;
import org.jooq.Record;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Currency;
import java.util.List;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PurchaseWishlist extends TableImpl<PurchaseWishlistRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.purchase_wishlist</code>
     */
    public static final PurchaseWishlist PURCHASE_WISHLIST = new PurchaseWishlist();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<PurchaseWishlistRecord> getRecordType() {
        return PurchaseWishlistRecord.class;
    }

    /**
     * The column <code>public.purchase_wishlist.id</code>.
     */
    public final TableField<PurchaseWishlistRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.purchase_wishlist.create_time</code>.
     */
    public final TableField<PurchaseWishlistRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.purchase_wishlist.update_time</code>.
     */
    public final TableField<PurchaseWishlistRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.purchase_wishlist.deleted</code>.
     */
    public final TableField<PurchaseWishlistRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.purchase_wishlist.owner_id</code>.
     */
    public final TableField<PurchaseWishlistRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.purchase_wishlist.material_good_id</code>.
     */
    public final TableField<PurchaseWishlistRecord, UUID> MATERIAL_GOOD_ID = createField(DSL.name("material_good_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.purchase_wishlist.quantity</code>.
     */
    public final TableField<PurchaseWishlistRecord, BigDecimal> QUANTITY = createField(DSL.name("quantity"), SQLDataType.NUMERIC(12, 4), this, "");

    /**
     * The column <code>public.purchase_wishlist.expected_price_amount</code>.
     */
    public final TableField<PurchaseWishlistRecord, Long> EXPECTED_PRICE_AMOUNT = createField(DSL.name("expected_price_amount"), SQLDataType.BIGINT, this, "");

    /**
     * The column <code>public.purchase_wishlist.expected_price_currency</code>.
     */
    public final TableField<PurchaseWishlistRecord, Currency> EXPECTED_PRICE_CURRENCY = createField(DSL.name("expected_price_currency"), SQLDataType.VARCHAR(3), this, "", new CurrencyConverter());

    /**
     * The column <code>public.purchase_wishlist.supplier_id</code>.
     */
    public final TableField<PurchaseWishlistRecord, UUID> SUPPLIER_ID = createField(DSL.name("supplier_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.purchase_wishlist.sources</code>.
     */
    public final TableField<PurchaseWishlistRecord, JSONB> SOURCES = createField(DSL.name("sources"), SQLDataType.JSONB, this, "");

    private PurchaseWishlist(Name alias, Table<PurchaseWishlistRecord> aliased) {
        this(alias, aliased, null);
    }

    private PurchaseWishlist(Name alias, Table<PurchaseWishlistRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.purchase_wishlist</code> table reference
     */
    public PurchaseWishlist(String alias) {
        this(DSL.name(alias), PURCHASE_WISHLIST);
    }

    /**
     * Create an aliased <code>public.purchase_wishlist</code> table reference
     */
    public PurchaseWishlist(Name alias) {
        this(alias, PURCHASE_WISHLIST);
    }

    /**
     * Create a <code>public.purchase_wishlist</code> table reference
     */
    public PurchaseWishlist() {
        this(DSL.name("purchase_wishlist"), null);
    }

    public <O extends Record> PurchaseWishlist(Table<O> child, ForeignKey<O, PurchaseWishlistRecord> key) {
        super(child, key, PURCHASE_WISHLIST);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public UniqueKey<PurchaseWishlistRecord> getPrimaryKey() {
        return Keys.PURCHASE_WISHLIST_PKEY;
    }

    @Override
    public List<UniqueKey<PurchaseWishlistRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.PURCHASE_WISHLIST_OWNER_ID_MATERIAL_GOOD_ID_SUPPLIER_ID_KEY);
    }

    @Override
    public List<ForeignKey<PurchaseWishlistRecord, ?>> getReferences() {
        return Arrays.asList(Keys.PURCHASE_WISHLIST__PURCHASE_WISHLIST_OWNER_ID_FKEY, Keys.PURCHASE_WISHLIST__PURCHASE_WISHLIST_MATERIAL_GOOD_ID_FKEY, Keys.PURCHASE_WISHLIST__PURCHASE_WISHLIST_SUPPLIER_ID_FKEY);
    }

    private transient Account _account;
    private transient MaterialGood _materialGood;
    private transient Company _company;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.PURCHASE_WISHLIST__PURCHASE_WISHLIST_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.material_good</code>
     * table.
     */
    public MaterialGood materialGood() {
        if (_materialGood == null)
            _materialGood = new MaterialGood(this, Keys.PURCHASE_WISHLIST__PURCHASE_WISHLIST_MATERIAL_GOOD_ID_FKEY);

        return _materialGood;
    }

    /**
     * Get the implicit join path to the <code>public.company</code> table.
     */
    public Company company() {
        if (_company == null)
            _company = new Company(this, Keys.PURCHASE_WISHLIST__PURCHASE_WISHLIST_SUPPLIER_ID_FKEY);

        return _company;
    }

    @Override
    public PurchaseWishlist as(String alias) {
        return new PurchaseWishlist(DSL.name(alias), this);
    }

    @Override
    public PurchaseWishlist as(Name alias) {
        return new PurchaseWishlist(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public PurchaseWishlist rename(String name) {
        return new PurchaseWishlist(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public PurchaseWishlist rename(Name name) {
        return new PurchaseWishlist(name, null);
    }

    // -------------------------------------------------------------------------
    // Row11 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row11<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, BigDecimal, Long, Currency, UUID, JSONB> fieldsRow() {
        return (Row11) super.fieldsRow();
    }
}
