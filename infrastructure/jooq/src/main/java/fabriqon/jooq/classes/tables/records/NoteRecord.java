/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.Note;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class NoteRecord extends UpdatableRecordImpl<NoteRecord> implements Record7<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.note.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.note.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.note.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.note.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.note.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.note.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.note.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.note.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.note.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.note.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.note.added_by_id</code>.
     */
    public void setAddedById(UUID value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.note.added_by_id</code>.
     */
    public UUID getAddedById() {
        return (UUID) get(5);
    }

    /**
     * Setter for <code>public.note.note</code>.
     */
    public void setNote(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.note.note</code>.
     */
    public String getNote() {
        return (String) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row7<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    @Override
    public Row7<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, String> valuesRow() {
        return (Row7) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return Note.NOTE.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return Note.NOTE.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return Note.NOTE.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return Note.NOTE.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return Note.NOTE.OWNER_ID;
    }

    @Override
    public Field<UUID> field6() {
        return Note.NOTE.ADDED_BY_ID;
    }

    @Override
    public Field<String> field7() {
        return Note.NOTE.NOTE_;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public UUID component6() {
        return getAddedById();
    }

    @Override
    public String component7() {
        return getNote();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public UUID value6() {
        return getAddedById();
    }

    @Override
    public String value7() {
        return getNote();
    }

    @Override
    public NoteRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public NoteRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public NoteRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public NoteRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public NoteRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public NoteRecord value6(UUID value) {
        setAddedById(value);
        return this;
    }

    @Override
    public NoteRecord value7(String value) {
        setNote(value);
        return this;
    }

    @Override
    public NoteRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, UUID value6, String value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached NoteRecord
     */
    public NoteRecord() {
        super(Note.NOTE);
    }

    /**
     * Create a detached, initialised NoteRecord
     */
    public NoteRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, UUID addedById, String note) {
        super(Note.NOTE);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setAddedById(addedById);
        setNote(note);
    }
}
