/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.ReservedInventory;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record14;
import org.jooq.Row14;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ReservedInventoryRecord extends UpdatableRecordImpl<ReservedInventoryRecord> implements Record14<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, UUID, UUID, UUID, BigDecimal, UUID, Boolean, UUID, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.reserved_inventory.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.reserved_inventory.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.reserved_inventory.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.reserved_inventory.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.reserved_inventory.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.reserved_inventory.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.reserved_inventory.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.reserved_inventory.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.reserved_inventory.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.reserved_inventory.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.reserved_inventory.material_good_id</code>.
     */
    public void setMaterialGoodId(UUID value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.reserved_inventory.material_good_id</code>.
     */
    public UUID getMaterialGoodId() {
        return (UUID) get(5);
    }

    /**
     * Setter for <code>public.reserved_inventory.location_id</code>.
     */
    public void setLocationId(UUID value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.reserved_inventory.location_id</code>.
     */
    public UUID getLocationId() {
        return (UUID) get(6);
    }

    /**
     * Setter for <code>public.reserved_inventory.sales_order_id</code>.
     */
    public void setSalesOrderId(UUID value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.reserved_inventory.sales_order_id</code>.
     */
    public UUID getSalesOrderId() {
        return (UUID) get(7);
    }

    /**
     * Setter for <code>public.reserved_inventory.manufacturing_order_id</code>.
     */
    public void setManufacturingOrderId(UUID value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.reserved_inventory.manufacturing_order_id</code>.
     */
    public UUID getManufacturingOrderId() {
        return (UUID) get(8);
    }

    /**
     * Setter for <code>public.reserved_inventory.quantity</code>.
     */
    public void setQuantity(BigDecimal value) {
        set(9, value);
    }

    /**
     * Getter for <code>public.reserved_inventory.quantity</code>.
     */
    public BigDecimal getQuantity() {
        return (BigDecimal) get(9);
    }

    /**
     * Setter for <code>public.reserved_inventory.inventory_unit_id</code>.
     */
    public void setInventoryUnitId(UUID value) {
        set(10, value);
    }

    /**
     * Getter for <code>public.reserved_inventory.inventory_unit_id</code>.
     */
    public UUID getInventoryUnitId() {
        return (UUID) get(10);
    }

    /**
     * Setter for <code>public.reserved_inventory.mandatory_for_order</code>.
     */
    public void setMandatoryForOrder(Boolean value) {
        set(11, value);
    }

    /**
     * Getter for <code>public.reserved_inventory.mandatory_for_order</code>.
     */
    public Boolean getMandatoryForOrder() {
        return (Boolean) get(11);
    }

    /**
     * Setter for <code>public.reserved_inventory.from_order</code>.
     */
    public void setFromOrder(UUID value) {
        set(12, value);
    }

    /**
     * Getter for <code>public.reserved_inventory.from_order</code>.
     */
    public UUID getFromOrder() {
        return (UUID) get(12);
    }

    /**
     * Setter for <code>public.reserved_inventory.servicing_order_id</code>.
     */
    public void setServicingOrderId(UUID value) {
        set(13, value);
    }

    /**
     * Getter for <code>public.reserved_inventory.servicing_order_id</code>.
     */
    public UUID getServicingOrderId() {
        return (UUID) get(13);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record14 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row14<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, UUID, UUID, UUID, BigDecimal, UUID, Boolean, UUID, UUID> fieldsRow() {
        return (Row14) super.fieldsRow();
    }

    @Override
    public Row14<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, UUID, UUID, UUID, BigDecimal, UUID, Boolean, UUID, UUID> valuesRow() {
        return (Row14) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return ReservedInventory.RESERVED_INVENTORY.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return ReservedInventory.RESERVED_INVENTORY.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return ReservedInventory.RESERVED_INVENTORY.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return ReservedInventory.RESERVED_INVENTORY.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return ReservedInventory.RESERVED_INVENTORY.OWNER_ID;
    }

    @Override
    public Field<UUID> field6() {
        return ReservedInventory.RESERVED_INVENTORY.MATERIAL_GOOD_ID;
    }

    @Override
    public Field<UUID> field7() {
        return ReservedInventory.RESERVED_INVENTORY.LOCATION_ID;
    }

    @Override
    public Field<UUID> field8() {
        return ReservedInventory.RESERVED_INVENTORY.SALES_ORDER_ID;
    }

    @Override
    public Field<UUID> field9() {
        return ReservedInventory.RESERVED_INVENTORY.MANUFACTURING_ORDER_ID;
    }

    @Override
    public Field<BigDecimal> field10() {
        return ReservedInventory.RESERVED_INVENTORY.QUANTITY;
    }

    @Override
    public Field<UUID> field11() {
        return ReservedInventory.RESERVED_INVENTORY.INVENTORY_UNIT_ID;
    }

    @Override
    public Field<Boolean> field12() {
        return ReservedInventory.RESERVED_INVENTORY.MANDATORY_FOR_ORDER;
    }

    @Override
    public Field<UUID> field13() {
        return ReservedInventory.RESERVED_INVENTORY.FROM_ORDER;
    }

    @Override
    public Field<UUID> field14() {
        return ReservedInventory.RESERVED_INVENTORY.SERVICING_ORDER_ID;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public UUID component6() {
        return getMaterialGoodId();
    }

    @Override
    public UUID component7() {
        return getLocationId();
    }

    @Override
    public UUID component8() {
        return getSalesOrderId();
    }

    @Override
    public UUID component9() {
        return getManufacturingOrderId();
    }

    @Override
    public BigDecimal component10() {
        return getQuantity();
    }

    @Override
    public UUID component11() {
        return getInventoryUnitId();
    }

    @Override
    public Boolean component12() {
        return getMandatoryForOrder();
    }

    @Override
    public UUID component13() {
        return getFromOrder();
    }

    @Override
    public UUID component14() {
        return getServicingOrderId();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public UUID value6() {
        return getMaterialGoodId();
    }

    @Override
    public UUID value7() {
        return getLocationId();
    }

    @Override
    public UUID value8() {
        return getSalesOrderId();
    }

    @Override
    public UUID value9() {
        return getManufacturingOrderId();
    }

    @Override
    public BigDecimal value10() {
        return getQuantity();
    }

    @Override
    public UUID value11() {
        return getInventoryUnitId();
    }

    @Override
    public Boolean value12() {
        return getMandatoryForOrder();
    }

    @Override
    public UUID value13() {
        return getFromOrder();
    }

    @Override
    public UUID value14() {
        return getServicingOrderId();
    }

    @Override
    public ReservedInventoryRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public ReservedInventoryRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public ReservedInventoryRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public ReservedInventoryRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public ReservedInventoryRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public ReservedInventoryRecord value6(UUID value) {
        setMaterialGoodId(value);
        return this;
    }

    @Override
    public ReservedInventoryRecord value7(UUID value) {
        setLocationId(value);
        return this;
    }

    @Override
    public ReservedInventoryRecord value8(UUID value) {
        setSalesOrderId(value);
        return this;
    }

    @Override
    public ReservedInventoryRecord value9(UUID value) {
        setManufacturingOrderId(value);
        return this;
    }

    @Override
    public ReservedInventoryRecord value10(BigDecimal value) {
        setQuantity(value);
        return this;
    }

    @Override
    public ReservedInventoryRecord value11(UUID value) {
        setInventoryUnitId(value);
        return this;
    }

    @Override
    public ReservedInventoryRecord value12(Boolean value) {
        setMandatoryForOrder(value);
        return this;
    }

    @Override
    public ReservedInventoryRecord value13(UUID value) {
        setFromOrder(value);
        return this;
    }

    @Override
    public ReservedInventoryRecord value14(UUID value) {
        setServicingOrderId(value);
        return this;
    }

    @Override
    public ReservedInventoryRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, UUID value6, UUID value7, UUID value8, UUID value9, BigDecimal value10, UUID value11, Boolean value12, UUID value13, UUID value14) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ReservedInventoryRecord
     */
    public ReservedInventoryRecord() {
        super(ReservedInventory.RESERVED_INVENTORY);
    }

    /**
     * Create a detached, initialised ReservedInventoryRecord
     */
    public ReservedInventoryRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, UUID materialGoodId, UUID locationId, UUID salesOrderId, UUID manufacturingOrderId, BigDecimal quantity, UUID inventoryUnitId, Boolean mandatoryForOrder, UUID fromOrder, UUID servicingOrderId) {
        super(ReservedInventory.RESERVED_INVENTORY);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setMaterialGoodId(materialGoodId);
        setLocationId(locationId);
        setSalesOrderId(salesOrderId);
        setManufacturingOrderId(manufacturingOrderId);
        setQuantity(quantity);
        setInventoryUnitId(inventoryUnitId);
        setMandatoryForOrder(mandatoryForOrder);
        setFromOrder(fromOrder);
        setServicingOrderId(servicingOrderId);
    }
}
