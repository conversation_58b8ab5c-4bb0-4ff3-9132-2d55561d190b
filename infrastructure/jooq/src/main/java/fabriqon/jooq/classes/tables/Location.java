/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.LocationRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row6;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Location extends TableImpl<LocationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.location</code>
     */
    public static final Location LOCATION = new Location();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LocationRecord> getRecordType() {
        return LocationRecord.class;
    }

    /**
     * The column <code>public.location.id</code>.
     */
    public final TableField<LocationRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.location.create_time</code>.
     */
    public final TableField<LocationRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.location.update_time</code>.
     */
    public final TableField<LocationRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.location.deleted</code>.
     */
    public final TableField<LocationRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.location.owner_id</code>.
     */
    public final TableField<LocationRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.location.details</code>.
     */
    public final TableField<LocationRecord, JSONB> DETAILS = createField(DSL.name("details"), SQLDataType.JSONB, this, "");

    private Location(Name alias, Table<LocationRecord> aliased) {
        this(alias, aliased, null);
    }

    private Location(Name alias, Table<LocationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.location</code> table reference
     */
    public Location(String alias) {
        this(DSL.name(alias), LOCATION);
    }

    /**
     * Create an aliased <code>public.location</code> table reference
     */
    public Location(Name alias) {
        this(alias, LOCATION);
    }

    /**
     * Create a <code>public.location</code> table reference
     */
    public Location() {
        this(DSL.name("location"), null);
    }

    public <O extends Record> Location(Table<O> child, ForeignKey<O, LocationRecord> key) {
        super(child, key, LOCATION);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public UniqueKey<LocationRecord> getPrimaryKey() {
        return Keys.LOCATION_PKEY;
    }

    @Override
    public List<ForeignKey<LocationRecord, ?>> getReferences() {
        return Arrays.asList(Keys.LOCATION__LOCATION_OWNER_ID_FKEY);
    }

    private transient Account _account;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.LOCATION__LOCATION_OWNER_ID_FKEY);

        return _account;
    }

    @Override
    public Location as(String alias) {
        return new Location(DSL.name(alias), this);
    }

    @Override
    public Location as(Name alias) {
        return new Location(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Location rename(String name) {
        return new Location(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public Location rename(Name name) {
        return new Location(name, null);
    }

    // -------------------------------------------------------------------------
    // Row6 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row6<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, JSONB> fieldsRow() {
        return (Row6) super.fieldsRow();
    }
}
