/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.EmployeeTimeoff;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record10;
import org.jooq.Row10;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class EmployeeTimeoffRecord extends UpdatableRecordImpl<EmployeeTimeoffRecord> implements Record10<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, LocalDateTime, LocalDateTime, String, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.employee_timeoff.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.employee_timeoff.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.employee_timeoff.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.employee_timeoff.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.employee_timeoff.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.employee_timeoff.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.employee_timeoff.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.employee_timeoff.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.employee_timeoff.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.employee_timeoff.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.employee_timeoff.user_id</code>.
     */
    public void setUserId(UUID value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.employee_timeoff.user_id</code>.
     */
    public UUID getUserId() {
        return (UUID) get(5);
    }

    /**
     * Setter for <code>public.employee_timeoff.start_time</code>.
     */
    public void setStartTime(LocalDateTime value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.employee_timeoff.start_time</code>.
     */
    public LocalDateTime getStartTime() {
        return (LocalDateTime) get(6);
    }

    /**
     * Setter for <code>public.employee_timeoff.end_time</code>.
     */
    public void setEndTime(LocalDateTime value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.employee_timeoff.end_time</code>.
     */
    public LocalDateTime getEndTime() {
        return (LocalDateTime) get(7);
    }

    /**
     * Setter for <code>public.employee_timeoff.type</code>.
     */
    public void setType(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.employee_timeoff.type</code>.
     */
    public String getType() {
        return (String) get(8);
    }

    /**
     * Setter for <code>public.employee_timeoff.servicing_order_id</code>.
     */
    public void setServicingOrderId(UUID value) {
        set(9, value);
    }

    /**
     * Getter for <code>public.employee_timeoff.servicing_order_id</code>.
     */
    public UUID getServicingOrderId() {
        return (UUID) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record10 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row10<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, LocalDateTime, LocalDateTime, String, UUID> fieldsRow() {
        return (Row10) super.fieldsRow();
    }

    @Override
    public Row10<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, LocalDateTime, LocalDateTime, String, UUID> valuesRow() {
        return (Row10) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return EmployeeTimeoff.EMPLOYEE_TIMEOFF.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return EmployeeTimeoff.EMPLOYEE_TIMEOFF.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return EmployeeTimeoff.EMPLOYEE_TIMEOFF.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return EmployeeTimeoff.EMPLOYEE_TIMEOFF.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return EmployeeTimeoff.EMPLOYEE_TIMEOFF.OWNER_ID;
    }

    @Override
    public Field<UUID> field6() {
        return EmployeeTimeoff.EMPLOYEE_TIMEOFF.USER_ID;
    }

    @Override
    public Field<LocalDateTime> field7() {
        return EmployeeTimeoff.EMPLOYEE_TIMEOFF.START_TIME;
    }

    @Override
    public Field<LocalDateTime> field8() {
        return EmployeeTimeoff.EMPLOYEE_TIMEOFF.END_TIME;
    }

    @Override
    public Field<String> field9() {
        return EmployeeTimeoff.EMPLOYEE_TIMEOFF.TYPE;
    }

    @Override
    public Field<UUID> field10() {
        return EmployeeTimeoff.EMPLOYEE_TIMEOFF.SERVICING_ORDER_ID;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public UUID component6() {
        return getUserId();
    }

    @Override
    public LocalDateTime component7() {
        return getStartTime();
    }

    @Override
    public LocalDateTime component8() {
        return getEndTime();
    }

    @Override
    public String component9() {
        return getType();
    }

    @Override
    public UUID component10() {
        return getServicingOrderId();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public UUID value6() {
        return getUserId();
    }

    @Override
    public LocalDateTime value7() {
        return getStartTime();
    }

    @Override
    public LocalDateTime value8() {
        return getEndTime();
    }

    @Override
    public String value9() {
        return getType();
    }

    @Override
    public UUID value10() {
        return getServicingOrderId();
    }

    @Override
    public EmployeeTimeoffRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public EmployeeTimeoffRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public EmployeeTimeoffRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public EmployeeTimeoffRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public EmployeeTimeoffRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public EmployeeTimeoffRecord value6(UUID value) {
        setUserId(value);
        return this;
    }

    @Override
    public EmployeeTimeoffRecord value7(LocalDateTime value) {
        setStartTime(value);
        return this;
    }

    @Override
    public EmployeeTimeoffRecord value8(LocalDateTime value) {
        setEndTime(value);
        return this;
    }

    @Override
    public EmployeeTimeoffRecord value9(String value) {
        setType(value);
        return this;
    }

    @Override
    public EmployeeTimeoffRecord value10(UUID value) {
        setServicingOrderId(value);
        return this;
    }

    @Override
    public EmployeeTimeoffRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, UUID value6, LocalDateTime value7, LocalDateTime value8, String value9, UUID value10) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached EmployeeTimeoffRecord
     */
    public EmployeeTimeoffRecord() {
        super(EmployeeTimeoff.EMPLOYEE_TIMEOFF);
    }

    /**
     * Create a detached, initialised EmployeeTimeoffRecord
     */
    public EmployeeTimeoffRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, UUID userId, LocalDateTime startTime, LocalDateTime endTime, String type, UUID servicingOrderId) {
        super(EmployeeTimeoff.EMPLOYEE_TIMEOFF);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setUserId(userId);
        setStartTime(startTime);
        setEndTime(endTime);
        setType(type);
        setServicingOrderId(servicingOrderId);
    }
}
