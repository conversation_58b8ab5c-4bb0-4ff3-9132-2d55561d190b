/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.Invoice;
import org.jooq.*;
import org.jooq.impl.UpdatableRecordImpl;

import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class InvoiceRecord extends UpdatableRecordImpl<InvoiceRecord> implements Record16<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, UUID, String, LocalDateTime, Boolean, LocalDateTime, JSONB, Boolean, String, JSONB, JSONB> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.invoice.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.invoice.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.invoice.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.invoice.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.invoice.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.invoice.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.invoice.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.invoice.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.invoice.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.invoice.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.invoice.sales_order_id</code>.
     */
    public void setSalesOrderId(UUID value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.invoice.sales_order_id</code>.
     */
    public UUID getSalesOrderId() {
        return (UUID) get(5);
    }

    /**
     * Setter for <code>public.invoice.customer_id</code>.
     */
    public void setCustomerId(UUID value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.invoice.customer_id</code>.
     */
    public UUID getCustomerId() {
        return (UUID) get(6);
    }

    /**
     * Setter for <code>public.invoice.number</code>.
     */
    public void setNumber(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.invoice.number</code>.
     */
    public String getNumber() {
        return (String) get(7);
    }

    /**
     * Setter for <code>public.invoice.due_date</code>.
     */
    public void setDueDate(LocalDateTime value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.invoice.due_date</code>.
     */
    public LocalDateTime getDueDate() {
        return (LocalDateTime) get(8);
    }

    /**
     * Setter for <code>public.invoice.paid</code>.
     */
    public void setPaid(Boolean value) {
        set(9, value);
    }

    /**
     * Getter for <code>public.invoice.paid</code>.
     */
    public Boolean getPaid() {
        return (Boolean) get(9);
    }

    /**
     * Setter for <code>public.invoice.sent_at</code>.
     */
    public void setSentAt(LocalDateTime value) {
        set(10, value);
    }

    /**
     * Getter for <code>public.invoice.sent_at</code>.
     */
    public LocalDateTime getSentAt() {
        return (LocalDateTime) get(10);
    }

    /**
     * Setter for <code>public.invoice.items</code>.
     */
    public void setItems(JSONB value) {
        set(11, value);
    }

    /**
     * Getter for <code>public.invoice.items</code>.
     */
    public JSONB getItems() {
        return (JSONB) get(11);
    }

    /**
     * Setter for <code>public.invoice.proforma</code>.
     */
    public void setProforma(Boolean value) {
        set(12, value);
    }

    /**
     * Getter for <code>public.invoice.proforma</code>.
     */
    public Boolean getProforma() {
        return (Boolean) get(12);
    }

    /**
     * Setter for <code>public.invoice.notes</code>.
     */
    public void setNotes(String value) {
        set(13, value);
    }

    /**
     * Getter for <code>public.invoice.notes</code>.
     */
    public String getNotes() {
        return (String) get(13);
    }

    /**
     * Setter for <code>public.invoice.rendering_details</code>.
     */
    public void setRenderingDetails(JSONB value) {
        set(14, value);
    }

    /**
     * Getter for <code>public.invoice.rendering_details</code>.
     */
    public JSONB getRenderingDetails() {
        return (JSONB) get(14);
    }

    /**
     * Setter for <code>public.invoice.global_discount</code>.
     */
    public void setGlobalDiscount(JSONB value) {
        set(15, value);
    }

    /**
     * Getter for <code>public.invoice.global_discount</code>.
     */
    public JSONB getGlobalDiscount() {
        return (JSONB) get(15);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record16 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row16<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, UUID, String, LocalDateTime, Boolean, LocalDateTime, JSONB, Boolean, String, JSONB, JSONB> fieldsRow() {
        return (Row16) super.fieldsRow();
    }

    @Override
    public Row16<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, UUID, String, LocalDateTime, Boolean, LocalDateTime, JSONB, Boolean, String, JSONB, JSONB> valuesRow() {
        return (Row16) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return Invoice.INVOICE.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return Invoice.INVOICE.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return Invoice.INVOICE.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return Invoice.INVOICE.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return Invoice.INVOICE.OWNER_ID;
    }

    @Override
    public Field<UUID> field6() {
        return Invoice.INVOICE.SALES_ORDER_ID;
    }

    @Override
    public Field<UUID> field7() {
        return Invoice.INVOICE.CUSTOMER_ID;
    }

    @Override
    public Field<String> field8() {
        return Invoice.INVOICE.NUMBER;
    }

    @Override
    public Field<LocalDateTime> field9() {
        return Invoice.INVOICE.DUE_DATE;
    }

    @Override
    public Field<Boolean> field10() {
        return Invoice.INVOICE.PAID;
    }

    @Override
    public Field<LocalDateTime> field11() {
        return Invoice.INVOICE.SENT_AT;
    }

    @Override
    public Field<JSONB> field12() {
        return Invoice.INVOICE.ITEMS;
    }

    @Override
    public Field<Boolean> field13() {
        return Invoice.INVOICE.PROFORMA;
    }

    @Override
    public Field<String> field14() {
        return Invoice.INVOICE.NOTES;
    }

    @Override
    public Field<JSONB> field15() {
        return Invoice.INVOICE.RENDERING_DETAILS;
    }

    @Override
    public Field<JSONB> field16() {
        return Invoice.INVOICE.GLOBAL_DISCOUNT;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public UUID component6() {
        return getSalesOrderId();
    }

    @Override
    public UUID component7() {
        return getCustomerId();
    }

    @Override
    public String component8() {
        return getNumber();
    }

    @Override
    public LocalDateTime component9() {
        return getDueDate();
    }

    @Override
    public Boolean component10() {
        return getPaid();
    }

    @Override
    public LocalDateTime component11() {
        return getSentAt();
    }

    @Override
    public JSONB component12() {
        return getItems();
    }

    @Override
    public Boolean component13() {
        return getProforma();
    }

    @Override
    public String component14() {
        return getNotes();
    }

    @Override
    public JSONB component15() {
        return getRenderingDetails();
    }

    @Override
    public JSONB component16() {
        return getGlobalDiscount();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public UUID value6() {
        return getSalesOrderId();
    }

    @Override
    public UUID value7() {
        return getCustomerId();
    }

    @Override
    public String value8() {
        return getNumber();
    }

    @Override
    public LocalDateTime value9() {
        return getDueDate();
    }

    @Override
    public Boolean value10() {
        return getPaid();
    }

    @Override
    public LocalDateTime value11() {
        return getSentAt();
    }

    @Override
    public JSONB value12() {
        return getItems();
    }

    @Override
    public Boolean value13() {
        return getProforma();
    }

    @Override
    public String value14() {
        return getNotes();
    }

    @Override
    public JSONB value15() {
        return getRenderingDetails();
    }

    @Override
    public JSONB value16() {
        return getGlobalDiscount();
    }

    @Override
    public InvoiceRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public InvoiceRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public InvoiceRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public InvoiceRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public InvoiceRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public InvoiceRecord value6(UUID value) {
        setSalesOrderId(value);
        return this;
    }

    @Override
    public InvoiceRecord value7(UUID value) {
        setCustomerId(value);
        return this;
    }

    @Override
    public InvoiceRecord value8(String value) {
        setNumber(value);
        return this;
    }

    @Override
    public InvoiceRecord value9(LocalDateTime value) {
        setDueDate(value);
        return this;
    }

    @Override
    public InvoiceRecord value10(Boolean value) {
        setPaid(value);
        return this;
    }

    @Override
    public InvoiceRecord value11(LocalDateTime value) {
        setSentAt(value);
        return this;
    }

    @Override
    public InvoiceRecord value12(JSONB value) {
        setItems(value);
        return this;
    }

    @Override
    public InvoiceRecord value13(Boolean value) {
        setProforma(value);
        return this;
    }

    @Override
    public InvoiceRecord value14(String value) {
        setNotes(value);
        return this;
    }

    @Override
    public InvoiceRecord value15(JSONB value) {
        setRenderingDetails(value);
        return this;
    }

    @Override
    public InvoiceRecord value16(JSONB value) {
        setGlobalDiscount(value);
        return this;
    }

    @Override
    public InvoiceRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, UUID value6, UUID value7, String value8, LocalDateTime value9, Boolean value10, LocalDateTime value11, JSONB value12, Boolean value13, String value14, JSONB value15, JSONB value16) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached InvoiceRecord
     */
    public InvoiceRecord() {
        super(Invoice.INVOICE);
    }

    /**
     * Create a detached, initialised InvoiceRecord
     */
    public InvoiceRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, UUID salesOrderId, UUID customerId, String number, LocalDateTime dueDate, Boolean paid, LocalDateTime sentAt, JSONB items, Boolean proforma, String notes, JSONB renderingDetails, JSONB globalDiscount) {
        super(Invoice.INVOICE);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setSalesOrderId(salesOrderId);
        setCustomerId(customerId);
        setNumber(number);
        setDueDate(dueDate);
        setPaid(paid);
        setSentAt(sentAt);
        setItems(items);
        setProforma(proforma);
        setNotes(notes);
        setRenderingDetails(renderingDetails);
        setGlobalDiscount(globalDiscount);
    }
}
