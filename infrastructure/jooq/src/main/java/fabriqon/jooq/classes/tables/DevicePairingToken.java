/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.DevicePairingTokenRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row4;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DevicePairingToken extends TableImpl<DevicePairingTokenRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.device_pairing_token</code>
     */
    public static final DevicePairingToken DEVICE_PAIRING_TOKEN = new DevicePairingToken();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<DevicePairingTokenRecord> getRecordType() {
        return DevicePairingTokenRecord.class;
    }

    /**
     * The column <code>public.device_pairing_token.create_time</code>.
     */
    public final TableField<DevicePairingTokenRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.device_pairing_token.user_id</code>.
     */
    public final TableField<DevicePairingTokenRecord, UUID> USER_ID = createField(DSL.name("user_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.device_pairing_token.token</code>.
     */
    public final TableField<DevicePairingTokenRecord, String> TOKEN = createField(DSL.name("token"), SQLDataType.VARCHAR(100).nullable(false), this, "");

    /**
     * The column <code>public.device_pairing_token.token_type</code>.
     */
    public final TableField<DevicePairingTokenRecord, String> TOKEN_TYPE = createField(DSL.name("token_type"), SQLDataType.VARCHAR(32).nullable(false), this, "");

    private DevicePairingToken(Name alias, Table<DevicePairingTokenRecord> aliased) {
        this(alias, aliased, null);
    }

    private DevicePairingToken(Name alias, Table<DevicePairingTokenRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.device_pairing_token</code> table
     * reference
     */
    public DevicePairingToken(String alias) {
        this(DSL.name(alias), DEVICE_PAIRING_TOKEN);
    }

    /**
     * Create an aliased <code>public.device_pairing_token</code> table
     * reference
     */
    public DevicePairingToken(Name alias) {
        this(alias, DEVICE_PAIRING_TOKEN);
    }

    /**
     * Create a <code>public.device_pairing_token</code> table reference
     */
    public DevicePairingToken() {
        this(DSL.name("device_pairing_token"), null);
    }

    public <O extends Record> DevicePairingToken(Table<O> child, ForeignKey<O, DevicePairingTokenRecord> key) {
        super(child, key, DEVICE_PAIRING_TOKEN);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<ForeignKey<DevicePairingTokenRecord, ?>> getReferences() {
        return Arrays.asList(Keys.DEVICE_PAIRING_TOKEN__DEVICE_PAIRING_TOKEN_USER_ID_FKEY);
    }

    private transient Users _users;

    /**
     * Get the implicit join path to the <code>public.users</code> table.
     */
    public Users users() {
        if (_users == null)
            _users = new Users(this, Keys.DEVICE_PAIRING_TOKEN__DEVICE_PAIRING_TOKEN_USER_ID_FKEY);

        return _users;
    }

    @Override
    public DevicePairingToken as(String alias) {
        return new DevicePairingToken(DSL.name(alias), this);
    }

    @Override
    public DevicePairingToken as(Name alias) {
        return new DevicePairingToken(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public DevicePairingToken rename(String name) {
        return new DevicePairingToken(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public DevicePairingToken rename(Name name) {
        return new DevicePairingToken(name, null);
    }

    // -------------------------------------------------------------------------
    // Row4 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row4<LocalDateTime, UUID, String, String> fieldsRow() {
        return (Row4) super.fieldsRow();
    }
}
