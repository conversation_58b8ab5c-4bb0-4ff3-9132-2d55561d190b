/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.ManufacturingtaskWorkstation;

import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record3;
import org.jooq.Row3;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ManufacturingtaskWorkstationRecord extends TableRecordImpl<ManufacturingtaskWorkstationRecord> implements Record3<UUID, UUID, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.manufacturingtask_workstation.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.manufacturingtask_workstation.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(0);
    }

    /**
     * Setter for
     * <code>public.manufacturingtask_workstation.manufacturing_workstation_id</code>.
     */
    public void setManufacturingWorkstationId(UUID value) {
        set(1, value);
    }

    /**
     * Getter for
     * <code>public.manufacturingtask_workstation.manufacturing_workstation_id</code>.
     */
    public UUID getManufacturingWorkstationId() {
        return (UUID) get(1);
    }

    /**
     * Setter for
     * <code>public.manufacturingtask_workstation.manufacturing_task_id</code>.
     */
    public void setManufacturingTaskId(UUID value) {
        set(2, value);
    }

    /**
     * Getter for
     * <code>public.manufacturingtask_workstation.manufacturing_task_id</code>.
     */
    public UUID getManufacturingTaskId() {
        return (UUID) get(2);
    }

    // -------------------------------------------------------------------------
    // Record3 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row3<UUID, UUID, UUID> fieldsRow() {
        return (Row3) super.fieldsRow();
    }

    @Override
    public Row3<UUID, UUID, UUID> valuesRow() {
        return (Row3) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return ManufacturingtaskWorkstation.MANUFACTURINGTASK_WORKSTATION.OWNER_ID;
    }

    @Override
    public Field<UUID> field2() {
        return ManufacturingtaskWorkstation.MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_WORKSTATION_ID;
    }

    @Override
    public Field<UUID> field3() {
        return ManufacturingtaskWorkstation.MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_TASK_ID;
    }

    @Override
    public UUID component1() {
        return getOwnerId();
    }

    @Override
    public UUID component2() {
        return getManufacturingWorkstationId();
    }

    @Override
    public UUID component3() {
        return getManufacturingTaskId();
    }

    @Override
    public UUID value1() {
        return getOwnerId();
    }

    @Override
    public UUID value2() {
        return getManufacturingWorkstationId();
    }

    @Override
    public UUID value3() {
        return getManufacturingTaskId();
    }

    @Override
    public ManufacturingtaskWorkstationRecord value1(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public ManufacturingtaskWorkstationRecord value2(UUID value) {
        setManufacturingWorkstationId(value);
        return this;
    }

    @Override
    public ManufacturingtaskWorkstationRecord value3(UUID value) {
        setManufacturingTaskId(value);
        return this;
    }

    @Override
    public ManufacturingtaskWorkstationRecord values(UUID value1, UUID value2, UUID value3) {
        value1(value1);
        value2(value2);
        value3(value3);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ManufacturingtaskWorkstationRecord
     */
    public ManufacturingtaskWorkstationRecord() {
        super(ManufacturingtaskWorkstation.MANUFACTURINGTASK_WORKSTATION);
    }

    /**
     * Create a detached, initialised ManufacturingtaskWorkstationRecord
     */
    public ManufacturingtaskWorkstationRecord(UUID ownerId, UUID manufacturingWorkstationId, UUID manufacturingTaskId) {
        super(ManufacturingtaskWorkstation.MANUFACTURINGTASK_WORKSTATION);

        setOwnerId(ownerId);
        setManufacturingWorkstationId(manufacturingWorkstationId);
        setManufacturingTaskId(manufacturingTaskId);
    }
}
