/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.ReceptionReceiptRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row12;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ReceptionReceipt extends TableImpl<ReceptionReceiptRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.reception_receipt</code>
     */
    public static final ReceptionReceipt RECEPTION_RECEIPT = new ReceptionReceipt();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ReceptionReceiptRecord> getRecordType() {
        return ReceptionReceiptRecord.class;
    }

    /**
     * The column <code>public.reception_receipt.id</code>.
     */
    public final TableField<ReceptionReceiptRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.reception_receipt.create_time</code>.
     */
    public final TableField<ReceptionReceiptRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.reception_receipt.update_time</code>.
     */
    public final TableField<ReceptionReceiptRecord, LocalDateTime> UPDATE_TIME = createField(DSL.name("update_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.reception_receipt.deleted</code>.
     */
    public final TableField<ReceptionReceiptRecord, Boolean> DELETED = createField(DSL.name("deleted"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field("false", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>public.reception_receipt.owner_id</code>.
     */
    public final TableField<ReceptionReceiptRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.reception_receipt.supplier_id</code>.
     */
    public final TableField<ReceptionReceiptRecord, UUID> SUPPLIER_ID = createField(DSL.name("supplier_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.reception_receipt.purchase_order_id</code>.
     */
    public final TableField<ReceptionReceiptRecord, UUID> PURCHASE_ORDER_ID = createField(DSL.name("purchase_order_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.reception_receipt.number</code>.
     */
    public final TableField<ReceptionReceiptRecord, String> NUMBER = createField(DSL.name("number"), SQLDataType.VARCHAR(20).nullable(false), this, "");

    /**
     * The column <code>public.reception_receipt.reception_date</code>.
     */
    public final TableField<ReceptionReceiptRecord, LocalDateTime> RECEPTION_DATE = createField(DSL.name("reception_date"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>public.reception_receipt.supporting_document</code>.
     */
    public final TableField<ReceptionReceiptRecord, JSONB> SUPPORTING_DOCUMENT = createField(DSL.name("supporting_document"), SQLDataType.JSONB.nullable(false), this, "");

    /**
     * The column <code>public.reception_receipt.details</code>.
     */
    public final TableField<ReceptionReceiptRecord, JSONB> DETAILS = createField(DSL.name("details"), SQLDataType.JSONB.nullable(false), this, "");

    /**
     * The column <code>public.reception_receipt.goods</code>.
     */
    public final TableField<ReceptionReceiptRecord, JSONB> GOODS = createField(DSL.name("goods"), SQLDataType.JSONB.nullable(false), this, "");

    private ReceptionReceipt(Name alias, Table<ReceptionReceiptRecord> aliased) {
        this(alias, aliased, null);
    }

    private ReceptionReceipt(Name alias, Table<ReceptionReceiptRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.reception_receipt</code> table reference
     */
    public ReceptionReceipt(String alias) {
        this(DSL.name(alias), RECEPTION_RECEIPT);
    }

    /**
     * Create an aliased <code>public.reception_receipt</code> table reference
     */
    public ReceptionReceipt(Name alias) {
        this(alias, RECEPTION_RECEIPT);
    }

    /**
     * Create a <code>public.reception_receipt</code> table reference
     */
    public ReceptionReceipt() {
        this(DSL.name("reception_receipt"), null);
    }

    public <O extends Record> ReceptionReceipt(Table<O> child, ForeignKey<O, ReceptionReceiptRecord> key) {
        super(child, key, RECEPTION_RECEIPT);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public UniqueKey<ReceptionReceiptRecord> getPrimaryKey() {
        return Keys.RECEPTION_RECEIPT_PKEY;
    }

    @Override
    public List<ForeignKey<ReceptionReceiptRecord, ?>> getReferences() {
        return Arrays.asList(Keys.RECEPTION_RECEIPT__RECEPTION_RECEIPT_OWNER_ID_FKEY, Keys.RECEPTION_RECEIPT__RECEPTION_RECEIPT_SUPPLIER_ID_FKEY, Keys.RECEPTION_RECEIPT__RECEPTION_RECEIPT_PURCHASE_ORDER_ID_FKEY);
    }

    private transient Account _account;
    private transient Company _company;
    private transient PurchaseOrder _purchaseOrder;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.RECEPTION_RECEIPT__RECEPTION_RECEIPT_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.company</code> table.
     */
    public Company company() {
        if (_company == null)
            _company = new Company(this, Keys.RECEPTION_RECEIPT__RECEPTION_RECEIPT_SUPPLIER_ID_FKEY);

        return _company;
    }

    /**
     * Get the implicit join path to the <code>public.purchase_order</code>
     * table.
     */
    public PurchaseOrder purchaseOrder() {
        if (_purchaseOrder == null)
            _purchaseOrder = new PurchaseOrder(this, Keys.RECEPTION_RECEIPT__RECEPTION_RECEIPT_PURCHASE_ORDER_ID_FKEY);

        return _purchaseOrder;
    }

    @Override
    public ReceptionReceipt as(String alias) {
        return new ReceptionReceipt(DSL.name(alias), this);
    }

    @Override
    public ReceptionReceipt as(Name alias) {
        return new ReceptionReceipt(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ReceptionReceipt rename(String name) {
        return new ReceptionReceipt(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ReceptionReceipt rename(Name name) {
        return new ReceptionReceipt(name, null);
    }

    // -------------------------------------------------------------------------
    // Row12 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row12<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, UUID, String, LocalDateTime, JSONB, JSONB, JSONB> fieldsRow() {
        return (Row12) super.fieldsRow();
    }
}
