/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.SalesorderFile;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SalesorderFileRecord extends TableRecordImpl<SalesorderFileRecord> implements Record5<LocalDateTime, UUID, UUID, UUID, String> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.salesorder_file.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.salesorder_file.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(0);
    }

    /**
     * Setter for <code>public.salesorder_file.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.salesorder_file.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(1);
    }

    /**
     * Setter for <code>public.salesorder_file.sales_order_id</code>.
     */
    public void setSalesOrderId(UUID value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.salesorder_file.sales_order_id</code>.
     */
    public UUID getSalesOrderId() {
        return (UUID) get(2);
    }

    /**
     * Setter for <code>public.salesorder_file.file_id</code>.
     */
    public void setFileId(UUID value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.salesorder_file.file_id</code>.
     */
    public UUID getFileId() {
        return (UUID) get(3);
    }

    /**
     * Setter for <code>public.salesorder_file.type</code>.
     */
    public void setType(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.salesorder_file.type</code>.
     */
    public String getType() {
        return (String) get(4);
    }

    // -------------------------------------------------------------------------
    // Record5 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row5<LocalDateTime, UUID, UUID, UUID, String> fieldsRow() {
        return (Row5) super.fieldsRow();
    }

    @Override
    public Row5<LocalDateTime, UUID, UUID, UUID, String> valuesRow() {
        return (Row5) super.valuesRow();
    }

    @Override
    public Field<LocalDateTime> field1() {
        return SalesorderFile.SALESORDER_FILE.CREATE_TIME;
    }

    @Override
    public Field<UUID> field2() {
        return SalesorderFile.SALESORDER_FILE.OWNER_ID;
    }

    @Override
    public Field<UUID> field3() {
        return SalesorderFile.SALESORDER_FILE.SALES_ORDER_ID;
    }

    @Override
    public Field<UUID> field4() {
        return SalesorderFile.SALESORDER_FILE.FILE_ID;
    }

    @Override
    public Field<String> field5() {
        return SalesorderFile.SALESORDER_FILE.TYPE;
    }

    @Override
    public LocalDateTime component1() {
        return getCreateTime();
    }

    @Override
    public UUID component2() {
        return getOwnerId();
    }

    @Override
    public UUID component3() {
        return getSalesOrderId();
    }

    @Override
    public UUID component4() {
        return getFileId();
    }

    @Override
    public String component5() {
        return getType();
    }

    @Override
    public LocalDateTime value1() {
        return getCreateTime();
    }

    @Override
    public UUID value2() {
        return getOwnerId();
    }

    @Override
    public UUID value3() {
        return getSalesOrderId();
    }

    @Override
    public UUID value4() {
        return getFileId();
    }

    @Override
    public String value5() {
        return getType();
    }

    @Override
    public SalesorderFileRecord value1(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public SalesorderFileRecord value2(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public SalesorderFileRecord value3(UUID value) {
        setSalesOrderId(value);
        return this;
    }

    @Override
    public SalesorderFileRecord value4(UUID value) {
        setFileId(value);
        return this;
    }

    @Override
    public SalesorderFileRecord value5(String value) {
        setType(value);
        return this;
    }

    @Override
    public SalesorderFileRecord values(LocalDateTime value1, UUID value2, UUID value3, UUID value4, String value5) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SalesorderFileRecord
     */
    public SalesorderFileRecord() {
        super(SalesorderFile.SALESORDER_FILE);
    }

    /**
     * Create a detached, initialised SalesorderFileRecord
     */
    public SalesorderFileRecord(LocalDateTime createTime, UUID ownerId, UUID salesOrderId, UUID fileId, String type) {
        super(SalesorderFile.SALESORDER_FILE);

        setCreateTime(createTime);
        setOwnerId(ownerId);
        setSalesOrderId(salesOrderId);
        setFileId(fileId);
        setType(type);
    }
}
