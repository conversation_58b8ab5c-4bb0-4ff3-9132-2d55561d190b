/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes;


import fabriqon.jooq.classes.tables.Account;
import fabriqon.jooq.classes.tables.AccountCustomer;
import fabriqon.jooq.classes.tables.AccountSupplier;
import fabriqon.jooq.classes.tables.Category;
import fabriqon.jooq.classes.tables.Company;
import fabriqon.jooq.classes.tables.CompanyFile;
import fabriqon.jooq.classes.tables.CustomerNote;
import fabriqon.jooq.classes.tables.Databasechangelog;
import fabriqon.jooq.classes.tables.Databasechangeloglock;
import fabriqon.jooq.classes.tables.DevicePairingToken;
import fabriqon.jooq.classes.tables.EmployeeManufacturingoperationtemplate;
import fabriqon.jooq.classes.tables.EmployeeTimeoff;
import fabriqon.jooq.classes.tables.ExecutedServices;
import fabriqon.jooq.classes.tables.File;
import fabriqon.jooq.classes.tables.FirebaseRegistrationToken;
import fabriqon.jooq.classes.tables.FutureSalesView;
import fabriqon.jooq.classes.tables.GoodsAccompanyingNote;
import fabriqon.jooq.classes.tables.Inventory;
import fabriqon.jooq.classes.tables.InventoryAdjustmentOrder;
import fabriqon.jooq.classes.tables.InventoryCurrentStock;
import fabriqon.jooq.classes.tables.InventoryEntriesView;
import fabriqon.jooq.classes.tables.InventoryItemsView;
import fabriqon.jooq.classes.tables.InventoryUnit;
import fabriqon.jooq.classes.tables.Invoice;
import fabriqon.jooq.classes.tables.JobStatus;
import fabriqon.jooq.classes.tables.Location;
import fabriqon.jooq.classes.tables.ManufacturingOperationTemplate;
import fabriqon.jooq.classes.tables.ManufacturingOrder;
import fabriqon.jooq.classes.tables.ManufacturingOrderNote;
import fabriqon.jooq.classes.tables.ManufacturingOrdersProcessingTimeView;
import fabriqon.jooq.classes.tables.ManufacturingTask;
import fabriqon.jooq.classes.tables.ManufacturingTasksView;
import fabriqon.jooq.classes.tables.ManufacturingWorkstation;
import fabriqon.jooq.classes.tables.ManufacturingorderFile;
import fabriqon.jooq.classes.tables.ManufacturingtaskEmployee;
import fabriqon.jooq.classes.tables.ManufacturingtaskWorkstation;
import fabriqon.jooq.classes.tables.ManufacturingworkstationManufacturingoperationtemplate;
import fabriqon.jooq.classes.tables.MaterialGood;
import fabriqon.jooq.classes.tables.MaterialIssueNote;
import fabriqon.jooq.classes.tables.Note;
import fabriqon.jooq.classes.tables.Notification;
import fabriqon.jooq.classes.tables.NotificationReadBy;
import fabriqon.jooq.classes.tables.ProductFile;
import fabriqon.jooq.classes.tables.PurchaseOrder;
import fabriqon.jooq.classes.tables.PurchaseOrderNote;
import fabriqon.jooq.classes.tables.PurchaseWishlist;
import fabriqon.jooq.classes.tables.PurchasesView;
import fabriqon.jooq.classes.tables.ReceptionReceipt;
import fabriqon.jooq.classes.tables.ReceptionreceiptFile;
import fabriqon.jooq.classes.tables.ReservedInventory;
import fabriqon.jooq.classes.tables.ResourceUtilizationRateView;
import fabriqon.jooq.classes.tables.RoCompanies;
import fabriqon.jooq.classes.tables.SalesOrder;
import fabriqon.jooq.classes.tables.SalesOrderNote;
import fabriqon.jooq.classes.tables.SalesOrdersProcessingTimeView;
import fabriqon.jooq.classes.tables.SalesOrdersView;
import fabriqon.jooq.classes.tables.SalesorderFile;
import fabriqon.jooq.classes.tables.SalesorderGoodsaccompanyingnote;
import fabriqon.jooq.classes.tables.Sequence;
import fabriqon.jooq.classes.tables.ServiceTemplate;
import fabriqon.jooq.classes.tables.ServicingOrder;
import fabriqon.jooq.classes.tables.ServicingOrderNote;
import fabriqon.jooq.classes.tables.ServicingorderFile;
import fabriqon.jooq.classes.tables.ServicingorderGoodsaccompanyingnote;
import fabriqon.jooq.classes.tables.SystemEvent;
import fabriqon.jooq.classes.tables.Users;

import java.util.Arrays;
import java.util.List;

import org.jooq.Catalog;
import org.jooq.Table;
import org.jooq.impl.SchemaImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Public extends SchemaImpl {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public</code>
     */
    public static final Public PUBLIC = new Public();

    /**
     * The table <code>public.account</code>.
     */
    public final Account ACCOUNT = Account.ACCOUNT;

    /**
     * The table <code>public.account_customer</code>.
     */
    public final AccountCustomer ACCOUNT_CUSTOMER = AccountCustomer.ACCOUNT_CUSTOMER;

    /**
     * The table <code>public.account_supplier</code>.
     */
    public final AccountSupplier ACCOUNT_SUPPLIER = AccountSupplier.ACCOUNT_SUPPLIER;

    /**
     * The table <code>public.category</code>.
     */
    public final Category CATEGORY = Category.CATEGORY;

    /**
     * The table <code>public.company</code>.
     */
    public final Company COMPANY = Company.COMPANY;

    /**
     * The table <code>public.company_file</code>.
     */
    public final CompanyFile COMPANY_FILE = CompanyFile.COMPANY_FILE;

    /**
     * The table <code>public.customer_note</code>.
     */
    public final CustomerNote CUSTOMER_NOTE = CustomerNote.CUSTOMER_NOTE;

    /**
     * The table <code>public.databasechangelog</code>.
     */
    public final Databasechangelog DATABASECHANGELOG = Databasechangelog.DATABASECHANGELOG;

    /**
     * The table <code>public.databasechangeloglock</code>.
     */
    public final Databasechangeloglock DATABASECHANGELOGLOCK = Databasechangeloglock.DATABASECHANGELOGLOCK;

    /**
     * The table <code>public.device_pairing_token</code>.
     */
    public final DevicePairingToken DEVICE_PAIRING_TOKEN = DevicePairingToken.DEVICE_PAIRING_TOKEN;

    /**
     * The table <code>public.employee_manufacturingoperationtemplate</code>.
     */
    public final EmployeeManufacturingoperationtemplate EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE = EmployeeManufacturingoperationtemplate.EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE;

    /**
     * The table <code>public.employee_timeoff</code>.
     */
    public final EmployeeTimeoff EMPLOYEE_TIMEOFF = EmployeeTimeoff.EMPLOYEE_TIMEOFF;

    /**
     * The table <code>public.executed_services</code>.
     */
    public final ExecutedServices EXECUTED_SERVICES = ExecutedServices.EXECUTED_SERVICES;

    /**
     * The table <code>public.file</code>.
     */
    public final File FILE = File.FILE;

    /**
     * The table <code>public.firebase_registration_token</code>.
     */
    public final FirebaseRegistrationToken FIREBASE_REGISTRATION_TOKEN = FirebaseRegistrationToken.FIREBASE_REGISTRATION_TOKEN;

    /**
     * The table <code>public.future_sales_view</code>.
     */
    public final FutureSalesView FUTURE_SALES_VIEW = FutureSalesView.FUTURE_SALES_VIEW;

    /**
     * The table <code>public.goods_accompanying_note</code>.
     */
    public final GoodsAccompanyingNote GOODS_ACCOMPANYING_NOTE = GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE;

    /**
     * The table <code>public.inventory</code>.
     */
    public final Inventory INVENTORY = Inventory.INVENTORY;

    /**
     * The table <code>public.inventory_adjustment_order</code>.
     */
    public final InventoryAdjustmentOrder INVENTORY_ADJUSTMENT_ORDER = InventoryAdjustmentOrder.INVENTORY_ADJUSTMENT_ORDER;

    /**
     * The table <code>public.inventory_current_stock</code>.
     */
    public final InventoryCurrentStock INVENTORY_CURRENT_STOCK = InventoryCurrentStock.INVENTORY_CURRENT_STOCK;

    /**
     * The table <code>public.inventory_entries_view</code>.
     */
    public final InventoryEntriesView INVENTORY_ENTRIES_VIEW = InventoryEntriesView.INVENTORY_ENTRIES_VIEW;

    /**
     * The table <code>public.inventory_items_view</code>.
     */
    public final InventoryItemsView INVENTORY_ITEMS_VIEW = InventoryItemsView.INVENTORY_ITEMS_VIEW;

    /**
     * The table <code>public.inventory_unit</code>.
     */
    public final InventoryUnit INVENTORY_UNIT = InventoryUnit.INVENTORY_UNIT;

    /**
     * The table <code>public.invoice</code>.
     */
    public final Invoice INVOICE = Invoice.INVOICE;

    /**
     * The table <code>public.job_status</code>.
     */
    public final JobStatus JOB_STATUS = JobStatus.JOB_STATUS;

    /**
     * The table <code>public.location</code>.
     */
    public final Location LOCATION = Location.LOCATION;

    /**
     * The table <code>public.manufacturing_operation_template</code>.
     */
    public final ManufacturingOperationTemplate MANUFACTURING_OPERATION_TEMPLATE = ManufacturingOperationTemplate.MANUFACTURING_OPERATION_TEMPLATE;

    /**
     * The table <code>public.manufacturing_order</code>.
     */
    public final ManufacturingOrder MANUFACTURING_ORDER = ManufacturingOrder.MANUFACTURING_ORDER;

    /**
     * The table <code>public.manufacturing_order_note</code>.
     */
    public final ManufacturingOrderNote MANUFACTURING_ORDER_NOTE = ManufacturingOrderNote.MANUFACTURING_ORDER_NOTE;

    /**
     * The table <code>public.manufacturing_orders_processing_time_view</code>.
     */
    public final ManufacturingOrdersProcessingTimeView MANUFACTURING_ORDERS_PROCESSING_TIME_VIEW = ManufacturingOrdersProcessingTimeView.MANUFACTURING_ORDERS_PROCESSING_TIME_VIEW;

    /**
     * The table <code>public.manufacturing_task</code>.
     */
    public final ManufacturingTask MANUFACTURING_TASK = ManufacturingTask.MANUFACTURING_TASK;

    /**
     * The table <code>public.manufacturing_tasks_view</code>.
     */
    public final ManufacturingTasksView MANUFACTURING_TASKS_VIEW = ManufacturingTasksView.MANUFACTURING_TASKS_VIEW;

    /**
     * The table <code>public.manufacturing_workstation</code>.
     */
    public final ManufacturingWorkstation MANUFACTURING_WORKSTATION = ManufacturingWorkstation.MANUFACTURING_WORKSTATION;

    /**
     * The table <code>public.manufacturingorder_file</code>.
     */
    public final ManufacturingorderFile MANUFACTURINGORDER_FILE = ManufacturingorderFile.MANUFACTURINGORDER_FILE;

    /**
     * The table <code>public.manufacturingtask_employee</code>.
     */
    public final ManufacturingtaskEmployee MANUFACTURINGTASK_EMPLOYEE = ManufacturingtaskEmployee.MANUFACTURINGTASK_EMPLOYEE;

    /**
     * The table <code>public.manufacturingtask_workstation</code>.
     */
    public final ManufacturingtaskWorkstation MANUFACTURINGTASK_WORKSTATION = ManufacturingtaskWorkstation.MANUFACTURINGTASK_WORKSTATION;

    /**
     * The table
     * <code>public.manufacturingworkstation_manufacturingoperationtemplate</code>.
     */
    public final ManufacturingworkstationManufacturingoperationtemplate MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE = ManufacturingworkstationManufacturingoperationtemplate.MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE;

    /**
     * The table <code>public.material_good</code>.
     */
    public final MaterialGood MATERIAL_GOOD = MaterialGood.MATERIAL_GOOD;

    /**
     * The table <code>public.material_issue_note</code>.
     */
    public final MaterialIssueNote MATERIAL_ISSUE_NOTE = MaterialIssueNote.MATERIAL_ISSUE_NOTE;

    /**
     * The table <code>public.note</code>.
     */
    public final Note NOTE = Note.NOTE;

    /**
     * The table <code>public.notification</code>.
     */
    public final Notification NOTIFICATION = Notification.NOTIFICATION;

    /**
     * The table <code>public.notification_read_by</code>.
     */
    public final NotificationReadBy NOTIFICATION_READ_BY = NotificationReadBy.NOTIFICATION_READ_BY;

    /**
     * The table <code>public.product_file</code>.
     */
    public final ProductFile PRODUCT_FILE = ProductFile.PRODUCT_FILE;

    /**
     * The table <code>public.purchase_order</code>.
     */
    public final PurchaseOrder PURCHASE_ORDER = PurchaseOrder.PURCHASE_ORDER;

    /**
     * The table <code>public.purchase_order_note</code>.
     */
    public final PurchaseOrderNote PURCHASE_ORDER_NOTE = PurchaseOrderNote.PURCHASE_ORDER_NOTE;

    /**
     * The table <code>public.purchase_wishlist</code>.
     */
    public final PurchaseWishlist PURCHASE_WISHLIST = PurchaseWishlist.PURCHASE_WISHLIST;

    /**
     * The table <code>public.purchases_view</code>.
     */
    public final PurchasesView PURCHASES_VIEW = PurchasesView.PURCHASES_VIEW;

    /**
     * The table <code>public.reception_receipt</code>.
     */
    public final ReceptionReceipt RECEPTION_RECEIPT = ReceptionReceipt.RECEPTION_RECEIPT;

    /**
     * The table <code>public.receptionreceipt_file</code>.
     */
    public final ReceptionreceiptFile RECEPTIONRECEIPT_FILE = ReceptionreceiptFile.RECEPTIONRECEIPT_FILE;

    /**
     * The table <code>public.reserved_inventory</code>.
     */
    public final ReservedInventory RESERVED_INVENTORY = ReservedInventory.RESERVED_INVENTORY;

    /**
     * The table <code>public.resource_utilization_rate_view</code>.
     */
    public final ResourceUtilizationRateView RESOURCE_UTILIZATION_RATE_VIEW = ResourceUtilizationRateView.RESOURCE_UTILIZATION_RATE_VIEW;

    /**
     * The table <code>public.ro_companies</code>.
     */
    public final RoCompanies RO_COMPANIES = RoCompanies.RO_COMPANIES;

    /**
     * The table <code>public.sales_order</code>.
     */
    public final SalesOrder SALES_ORDER = SalesOrder.SALES_ORDER;

    /**
     * The table <code>public.sales_order_note</code>.
     */
    public final SalesOrderNote SALES_ORDER_NOTE = SalesOrderNote.SALES_ORDER_NOTE;

    /**
     * The table <code>public.sales_orders_processing_time_view</code>.
     */
    public final SalesOrdersProcessingTimeView SALES_ORDERS_PROCESSING_TIME_VIEW = SalesOrdersProcessingTimeView.SALES_ORDERS_PROCESSING_TIME_VIEW;

    /**
     * The table <code>public.sales_orders_view</code>.
     */
    public final SalesOrdersView SALES_ORDERS_VIEW = SalesOrdersView.SALES_ORDERS_VIEW;

    /**
     * The table <code>public.salesorder_file</code>.
     */
    public final SalesorderFile SALESORDER_FILE = SalesorderFile.SALESORDER_FILE;

    /**
     * The table <code>public.salesorder_goodsaccompanyingnote</code>.
     */
    public final SalesorderGoodsaccompanyingnote SALESORDER_GOODSACCOMPANYINGNOTE = SalesorderGoodsaccompanyingnote.SALESORDER_GOODSACCOMPANYINGNOTE;

    /**
     * The table <code>public.sequence</code>.
     */
    public final Sequence SEQUENCE = Sequence.SEQUENCE;

    /**
     * The table <code>public.service_template</code>.
     */
    public final ServiceTemplate SERVICE_TEMPLATE = ServiceTemplate.SERVICE_TEMPLATE;

    /**
     * The table <code>public.servicing_order</code>.
     */
    public final ServicingOrder SERVICING_ORDER = ServicingOrder.SERVICING_ORDER;

    /**
     * The table <code>public.servicing_order_note</code>.
     */
    public final ServicingOrderNote SERVICING_ORDER_NOTE = ServicingOrderNote.SERVICING_ORDER_NOTE;

    /**
     * The table <code>public.servicingorder_file</code>.
     */
    public final ServicingorderFile SERVICINGORDER_FILE = ServicingorderFile.SERVICINGORDER_FILE;

    /**
     * The table <code>public.servicingorder_goodsaccompanyingnote</code>.
     */
    public final ServicingorderGoodsaccompanyingnote SERVICINGORDER_GOODSACCOMPANYINGNOTE = ServicingorderGoodsaccompanyingnote.SERVICINGORDER_GOODSACCOMPANYINGNOTE;

    /**
     * The table <code>public.system_event</code>.
     */
    public final SystemEvent SYSTEM_EVENT = SystemEvent.SYSTEM_EVENT;

    /**
     * The table <code>public.users</code>.
     */
    public final Users USERS = Users.USERS;

    /**
     * No further instances allowed
     */
    private Public() {
        super("public", null);
    }


    @Override
    public Catalog getCatalog() {
        return DefaultCatalog.DEFAULT_CATALOG;
    }

    @Override
    public final List<Table<?>> getTables() {
        return Arrays.asList(
            Account.ACCOUNT,
            AccountCustomer.ACCOUNT_CUSTOMER,
            AccountSupplier.ACCOUNT_SUPPLIER,
            Category.CATEGORY,
            Company.COMPANY,
            CompanyFile.COMPANY_FILE,
            CustomerNote.CUSTOMER_NOTE,
            Databasechangelog.DATABASECHANGELOG,
            Databasechangeloglock.DATABASECHANGELOGLOCK,
            DevicePairingToken.DEVICE_PAIRING_TOKEN,
            EmployeeManufacturingoperationtemplate.EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE,
            EmployeeTimeoff.EMPLOYEE_TIMEOFF,
            ExecutedServices.EXECUTED_SERVICES,
            File.FILE,
            FirebaseRegistrationToken.FIREBASE_REGISTRATION_TOKEN,
            FutureSalesView.FUTURE_SALES_VIEW,
            GoodsAccompanyingNote.GOODS_ACCOMPANYING_NOTE,
            Inventory.INVENTORY,
            InventoryAdjustmentOrder.INVENTORY_ADJUSTMENT_ORDER,
            InventoryCurrentStock.INVENTORY_CURRENT_STOCK,
            InventoryEntriesView.INVENTORY_ENTRIES_VIEW,
            InventoryItemsView.INVENTORY_ITEMS_VIEW,
            InventoryUnit.INVENTORY_UNIT,
            Invoice.INVOICE,
            JobStatus.JOB_STATUS,
            Location.LOCATION,
            ManufacturingOperationTemplate.MANUFACTURING_OPERATION_TEMPLATE,
            ManufacturingOrder.MANUFACTURING_ORDER,
            ManufacturingOrderNote.MANUFACTURING_ORDER_NOTE,
            ManufacturingOrdersProcessingTimeView.MANUFACTURING_ORDERS_PROCESSING_TIME_VIEW,
            ManufacturingTask.MANUFACTURING_TASK,
            ManufacturingTasksView.MANUFACTURING_TASKS_VIEW,
            ManufacturingWorkstation.MANUFACTURING_WORKSTATION,
            ManufacturingorderFile.MANUFACTURINGORDER_FILE,
            ManufacturingtaskEmployee.MANUFACTURINGTASK_EMPLOYEE,
            ManufacturingtaskWorkstation.MANUFACTURINGTASK_WORKSTATION,
            ManufacturingworkstationManufacturingoperationtemplate.MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE,
            MaterialGood.MATERIAL_GOOD,
            MaterialIssueNote.MATERIAL_ISSUE_NOTE,
            Note.NOTE,
            Notification.NOTIFICATION,
            NotificationReadBy.NOTIFICATION_READ_BY,
            ProductFile.PRODUCT_FILE,
            PurchaseOrder.PURCHASE_ORDER,
            PurchaseOrderNote.PURCHASE_ORDER_NOTE,
            PurchaseWishlist.PURCHASE_WISHLIST,
            PurchasesView.PURCHASES_VIEW,
            ReceptionReceipt.RECEPTION_RECEIPT,
            ReceptionreceiptFile.RECEPTIONRECEIPT_FILE,
            ReservedInventory.RESERVED_INVENTORY,
            ResourceUtilizationRateView.RESOURCE_UTILIZATION_RATE_VIEW,
            RoCompanies.RO_COMPANIES,
            SalesOrder.SALES_ORDER,
            SalesOrderNote.SALES_ORDER_NOTE,
            SalesOrdersProcessingTimeView.SALES_ORDERS_PROCESSING_TIME_VIEW,
            SalesOrdersView.SALES_ORDERS_VIEW,
            SalesorderFile.SALESORDER_FILE,
            SalesorderGoodsaccompanyingnote.SALESORDER_GOODSACCOMPANYINGNOTE,
            Sequence.SEQUENCE,
            ServiceTemplate.SERVICE_TEMPLATE,
            ServicingOrder.SERVICING_ORDER,
            ServicingOrderNote.SERVICING_ORDER_NOTE,
            ServicingorderFile.SERVICINGORDER_FILE,
            ServicingorderGoodsaccompanyingnote.SERVICINGORDER_GOODSACCOMPANYINGNOTE,
            SystemEvent.SYSTEM_EVENT,
            Users.USERS
        );
    }
}
