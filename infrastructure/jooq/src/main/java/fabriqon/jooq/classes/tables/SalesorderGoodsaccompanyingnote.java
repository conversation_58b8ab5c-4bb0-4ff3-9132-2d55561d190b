/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.SalesorderGoodsaccompanyingnoteRecord;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row2;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SalesorderGoodsaccompanyingnote extends TableImpl<SalesorderGoodsaccompanyingnoteRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>public.salesorder_goodsaccompanyingnote</code>
     */
    public static final SalesorderGoodsaccompanyingnote SALESORDER_GOODSACCOMPANYINGNOTE = new SalesorderGoodsaccompanyingnote();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SalesorderGoodsaccompanyingnoteRecord> getRecordType() {
        return SalesorderGoodsaccompanyingnoteRecord.class;
    }

    /**
     * The column
     * <code>public.salesorder_goodsaccompanyingnote.sales_order_id</code>.
     */
    public final TableField<SalesorderGoodsaccompanyingnoteRecord, UUID> SALES_ORDER_ID = createField(DSL.name("sales_order_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column
     * <code>public.salesorder_goodsaccompanyingnote.goods_accompanying_note_id</code>.
     */
    public final TableField<SalesorderGoodsaccompanyingnoteRecord, UUID> GOODS_ACCOMPANYING_NOTE_ID = createField(DSL.name("goods_accompanying_note_id"), SQLDataType.UUID.nullable(false), this, "");

    private SalesorderGoodsaccompanyingnote(Name alias, Table<SalesorderGoodsaccompanyingnoteRecord> aliased) {
        this(alias, aliased, null);
    }

    private SalesorderGoodsaccompanyingnote(Name alias, Table<SalesorderGoodsaccompanyingnoteRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.salesorder_goodsaccompanyingnote</code>
     * table reference
     */
    public SalesorderGoodsaccompanyingnote(String alias) {
        this(DSL.name(alias), SALESORDER_GOODSACCOMPANYINGNOTE);
    }

    /**
     * Create an aliased <code>public.salesorder_goodsaccompanyingnote</code>
     * table reference
     */
    public SalesorderGoodsaccompanyingnote(Name alias) {
        this(alias, SALESORDER_GOODSACCOMPANYINGNOTE);
    }

    /**
     * Create a <code>public.salesorder_goodsaccompanyingnote</code> table
     * reference
     */
    public SalesorderGoodsaccompanyingnote() {
        this(DSL.name("salesorder_goodsaccompanyingnote"), null);
    }

    public <O extends Record> SalesorderGoodsaccompanyingnote(Table<O> child, ForeignKey<O, SalesorderGoodsaccompanyingnoteRecord> key) {
        super(child, key, SALESORDER_GOODSACCOMPANYINGNOTE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<UniqueKey<SalesorderGoodsaccompanyingnoteRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.UNIQUE_CONSTRAINTS);
    }

    @Override
    public List<ForeignKey<SalesorderGoodsaccompanyingnoteRecord, ?>> getReferences() {
        return Arrays.asList(Keys.SALESORDER_GOODSACCOMPANYINGNOTE__SALESORDER_GOODSACCOMPANYINGNOTE_SALES_ORDER_ID_FKEY, Keys.SALESORDER_GOODSACCOMPANYINGNOTE__SALESORDER_GOODSACCOMPANYINGNOT_GOODS_ACCOMPANYING_NOTE_ID_FKEY);
    }

    private transient SalesOrder _salesOrder;
    private transient GoodsAccompanyingNote _goodsAccompanyingNote;

    /**
     * Get the implicit join path to the <code>public.sales_order</code> table.
     */
    public SalesOrder salesOrder() {
        if (_salesOrder == null)
            _salesOrder = new SalesOrder(this, Keys.SALESORDER_GOODSACCOMPANYINGNOTE__SALESORDER_GOODSACCOMPANYINGNOTE_SALES_ORDER_ID_FKEY);

        return _salesOrder;
    }

    /**
     * Get the implicit join path to the
     * <code>public.goods_accompanying_note</code> table.
     */
    public GoodsAccompanyingNote goodsAccompanyingNote() {
        if (_goodsAccompanyingNote == null)
            _goodsAccompanyingNote = new GoodsAccompanyingNote(this, Keys.SALESORDER_GOODSACCOMPANYINGNOTE__SALESORDER_GOODSACCOMPANYINGNOT_GOODS_ACCOMPANYING_NOTE_ID_FKEY);

        return _goodsAccompanyingNote;
    }

    @Override
    public SalesorderGoodsaccompanyingnote as(String alias) {
        return new SalesorderGoodsaccompanyingnote(DSL.name(alias), this);
    }

    @Override
    public SalesorderGoodsaccompanyingnote as(Name alias) {
        return new SalesorderGoodsaccompanyingnote(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SalesorderGoodsaccompanyingnote rename(String name) {
        return new SalesorderGoodsaccompanyingnote(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public SalesorderGoodsaccompanyingnote rename(Name name) {
        return new SalesorderGoodsaccompanyingnote(name, null);
    }

    // -------------------------------------------------------------------------
    // Row2 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row2<UUID, UUID> fieldsRow() {
        return (Row2) super.fieldsRow();
    }
}
