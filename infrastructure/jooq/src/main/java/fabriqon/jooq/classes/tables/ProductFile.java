/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Keys;
import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.ProductFileRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row4;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ProductFile extends TableImpl<ProductFileRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.product_file</code>
     */
    public static final ProductFile PRODUCT_FILE = new ProductFile();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ProductFileRecord> getRecordType() {
        return ProductFileRecord.class;
    }

    /**
     * The column <code>public.product_file.create_time</code>.
     */
    public final TableField<ProductFileRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>public.product_file.owner_id</code>.
     */
    public final TableField<ProductFileRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.product_file.product_id</code>.
     */
    public final TableField<ProductFileRecord, UUID> PRODUCT_ID = createField(DSL.name("product_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>public.product_file.file_id</code>.
     */
    public final TableField<ProductFileRecord, UUID> FILE_ID = createField(DSL.name("file_id"), SQLDataType.UUID.nullable(false), this, "");

    private ProductFile(Name alias, Table<ProductFileRecord> aliased) {
        this(alias, aliased, null);
    }

    private ProductFile(Name alias, Table<ProductFileRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>public.product_file</code> table reference
     */
    public ProductFile(String alias) {
        this(DSL.name(alias), PRODUCT_FILE);
    }

    /**
     * Create an aliased <code>public.product_file</code> table reference
     */
    public ProductFile(Name alias) {
        this(alias, PRODUCT_FILE);
    }

    /**
     * Create a <code>public.product_file</code> table reference
     */
    public ProductFile() {
        this(DSL.name("product_file"), null);
    }

    public <O extends Record> ProductFile(Table<O> child, ForeignKey<O, ProductFileRecord> key) {
        super(child, key, PRODUCT_FILE);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<ForeignKey<ProductFileRecord, ?>> getReferences() {
        return Arrays.asList(Keys.PRODUCT_FILE__PRODUCT_FILE_OWNER_ID_FKEY, Keys.PRODUCT_FILE__PRODUCT_FILE_PRODUCT_ID_FKEY, Keys.PRODUCT_FILE__PRODUCT_FILE_FILE_ID_FKEY);
    }

    private transient Account _account;
    private transient MaterialGood _materialGood;
    private transient File _file;

    /**
     * Get the implicit join path to the <code>public.account</code> table.
     */
    public Account account() {
        if (_account == null)
            _account = new Account(this, Keys.PRODUCT_FILE__PRODUCT_FILE_OWNER_ID_FKEY);

        return _account;
    }

    /**
     * Get the implicit join path to the <code>public.material_good</code>
     * table.
     */
    public MaterialGood materialGood() {
        if (_materialGood == null)
            _materialGood = new MaterialGood(this, Keys.PRODUCT_FILE__PRODUCT_FILE_PRODUCT_ID_FKEY);

        return _materialGood;
    }

    /**
     * Get the implicit join path to the <code>public.file</code> table.
     */
    public File file() {
        if (_file == null)
            _file = new File(this, Keys.PRODUCT_FILE__PRODUCT_FILE_FILE_ID_FKEY);

        return _file;
    }

    @Override
    public ProductFile as(String alias) {
        return new ProductFile(DSL.name(alias), this);
    }

    @Override
    public ProductFile as(Name alias) {
        return new ProductFile(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ProductFile rename(String name) {
        return new ProductFile(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ProductFile rename(Name name) {
        return new ProductFile(name, null);
    }

    // -------------------------------------------------------------------------
    // Row4 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row4<LocalDateTime, UUID, UUID, UUID> fieldsRow() {
        return (Row4) super.fieldsRow();
    }
}
