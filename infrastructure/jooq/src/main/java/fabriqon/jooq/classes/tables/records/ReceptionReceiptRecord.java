/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.ReceptionReceipt;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.Record12;
import org.jooq.Row12;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ReceptionReceiptRecord extends UpdatableRecordImpl<ReceptionReceiptRecord> implements Record12<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, UUID, String, LocalDateTime, JSONB, JSONB, JSONB> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.reception_receipt.id</code>.
     */
    public void setId(UUID value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.reception_receipt.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>public.reception_receipt.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.reception_receipt.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(1);
    }

    /**
     * Setter for <code>public.reception_receipt.update_time</code>.
     */
    public void setUpdateTime(LocalDateTime value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.reception_receipt.update_time</code>.
     */
    public LocalDateTime getUpdateTime() {
        return (LocalDateTime) get(2);
    }

    /**
     * Setter for <code>public.reception_receipt.deleted</code>.
     */
    public void setDeleted(Boolean value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.reception_receipt.deleted</code>.
     */
    public Boolean getDeleted() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>public.reception_receipt.owner_id</code>.
     */
    public void setOwnerId(UUID value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.reception_receipt.owner_id</code>.
     */
    public UUID getOwnerId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>public.reception_receipt.supplier_id</code>.
     */
    public void setSupplierId(UUID value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.reception_receipt.supplier_id</code>.
     */
    public UUID getSupplierId() {
        return (UUID) get(5);
    }

    /**
     * Setter for <code>public.reception_receipt.purchase_order_id</code>.
     */
    public void setPurchaseOrderId(UUID value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.reception_receipt.purchase_order_id</code>.
     */
    public UUID getPurchaseOrderId() {
        return (UUID) get(6);
    }

    /**
     * Setter for <code>public.reception_receipt.number</code>.
     */
    public void setNumber(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.reception_receipt.number</code>.
     */
    public String getNumber() {
        return (String) get(7);
    }

    /**
     * Setter for <code>public.reception_receipt.reception_date</code>.
     */
    public void setReceptionDate(LocalDateTime value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.reception_receipt.reception_date</code>.
     */
    public LocalDateTime getReceptionDate() {
        return (LocalDateTime) get(8);
    }

    /**
     * Setter for <code>public.reception_receipt.supporting_document</code>.
     */
    public void setSupportingDocument(JSONB value) {
        set(9, value);
    }

    /**
     * Getter for <code>public.reception_receipt.supporting_document</code>.
     */
    public JSONB getSupportingDocument() {
        return (JSONB) get(9);
    }

    /**
     * Setter for <code>public.reception_receipt.details</code>.
     */
    public void setDetails(JSONB value) {
        set(10, value);
    }

    /**
     * Getter for <code>public.reception_receipt.details</code>.
     */
    public JSONB getDetails() {
        return (JSONB) get(10);
    }

    /**
     * Setter for <code>public.reception_receipt.goods</code>.
     */
    public void setGoods(JSONB value) {
        set(11, value);
    }

    /**
     * Getter for <code>public.reception_receipt.goods</code>.
     */
    public JSONB getGoods() {
        return (JSONB) get(11);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record12 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row12<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, UUID, String, LocalDateTime, JSONB, JSONB, JSONB> fieldsRow() {
        return (Row12) super.fieldsRow();
    }

    @Override
    public Row12<UUID, LocalDateTime, LocalDateTime, Boolean, UUID, UUID, UUID, String, LocalDateTime, JSONB, JSONB, JSONB> valuesRow() {
        return (Row12) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return ReceptionReceipt.RECEPTION_RECEIPT.ID;
    }

    @Override
    public Field<LocalDateTime> field2() {
        return ReceptionReceipt.RECEPTION_RECEIPT.CREATE_TIME;
    }

    @Override
    public Field<LocalDateTime> field3() {
        return ReceptionReceipt.RECEPTION_RECEIPT.UPDATE_TIME;
    }

    @Override
    public Field<Boolean> field4() {
        return ReceptionReceipt.RECEPTION_RECEIPT.DELETED;
    }

    @Override
    public Field<UUID> field5() {
        return ReceptionReceipt.RECEPTION_RECEIPT.OWNER_ID;
    }

    @Override
    public Field<UUID> field6() {
        return ReceptionReceipt.RECEPTION_RECEIPT.SUPPLIER_ID;
    }

    @Override
    public Field<UUID> field7() {
        return ReceptionReceipt.RECEPTION_RECEIPT.PURCHASE_ORDER_ID;
    }

    @Override
    public Field<String> field8() {
        return ReceptionReceipt.RECEPTION_RECEIPT.NUMBER;
    }

    @Override
    public Field<LocalDateTime> field9() {
        return ReceptionReceipt.RECEPTION_RECEIPT.RECEPTION_DATE;
    }

    @Override
    public Field<JSONB> field10() {
        return ReceptionReceipt.RECEPTION_RECEIPT.SUPPORTING_DOCUMENT;
    }

    @Override
    public Field<JSONB> field11() {
        return ReceptionReceipt.RECEPTION_RECEIPT.DETAILS;
    }

    @Override
    public Field<JSONB> field12() {
        return ReceptionReceipt.RECEPTION_RECEIPT.GOODS;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public LocalDateTime component2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime component3() {
        return getUpdateTime();
    }

    @Override
    public Boolean component4() {
        return getDeleted();
    }

    @Override
    public UUID component5() {
        return getOwnerId();
    }

    @Override
    public UUID component6() {
        return getSupplierId();
    }

    @Override
    public UUID component7() {
        return getPurchaseOrderId();
    }

    @Override
    public String component8() {
        return getNumber();
    }

    @Override
    public LocalDateTime component9() {
        return getReceptionDate();
    }

    @Override
    public JSONB component10() {
        return getSupportingDocument();
    }

    @Override
    public JSONB component11() {
        return getDetails();
    }

    @Override
    public JSONB component12() {
        return getGoods();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public LocalDateTime value2() {
        return getCreateTime();
    }

    @Override
    public LocalDateTime value3() {
        return getUpdateTime();
    }

    @Override
    public Boolean value4() {
        return getDeleted();
    }

    @Override
    public UUID value5() {
        return getOwnerId();
    }

    @Override
    public UUID value6() {
        return getSupplierId();
    }

    @Override
    public UUID value7() {
        return getPurchaseOrderId();
    }

    @Override
    public String value8() {
        return getNumber();
    }

    @Override
    public LocalDateTime value9() {
        return getReceptionDate();
    }

    @Override
    public JSONB value10() {
        return getSupportingDocument();
    }

    @Override
    public JSONB value11() {
        return getDetails();
    }

    @Override
    public JSONB value12() {
        return getGoods();
    }

    @Override
    public ReceptionReceiptRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public ReceptionReceiptRecord value2(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public ReceptionReceiptRecord value3(LocalDateTime value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public ReceptionReceiptRecord value4(Boolean value) {
        setDeleted(value);
        return this;
    }

    @Override
    public ReceptionReceiptRecord value5(UUID value) {
        setOwnerId(value);
        return this;
    }

    @Override
    public ReceptionReceiptRecord value6(UUID value) {
        setSupplierId(value);
        return this;
    }

    @Override
    public ReceptionReceiptRecord value7(UUID value) {
        setPurchaseOrderId(value);
        return this;
    }

    @Override
    public ReceptionReceiptRecord value8(String value) {
        setNumber(value);
        return this;
    }

    @Override
    public ReceptionReceiptRecord value9(LocalDateTime value) {
        setReceptionDate(value);
        return this;
    }

    @Override
    public ReceptionReceiptRecord value10(JSONB value) {
        setSupportingDocument(value);
        return this;
    }

    @Override
    public ReceptionReceiptRecord value11(JSONB value) {
        setDetails(value);
        return this;
    }

    @Override
    public ReceptionReceiptRecord value12(JSONB value) {
        setGoods(value);
        return this;
    }

    @Override
    public ReceptionReceiptRecord values(UUID value1, LocalDateTime value2, LocalDateTime value3, Boolean value4, UUID value5, UUID value6, UUID value7, String value8, LocalDateTime value9, JSONB value10, JSONB value11, JSONB value12) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ReceptionReceiptRecord
     */
    public ReceptionReceiptRecord() {
        super(ReceptionReceipt.RECEPTION_RECEIPT);
    }

    /**
     * Create a detached, initialised ReceptionReceiptRecord
     */
    public ReceptionReceiptRecord(UUID id, LocalDateTime createTime, LocalDateTime updateTime, Boolean deleted, UUID ownerId, UUID supplierId, UUID purchaseOrderId, String number, LocalDateTime receptionDate, JSONB supportingDocument, JSONB details, JSONB goods) {
        super(ReceptionReceipt.RECEPTION_RECEIPT);

        setId(id);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        setDeleted(deleted);
        setOwnerId(ownerId);
        setSupplierId(supplierId);
        setPurchaseOrderId(purchaseOrderId);
        setNumber(number);
        setReceptionDate(receptionDate);
        setSupportingDocument(supportingDocument);
        setDetails(details);
        setGoods(goods);
    }
}
