/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables.records;


import fabriqon.jooq.classes.tables.JobStatus;

import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.Record3;
import org.jooq.Row3;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class JobStatusRecord extends TableRecordImpl<JobStatusRecord> implements Record3<LocalDateTime, String, Boolean> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>public.job_status.create_time</code>.
     */
    public void setCreateTime(LocalDateTime value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.job_status.create_time</code>.
     */
    public LocalDateTime getCreateTime() {
        return (LocalDateTime) get(0);
    }

    /**
     * Setter for <code>public.job_status.name</code>.
     */
    public void setName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.job_status.name</code>.
     */
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>public.job_status.status</code>.
     */
    public void setStatus(Boolean value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.job_status.status</code>.
     */
    public Boolean getStatus() {
        return (Boolean) get(2);
    }

    // -------------------------------------------------------------------------
    // Record3 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row3<LocalDateTime, String, Boolean> fieldsRow() {
        return (Row3) super.fieldsRow();
    }

    @Override
    public Row3<LocalDateTime, String, Boolean> valuesRow() {
        return (Row3) super.valuesRow();
    }

    @Override
    public Field<LocalDateTime> field1() {
        return JobStatus.JOB_STATUS.CREATE_TIME;
    }

    @Override
    public Field<String> field2() {
        return JobStatus.JOB_STATUS.NAME;
    }

    @Override
    public Field<Boolean> field3() {
        return JobStatus.JOB_STATUS.STATUS;
    }

    @Override
    public LocalDateTime component1() {
        return getCreateTime();
    }

    @Override
    public String component2() {
        return getName();
    }

    @Override
    public Boolean component3() {
        return getStatus();
    }

    @Override
    public LocalDateTime value1() {
        return getCreateTime();
    }

    @Override
    public String value2() {
        return getName();
    }

    @Override
    public Boolean value3() {
        return getStatus();
    }

    @Override
    public JobStatusRecord value1(LocalDateTime value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public JobStatusRecord value2(String value) {
        setName(value);
        return this;
    }

    @Override
    public JobStatusRecord value3(Boolean value) {
        setStatus(value);
        return this;
    }

    @Override
    public JobStatusRecord values(LocalDateTime value1, String value2, Boolean value3) {
        value1(value1);
        value2(value2);
        value3(value3);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached JobStatusRecord
     */
    public JobStatusRecord() {
        super(JobStatus.JOB_STATUS);
    }

    /**
     * Create a detached, initialised JobStatusRecord
     */
    public JobStatusRecord(LocalDateTime createTime, String name, Boolean status) {
        super(JobStatus.JOB_STATUS);

        setCreateTime(createTime);
        setName(name);
        setStatus(status);
    }
}
