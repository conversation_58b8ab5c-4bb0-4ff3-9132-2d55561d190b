/*
 * This file is generated by jOOQ.
 */
package fabriqon.jooq.classes.tables;


import fabriqon.jooq.classes.Public;
import fabriqon.jooq.classes.tables.records.PurchasesViewRecord;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row6;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PurchasesView extends TableImpl<PurchasesViewRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>public.purchases_view</code>
     */
    public static final PurchasesView PURCHASES_VIEW = new PurchasesView();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<PurchasesViewRecord> getRecordType() {
        return PurchasesViewRecord.class;
    }

    /**
     * The column <code>public.purchases_view.owner_id</code>.
     */
    public final TableField<PurchasesViewRecord, UUID> OWNER_ID = createField(DSL.name("owner_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.purchases_view.create_time</code>.
     */
    public final TableField<PurchasesViewRecord, LocalDateTime> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>public.purchases_view.reception_receipt_id</code>.
     */
    public final TableField<PurchasesViewRecord, UUID> RECEPTION_RECEIPT_ID = createField(DSL.name("reception_receipt_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>public.purchases_view.reception_price</code>.
     */
    public final TableField<PurchasesViewRecord, BigDecimal> RECEPTION_PRICE = createField(DSL.name("reception_price"), SQLDataType.NUMERIC, this, "");

    /**
     * The column <code>public.purchases_view.quantity</code>.
     */
    public final TableField<PurchasesViewRecord, BigDecimal> QUANTITY = createField(DSL.name("quantity"), SQLDataType.NUMERIC(12, 4), this, "");

    /**
     * The column <code>public.purchases_view.supplier</code>.
     */
    public final TableField<PurchasesViewRecord, String> SUPPLIER = createField(DSL.name("supplier"), SQLDataType.VARCHAR(200), this, "");

    private PurchasesView(Name alias, Table<PurchasesViewRecord> aliased) {
        this(alias, aliased, null);
    }

    private PurchasesView(Name alias, Table<PurchasesViewRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.view("create view \"purchases_view\" as  SELECT inventory.owner_id,\n    inventory.create_time,\n    inventory.reception_receipt_id,\n    (((inventory.reception_price_amount)::numeric * inventory.quantity) / (100)::numeric) AS reception_price,\n    inventory.quantity,\n    company.company_name AS supplier\n   FROM (inventory\n     JOIN company ON ((inventory.supplier_id = company.id)))\n  WHERE (inventory.reception_receipt_id IS NOT NULL);"));
    }

    /**
     * Create an aliased <code>public.purchases_view</code> table reference
     */
    public PurchasesView(String alias) {
        this(DSL.name(alias), PURCHASES_VIEW);
    }

    /**
     * Create an aliased <code>public.purchases_view</code> table reference
     */
    public PurchasesView(Name alias) {
        this(alias, PURCHASES_VIEW);
    }

    /**
     * Create a <code>public.purchases_view</code> table reference
     */
    public PurchasesView() {
        this(DSL.name("purchases_view"), null);
    }

    public <O extends Record> PurchasesView(Table<O> child, ForeignKey<O, PurchasesViewRecord> key) {
        super(child, key, PURCHASES_VIEW);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public PurchasesView as(String alias) {
        return new PurchasesView(DSL.name(alias), this);
    }

    @Override
    public PurchasesView as(Name alias) {
        return new PurchasesView(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public PurchasesView rename(String name) {
        return new PurchasesView(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public PurchasesView rename(Name name) {
        return new PurchasesView(name, null);
    }

    // -------------------------------------------------------------------------
    // Row6 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row6<UUID, LocalDateTime, UUID, BigDecimal, BigDecimal, String> fieldsRow() {
        return (Row6) super.fieldsRow();
    }
}
