package fabriqon.jooq;

import org.jooq.Field;
import org.jooq.Record;
import org.jooq.SQL;
import org.jooq.TableField;

import java.util.Arrays;
import java.util.stream.Collectors;

import static org.jooq.impl.DSL.*;

public class JooqTextSearchFunctions {

    public static SQL textSearch(TableField<? extends Record, ?> tableField, String query) {
        return sql("{0} @@ to_tsquery('" + toTextSearchExpression(query) + "')", tableField);
    }

    public static Object toTsVector(String value) {
        return field("to_tsvector('simple', '" + value + "')");
    }

    private static String toTextSearchExpression(String query) {
        return Arrays.stream(query.split(" "))
                .filter(s -> !s.isBlank())
                .map(String::trim)
                .map(s -> s + ":*")
                .collect(Collectors.joining("&"));
    }

}
