package fabriqon.jooq;

import org.jooq.Record;
import org.jooq.*;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.sql;

public class JooqJsonbFunctions {

    //value extractors
    public static Field<String> stringField(TableField<? extends Record, JSONB> tableField, String name) {
        return field("{0} ->> {1}", String.class, tableField, name);
    }

    public static Field<Boolean> booleanField(TableField<? extends Record, JSONB> tableField, String name) {
        return field("({0} ->> {1})::boolean", Boolean.class, tableField, name);
    }

    //conditionals
    public static SQL arrayContainsJson(TableField<? extends Record, JSONB> tableField, String json) {
        return sql("{0} @> {1}::jsonb", tableField, json);
    }

    public static SQL arrayContainsValue(TableField<? extends Record, JSONB> tableField, String value) {
        return sql("{0} ? {1}::jsonb", tableField, value);
    }

}
