<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>infrastructure</artifactId>
        <groupId>fabriqon</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jooq</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jooq</artifactId>
        </dependency>
        <dependency>
            <groupId>fabriqon</groupId>
            <artifactId>misc</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-json</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>generate-sources-profile</id>
            <build>
                <plugins>
                    <plugin>

                        <!-- Specify the maven code generator plugin -->
                        <!-- Use org.jooq                for the Open Source Edition
                                 org.jooq.pro            for commercial editions with Java 17 support,
                                 org.jooq.pro-java-11    for commercial editions with Java 11 support,
                                 org.jooq.pro-java-8     for commercial editions with Java 8 support,
                                 org.jooq.trial          for the free trial edition with Java 17 support,
                                 org.jooq.trial-java-11  for the free trial edition with Java 11 support,
                                 org.jooq.trial-java-8   for the free trial edition with Java 8 support

                             Note: Only the Open Source Edition is hosted on Maven Central.
                                   Import the others manually from your distribution -->
                        <groupId>org.jooq</groupId>
                        <artifactId>jooq-codegen-maven</artifactId>
                        <version>3.16.6</version>

                        <!-- The plugin should hook into the generate goal -->
                        <executions>
                            <execution>
                                <goals>
                                    <goal>generate</goal>
                                </goals>
                            </execution>
                        </executions>

                        <!-- Manage the plugin's dependency. In this example, we'll use a PostgreSQL database -->
                        <dependencies>
                            <dependency>
                                <groupId>org.postgresql</groupId>
                                <artifactId>postgresql</artifactId>
                                <version>42.4.0</version>
                            </dependency>
                        </dependencies>

                        <!-- Specify the plugin configuration.
                             The configuration format is the same as for the standalone code generator -->
                        <configuration>

                            <!-- JDBC connection parameters -->
                            <jdbc>
                                <driver>org.postgresql.Driver</driver>
                                <url>**************************************</url>
                                <user>postgres</user>
                                <password>password</password>
                            </jdbc>

                            <!-- Generator parameters -->
                            <generator>
                                <database>
                                    <name>org.jooq.meta.postgres.PostgresDatabase</name>
                                    <includes>.*</includes>
                                    <excludes></excludes>
                                    <!-- In case your database supports catalogs, e.g. SQL Server:
                                    <inputCatalog>public</inputCatalog>
                                      -->
                                    <inputSchema>public</inputSchema>

                                    <forcedTypes>
                                        <forcedType>
                                            <!-- Specify the Java type of your custom type. This corresponds to the Converter's <U> type. -->
                                            <userType>java.util.Currency</userType>
                                            <!-- Associate that custom type with your converter. -->
                                            <converter>fabriqon.jooq.converters.CurrencyConverter</converter>
                                            <!-- A Java regex matching fully-qualified columns, attributes, parameters. Use the pipe to separate several expressions. -->
                                            <includeExpression>.*.\.*_currency.*</includeExpression>
                                        </forcedType>
                                    </forcedTypes>
                                </database>
                                <target>
                                    <packageName>fabriqon.jooq.classes</packageName>
                                    <directory>src/main/java</directory>
                                </target>
                            </generator>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>