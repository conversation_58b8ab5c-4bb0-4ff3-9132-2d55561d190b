package fabriqon.events;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Arrays;
import java.util.List;

@SuppressWarnings({"unchecked", "rawtypes"})
@Component
public class Events {

    private final TransactionTemplate transactionTemplate;
    private final List<EventConsumer> consumers;

    @Autowired
    public Events(TransactionTemplate transactionTemplate,
                  //consumers often times use services themselves leading inevitably to circular dependencies
                  @Lazy List<EventConsumer> consumers) {
        this.transactionTemplate = transactionTemplate;
        this.consumers = consumers;
    }

    public void publish(Event... events) {
        Arrays.stream(events)
                .forEach(event -> consumers.stream()
                        .filter(consumer -> consumer.persistentClass.isAssignableFrom(event.getClass()))
                        .forEach(consumer -> transactionTemplate.execute(new TransactionCallbackWithoutResult() {
                            public void doInTransactionWithoutResult(TransactionStatus status) {
                                consumer.processEvent(event);
                            }
                        }))
                );
    }

}
