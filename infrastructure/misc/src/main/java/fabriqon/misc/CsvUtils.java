package fabriqon.misc;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.IOUtils;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

public class CsvUtils {

    public List<CSVRecord> parse(String source) {
        return parse(source, StandardCharsets.UTF_8);
    }

    public List<CSVRecord> parse(String source, Charset charset) {
        try (
                Reader reader = new InputStreamReader(IOUtils.toInputStream(source, charset));
                CSVParser parser = new CSVParser(reader, defaultFormat().build());
        ) {
            return parser.getRecords();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public int parseAndProcess(InputStream source, Charset charset, String separator, Consumer<CSVRecord> consumer) {
        try {
            // Wrap input stream in BufferedReader to handle BOM
            BufferedReader reader = new BufferedReader(new InputStreamReader(source, charset));
            if (charset == StandardCharsets.UTF_8) {
                // Check for BOM and remove it
                reader.mark(1);
                if (reader.read() != '\uFEFF') {
                    reader.reset();  // No BOM, reset to start
                }
            }
            CSVParser parser = CSVFormat.Builder.create(defaultFormat().build())
                    .setDelimiter(separator)
                    .setQuote(separator.equals(",") ? '"' : null)
                    .build()
                    .parse(reader);

            var recordCount = new AtomicInteger();
            for (CSVRecord record : parser) {
                consumer.accept(record);
                recordCount.incrementAndGet();
            }
            return recordCount.get();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public CSVFormat.Builder defaultFormat() {
        return CSVFormat.DEFAULT.builder().setHeader().setSkipHeaderRecord(true).setIgnoreHeaderCase(true);
    }

}
