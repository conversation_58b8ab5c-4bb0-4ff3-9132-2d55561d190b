package fabriqon.misc;

public class Tuple {

    public static <A, B> Tuple2<A, B> of(A a, B b) {
        return new Tuple2<>(a, b);
    }

    public static <A, B, C> Tuple3<A, B, C> of(A a, B b, C c) {
        return new Tuple3<>(a, b, c);
    }

    public static <A, B, C, D> Tuple4<A, B, C, D> of(A a, B b, C c, D d) {
        return new Tuple4<>(a, b, c, d);
    }

    public record Tuple2<A, B>(A a, B b) {
    }

    public record Tuple3<A, B, C>(A a, B b, C c) {
    }

    public record Tuple4<A, B, C, D>(A a, B b, C c, D d) {
    }

}
