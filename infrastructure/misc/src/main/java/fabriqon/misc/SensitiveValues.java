package fabriqon.misc;

import java.util.function.Function;
import java.util.regex.Pattern;

import static java.lang.Math.max;
import static java.lang.Math.min;

public final class SensitiveValues {

    private SensitiveValues() {
        // no-op
    }

    public static String mask(final String target) {
        return mask(target, 0);
    }

    public static String mask(final String target, final int clear) {
        final var mask = target.length() - min(target.length(), max(clear, 0));
        return "*".repeat(mask) + target.substring(mask);
    }

    public static String mask(final String target, final String pattern) {
        return mask(target, pattern, SensitiveValues::mask);
    }

    public static String mask(final String target, final Pattern pattern) {
        return mask(target, pattern, SensitiveValues::mask);
    }

    public static String mask(final String target, final String pattern, final String replacement) {
        return mask(target, pattern, match -> replacement);
    }

    public static String mask(final String target, final String pattern, final Function<String, String> replacement) {
        return mask(target, Pattern.compile(pattern, Pattern.CASE_INSENSITIVE), replacement);
    }

    public static String mask(final String target, final Pattern pattern, final Function<String, String> replacement) {
        final var matches = pattern.matcher(target);
        if (matches.groupCount() == 0) {
            return matches.replaceAll(result -> replacement.apply(result.group()));
        } else {
            final var masked = new StringBuilder();
            var position = 0;
            while (matches.find()) {
                for (int group = 1; group <= matches.groupCount(); group++) {
                    masked.append(target, position, matches.start(group));
                    final var match = matches.group(group);
                    if (match != null) {
                        masked.append(replacement.apply(match));
                        position = matches.end(group);
                    }
                }
            }
            masked.append(target, position, target.length());
            return masked.toString();
        }
    }

}
