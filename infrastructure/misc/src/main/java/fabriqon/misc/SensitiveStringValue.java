package fabriqon.misc;

public final class SensitiveStringValue {

    public final String value;

    public SensitiveStringValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return value == null ? "null" : SensitiveValues.mask(value,
                //unlikely as secrets have longer values but we want to be accurate in the masking
                Math.min(4, value.length() / 2));
    }

}
