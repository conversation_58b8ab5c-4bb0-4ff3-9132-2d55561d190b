package fabriqon.pdf;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

@Component
public class FabriqonHtmlToPdfConverter implements HtmlToPdfConverter {

    @Value("${fabriqon.html2pdf.url}")
    String fabriqonHtml2PdfUrl;

    @Override
    public byte[] convert(String html) {
        return WebClient.create(fabriqonHtml2PdfUrl)
                .mutate().codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(16 * 1024 * 1024)).build()
                .post()
                .contentType(MediaType.TEXT_HTML)
                .bodyValue(html)
                .retrieve()
                .bodyToMono(byte[].class)
                .block();
    }
}
