package fabriqon.email;

import com.mailgun.api.v3.MailgunMessagesApi;
import com.mailgun.model.message.Message;
import feign.form.FormData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

@Slf4j
public class MailgunEmailSender implements EmailSender {

    @Value("${fabriqon.email.sender.mailgun.domain:mg.fabriqon.app}")
    private String emailSenderMailgunDomain;

    private final MailgunMessagesApi mailgunMessagesApi;

    @Autowired
    public MailgunEmailSender(MailgunMessagesApi mailgunMessagesApi) {
        this.mailgunMessagesApi = mailgunMessagesApi;
    }

    @Override
    public void send(Email email) {
        mailgunMessagesApi.sendMessage(emailSenderMailgunDomain, Message.builder()
                .from(email.from())
                .to(email.to())
//              .cc(email.cc())
                .subject(email.subject())
                .html(email.body())
                .formData(email.attachments().entrySet().stream().map(e -> new FormData(mimeByExtension(e.getKey()), e.getKey(), e.getValue())).toList())
                .build());
    }

    private String mimeByExtension(String key) {
        switch (key.substring(key.lastIndexOf(".") + 1)) {
            case "pdf":
                return "application/pdf";
            default:
                return "text/html";
        }
    }

}
