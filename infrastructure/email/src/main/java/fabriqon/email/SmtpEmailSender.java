package fabriqon.email;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SmtpEmailSender implements EmailSender {

    private final JavaMailSender emailSender;

    @Autowired
    public SmtpEmailSender(JavaMailSender javaMailSender) {
        this.emailSender = javaMailSender;
    }

    @Override
    public void send(Email email) {
        try {
            MimeMessage message = emailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);

            helper.setFrom(email.from());
            helper.setTo(email.to().toArray(new String[]{}));
            helper.setSubject(email.subject());
            helper.setText(email.body(), true);
            email.attachments().forEach((key, value) -> {
                try {
                    helper.addAttachment(key, new ByteArrayResource(value));
                } catch (MessagingException e) {
                    log.error("cannot add attachment", e);
                    throw new RuntimeException(e);
                }
            });
            emailSender.send(message);
        } catch (MessagingException e) {
            throw new RuntimeException(e);
        }
    }

}
