package fabriqon.email;

import com.mailgun.api.v3.MailgunMessagesApi;
import com.mailgun.client.MailgunClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Scope;

@org.springframework.context.annotation.Configuration
public class Configuration {

    @Value("${fabriqon.email.sender.mailgun.privatekey:}")
    private String emailSenderMailgunPrivatekey;

    @Bean("emailSender")
    @ConditionalOnProperty(name = "fabriqon.email.sender", havingValue = "mailgun")
    @Primary
    @Autowired
    public EmailSender emailSender(MailgunMessagesApi mailgunMessagesApi) {
        return new MailgunEmailSender(mailgunMessagesApi);
    }

    @Bean
    @Scope("singleton")
    @ConditionalOnProperty(name = "fabriqon.email.sender", havingValue = "mailgun")
    public MailgunMessagesApi mailgunMessagesApi() {
        return MailgunClient.config("https://api.eu.mailgun.net/", emailSenderMailgunPrivatekey)
                .createApi(MailgunMessagesApi.class);
    }

}
