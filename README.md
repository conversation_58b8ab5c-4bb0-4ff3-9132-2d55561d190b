# Fabriqon App

Backend for <PERSON><PERSON>riqon

## Running the application

### Prerequisites
Running the application requires only Docker.

### Make commands
There are a set of `make` goals to help you streamline that process:

```
attach               Starts a bash shell inside application container
build                Builds a docker image with a fat jar file 
down                 Tears down the application and dependent services, removing volumes
down_backing         Tears down dependent services, removing volumes
local_build          Builds the application locally
local_run            Runs the application locally
up                   Starts the application and all dependent services
up_backing           Starts all dependent services
load_test_data       Inserts test data in the local DB
```

By default, the application API should be available on `localhost:8080` after doing `make build && make up_backing && make up`.

Seeding the local db with test data: `make load_test_data`

You can access the OpenAPI docs at: `http://localhost:8080/swagger-ui/index.html`

Alternatively, you may want to start only backing services such as Postgres, etc. and start the application manually :

```
$ make build up_backing
$ make local_run
```

## Setting up developer environment

Make sure you have JDK 17 installed, you can easily change versions using [SDKMAN](https://sdkman.io/):

```
sdk install java TBD
sdk use java TBD
```

We've included a wrapper for <PERSON><PERSON> in the same repository. To test everything builds properly run:

```
./mvnw clean package
```

### Running the tests
#### On MacOs
In case you want to run the tests as well you need to install MacPorts and install the latest version of the [JNA](https://ports.macports.org/port/jna/).

_Remember you can skip tests using `-DskipTests`._

### Running the application from the command line
```
./mvnw spring-boot:run -Dspring.profiles.active=dev
```

 