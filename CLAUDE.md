# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build & Development Commands

### Local Development
```bash
# Build locally
mvn clean package

# Run application locally (with backing services)
make up_backing
make local_run

# Alternative: Run with <PERSON>ven directly
mvn spring-boot:run -Dspring.profiles.active=dev
```

### Docker Development
```bash
# Build Docker image
make build

# Start full application stack
make up

# Start only backing services (PostgreSQL, etc.)
make up_backing

# Tear down services
make down          # Full stack with volumes
make down_backing  # Only backing services
```

### Testing
```bash
# Run tests (may require JNA on macOS via MacPorts)
mvn test

# Skip tests during build
mvn clean package -DskipTests
```

## Architecture Overview

### Multi-Module Maven Project
- **Application Module**: Core business logic and REST controllers
- **Infrastructure Module**: Cross-cutting concerns (database, email, PDF, templates, etc.)

### Core Business Domains
- **Manufacturing**: Production orders, tasks, workstations, AI-powered scheduling (Timefold)
- **Sales**: Orders, quotes, goods accompanying notes, service orders
- **Purchasing**: Purchase orders, supplier management, reception receipts
- **Inventory**: Stock tracking, adjustments, reservations, FIFO/weighted average costing
- **Invoicing**: Invoice generation, e-factura (Romanian e-invoicing), PDF documents
- **HR**: Employee management, time-off tracking, task assignments

### Technology Stack
- Spring Boot 3.3.5 with Java 21
- PostgreSQL with jOOQ for type-safe queries
- OAuth2/JWT authentication via Auth0
- Timefold AI for task optimization
- Liquibase for database migrations
- Docker for containerization

### Key Services Architecture
- Domain services in `fabriqon.app.business.*` packages
- REST controllers organized by business domain
- Event-driven communication between domains
- Template-based document generation (Mustache)
- Multi-language support (EN, RO, DE, HU)

### Database
- Primary database: PostgreSQL
- Schema management: Liquibase migrations
- Query layer: jOOQ generated classes
- Embedded PostgreSQL for integration tests

### Testing
- Integration tests with embedded PostgreSQL
- Test data loading via `make load_test_data`
- Domain-specific test scenarios

### API Access
- Application runs on `localhost:8080` by default
- OpenAPI docs: `http://localhost:8080/swagger-ui/index.html`
- OAuth2 protected endpoints (except public fabrication workflows)

### Business Process Integration
The system handles complete business workflows:
- Order-to-Cash: Sales → Manufacturing → Shipping → Invoicing  
- Procure-to-Pay: Requisitions → Purchase orders → Receiving → Payment
- Plan-to-Produce: Planning → Task scheduling → Material issuing → Production reporting