package fabriqon.app.http.controllers.goods;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.exceptions.BusinessException;
import fabriqon.app.business.goods.*;
import fabriqon.app.business.goods.materials.metals.MetalCalculatorService;
import fabriqon.app.business.goods.materials.metals.MetalMaterialService;
import fabriqon.app.business.inventory.InventoryService;
import fabriqon.app.business.manufacturing.ManufacturingOperation;
import fabriqon.app.business.manufacturing.ManufacturingOperationService;
import fabriqon.app.common.model.File;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.common.model.Money;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.controllers.FilesController;
import fabriqon.app.http.controllers.manufacturing.ManufacturingOperations;
import fabriqon.app.http.response.Entity;
import fabriqon.app.http.response.StringEntity;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static fabriqon.app.http.controllers.HttpApiConstants.*;
import static fabriqon.jooq.JooqJsonbFunctions.booleanField;
import static fabriqon.jooq.JooqJsonbFunctions.stringField;
import static fabriqon.jooq.JooqTextSearchFunctions.textSearch;
import static fabriqon.jooq.classes.Tables.*;
import static org.apache.commons.lang3.BooleanUtils.isNotTrue;
import static org.apache.commons.lang3.BooleanUtils.isTrue;
import static org.apache.commons.lang3.ObjectUtils.isEmpty;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.jooq.impl.DSL.select;

@RestController
@RequestMapping(path = "/goods")
public class GoodsController {

    private final AccountContext accountContext;
    private final GoodsService goodsService;
    private final InventoryService inventoryService;
    private final ManufacturingOperationService manufacturingOperationService;
    private final ManufacturingOperations manufacturingOperations;
    private final DSLContext db;
    private final MetalMaterialService metalMaterialService;
    private final MetalCalculatorService metalCalculatorService;

    @Autowired
    public GoodsController(AccountContext accountContext,
                           GoodsService goodsService,
                           InventoryService inventoryService,
                           ManufacturingOperationService manufacturingOperationService,
                           ManufacturingOperations manufacturingOperations,
                           DSLContext db,
                           MetalMaterialService metalMaterialService,
                           MetalCalculatorService metalCalculatorService) {
        this.accountContext = accountContext;
        this.goodsService = goodsService;
        this.inventoryService = inventoryService;
        this.manufacturingOperationService = manufacturingOperationService;
        this.manufacturingOperations = manufacturingOperations;
        this.db = db;
        this.metalMaterialService = metalMaterialService;
        this.metalCalculatorService = metalCalculatorService;
    }

    @GetMapping(path = "list")
    public List<GoodDetails> goods(@AuthenticationPrincipal Jwt principal,
                                   @RequestParam(value = "includeVariants", required = false) Boolean includeVariants,
                                   @RequestParam(value = "includeOnlyPurchased", required = false) Boolean includeOnlyPurchased,
                                   @RequestParam(value = "categoryIds", required = false) List<UUID> categoryIds,
                                   @RequestParam(value = "q", required = false) String textSearch,
                                   @RequestParam(value = LIMIT_QUERY, defaultValue = LIMIT_DEFAULT) Integer limit,
                                   @RequestParam(value = OFFSET_QUERY, defaultValue = OFFSET_DEFAULT) Integer offset
    ) {
        var filter = MATERIAL_GOOD.OWNER_ID.eq(accountContext.accountId(principal))
                .and(MATERIAL_GOOD.DELETED.isFalse());
        if (isNotTrue(includeVariants)) {
            filter = filter.and(MATERIAL_GOOD.PARENT_ID.isNull());
        }
        if (isTrue(includeOnlyPurchased)) {
            filter = filter.and(booleanField(MATERIAL_GOOD.DETAILS, "produced").isFalse().or(booleanField(MATERIAL_GOOD.DETAILS, "produced").isNull()));
        }
        if (isNotEmpty(categoryIds)) {
            filter = filter.and(MATERIAL_GOOD.CATEGORY_ID.in(categoryIds));
        }
        if (isNotBlank(textSearch)) {
            filter = filter.and(textSearch(MATERIAL_GOOD.TEXT_SEARCH, textSearch));
        }
        return db.selectFrom(MATERIAL_GOOD)
                .where(filter)
                .orderBy(MATERIAL_GOOD.NAME)
                .limit(limit).offset(offset)
                .fetchInto(MaterialGood.class)
                .stream().map(m -> details(m, accountContext.account(principal))).toList();
    }

    @GetMapping(path = "list/light")
    public List<LightGood> light(@AuthenticationPrincipal Jwt principal,
                                 @RequestParam(value = "includeVariants", required = false) Boolean includeVariants,
                                 @RequestParam(value = "includeOnlyPurchased", required = false) Boolean includeOnlyPurchased,
                                 @RequestParam(value = "includeOnlyProduced", required = false) Boolean includeOnlyProduced,
                                 @RequestParam(value = "categoryIds", required = false) List<UUID> categoryIds,
                                 @RequestParam(value = "q", required = false) String textSearch,
                                 @RequestParam(value = LIMIT_QUERY, required = false, defaultValue = "10000") Integer limit,
                                 @RequestParam(value = OFFSET_QUERY, defaultValue = OFFSET_DEFAULT) Integer offset
    ) {
        var filter = MATERIAL_GOOD.OWNER_ID.eq(accountContext.accountId(principal))
                .and(MATERIAL_GOOD.DELETED.isFalse());
        if (isNotTrue(includeVariants)) {
            filter = filter.and(MATERIAL_GOOD.PARENT_ID.isNull());
        }
        if (isTrue(includeOnlyPurchased)) {
            filter = filter.and(booleanField(MATERIAL_GOOD.DETAILS, "produced").isFalse().or(booleanField(MATERIAL_GOOD.DETAILS, "produced").isNull()));
        }
        if (isTrue(includeOnlyProduced)) {
            filter = filter.and(booleanField(MATERIAL_GOOD.DETAILS, "produced").isTrue());
        }
        if (isNotEmpty(categoryIds)) {
            filter = filter.and(MATERIAL_GOOD.CATEGORY_ID.in(categoryIds));
        }
        if (isNotBlank(textSearch)) {
            filter = filter.and(textSearch(MATERIAL_GOOD.TEXT_SEARCH, textSearch));
        }
        return db.select(MATERIAL_GOOD.ID, MATERIAL_GOOD.NAME, MATERIAL_GOOD.CODE, MATERIAL_GOOD.CATEGORY_ID, stringField(MATERIAL_GOOD.DETAILS, "measurementUnit").as("measurement_unit"))
                .from(MATERIAL_GOOD)
                .where(filter)
                .orderBy(MATERIAL_GOOD.NAME)
                .limit(limit)
                .offset(offset)
                .fetch()
                .map(r -> {
                    var measurementUnit = MeasurementUnit.valueOf(r.value5());
                    return new LightGood(r.value1(), r.value2(), r.value3(), r.value4(), new StringEntity(measurementUnit.name(), measurementUnit.symbol()));
                });
    }


    @GetMapping(path = "{id}/details")
    public GoodDetails details(@AuthenticationPrincipal Jwt principal,
                               @PathVariable("id") UUID productId) {
        return details(
                db.selectFrom(MATERIAL_GOOD)
                        .where(
                                MATERIAL_GOOD.OWNER_ID.eq(accountContext.accountId(principal)),
                                MATERIAL_GOOD.DELETED.isFalse(),
                                MATERIAL_GOOD.ID.eq(productId))
                        .fetchSingle()
                        .into(MaterialGood.class),
                accountContext.account(principal)
        );
    }

    @GetMapping(path = "{id}/all-required-materials")
    public List<ManufacturingOperations.RequiredMaterialDetails> allRequiredMaterials(@AuthenticationPrincipal Jwt principal,
                                                                                      @PathVariable("id") UUID goodId) {
        var good = db.selectFrom(MATERIAL_GOOD)
                .where(
                        MATERIAL_GOOD.OWNER_ID.eq(accountContext.accountId(principal)),
                        MATERIAL_GOOD.DELETED.isFalse(),
                        MATERIAL_GOOD.ID.eq(goodId))
                .fetchSingle()
                .into(MaterialGood.class);
        return allRequiredMaterials(good).stream()
                .collect(Collectors.groupingBy(
                        material -> material.materialIds().getFirst(),
                        Collectors.reducing(
                                null,
                                (m1, m2) -> {
                                    if (m1 == null) return m2;
                                    return new RequiredMaterial(
                                            m1.materialIds(),
                                            m1.quantity().add(m2.quantity()),
                                            m1.optional(),
                                            m1.configurableWithOptions(),
                                            m1.replaceableWithOptions(),
                                            m1.wastePercentage(),
                                            m1.dimensions(),
                                            m1.totalDimensions()
                                    );
                                }
                        )
                ))
                .values()
                .stream()
                .map(m -> manufacturingOperations.requiredMaterialDetails(accountContext.accountId(principal), good.details.unitOfProduction(), m, inventoryService.defaultInventoryUnitId(accountContext.accountId(principal))))
                .sorted(Comparator.comparing(m -> m.options().getFirst().name()))
                .toList();
    }

    private List<RequiredMaterial> allRequiredMaterials(MaterialGood good) {
        return good.details.manufacturingOperations().stream()
                .flatMap(op -> op.materials().stream())
                .flatMap(m -> {
                    var material = db.selectFrom(MATERIAL_GOOD)
                            .where(MATERIAL_GOOD.ID.eq(m.materialIds().getFirst()))
                            .fetchSingleInto(MaterialGood.class);
                    return material.details.produced()
                            ? allRequiredMaterials(material).stream().map(m2 -> m2.setQuantity(m2.quantity().multiply(m.quantity())))
                            : Stream.of(m);
                })
                .toList();
    }

    @PostMapping(path = "create")
    public GoodDetails create(@AuthenticationPrincipal Jwt principal, @Validated @RequestBody GoodDefinition create) {
        validate(create);
        var unitOfProduction = create.unitOfProduction != null ? create.unitOfProduction : BigDecimal.ONE;
        return details(goodsService.create(accountContext.accountId(principal), new MaterialGood(null, null, null, false,
                        accountContext.accountId(principal),
                        create.name,
                        create.code,
                        create.externalCode,
                        create.categoryId,
                        null,
                        create.description,
                        create.sellPrice,
                        new MaterialGood.Details(
                                create.criticalOnHand, create.measurementUnit,
                                create.manufacturingOperations != null
                                        ? create.manufacturingOperations.stream()
                                        //for operations with a template we only store some fields; all the other fields are going to be retrieved from the template
                                        .map(operation -> {
                                            List<RequiredMaterial> materials = isNotEmpty(operation.materials()) ? operation.materials().stream()
                                                    .map(material -> new RequiredMaterial(
                                                            material.materialIds(), material.quantity().setScale(4, RoundingMode.HALF_EVEN).divide(unitOfProduction, RoundingMode.HALF_EVEN),
                                                            material.optional(), material.configurableWithOptions(), material.replaceableWithOptions(),
                                                            material.wastePercentage() != null ? material.wastePercentage() : BigDecimal.ZERO,
                                                            material.dimensions(), material.totalDimensions()))
                                                    .toList()
                                                    : List.of();
                                            var perUnitDuration = new BigDecimal(operation.durationInMinutes()).divide(unitOfProduction, RoundingMode.HALF_EVEN).intValue();
                                            return operation.operationTemplateId() != null
                                                    ? new ManufacturingOperation(operation.operationTemplateId(), null, null, null, perUnitDuration,
                                                    null, null, null, null, null, materials)
                                                    : new ManufacturingOperation(null, operation.candidateWorkstationIds(), operation.candidateEmployeeIds(), operation.name(), perUnitDuration,
                                                    operation.parallelizable(), operation.costPerHour(), operation.numberOfAssignees(), operation.manuallyAssignedEmployees(), operation.manuallyAssignedWorkstations(), materials);
                                        })
                                        .toList()
                                        : List.of(),
                                null,
                                null,
                                create.produced,
                                create.markup,
                                create.vatRate,
                                unitOfProduction,
                                create.dimensions,
                                create.material
                        )
                )),
                accountContext.account(principal));
    }

    @PostMapping(path = "{id}/update")
    public void update(@AuthenticationPrincipal Jwt principal,
                       @PathVariable("id") UUID productId,
                       @Validated @RequestBody GoodDefinition update) {
        validate(update);
        goodsService.update(productId, accountContext.accountId(principal), new MaterialGood(null, null, null, false,
                accountContext.accountId(principal),
                update.name,
                update.code,
                update.externalCode,
                update.categoryId,
                null,
                update.description,
                update.sellPrice,
                new MaterialGood.Details(
                        update.criticalOnHand, update.measurementUnit,
                        update.manufacturingOperations == null ? null : update.manufacturingOperations,
                        null,
                        null,
                        update.produced,
                        update.markup,
                        update.vatRate,
                        update.unitOfProduction,
                        update.dimensions,
                        update.material
                )
        ));
    }

    @PostMapping(path = "{id}/clone")
    public GoodDetails clone(@AuthenticationPrincipal Jwt principal,
                             @PathVariable("id") UUID productId) {
        return details(goodsService.clone(accountContext.accountId(principal), productId), accountContext.account(principal));
    }

    @PostMapping(path = "{id}/attach-file", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public GoodDetails attachFile(@AuthenticationPrincipal Jwt principal,
                                  @PathVariable("id") UUID productId,
                                  @RequestParam("file") MultipartFile file) throws IOException {
        return details(goodsService.attach(accountContext.accountId(principal), productId,
                file.getOriginalFilename(), file.getBytes()), accountContext.account(principal));
    }

    @DeleteMapping(path = "{id}/files/{fileId}/delete")
    public GoodDetails deleteFile(@AuthenticationPrincipal Jwt principal,
                                  @PathVariable("id") UUID productId,
                                  @PathVariable("fileId") UUID fileId) throws IOException {
        return details(goodsService.deleteFile(accountContext.accountId(principal), productId, fileId), accountContext.account(principal));
    }


    @GetMapping(path = "{id}/variants/list")
    public List<GoodDetails> variants(@AuthenticationPrincipal Jwt principal,
                                      @PathVariable("id") UUID productId) {
        return db.selectFrom(MATERIAL_GOOD)
                .where(
                        MATERIAL_GOOD.OWNER_ID.eq(accountContext.accountId(principal)),
                        MATERIAL_GOOD.DELETED.isFalse(),
                        MATERIAL_GOOD.PARENT_ID.eq(productId))
                .fetchInto(MaterialGood.class)
                .stream().map(v -> details(v, accountContext.account(principal))).toList();
    }


    @PostMapping(path = "{id}/variants/update")
    public void updateVariants(@AuthenticationPrincipal Jwt principal,
                               @PathVariable("id") UUID productId,
                               @RequestBody List<MaterialGood.VariantOption> variantOptions) {
        goodsService.updateVariants(accountContext.accountId(principal), productId, variantOptions);
    }

    @DeleteMapping(path = "{id}/delete")
    public void delete(@AuthenticationPrincipal Jwt principal,
                       @PathVariable("id") UUID productId) {
        goodsService.delete(accountContext.accountId(principal), productId);
    }

    @PostMapping("{id}/weight-in-kg")
    public Map<String, BigDecimal> weightInKg(@AuthenticationPrincipal Jwt principal,
                                                     @PathVariable("id") UUID materialId,
                                                     @RequestBody Dimensions requiredDimensions) {
        //just do this here to authenticate
        accountContext.accountId(principal);
        return Map.of("weightInKg", metalCalculatorService.calculateWeight(materialId, requiredDimensions));
    }

    private GoodDetails details(final MaterialGood product, final Account account) {
        var stock = inventoryService.getCurrentStock(product.ownerId, product.id, inventoryService.defaultInventoryUnitId(product.ownerId));
        var variantCount = variantCount(product.id);
        var enhancedManufacturingOperations = manufacturingOperationService.enhance(product.details.manufacturingOperations());
        var estimatedMaterialCosts = goodsService.estimatedMaterialCosts(product.ownerId, product.id);
        var estimatedLaborCosts = goodsService.laborCosts(product.ownerId, product.id);
        var estimatedProductionOverheadCosts = goodsService.productionOverheadCosts(product.ownerId, product.id);
        var estimatedManufacturingCosts = estimatedMaterialCosts.add(estimatedLaborCosts);
        var costPerItem = inventoryService.getAverageCost(product.ownerId, product.id);

        var lastPurchase = lastPurchase(product.id, inventoryService.lastOrderedFrom(product.ownerId, product.id));
        return new GoodDetails(
                product.id, product.createTime, product.updateTime,
                product.name,
                product.code,
                product.externalCode,
                product.parentId,
                product.categoryId != null ? new Entity(product.categoryId, categoryName(product.categoryId)) : null,
                product.description,
                product.sellPrice,
                product.details.markup(),
                product.details.vatRate(),
                product.details.criticalOnHand(),
                new StringEntity(product.details.measurementUnit().name(), product.details.measurementUnit().symbol()),
                enhancedManufacturingOperations.stream()
                        .map(op -> manufacturingOperations.manufacturingOperationDetails(product.ownerId, product.details().unitOfProduction(), op))
                        .toList(),
                variantCount,
                product.details.variantOptions(),
                product.details.appliedVariantOptions(),
                estimatedMaterialCosts,
                estimatedLaborCosts,
                estimatedProductionOverheadCosts,
                estimatedManufacturingCosts,
                goodsService.administrativeOverheadCosts(product.ownerId, product.id),
                stock,
                stock.compareTo(BigDecimal.ZERO) <= 0 && variantCount == 0,
                product.details.produced(),
                costPerItem,
                calculateMargin(product, estimatedManufacturingCosts, lastPurchase),
                files(product.ownerId, product.id),
                lastPurchase,
                product.details.manufacturingOperations().stream().flatMap(op -> op.materials().stream()).anyMatch(m -> m.configurableWithOptions() || m.optional()),
                product.details.unitOfProduction(),
                product.details.dimensions(),
                product.details.material() != null
                        ? new MaterialDetails(
                        metalMaterialService.getMaterialByKey(product.details.material().key(), account.settings().general().defaultLanguage()),
                        new MaterialDetails.Shape(product.details.material().shape(), metalMaterialService.getShapeName(product.details.material().shape(), account.settings().general().defaultLanguage())),
                        product.details.material().dimensions().stream()
                                .map(d -> new MaterialDetails.Dimension(d.key(), metalMaterialService.getDimensionName(d.key(), account.settings().general().defaultLanguage()), d.value()))
                                .toList())
                        : null
        );
    }

    private BigDecimal calculateMargin(MaterialGood product, Money estimatedManufacturingCosts, LastPurchase lastPurchase) {
        if (product.sellPrice == null || product.sellPrice.amount() == 0) {
            return BigDecimal.ZERO;
        }
        if (product.details.produced()) {
            if (estimatedManufacturingCosts.amount() != 0) {
                return BigDecimal.valueOf(product.sellPrice.amount() - estimatedManufacturingCosts.amount())
                        .setScale(2, RoundingMode.HALF_EVEN)
                        .divide(BigDecimal.valueOf(estimatedManufacturingCosts.amount()), RoundingMode.HALF_EVEN);
            }
        } else {
            if (lastPurchase != null && lastPurchase.price != null) {
                return BigDecimal.valueOf(product.sellPrice.amount() - lastPurchase.price.amount())
                        .setScale(2, RoundingMode.HALF_EVEN)
                        .divide(BigDecimal.valueOf(lastPurchase.price.amount()), RoundingMode.HALF_EVEN);
            }
        }
        return BigDecimal.ZERO;
    }

    private List<File> files(UUID ownerId, UUID productId) {
        return db.select(FILE.ID, FILE.FILE_NAME)
                .from(FILE)
                .where(FILE.ID.in(select(PRODUCT_FILE.FILE_ID).from(PRODUCT_FILE).where(PRODUCT_FILE.OWNER_ID.eq(ownerId), PRODUCT_FILE.PRODUCT_ID.eq(productId))))
                .fetch()
                .stream()
                .map(f -> new File(f.value1(), f.value2(),
                        FilesController.previewLink(f.value1()), FilesController.downloadLink(f.value1())))
                .toList();
    }

    private Entity category(UUID materialId) {
        var category = db.select()
                .from(CATEGORY)
                .where(CATEGORY.ID.eq(select(MATERIAL_GOOD.CATEGORY_ID).from(MATERIAL_GOOD).where(MATERIAL_GOOD.ID.eq(materialId))))
                .fetchSingleInto(Category.class);
        return new Entity(category.id(), category.details().name());
    }

    private String categoryName(UUID categoryId) {
        return db.selectFrom(CATEGORY)
                .where(CATEGORY.ID.eq(categoryId))
                .fetchSingle().into(Category.class)
                .details().name();
    }

    private int variantCount(UUID productId) {
        return db.fetchCount(MATERIAL_GOOD, MATERIAL_GOOD.PARENT_ID.eq(productId), MATERIAL_GOOD.DELETED.isFalse());
    }

    //TODO: do this with a custom validator that integrates into spring's validation framework
    private void validate(GoodDefinition definition) {
        if (definition.produced) {
            var requiredMaterials = definition.manufacturingOperations.stream().flatMap(mo -> mo.materials().stream()).toList();
            if (isEmpty(requiredMaterials)) {
                return;
            }
            if (requiredMaterials.stream().anyMatch(m -> m.materialIds().size() != Set.copyOf(m.materialIds()).size())) {
                throw new BusinessException("can't repeat materials in the options list", "invalid_bom_definition");
            }
            if (requiredMaterials.stream().anyMatch(m -> m.configurableWithOptions() && m.replaceableWithOptions())) {
                throw new BusinessException("can't have materials with both 'configurableWithOptions' and 'replaceableWithOptions' true", "invalid_bom_definition");
            }
            if (requiredMaterials.stream().anyMatch(m -> m.materialIds().size() > 1 && !(m.configurableWithOptions() || m.replaceableWithOptions()))) {
                throw new BusinessException("when more than 1 material is specified either 'configurableWithOptions' or 'replaceableWithOptions' need to be true", "invalid_bom_definition");
            }
            if (requiredMaterials.stream().anyMatch(m -> {
                if (m.materialIds().size() > 1) {
                    var m1Category = category(m.materialIds().getFirst()).id();
                    return m.materialIds().stream().anyMatch(mId -> !category(mId).id().equals(m1Category));
                }
                return false;
            })) {
                throw new BusinessException("materials in the same row must be from the same category", "invalid_bom_definition");
            }
        }
    }

    private LastPurchase lastPurchase(UUID productId, Entity supplier) {
        return new LastPurchase(supplier != null ? supplier.id() : null, supplier != null ? supplier.name() : null,
                db.select(INVENTORY.RECEPTION_PRICE_AMOUNT, INVENTORY.RECEPTION_PRICE_CURRENCY)
                        .from(INVENTORY)
                        .where(INVENTORY.MATERIAL_GOOD_ID.eq(productId))
                        .orderBy(INVENTORY.CREATE_TIME.desc())
                        .limit(1)
                        .fetchOneInto(Money.class)

        );
    }

    public record GoodDefinition(
            String name,
            String code,
            String externalCode,
            UUID categoryId,
            String description,
            Money sellPrice,
            BigDecimal markup,
            BigDecimal vatRate,
            BigDecimal criticalOnHand,
            MeasurementUnit measurementUnit,
            List<ManufacturingOperation> manufacturingOperations,
            boolean produced,
            BigDecimal unitOfProduction,
            Dimensions dimensions,
            MaterialGood.Material material
    ) {
    }

    public record GoodDetails(
            UUID id, Instant createTime, Instant updateTime,
            String name,
            String code,
            String externalCode,
            UUID parentId,
            Entity category,
            String description,
            Money sellPrice,
            BigDecimal markup,
            BigDecimal vatRate,
            BigDecimal criticalOnHand,
            StringEntity measurementUnit,
            List<ManufacturingOperations.ManufacturingOperationDetails> manufacturingOperations,
            int variantCount,
            List<MaterialGood.VariantOption> variantOptions,
            List<MaterialGood.AppliedVariantOption> appliedVariantOptions,
            Money estimatedMaterialCost,
            Money estimatedEmployeeAndWorkstationCost,
            Money estimatedProductionOverheadCost,
            Money estimatedProductionCost,
            Money estimatedAdministrativeCost,
            BigDecimal stock,
            boolean deletable,
            Boolean produced,
            Money inventoryCostPerItem,
            BigDecimal margin,
            List<File> files,
            LastPurchase lastPurchase,
            boolean customizable,
            BigDecimal unitOfProduction,
            Dimensions dimensions,
            MaterialDetails material
    ) {
    }

    public record LastPurchase(UUID supplierId,
                               String supplierName,
                               Money price) {
    }

    public record LightGood(UUID id, String name, String code, UUID categoryId, StringEntity measurementUnit) {
    }

    public record MaterialDetails(MetalMaterialService.Material material, Shape shape, List<Dimension> dimensions) {
        public record Shape(MetalMaterialService.ShapeType shape, String name) {
        }

        public record Dimension(MetalMaterialService.DimensionKey dimensionKey, String name, BigDecimal value) {
        }
    }
}
