package fabriqon.app.http.controllers;

import fabriqon.app.business.employees.Employee;
import fabriqon.app.business.employees.EmployeeService;
import fabriqon.app.business.employees.EmployeeTimeoff;
import fabriqon.app.business.employees.EmployeeTimeoffService;
import fabriqon.app.business.exceptions.BusinessException;
import fabriqon.app.business.exceptions.ResourceNotFoundException;
import fabriqon.app.common.model.DevicePairingTokenType;
import fabriqon.app.common.model.Money;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.response.Entity;
import fabriqon.misc.Tuple;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.net.MalformedURLException;
import java.net.URI;
import java.net.URL;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import static fabriqon.jooq.JooqTextSearchFunctions.textSearch;
import static fabriqon.jooq.classes.Tables.*;
import static java.lang.String.format;
import static org.apache.commons.lang3.BooleanUtils.isTrue;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.jooq.impl.DSL.select;

@RestController
@RequestMapping(path = "/employees")
public class EmployeesController {
    private final AccountContext accountContext;
    private final EmployeeService employeeService;
    private final EmployeeTimeoffService employeeTimeoffService;
    private final DSLContext db;

    @Value("${fabriqon.fabrication.url}")
    String fabriqonFabricationUrl;

    @Autowired
    public EmployeesController(final AccountContext accountContext,
                               final EmployeeService employeeService,
                               final EmployeeTimeoffService employeeTimeoffService,
                               final DSLContext db) {
        this.accountContext = accountContext;
        this.employeeService = employeeService;
        this.employeeTimeoffService = employeeTimeoffService;
        this.db = db;
    }

    @PostMapping(path = "create")
    public EmployeeDetails create(@AuthenticationPrincipal Jwt principal, @Validated @RequestBody EmployeeDefinition create) {
        return details(employeeService.create(accountContext.accountId(principal),
                isNotEmpty(create.manufacturingOperationTemplates)
                        ? create.manufacturingOperationTemplates.stream().map(op -> Tuple.of(op.id, op.preferential)).toList()
                        : List.of(),
                create.name(),
                new Employee.Details(create.position, create.monthlyGrossSalary, null)));
    }

    @PostMapping(path = "{id}/update")
    public EmployeeDetails update(@AuthenticationPrincipal Jwt principal,
                                  @PathVariable(value = "id") UUID employeeId,
                                  @Validated @RequestBody EmployeeDefinition update) {
        return details(employeeService.update(accountContext.accountId(principal), employeeId,
                isNotEmpty(update.manufacturingOperationTemplates)
                        ? update.manufacturingOperationTemplates.stream().map(op -> Tuple.of(op.id, op.preferential)).toList()
                        : List.of(),
                update.name(),
                new Employee.Details(update.position, update.monthlyGrossSalary, null)));
    }

    @DeleteMapping(path = "{id}/delete")
    public void delete(@AuthenticationPrincipal Jwt principal,
                       @PathVariable(value = "id") UUID employeeId) {
        employeeService.delete(accountContext.accountId(principal), employeeId);
    }

    @GetMapping(path = "list")
    public List<EmployeeDetails> list(@AuthenticationPrincipal Jwt principal,
                                      @RequestParam(value = "workersOnly", required = false) Boolean workers,
                                      @RequestParam(value = "q", required = false) String textSearch
                                      ) {
        var filter = USERS.OWNER_ID.eq(accountContext.accountId(principal)).and(USERS.DELETED.isFalse()).and(USERS.HIDDEN.isFalse());
        if (isTrue(workers)) {
            filter = filter
                    .and(USERS.ID.in(select(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.USER_ID)
                    .from(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE)
                            .where(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.OWNER_ID.eq(accountContext.accountId(principal)))));
        }
        if (isNotBlank(textSearch)) {
            filter = filter.and(textSearch(USERS.TEXT_SEARCH, textSearch));
        }
        return db.select()
                .from(USERS)
                .where(filter)
                .orderBy(USERS.NAME)
                .fetchInto(Employee.class)
                .stream().map(this::details).toList();
    }

    @GetMapping(path = "{id}/details")
    public EmployeeDetails details(@AuthenticationPrincipal Jwt principal,
                                   @PathVariable(value = "id") UUID employeeId) {
        return details(db.select()
                .from(USERS)
                .where(USERS.OWNER_ID.eq(accountContext.accountId(principal)), USERS.ID.eq(employeeId))
                .fetchSingleInto(Employee.class));
    }

    @GetMapping(path = "{id}/initiate-device-pairing")
    public DevicePairingDetails initiateDevicePairing(@AuthenticationPrincipal Jwt principal,
                                                      @PathVariable(value = "id") UUID employeeId)
            throws MalformedURLException {
        var count = db.fetchCount(USERS, USERS.ID.eq(employeeId), USERS.OWNER_ID.eq(accountContext.accountId(principal)));
        if (count != 1) {
            throw new ResourceNotFoundException("employee not found", "not_found");
        }
        String token = UUID.randomUUID().toString();
        //we need these tokens to be always available so we hardcode them to these users
        if (employeeId.equals(UUID.fromString("4c212352-643c-475f-8026-5a11d2661316"))) {//android test user
            token = "ANDROID_TOKEN";
        } else if (employeeId.equals(UUID.fromString("c68e6a46-ede3-4de9-ba89-760b0bd24ac2"))) {//ios test user
            token = "IOS_TOKEN";
        } else {
            db.insertInto(DEVICE_PAIRING_TOKEN)
                    .set(DEVICE_PAIRING_TOKEN.TOKEN_TYPE, DevicePairingTokenType.INITIATION.name())
                    .set(DEVICE_PAIRING_TOKEN.TOKEN, token)
                    .set(DEVICE_PAIRING_TOKEN.USER_ID, employeeId)
                    .execute();
        }
        return new DevicePairingDetails(token, URI.create(format("%s/pairing/confirmation/%s", fabriqonFabricationUrl, token)).toURL());
    }

    @PostMapping(path = "{id}/timeoff/add")
    public EmployeeTimeoffDetails addTimeoff(@AuthenticationPrincipal Jwt principal,
                                                                      @PathVariable(value = "id") UUID employeeId,
                                                                      @Validated @RequestBody EmployeeTimeoffDefinition timeoff) {
        if (timeoff.startTime.isAfter(timeoff.endTime)) {
            throw new BusinessException("'start' must be after 'end'", "invalid_parameters");
        }
        return timeoffDetails(employeeTimeoffService.add(accountContext.accountId(principal),
                accountContext.userId(principal),
                employeeId, timeoff.startTime, timeoff.endTime,
                timeoff.type, timeoff.servicingOrderId));
    }

    private EmployeeTimeoffDetails timeoffDetails(EmployeeTimeoff timeoff) {
        return new EmployeeTimeoffDetails(
                timeoff.id(),
                timeoff.createTime(),
                timeoff.startTime(),
                timeoff.endTime(),
                timeoff.type(),
                timeoff.servicingOrderId() != null
                        ? db.select(SERVICING_ORDER.ID, SERVICING_ORDER.NUMBER).from(SERVICING_ORDER).where(SERVICING_ORDER.ID.eq(timeoff.servicingOrderId())).fetchSingleInto(Entity.class)
                        : null,
                timeoff.servicingOrderId() != null
                        ? db.select(COMPANY.ID, COMPANY.COMPANY_NAME).from(COMPANY).where(COMPANY.ID.eq(
                                select(SALES_ORDER.CUSTOMER_ID).from(SALES_ORDER).where(SALES_ORDER.ID.eq(select(SERVICING_ORDER.SALES_ORDER_ID).from(SERVICING_ORDER).where(SERVICING_ORDER.ID.eq(timeoff.servicingOrderId()))))))
                        .fetchOptionalInto(Entity.class).orElse(null)
                        : null
        );
    }

    @DeleteMapping(path = "{employeeId}/timeoff/{timeoffId}/delete")
    public void deleteTimeoff(@AuthenticationPrincipal Jwt principal,
                              @PathVariable(value = "employeeId") UUID employeeId,
                              @PathVariable(value = "timeoffId") UUID timeoffId) {
        employeeTimeoffService.delete(accountContext.accountId(principal), employeeId, timeoffId);
    }

    private EmployeeDetails details(Employee employee) {
        return new EmployeeDetails(
                employee.id(),
                employee.createTime(),
                employee.updateTime(),
                employee.name(),
                employee.details().position(),
                employee.details().monthlyGrossSalary(),
                employee.details().hourlyRate(),
                operationTemplates(employee.id()),
                isDevicePaired(employee.id()),
                activeTimeoffs(employee.id())
        );
    }

    private List<EmployeeTimeoffDetails> activeTimeoffs(UUID employeeId) {
        return db.selectFrom(EMPLOYEE_TIMEOFF)
                .where(EMPLOYEE_TIMEOFF.USER_ID.eq(employeeId), EMPLOYEE_TIMEOFF.END_TIME.gt(LocalDateTime.now()))
                .fetchInto(fabriqon.app.business.employees.EmployeeTimeoff.class)
                .stream().map(this::timeoffDetails).toList();
    }

    private boolean isDevicePaired(UUID id) {
        return db.fetchCount(DEVICE_PAIRING_TOKEN,
                DEVICE_PAIRING_TOKEN.USER_ID.eq(id).and(DEVICE_PAIRING_TOKEN.TOKEN_TYPE.eq(DevicePairingTokenType.ACCESS.name()))) == 1;
    }

    private List<ManufacturingOperationDetails> operationTemplates(UUID employeeId) {
        return db.select(MANUFACTURING_OPERATION_TEMPLATE.ID, MANUFACTURING_OPERATION_TEMPLATE.NAME, EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.PREFERENTIAL)
                .from(MANUFACTURING_OPERATION_TEMPLATE)
                .join(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE).on(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_OPERATION_TEMPLATE_ID.eq(MANUFACTURING_OPERATION_TEMPLATE.ID))
                .where(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.USER_ID.eq(employeeId), MANUFACTURING_OPERATION_TEMPLATE.DELETED.isFalse())
                .fetchInto(ManufacturingOperationDetails.class);
    }

    public record EmployeeDefinition(
            String name,
            String position,
            Money monthlyGrossSalary,
            List<EmployeeManufacturingOperation> manufacturingOperationTemplates
    ) {
    }

    public record EmployeeManufacturingOperation(
            UUID id,
            boolean preferential
    ) { }

    public record EmployeeDetails(
            UUID id,
            Instant createTime,
            Instant updateTime,
            String name,
            String position,
            Money monthlyGrossSalary,
            Money hourlyRate,
            List<ManufacturingOperationDetails> manufacturingOperationTemplates,
            boolean isDevicePaired,
            List<EmployeeTimeoffDetails> activeTimeoffs
    ) {
    }

    public record ManufacturingOperationDetails(
            UUID id,
            String name,
            boolean preferential
    ) {}

    public record DevicePairingDetails(
            String token,
            URL pairingLink
    ) {
    }

    public record EmployeeTimeoffDefinition(
            LocalDateTime startTime,
            LocalDateTime endTime,
            fabriqon.app.business.employees.EmployeeTimeoff.Type type,
            UUID servicingOrderId
    ) {
    }

    public record EmployeeTimeoffDetails(
            UUID id,
            Instant createTime,
            LocalDateTime startTime,
            LocalDateTime endTime,
            fabriqon.app.business.employees.EmployeeTimeoff.Type type,
            Entity servicingOrder,
            Entity customer
    ) {
    }

}