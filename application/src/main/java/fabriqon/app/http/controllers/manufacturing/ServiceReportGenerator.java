package fabriqon.app.http.controllers.manufacturing;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.services.ServicingOrder;
import fabriqon.templates.Localizer;
import fabriqon.templates.Templates;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

import static fabriqon.jooq.classes.Tables.*;
import static org.jooq.impl.DSL.select;

@Component
public class ServiceReportGenerator {

    private final DSLContext db;
    private final Templates templates;

    @Autowired
    public ServiceReportGenerator(DSLContext db, Templates templates) {
        this.db = db;
        this.templates = templates;
    }


    public String html(ServicingOrder order, String base64ClientSignature, String clientRepresentative, String base64WorkerSignature) {
        var account = account(order.ownerId());
        var localizer = new Localizer(new Locale(account.information().address().country()));

        var orderMap = new HashMap<>();
        orderMap.put("number", order.number());
        orderMap.put("date", localizer.localizeDate(order.createTime()));
        orderMap.put("notes", order.notes());

        var materialIndexer = new AtomicInteger(0);
        var materials = order.materials().stream()
                .filter(m -> m.usedQuantity() != null && m.usedQuantity().compareTo(BigDecimal.ZERO) > 0)
                .map(m -> {
                    var material = db.selectFrom(MATERIAL_GOOD).where(MATERIAL_GOOD.ID.eq(m.materialIds().getFirst())).fetchSingleInto(MaterialGood.class);
                    return Map.of(
                            "index", materialIndexer.incrementAndGet(),
                            "name", material.name,
                            "code", material.code,
                            "quantity", m.usedQuantity().stripTrailingZeros().toPlainString(),
                            "measurementUnit", localizer.localizedValue(material.details.measurementUnit().name())
                    );
                })
                .toList();

        var operationIndexer = new AtomicInteger(0);
        HashMap<String, Object> map = new HashMap<>();
        map.put("number", order.number());
        map.put("date", localizer.localizeDate(order.createTime()));
        map.put("notes", order.notes());
        map.put("materials", materials);
        map.put("supplier", account.name());
        map.put("customer", customer(order.id()));
        map.put("worker", workerName(order.assignedTo()));
        map.put("operations", order.operations().stream()
                .map(operation -> {
                    var m = new HashMap<String, Object>();
                    m.put("index", operationIndexer.incrementAndGet());
                    m.put("name", operation.name());
                    m.put("duration", localizer.humanReadableDuration(Duration.ofMinutes(operation.durationInMinutes())));
                    return m;
                })
                .toList());
        map.put("labels", localizer.labels());
        map.put("base64Logo", account.logo() != null ? account.logo() : "");
        map.put("clientSignature", base64ClientSignature != null ? base64ClientSignature : order.base64ClientSignature());
        map.put("clientRepresentative", clientRepresentative != null ? clientRepresentative : order.clientRepresentative());
        map.put("workerSignature", base64WorkerSignature != null ? base64WorkerSignature : order.base64WorkerSignature());
        return templates.render("pdf/service_report.html", map);
    }

    private Account account(UUID accountId) {
        return db.selectFrom(ACCOUNT).where(ACCOUNT.ID.eq(accountId)).fetchSingleInto(Account.class);
    }

    private String customer(UUID orderId) {
        return db.select(COMPANY.COMPANY_NAME).from(COMPANY).where(COMPANY.ID.eq(
                        select(SALES_ORDER.CUSTOMER_ID).from(SALES_ORDER).where(SALES_ORDER.ID.eq(select(SERVICING_ORDER.SALES_ORDER_ID).from(SERVICING_ORDER).where(SERVICING_ORDER.ID.eq(orderId))))))
                .fetchOptionalInto(String.class).orElse(null);
    }

    private String workerName(UUID workerId) {
        return db.select(USERS.NAME).from(USERS).where(USERS.ID.eq(workerId)).fetchOptionalInto(String.class).orElse("");
    }

}
