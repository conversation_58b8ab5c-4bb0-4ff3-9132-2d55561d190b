package fabriqon.app.http.controllers;

import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.response.Entity;
import org.jooq.DSLContext;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static fabriqon.jooq.classes.Tables.USERS;

@RestController
@RequestMapping(path = "/users")
public class UsersController {

    private final AccountContext accountContext;
    private final DSLContext db;

    public UsersController(AccountContext accountContext, DSLContext db) {
        this.accountContext = accountContext;
        this.db = db;
    }

    @GetMapping(path = "list")
    public List<Entity> users(@AuthenticationPrincipal Jwt principal) {
        return db.select(USERS.ID, USERS.NAME)
                .from(USERS)
                .where(USERS.OWNER_ID.eq(accountContext.accountId(principal)).and(USERS.HIDDEN.isFalse()))
                .orderBy(USERS.NAME.asc())
                .fetchInto(Entity.class);
    }

}
