package fabriqon.app.http.controllers;

import fabriqon.app.business.servicetemplates.ServiceTemplate;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.common.model.Money;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.response.Entity;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.jooq.DSLContext;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.SERVICE_TEMPLATE;

@RestController
@RequestMapping(path = "/service-templates")
public class ServiceTemplatesController {

    private final AccountContext accountContext;
    private final DSLContext db;

    public ServiceTemplatesController(AccountContext accountContext, DSLContext db) {
        this.accountContext = accountContext;
        this.db = db;
    }

    @PostMapping(path = "create")
    public ServiceTemplate create(@AuthenticationPrincipal Jwt principal, @Validated @RequestBody ServiceTemplateDefinition create) {
        var id = UUID.randomUUID();
        var accountId = accountContext.accountId(principal);
        db.insertInto(SERVICE_TEMPLATE)
                .set(SERVICE_TEMPLATE.ID, id)
                .set(SERVICE_TEMPLATE.OWNER_ID, accountId)
                .set(SERVICE_TEMPLATE.NAME, create.name())
                .set(SERVICE_TEMPLATE.VAT_RATE, create.vatRate())
                .set(SERVICE_TEMPLATE.MEASUREMENT_UNIT, create.measurementUnit().name())
                .set(SERVICE_TEMPLATE.SELL_PRICE_AMOUNT, (int) create.sellPrice().amount())
                .set(SERVICE_TEMPLATE.SELL_PRICE_CURRENCY, create.sellPrice().currency())
                .set(SERVICE_TEMPLATE.COST_AMOUNT, create.cost != null ? (int) create.cost().amount() : null)
                .set(SERVICE_TEMPLATE.COST_CURRENCY, create.cost != null ? create.cost().currency() : null)
                .execute();
        return new ServiceTemplate(id, null, null, false, accountId, create.name(), create.vatRate, create.measurementUnit, create.sellPrice, create.cost);
    }

    @PostMapping(path = "{id}/update")
    public Entity update(@AuthenticationPrincipal Jwt principal,
                         @PathVariable("id") UUID id,
                         @Validated @RequestBody ServiceTemplateDefinition update) {
        db.update(SERVICE_TEMPLATE)
                .set(SERVICE_TEMPLATE.NAME, update.name())
                .set(SERVICE_TEMPLATE.VAT_RATE, update.vatRate())
                .set(SERVICE_TEMPLATE.MEASUREMENT_UNIT, update.measurementUnit().name())
                .set(SERVICE_TEMPLATE.SELL_PRICE_AMOUNT, (int) update.sellPrice().amount())
                .set(SERVICE_TEMPLATE.SELL_PRICE_CURRENCY, update.sellPrice().currency())
                .set(SERVICE_TEMPLATE.COST_AMOUNT, update.cost != null ? (int) update.cost().amount() : null)
                .set(SERVICE_TEMPLATE.COST_CURRENCY, update.cost != null ? update.cost().currency() : null)
                .where(SERVICE_TEMPLATE.OWNER_ID.eq(accountContext.accountId(principal)), SERVICE_TEMPLATE.ID.eq(id))
                .execute();
        return new Entity(id, update.name());
    }

    @GetMapping(path = "list")
    public List<ServiceTemplate> list(@AuthenticationPrincipal Jwt principal) {
        return db.selectFrom(SERVICE_TEMPLATE)
                .where(SERVICE_TEMPLATE.OWNER_ID.eq(accountContext.accountId(principal)), SERVICE_TEMPLATE.DELETED.isFalse())
                .fetchInto(ServiceTemplate.class);
    }

    @DeleteMapping(path = "{id}/delete")
    public void delete(@AuthenticationPrincipal Jwt principal, @PathVariable("id") UUID id) {
        db.update(SERVICE_TEMPLATE)
                .set(SERVICE_TEMPLATE.DELETED, true)
                .where(SERVICE_TEMPLATE.ID.eq(id), SERVICE_TEMPLATE.OWNER_ID.eq(accountContext.accountId(principal)))
                .execute();
    }

    public record ServiceTemplateDefinition(@NotBlank String name,
                                            @NotNull BigDecimal vatRate,
                                            @NotNull MeasurementUnit measurementUnit,
                                            @NotNull Money sellPrice,
                                            Money cost
                                            ) {
    }

}
