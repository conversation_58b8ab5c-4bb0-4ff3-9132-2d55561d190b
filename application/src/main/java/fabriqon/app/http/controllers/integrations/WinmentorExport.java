package fabriqon.app.http.controllers.integrations;

import fabriqon.app.business.exceptions.BusinessException;
import fabriqon.app.business.financial.VatCalculatorProvider;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.manufacturing.MaterialIssueNote;
import fabriqon.app.common.model.Address;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.http.controllers.InvoicesController;
import fabriqon.templates.Templates;
import org.jooq.DSLContext;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

import static fabriqon.jooq.classes.Tables.MATERIAL_GOOD;

public class WinmentorExport {

    private final DSLContext db;
    private final VatCalculatorProvider vatCalculatorProvider;
    private final Templates templates;

    public WinmentorExport(DSLContext db, VatCalculatorProvider vatCalculatorProvider, Templates templates) {
        this.db = db;
        this.vatCalculatorProvider = vatCalculatorProvider;
        this.templates = templates;
    }

    public String transformInvoices(List<InvoicesController.InvoiceDetails> invoices) {
        var year = invoices.get(0).creationDate().getYear();
        var month = invoices.get(0).creationDate().getMonthValue();
        //check that all invoices are in the month
        if (invoices.stream().anyMatch(invoice -> invoice.creationDate().getYear() != year || invoice.creationDate().getMonthValue() != month)) {
            throw new BusinessException("You can only export invoices for the same month", "invalid_export_period");
        }

        var invoiceCounter = new AtomicInteger(0);
        return templates.render("exports/winmentor_facturi_ro.txt", Map.of(
                "meta", Map.of(
                        "year", year,
                        "month", month,
                        "count", invoices.size()
                ),
                "invoices", invoices.stream()
                        .map(invoice -> {
                            var vatCalculator = vatCalculatorProvider.with(invoice.supplier().id, invoice.customer().id);
                            var itemCounter = new AtomicInteger(0);
                            var currency = invoice.total().currency();
                            return Map.of(
                                    "index", invoiceCounter.incrementAndGet(),
                                    "number", numberPartOfInvoice(invoice.number()),
                                    "series", seriesPartOfInvoice(invoice.number()),
                                    "date", DateTimeFormatter.ofPattern("dd.MM.yyyy").format(invoice.creationDate()),
                                    "customer", Map.of(
                                            "taxIdentificationNumber", invoice.customer().taxIdentificationNumber,
                                            "billingAddressCity", invoice.customer().addresses.stream()
                                                    .filter(address -> address.types().contains(Address.Type.BILLING)).findFirst().orElseThrow()
                                                    .city()
                                    ),
                                    "dueDate", DateTimeFormatter.ofPattern("dd.MM.yyyy").format(invoice.dueDate()),
                                    "notes", invoice.notes() != null ? invoice.notes() : "",
                                    "itemCount", invoice.items().size(),
                                    "items",
                                    invoice.items().stream()
                                            .map(item -> {
                                                var unitPrice = BigDecimal.valueOf(item.unitPrice().amount());
                                                var subTotal = item.quantity().multiply(unitPrice).longValue();
                                                var discountAmount = item.discount() != null
                                                        ? item.discount().multiply(new BigDecimal(subTotal)).longValue()
                                                        : 0;
                                                return Map.of(
                                                        "index", itemCounter.incrementAndGet(),
                                                        "code", code(item.productId()),
                                                        "measurementUnit", winmentorUnit(measurementUnit(item.productId())),
                                                        "quantity", item.quantity(),
                                                        "subTotal", BigDecimal.valueOf(subTotal - discountAmount).movePointLeft(currency.getDefaultFractionDigits()),
                                                        "vat", BigDecimal.valueOf(vatCalculator.calculate(subTotal - discountAmount, item.vatRate())).movePointLeft(currency.getDefaultFractionDigits())
                                                );
                                            })
                                            .toList()
                            );
                        })
                        .toList()
        ));
    }

    public String transformMaterialIssueNotes(List<MaterialIssueNote> notes) {
        if (notes.isEmpty()) {
            throw new BusinessException("no data selected.", "no_data_selected");
        }
        var year = notes.get(0).date().getYear();
        var month = notes.get(0).date().getMonthValue();
        //check that all invoices are in the month
        if (notes.stream().anyMatch(note -> note.date().getYear() != year || note.date().getMonthValue() != month)) {
            throw new BusinessException("You can only export notes for the same month", "invalid_export_period");
        }

        var noteCounter = new AtomicInteger(0);
        return templates.render("exports/winmentor_bonuri_consum.txt", Map.of(
                "meta", Map.of(
                        "year", year,
                        "month", month,
                        "count", notes.size()
                ),
                "materialIssueNotes", notes.stream()
                        .map(note -> {
                            var itemCounter = new AtomicInteger(0);
                            return Map.of(
                                    "index", noteCounter.incrementAndGet(),
                                    "number", note.number(),
                                    "date", DateTimeFormatter.ofPattern("dd.MM.yyyy").format(note.date()),
                                    "notes", "",
                                    "itemCount", note.materials().size(),
                                    "items", note.materials().stream()
                                            .map(item -> Map.of(
                                                    "index", itemCounter.incrementAndGet(),
                                                    "code", code(item.id()),
                                                    "measurementUnit", winmentorUnit(measurementUnit(item.id())),
                                                    "quantity", item.quantity(),
                                                    "sellPrice", 0,
                                                    "buyPrice", new BigDecimal(item.totalCost().amount()).movePointLeft(item.totalCost().currency().getDefaultFractionDigits())
                                            ))
                                            .toList()
                            );
                        })
                        .toList()
        ));
    }

    private MeasurementUnit measurementUnit(UUID id) {
        if (id == null) {
            //this is a service so the unit is always PCS
            return MeasurementUnit.PIECE;
        }
        return db.select(MATERIAL_GOOD.DETAILS)
                .from(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(id))
                .fetchSingleInto(MaterialGood.Details.class)
                .measurementUnit();
    }

    private String code(UUID id) {
        if (id != null) {
            return db.select(MATERIAL_GOOD.CODE)
                    .from(MATERIAL_GOOD)
                    .where(MATERIAL_GOOD.ID.eq(id))
                    .fetchSingleInto(String.class);
        } else {
            throw new BusinessException("Cannot export services for now", "service_not_implemented");
        }
    }

    private String numberPartOfInvoice(String number) {
        return number.replaceAll("[^0-9]", "");
    }

    private String seriesPartOfInvoice(String number) {
        return number.replaceAll("[^a-zA-Z]", "");
    }

    private String winmentorUnit(MeasurementUnit measurementUnit) {
        return switch (measurementUnit) {
            case PIECE -> "BUC";
            case KG -> "KG";
            case LITER -> "LITRI";
            case M -> "M";
            case G -> "GRAME";
            //todo map the other values
            default -> "BUC";
        };
    }

}
