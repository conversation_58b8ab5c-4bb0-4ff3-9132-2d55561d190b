package fabriqon.app.http.controllers.manufacturing;

import fabriqon.app.business.manufacturing.ManufacturingOrder;
import fabriqon.app.business.manufacturing.tasks.TimefoldTaskScheduler;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.response.Entity;
import fabriqon.app.http.response.StringEntity;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static fabriqon.app.business.manufacturing.ManufacturingOrder.Status.SUBMITTED;
import static fabriqon.app.business.manufacturing.ManufacturingTask.Status.TODO;
import static fabriqon.jooq.classes.Tables.*;
import static org.jooq.impl.DSL.select;

@RestController
@RequestMapping(path = "/manufacturing/tasks/timefold")
public class ManufacturingTasksTimefoldController {

    private final AccountContext accountContext;

    private final DSLContext db;
    private final TimefoldTaskScheduler timefoldTaskScheduler;

    @Autowired
    public ManufacturingTasksTimefoldController(AccountContext accountContext, DSLContext db,
                                                TimefoldTaskScheduler timefoldTaskScheduler) {
        this.accountContext = accountContext;
        this.db = db;
        this.timefoldTaskScheduler = timefoldTaskScheduler;
    }

    @GetMapping(path = "list")
    public List<ManufacturingTasks.ManufacturingTask> list(
            @AuthenticationPrincipal Jwt principal,
            @RequestParam(value = "day", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate day,
            @RequestParam(value = "start", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate start,
            @RequestParam(value = "end", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate end
    ) {
        if (day != null && (start != null || end != null)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "can't filter both on 'day' and 'start'/'end'");
        }
        var s = day != null ? day.atStartOfDay() : start != null ? start.atStartOfDay() : null;
        var e = day != null ? day.atTime(23, 59, 59) : end != null ? end.atTime(23, 59, 59) : null;
        var schedule = timefoldTaskScheduler.schedule(db.selectFrom(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.OWNER_ID.eq(accountContext.accountId(principal)), MANUFACTURING_ORDER.STATUS.in(SUBMITTED.name()))
                .fetchInto(ManufacturingOrder.class)
        );
        return schedule
                .getOperations()
                .stream()
                /*.filter(op -> {
                            if (s != null && e != null) {
                                return (op.getStartTime().isAfter(s) && op.getStartTime().isBefore(e)) ||
                                        (op.getEndTime().isAfter(s) && op.getEndTime().isBefore(e)) ||
                                        (op.getStartTime().isBefore(s) && op.getEndTime().isAfter(e));
                            }
                            return true;
                        }
                )*/
                .map(operation -> new ManufacturingTasks.ManufacturingTask(
                        operation.getId(),
                        order(operation.getManufacturingOrderId()),
                        product(operation.getManufacturingOrderId()),
                        TODO,
                        null,
                        operation.getStartTime(),
                        operation.getStartTime(),
                        operation.getEndTime(),
                        operation.getEndTime(),
                        null,
                        null,
                        operation.getDurationInMinutes(),
                        operation.getDurationInMinutes(),
                        operation.getName(),
                        operation.getName(),
                        BigDecimal.ZERO,
                        new StringEntity("PIECE", "pcs"),
                        operation.getEmployeeAssignments().stream().findFirst()
                                .map(ea -> ea.getWorkstation() != null ? List.of(new Entity(ea.getWorkstation().getId(), ea.getWorkstation().getName()))
                                        : List.<Entity>of())
                                .orElseThrow(),
                        operation.getEmployeeAssignments().stream()
                                .map(ea -> new Entity(ea.getEmployeeId(), ea.getEmployee().getName()))
                                .toList(),
                        operation.getNumberOfAssignees(),
                        false,
                        null)
                )
                .toList();
    }

    private ManufacturingTasks.ManufacturingOrderDetails order(UUID manufacturingOrderId) {
        var order = db.selectFrom(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.ID.eq(manufacturingOrderId))
                .fetchSingleInto(ManufacturingOrder.class);
        return new ManufacturingTasks.ManufacturingOrderDetails(order.id(), order.number(), order.ranking(), null);
    }

    private Entity product(UUID orderId) {
        return db.select(MATERIAL_GOOD.ID, MATERIAL_GOOD.NAME)
                .from(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(select(MANUFACTURING_ORDER.PRODUCT_ID).from(MANUFACTURING_ORDER).where(MANUFACTURING_ORDER.ID.eq(orderId))))
                .fetchOptionalInto(Entity.class)
                .orElseGet(() -> db.select(SERVICE_TEMPLATE.ID, SERVICE_TEMPLATE.NAME)
                        .from(SERVICE_TEMPLATE)
                        .where(SERVICE_TEMPLATE.ID.eq(select(MANUFACTURING_ORDER.SERVICE_ID).from(MANUFACTURING_ORDER).where(MANUFACTURING_ORDER.ID.eq(orderId))))
                        .fetchSingleInto(Entity.class)
                );
    }


}
