package fabriqon.app.http.controllers.inventory;

import fabriqon.app.business.exceptions.BusinessException;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.inventory.InventoryAdjustmentOrder;
import fabriqon.app.business.inventory.InventoryAdjustmentService;
import fabriqon.app.common.model.Company;
import fabriqon.app.common.model.Money;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.controllers.util.Sorting;
import fabriqon.app.http.response.Entity;
import fabriqon.app.http.response.StringEntity;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import org.jooq.DSLContext;
import org.jooq.TableField;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.*;

@RestController
@RequestMapping(path = "/inventory/adjustments")
public class InventoryAdjustmentsController {

    private final AccountContext accountContext;
    private final InventoryAdjustmentService inventoryAdjustmentService;
    private final DSLContext db;


    @Autowired
    public InventoryAdjustmentsController(AccountContext accountContext,
                                          InventoryAdjustmentService inventoryAdjustmentService,
                                          DSLContext db) {
        this.accountContext = accountContext;
        this.inventoryAdjustmentService = inventoryAdjustmentService;
        this.db = db;

    }

    @PostMapping(path = "adjust")
    public AdjustOrderDetails adjust(@AuthenticationPrincipal Jwt principal, @Validated @RequestBody AdjustOrder adjustOrder) {
        return map(inventoryAdjustmentService.adjustInventory(accountContext.accountId(principal), adjustOrder.reason, adjustOrder.inventoryEntries));
    }

    @PostMapping(path = "move-to-unit")
    public AdjustOrderDetails moveToUnit(@AuthenticationPrincipal Jwt principal, @Validated @RequestBody MoveUnitOrder adjustOrder) {
        if (adjustOrder.inventoryEntries.stream().anyMatch(entry -> BigDecimal.ZERO.compareTo(entry.quantity()) >= 0)) {
            throw new BusinessException("quantity must be greater than 0", "invalid_quantity_must_be_gt_0");
        }
        return map(inventoryAdjustmentService.moveToUnit(accountContext.accountId(principal), adjustOrder.reason, adjustOrder.deliveredBy, adjustOrder.receivedBy, adjustOrder.inventoryEntries));
    }

    private static final Map<String, TableField> sortFields = Map.of(
            "createTime", INVENTORY_ADJUSTMENT_ORDER.CREATE_TIME,
            "updateTime", INVENTORY_ADJUSTMENT_ORDER.UPDATE_TIME
    );

    @GetMapping(path = "list")
    public List<AdjustOrderDetails> list(@AuthenticationPrincipal Jwt principal,
                                         @RequestParam(value = "sort", required = false)
                                         @Parameter(description = "Available fields for sorting: createTime, updateTime")
                                         String sorting
    ) {
        return db.select()
                .from(INVENTORY_ADJUSTMENT_ORDER)
                .where(INVENTORY_ADJUSTMENT_ORDER.OWNER_ID.eq(accountContext.accountId(principal)))
                .orderBy(sorting == null ? List.of(INVENTORY_ADJUSTMENT_ORDER.CREATE_TIME.desc()) : Sorting.toJooqSortFields(sortFields, sorting))
                .fetchInto(InventoryAdjustmentOrder.class)
                .stream().map(this::map).toList();
    }

    @GetMapping(path = "{id}/details")
    public AdjustOrderDetails details(@AuthenticationPrincipal Jwt principal,
                                      @PathVariable("id") UUID id) {
        return map(db.select()
                .from(INVENTORY_ADJUSTMENT_ORDER)
                .where(INVENTORY_ADJUSTMENT_ORDER.OWNER_ID.eq(accountContext.accountId(principal)), INVENTORY_ADJUSTMENT_ORDER.ID.eq(id))
                .fetchSingleInto(InventoryAdjustmentOrder.class));
    }


    private AdjustOrderDetails map(InventoryAdjustmentOrder adjustInventory) {
        return new AdjustOrderDetails(
                adjustInventory.id(),
                adjustInventory.createTime(),
                adjustInventory.updateTime(),
                adjustInventory.number(),
                adjustInventory.type(),
                adjustInventory.details().reason(),
                new Money(adjustInventory.details().inventoryEntries().stream()
                        .map(entry -> entry.quantity().multiply(BigDecimal.valueOf(entry.price().amount())))
                        .mapToLong(BigDecimal::longValue).sum(),
                        adjustInventory.details().inventoryEntries().get(0).price().currency()),
                adjustInventory.details().deliveredBy(),
                adjustInventory.details().receivedBy(),
                adjustInventory.details().inventoryEntries().stream().map(this::adjustmentEntry).toList()
        );
    }

    private AdjustmentEntry adjustmentEntry(InventoryAdjustmentOrder.InventoryEntry inventoryEntry) {
        return new AdjustmentEntry(
                materialGood(inventoryEntry.materialGoodId()),
                inventoryEntry.locationId(),
                inventoryEntry.supplierId() != null ? new Entity(inventoryEntry.supplierId(), supplierName(inventoryEntry.supplierId())) : null,
                inventoryUnit(inventoryEntry.fromUnit()),
                inventoryUnit(inventoryEntry.unitId()),
                inventoryEntry.purchaseDate(),
                inventoryEntry.expiryDate(),
                inventoryEntry.price(),
                inventoryEntry.quantity(),
                inventoryEntry.originalQuantity(),
                inventoryEntry.originalFromQuantity()
        );
    }

    private MaterialGoodDetails materialGood(UUID materialGoodId) {
        var materialGood = db.selectFrom(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(materialGoodId))
                .fetchSingle().into(MaterialGood.class);
        return new MaterialGoodDetails(materialGoodId, materialGood.name, materialGood.code,
                new StringEntity(materialGood.details.measurementUnit().name(), materialGood.details.measurementUnit().symbol()));
    }

    private String supplierName(UUID supplierId) {
        return db.selectFrom(COMPANY)
                .where(COMPANY.ID.eq(supplierId))
                .fetchSingle().into(Company.class)
                .name();
    }

    private Entity inventoryUnit(UUID id) {
        if (id == null) {
            return null;
        }
        return db.select(INVENTORY_UNIT.ID, INVENTORY_UNIT.NAME)
                .from(INVENTORY_UNIT)
                .where(INVENTORY_UNIT.ID.eq(id))
                .fetchSingleInto(Entity.class);
    }

    public record AdjustOrder(
            String reason,
            @Valid
            List<InventoryAdjustmentOrder.InventoryEntry> inventoryEntries
    ) {
    }

    public record MoveUnitOrder(
            String reason,
            String deliveredBy,
            String receivedBy,
            List<InventoryAdjustmentOrder.InventoryEntry> inventoryEntries
    ) {
    }

    public record AdjustOrderDetails(
            UUID id,
            Instant createTime,
            Instant updateTime,
            String number,
            InventoryAdjustmentOrder.Type type,
            String reason,
            Money totalValue,
            String deliveredBy,
            String receivedBy,
            List<AdjustmentEntry> inventoryEntries
    ) {
    }

    public record AdjustmentEntry(
            MaterialGoodDetails materialGood, UUID locationId, Entity supplier,
            Entity fromUnit, Entity unit, LocalDateTime purchaseDate, LocalDateTime expiryDate,
            Money price, BigDecimal quantity, BigDecimal originalQuantity, BigDecimal originalFromQuantity
    ) {
    }

    public record MaterialGoodDetails(
            UUID id,
            String name,
            String code,
            StringEntity measurementUnit
    ) {
    }
}
