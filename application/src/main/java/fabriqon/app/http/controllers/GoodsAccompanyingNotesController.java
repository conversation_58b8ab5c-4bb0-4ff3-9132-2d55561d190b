package fabriqon.app.http.controllers;

import fabriqon.app.business.inventory.InventoryService;
import fabriqon.app.business.sales.GoodsAccompanyingNote;
import fabriqon.app.business.sales.GoodsAccompanyingNotesService;
import fabriqon.app.common.model.Address;
import fabriqon.app.common.model.Money;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.response.Entity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.constraints.NotNull;
import org.jooq.DSLContext;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.*;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.jooq.impl.DSL.select;

@RestController
@RequestMapping(path = "/goods-accompanying-notes")
public class GoodsAccompanyingNotesController {


    private final DSLContext db;
    private final AccountContext accountContext;
    private final GoodsAccompanyingNotesService goodsAccompanyingNotesService;
    private final InventoryService inventoryService;

    public GoodsAccompanyingNotesController(DSLContext db, AccountContext accountContext,
                                            GoodsAccompanyingNotesService goodsAccompanyingNotesService,
                                            InventoryService inventoryService) {
        this.db = db;
        this.accountContext = accountContext;
        this.goodsAccompanyingNotesService = goodsAccompanyingNotesService;
        this.inventoryService = inventoryService;
    }

    @GetMapping(path = "list")
    public List<GoodsAccompanyingNoteDetails> list(@AuthenticationPrincipal Jwt principal,
                                                   @RequestParam(value = "q", required = false) String textSearch,
                                                   @RequestParam(value = "delegateIds", required = false) List<UUID> delegateIds,
                                                   @RequestParam(value = "customerIds", required = false) List<UUID> customerIds,
                                                   @RequestParam(value = "start", required = false) LocalDate start,
                                                   @RequestParam(value = "end", required = false) LocalDate end) {
        var filter = GOODS_ACCOMPANYING_NOTE.DELETED.isFalse().and(GOODS_ACCOMPANYING_NOTE.OWNER_ID.eq(accountContext.accountId(principal)));
        if (isNotBlank(textSearch)) {
            //todo
        }
        if (isNotEmpty(delegateIds)) {
            filter = filter.and(GOODS_ACCOMPANYING_NOTE.DELEGATE_ID.in(delegateIds));
        }
        if (isNotEmpty(customerIds)) {
            filter = filter.and(GOODS_ACCOMPANYING_NOTE.ID.in(
                    select(SERVICINGORDER_GOODSACCOMPANYINGNOTE.GOODS_ACCOMPANYING_NOTE_ID)
                            .from(SERVICINGORDER_GOODSACCOMPANYINGNOTE)
                    .where(SERVICINGORDER_GOODSACCOMPANYINGNOTE.SERVICING_ORDER_ID.in(
                            select(SERVICING_ORDER.ID).from(SERVICING_ORDER).where(SERVICING_ORDER.SALES_ORDER_ID.in(
                                    select(SALES_ORDER.ID).from(SALES_ORDER).where(SALES_ORDER.CUSTOMER_ID.in(customerIds))
                            ))
                    ))
                            .union(select(SALESORDER_GOODSACCOMPANYINGNOTE.GOODS_ACCOMPANYING_NOTE_ID)
                                    .from(SALESORDER_GOODSACCOMPANYINGNOTE)
                                    .where(SALESORDER_GOODSACCOMPANYINGNOTE.SALES_ORDER_ID.in(select(SALES_ORDER.ID).from(SALES_ORDER).where(SALES_ORDER.CUSTOMER_ID.in(customerIds)))))
                    ));
        }
        if (start != null) {
            filter = filter.and(GOODS_ACCOMPANYING_NOTE.DELIVERY_DATE.greaterOrEqual(start.atStartOfDay()));
        }
        if (end != null) {
            filter = filter.and(GOODS_ACCOMPANYING_NOTE.DELIVERY_DATE.lessOrEqual(end.atStartOfDay()));
        }
        return db.selectFrom(GOODS_ACCOMPANYING_NOTE)
                .where(filter)
                .fetchInto(GoodsAccompanyingNote.class)
                .stream().map(this::details).toList();
    }

    @PostMapping(path = "create")
    public GoodsAccompanyingNoteDetails create(@AuthenticationPrincipal Jwt principal,
                                               @RequestBody @Validated GoodsAccompanyingNoteDefinition create) {
        return details(goodsAccompanyingNotesService.create(accountContext.accountId(principal),
                create.deliveryDate, create.from, create.to, create.delegateId, create.transportRegistrationNumber,
                create.notes, create.items.stream().map(i -> new GoodsAccompanyingNote.Item(i.id(), i.quantity(),
                        inventoryService.getAverageCost(accountContext.accountId(principal), i.id()), null)).toList()));
    }

    @PostMapping(path = "preview")
    public ResponseEntity<String> preview(@AuthenticationPrincipal Jwt principal,
                                          @RequestBody @Validated GoodsAccompanyingNoteDefinition create) {
        return ResponseEntity.ok(goodsAccompanyingNotesService.html(null, new GoodsAccompanyingNote(null, null, null, false,
                accountContext.accountId(principal), null,
                create.deliveryDate, create.from, create.to, create.delegateId, create.transportRegistrationNumber,
                create.notes, create.items.stream()
                .map(i -> new GoodsAccompanyingNote.Item(i.id(), i.quantity(),
                        inventoryService.getAverageCost(accountContext.accountId(principal), i.id()), null))
                .toList())));
    }

    @PutMapping(path = "{id}/update")
    public GoodsAccompanyingNoteDetails update(@AuthenticationPrincipal Jwt principal, @PathVariable("id") UUID id,
                                               @RequestBody GoodsAccompanyingNoteDefinition update) {
        return details(goodsAccompanyingNotesService.update(accountContext.accountId(principal), id,
                new GoodsAccompanyingNote(null, null, null, false,
                        null, null,
                        update.deliveryDate, update.from, update.to, update.delegateId, update.transportRegistrationNumber,
                        update.notes, update.items.stream().map(i -> new GoodsAccompanyingNote.Item(i.id, i.quantity, null, null)).toList())));
    }

    @SuppressWarnings("rawtypes")
    @Operation(responses = {
            @ApiResponse(responseCode = "200", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = GoodsAccompanyingNotesController.GoodsAccompanyingNoteDetails.class)),
                    @Content(mediaType = "application/pdf"),
                    @Content(mediaType = "text/html"),
            })
    })
    @GetMapping(path = "{id}/details",
            produces = {
                    MediaType.TEXT_HTML_VALUE,
                    MediaType.APPLICATION_JSON_VALUE,
                    MediaType.APPLICATION_PDF_VALUE
            }
    )
    public ResponseEntity details(@AuthenticationPrincipal Jwt principal,
                                  @RequestHeader(HttpHeaders.ACCEPT) String acceptHeader,
                                  @PathVariable("id") UUID noteId
    ) {
        var note = db.selectFrom(GOODS_ACCOMPANYING_NOTE)
                .where(GOODS_ACCOMPANYING_NOTE.OWNER_ID.eq(accountContext.accountId(principal)), GOODS_ACCOMPANYING_NOTE.ID.eq(noteId))
                .fetchSingleInto(GoodsAccompanyingNote.class);
        if (acceptHeader.contains(MediaType.TEXT_HTML_VALUE)) {
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(goodsAccompanyingNotesService.html(null, note));
        } else if (acceptHeader.contains(MediaType.APPLICATION_PDF_VALUE)) {
            var pdf = goodsAccompanyingNotesService.pdf(goodsAccompanyingNotesService.html(null, note));
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                            + "goods_accompanying_note_" + DateTimeFormatter.ofPattern("yyyy-MM-dd")
                            .format(LocalDate.ofInstant(note.createTime(), ZoneId.systemDefault())) + ".pdf")
                    .body(pdf);
        } else {
            return new ResponseEntity<>(details(note), HttpStatus.OK);
        }
    }

    private GoodsAccompanyingNoteDetails details(GoodsAccompanyingNote note) {
        var servicingOrder = db.select(SERVICING_ORDER.ID, SERVICING_ORDER.SALES_ORDER_ID, SERVICING_ORDER.NUMBER)
                .from(SERVICING_ORDER)
                .where(SERVICING_ORDER.ID.eq(select(SERVICINGORDER_GOODSACCOMPANYINGNOTE.SERVICING_ORDER_ID).from(SERVICINGORDER_GOODSACCOMPANYINGNOTE).where(SERVICINGORDER_GOODSACCOMPANYINGNOTE.GOODS_ACCOMPANYING_NOTE_ID.eq(note.id()))))
                .fetchOptional();
        var salesOrder = db.select(SALES_ORDER.ID, SALES_ORDER.CUSTOMER_ID, SALES_ORDER.NUMBER)
                .from(SALES_ORDER)
                .where(SALES_ORDER.ID.eq(select(SALESORDER_GOODSACCOMPANYINGNOTE.SALES_ORDER_ID).from(SALESORDER_GOODSACCOMPANYINGNOTE).where(SALESORDER_GOODSACCOMPANYINGNOTE.GOODS_ACCOMPANYING_NOTE_ID.eq(note.id()))))
                .fetchOptional()
                .or(() -> servicingOrder.flatMap(r -> db.select(SALES_ORDER.ID, SALES_ORDER.CUSTOMER_ID, SALES_ORDER.NUMBER)
                        .from(SALES_ORDER)
                        .where(SALES_ORDER.ID.eq(r.value2()))
                        .fetchOptional()));
        return new GoodsAccompanyingNoteDetails(
                note.id(),
                note.createTime(),
                salesOrder.map(r -> customerFromSo(r.value2())).orElse(null),
                salesOrder.map(r -> new Entity(r.value1(), r.value3())).orElse(null),
                servicingOrder.map(r -> new Entity(r.value1(), r.value3())).orElse(null),
                note.number(),
                note.deliveryDate(),
                note.from(),
                note.to(),
                note.delegateId() != null ? db.select(USERS.ID, USERS.NAME).from(USERS).where(USERS.ID.eq(note.delegateId())).fetchSingleInto(Entity.class) : null,
                note.transportRegistrationNumber(),
                note.notes(),
                note.items().stream()
                        .map(item -> new GoodsAccompanyingNoteDetails.Item(item.id(), itemName(item.id()), item.quantity(), item.price()))
                        .toList()
        );
    }

    private String itemName(UUID id) {
        return db.select(MATERIAL_GOOD.NAME)
                .from(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(id))
                .fetchSingleInto(String.class);
    }

    private Entity customerFromSo(UUID customerId) {
        return db.select(COMPANY.ID, COMPANY.COMPANY_NAME)
                .from(COMPANY)
                .where(COMPANY.ID.eq(customerId))
                .fetchSingleInto(Entity.class);
    }

    public record GoodsAccompanyingNoteDefinition(
            @NotNull
            LocalDate deliveryDate,
            Address from,
            Address to,
            UUID delegateId,
            String transportRegistrationNumber,
            String notes,
            @NotNull
            List<GoodsAccompanyingNoteDefinitionItem> items
    ) {
        public record GoodsAccompanyingNoteDefinitionItem(
                UUID id,
                BigDecimal quantity
        ) {
        }
    }

    public record GoodsAccompanyingNoteDetails(
            UUID id,
            Instant createTime,
            Entity customer,
            Entity salesOrder,
            Entity servicingOrder,
            String number,
            LocalDate deliveryDate,
            Address from,
            Address to,
            Entity delegate,
            String transportRegistrationNumber,
            String notes,
            List<Item> items
    ) {
        public record Item(
                UUID id,
                String name,
                BigDecimal quantity,
                Money price
        ) {
        }

    }
}
