package fabriqon.app.http.controllers.fabrication;

import fabriqon.app.business.manufacturing.MaterialIssueNote;
import fabriqon.app.business.services.ServicingOrder;
import fabriqon.app.business.services.ServicingService;
import fabriqon.app.common.model.DevicePairingTokenType;
import fabriqon.app.http.controllers.services.ServicingOrdersController;
import fabriqon.pdf.HtmlToPdfConverter;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.*;

@RestController
@RequestMapping(path = "/fabrication/servicing/orders")
public class FabricationServicingOrdersController {

    private final DSLContext db;
    private final ServicingService servicingService;
    private final HtmlToPdfConverter htmlToPdfConverter;

    @Autowired
    public FabricationServicingOrdersController(DSLContext db, ServicingService servicingService, HtmlToPdfConverter htmlToPdfConverter) {
        this.db = db;
        this.servicingService = servicingService;
        this.htmlToPdfConverter = htmlToPdfConverter;
    }

    @PostMapping(path = "{id}/update")
    public void update(
            @RequestHeader("X-Auth") String xAuthHeader,
            @PathVariable(value = "id") UUID orderId,
            @Validated @RequestBody ServicingOrdersController.UpdateServicingOrder update) {
        var employeeId = employeeIdFromAuth(xAuthHeader);
        var ownerId = ownerIdForEmployee(employeeId);
        checkAccess(ownerId, employeeId, orderId);
        servicingService.updateServicingOrder(
                ownerId,
                orderId,
                update.assignedTo(),
                update.productionDeadline(),
                update.quantity(),
                update.manufacturingOperations(),
                update.materials() != null
                        ? update.materials().stream().map(material -> new ServicingOrder.Material(List.of(material.id()), material.quantity(), material.usedQuantity())).toList()
                        : null,
                update.notes()
        );
    }

    @PostMapping(path = "{id}/in-progress")
    public void inProgress(
            @RequestHeader("X-Auth") String xAuthHeader,
            @PathVariable(value = "id") UUID orderId) {
        var employeeId = employeeIdFromAuth(xAuthHeader);
        var ownerId = ownerIdForEmployee(employeeId);
        checkAccess(ownerId, employeeId, orderId); 
        servicingService.inProgress(ownerId, orderId);
    }

    @PostMapping(path = "{id}/executed")
    public void executed(
            @RequestHeader("X-Auth") String xAuthHeader,
            @PathVariable(value = "id") UUID orderId,
            @RequestBody ServicingOrdersController.Signatures signatures) {
        var employeeId = employeeIdFromAuth(xAuthHeader);
        var ownerId = ownerIdForEmployee(employeeId);
        checkAccess(ownerId, employeeId, orderId);
        servicingService.executed(ownerId, orderId, 
                signatures.base64ClientSignature(), signatures.clientRepresentative(), signatures.base64WorkerSignature());
    }

    @PostMapping(path = "{id}/closed")
    public MaterialIssueNote closed(
            @RequestHeader("X-Auth") String xAuthHeader,
            @PathVariable(value = "id") UUID orderId,
            @Validated @RequestBody ServicingOrdersController.ServicingOrderDone orderDone) {
        var employeeId = employeeIdFromAuth(xAuthHeader);
        var ownerId = ownerIdForEmployee(employeeId);
        checkAccess(ownerId, employeeId, orderId);
        return servicingService.closed(
                ownerId, orderId,
                orderDone.date(),
                orderDone.inventoryManager(),
                orderDone.worker(),
                orderDone.materials());
    }

    @PostMapping(path = "{id}/blocked")
    public void blocked(
            @RequestHeader("X-Auth") String xAuthHeader,
            @PathVariable(value = "id") UUID orderId,
            @RequestBody ServicingOrdersController.BlockedReasonDefinition blockedReason) {
        var employeeId = employeeIdFromAuth(xAuthHeader);
        var ownerId = ownerIdForEmployee(employeeId);
        checkAccess(ownerId, employeeId, orderId);
        servicingService.blocked(ownerId, orderId,
                blockedReason.blockedReason());
    }

    @GetMapping(path = "{id}/service-report", produces = {MediaType.TEXT_HTML_VALUE, MediaType.APPLICATION_PDF_VALUE})
    public ResponseEntity serviceReport(@RequestHeader("X-Auth") String xAuthHeader,
                                        @RequestHeader(HttpHeaders.ACCEPT) String acceptHeader,
                                        @PathVariable("id") UUID orderId) {
        var employeeId = employeeIdFromAuth(xAuthHeader);
        var ownerId = ownerIdForEmployee(employeeId);
        checkAccess(ownerId, employeeId, orderId);
        var serviceReport = servicingService.serviceReport(ownerId, orderId);
        if (acceptHeader.contains(MediaType.TEXT_HTML_VALUE)) {
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(serviceReport.a());
        } else if (acceptHeader.contains(MediaType.APPLICATION_PDF_VALUE)) {
            var pdf = htmlToPdfConverter.convert(serviceReport.a());
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                            + "service_report_" + serviceReport.b() + "_"
                            + DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.ofInstant(serviceReport.c(), ZoneId.systemDefault()))
                            + ".pdf")
                    .body(pdf);
        }
        return ResponseEntity.status(HttpStatus.UNSUPPORTED_MEDIA_TYPE).build();
    }

    @SuppressWarnings("rawtypes")
    @PostMapping(path = "{id}/preview-service-report", produces = {MediaType.TEXT_HTML_VALUE, MediaType.APPLICATION_PDF_VALUE})
    public ResponseEntity previewServiceReport(@RequestHeader("X-Auth") String xAuthHeader,
                                               @RequestHeader(HttpHeaders.ACCEPT) String acceptHeader,
                                               @PathVariable("id") UUID orderId,
                                               @RequestBody ServicingOrdersController.Signatures signatures) {
        var employeeId = employeeIdFromAuth(xAuthHeader);
        var ownerId = ownerIdForEmployee(employeeId);
        checkAccess(ownerId, employeeId, orderId);
        var serviceReport = servicingService.previewServiceReport(ownerId, orderId, signatures.base64ClientSignature(), signatures.clientRepresentative(), signatures.base64WorkerSignature());
        if (acceptHeader.contains(MediaType.TEXT_HTML_VALUE)) {
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(serviceReport.a());
        } else if (acceptHeader.contains(MediaType.APPLICATION_PDF_VALUE)) {
            var pdf = htmlToPdfConverter.convert(serviceReport.a());
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                            + "service_report_" + serviceReport.b() + "_"
                            + DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.ofInstant(serviceReport.c(), ZoneId.systemDefault()))
                            + ".pdf")
                    .body(pdf);
        }
        return ResponseEntity.status(HttpStatus.UNSUPPORTED_MEDIA_TYPE).build();
    }

    private void checkAccess(UUID ownerId, UUID userId, UUID orderId) {
        db.selectFrom(SERVICING_ORDER)
                .where(SERVICING_ORDER.OWNER_ID.eq(ownerId), SERVICING_ORDER.ID.eq(orderId), SERVICING_ORDER.ASSIGNED_TO.eq(userId))
                .fetchSingle();
    }

    private UUID employeeIdFromAuth(String authHeader) {
        return db.select(DEVICE_PAIRING_TOKEN.USER_ID)
                .from(DEVICE_PAIRING_TOKEN)
                .where(DEVICE_PAIRING_TOKEN.TOKEN.eq(authHeader), DEVICE_PAIRING_TOKEN.TOKEN_TYPE.eq(DevicePairingTokenType.ACCESS.name()))
                .fetchSingleInto(UUID.class);
    }

    private UUID ownerIdForEmployee(UUID employeeId) {
        return db.select(USERS.OWNER_ID)
                .from(USERS)
                .where(USERS.ID.eq(employeeId))
                .fetchSingleInto(UUID.class);
    }
}
