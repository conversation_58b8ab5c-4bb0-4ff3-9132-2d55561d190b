package fabriqon.app.http.controllers;


import fabriqon.app.business.locations.LocationService;
import fabriqon.app.common.model.Location;
import fabriqon.app.config.security.AccountContext;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static fabriqon.jooq.classes.Tables.LOCATION;

@RestController
@RequestMapping(path = "/locations")
public class LocationsController {
    private final AccountContext accountContext;
    private final LocationService locationService;
    private final DSLContext db;

    @Autowired
    public LocationsController(final AccountContext accountContext,
                               final LocationService locationService,
                               final DSLContext db) {
        this.accountContext = accountContext;
        this.locationService = locationService;
        this.db = db;
    }

    @PostMapping(path = "create")
    public Location create(@AuthenticationPrincipal Jwt principal, @Validated @RequestBody CreateLocation create) {
        return locationService.create(accountContext.accountId(principal), create.name());
    }

    @GetMapping(path = "list")
    public List<Location> list(@AuthenticationPrincipal Jwt principal) {
        var accountId = accountContext.accountId(principal);
        return db.select()
                .from(LOCATION)
                .where(LOCATION.OWNER_ID.eq(accountId))
                .fetchInto(Location.class);
    }

    public record CreateLocation(
            String name
    ) {
    }
}