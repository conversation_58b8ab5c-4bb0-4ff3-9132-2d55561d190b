package fabriqon.app.http.controllers.manufacturing;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.manufacturing.MaterialIssueNote;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.common.model.Money;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.controllers.integrations.WinmentorExport;
import fabriqon.app.http.response.Entity;
import fabriqon.jooq.JooqJsonbFunctions;
import fabriqon.misc.Tuple;
import fabriqon.pdf.HtmlToPdfConverter;
import fabriqon.templates.Localizer;
import fabriqon.templates.Templates;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static fabriqon.jooq.classes.Tables.*;
import static java.lang.String.format;
import static java.util.stream.Collectors.toList;
import static org.jooq.impl.DSL.select;

@RestController
@RequestMapping(path = "/manufacturing/material-issue-notes")
public class MaterialIssueNotesController {

    private static final String MEDIA_TYPE_BON_CONSUM_WINMENTOR = "application/bonconsum-winmentor+txt";
    private static final String MEDIA_TYPE_BON_CONSUM_WINMENTOR_ZIP = "application/bonconsum-winmentor+zip";

    private final AccountContext accountContext;
    private final DSLContext db;
    private final Templates templates;
    private final HtmlToPdfConverter htmlToPdfConverter;

    @Autowired
    public MaterialIssueNotesController(AccountContext accountContext, DSLContext db, Templates templates, HtmlToPdfConverter htmlToPdfConverter) {
        this.accountContext = accountContext;
        this.db = db;
        this.templates = templates;
        this.htmlToPdfConverter = htmlToPdfConverter;
    }

    @SuppressWarnings("rawtypes")
    @GetMapping(path = "list",
            produces = {
                    MediaType.APPLICATION_JSON_VALUE,
                    MediaType.APPLICATION_PDF_VALUE + "+zip",
                    MEDIA_TYPE_BON_CONSUM_WINMENTOR_ZIP
            })
    public ResponseEntity list(
            @AuthenticationPrincipal Jwt principal,
            @RequestHeader(HttpHeaders.ACCEPT) String acceptHeader,
            @RequestParam(value = "start", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate start,
            @RequestParam(value = "end", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate end)
            throws IOException {

        var s = start != null ? start.atStartOfDay() : null;
        var e = end != null ? end.atTime(23, 59, 59) : null;
        var filter = MATERIAL_ISSUE_NOTE.OWNER_ID.eq(accountContext.accountId(principal));
        if (s != null) {
            filter = filter.and(MATERIAL_ISSUE_NOTE.DATE.greaterOrEqual(s));
        }
        if (e != null) {
            filter = filter.and(MATERIAL_ISSUE_NOTE.DATE.lessOrEqual(e));
        }
        var notes = db.selectFrom(MATERIAL_ISSUE_NOTE)
                .where(filter)
                .fetchInto(MaterialIssueNote.class);

        if (acceptHeader.contains(MediaType.APPLICATION_PDF_VALUE + "+zip")) {
            var zip = new ByteArrayOutputStream();
            ZipOutputStream zipOut = new ZipOutputStream(zip);
            notes.forEach(note -> {
                try {
                    ZipEntry zipEntry = new ZipEntry("material_issue_note_" +
                            DateTimeFormatter.ofPattern("yyyy-MM-dd").format(note.date()) + "_" + note.number() + ".pdf");
                    zipOut.putNextEntry(zipEntry);
                    zipOut.write(pdf(html(note)));
                } catch (IOException ex) {
                    throw new RuntimeException(ex);
                }
            });
            zipOut.close();
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=material_issue_notes.zip")
                    .body(zip.toByteArray());
        } else if (acceptHeader.contains(MEDIA_TYPE_BON_CONSUM_WINMENTOR_ZIP)) {
            var zip = new ByteArrayOutputStream();
            ZipOutputStream zipOut = new ZipOutputStream(zip);
            notes.stream()
                    .collect(Collectors.groupingBy(note -> Tuple.of(note.date().getYear(), note.date().getMonth()), toList()))
                    .forEach((key, list) -> {
                        var export = new WinmentorExport(db, null, templates).transformMaterialIssueNotes(list);
                        var fileName = format("bonuri_consum_winmentor_%1$s_%2$s.txt", key.a(), key.b());
                        try {
                            ZipEntry zipEntry = new ZipEntry(fileName);
                            zipOut.putNextEntry(zipEntry);
                            zipOut.write(export.getBytes());
                        } catch (IOException ex) {
                            throw new RuntimeException(ex);
                        }
                    });
            zipOut.close();
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=bonuri_consum_winmentor.zip")
                    .body(zip.toByteArray());
        } else {
            return new ResponseEntity<>(notes.stream().map(this::details).toList(), HttpStatus.OK);
        }
    }

    @SuppressWarnings("rawtypes")
    @Operation(responses = {
            @ApiResponse(responseCode = "200", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = MaterialIssueNoteDetails.class)),
                    @Content(mediaType = "application/pdf"),
                    @Content(mediaType = "text/html")
            }),
    })
    @GetMapping(path = "{id}/details",
            produces = {
                    MediaType.TEXT_HTML_VALUE,
                    MediaType.APPLICATION_JSON_VALUE,
                    MediaType.APPLICATION_PDF_VALUE
            }
    )
    public ResponseEntity details(@AuthenticationPrincipal Jwt principal,
                                  @RequestHeader(HttpHeaders.ACCEPT) String acceptHeader,
                                  @PathVariable("id") UUID noteId
    ) {
        var materialIssueNote = db.selectFrom(MATERIAL_ISSUE_NOTE)
                .where(MATERIAL_ISSUE_NOTE.OWNER_ID.eq(accountContext.accountId(principal)), MATERIAL_ISSUE_NOTE.ID.eq(noteId))
                .fetchSingleInto(MaterialIssueNote.class);
        if (acceptHeader.contains(MediaType.TEXT_HTML_VALUE)) {
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(html(materialIssueNote));
        } else if (acceptHeader.contains(MediaType.APPLICATION_PDF_VALUE)) {
            var pdf = pdf(html(materialIssueNote));
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                            + "material_issue_note_" + DateTimeFormatter.ofPattern("yyyy-MM-dd")
                            .format(LocalDate.ofInstant(materialIssueNote.createTime(), ZoneId.systemDefault())) + ".pdf")
                    .body(pdf);
        } else {
            return new ResponseEntity<>(details(materialIssueNote), HttpStatus.OK);
        }
    }

    private MaterialIssueNoteDetails details(MaterialIssueNote note) {
        return new MaterialIssueNoteDetails(
                note.id(),
                note.createTime(),
                note.ownerId(),
                new Entity(note.manufacturingOrderId(), orderNumber(note.manufacturingOrderId(), note.servicingOrderId())),
                note.number(),
                note.date(),
                note.details().inventoryManager(),
                note.details().worker(),
                note.materials().stream().map(m -> new MaterialIssueNoteDetails.Material(new Entity(m.id(), materialName(m.id())), m.quantity(), m.totalCost(), m.wastedQuantity())).toList());
    }

    private String html(MaterialIssueNote note) {
        var account = account(note.ownerId());
        var localizer = new Localizer(new Locale(account.information().address().country()));
        var currency = note.materials().stream()
                .filter(item -> item.totalCost() != null)
                .map(item -> item.totalCost().currency())
                .findFirst().orElse(account.settings().general().defaultCurrency());
        var product = product(note.manufacturingOrderId(), note.servicingOrderId());

        var itemIndexer = new AtomicInteger();
        var noteMap = new HashMap<>();
        noteMap.put("number", note.number());
        noteMap.put("date", localizer.localizeDate(note.date()));
        noteMap.put("manufacturingOrderNumber", orderNumber(note.manufacturingOrderId(), note.servicingOrderId()));
        noteMap.put("productName", product.a());
        noteMap.put("productSku", product.b());
        noteMap.put("productQuantity", manufacturedQuantity(note.manufacturingOrderId(), note.servicingOrderId()).stripTrailingZeros().toPlainString()
                + " " + localizer.localizedValue(product.c()));
        noteMap.put("businessUnit", account.name());
        noteMap.put("materials", note.materials().stream()
                .map(item -> {
                    return Map.of(
                            "index", itemIndexer.incrementAndGet(),
                            "name", materialName(item.id()),
                            "measurementUnit", localizer.localizedValue(measurementUnit(item.id()).symbol()),
                            "neededQuantity", item.quantity(),
                            "usedQuantity", item.quantity(),
                            "price", item.totalCost().divide(item.quantity()).displayAmount(),
                            "value", item.totalCost().displayAmount()
                    );
                })
                .toList());
        noteMap.put("totalValue", note.materials().stream().map(MaterialIssueNote.Material::totalCost).reduce(Money::add).orElse(new Money(0, currency)).displayAmount());
        noteMap.put("inventoryManager", note.details().inventoryManager());
        noteMap.put("worker", note.details().worker());

        return templates.render("pdf/material_issue_note.html",
                Map.of(
                        "note", noteMap,
                        "labels", localizer.labels(),
                        "base64Logo", account.logo() != null ? account.logo() : "",
                        "currency", currency
                )
        );
    }

    private byte[] pdf(String html) {
        return htmlToPdfConverter.convert(html);
    }

    private String orderNumber(UUID orderId, UUID servicingOrderId) {
        if (orderId != null) {
            return db.select(MANUFACTURING_ORDER.NUMBER)
                    .from(MANUFACTURING_ORDER)
                    .where(MANUFACTURING_ORDER.ID.eq(orderId))
                    .fetchSingleInto(String.class);
        } else {
            return db.select(SERVICING_ORDER.NUMBER)
                    .from(SERVICING_ORDER)
                    .where(SERVICING_ORDER.ID.eq(servicingOrderId))
                    .fetchSingleInto(String.class);
        }
    }

    private String materialName(UUID materialId) {
        return db.select(MATERIAL_GOOD.NAME)
                .from(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(materialId))
                .fetchSingleInto(String.class);
    }

    private MeasurementUnit measurementUnit(UUID materialId) {
        return db.select(MATERIAL_GOOD.DETAILS)
                .from(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(materialId))
                .fetchSingleInto(MaterialGood.Details.class)
                .measurementUnit();
    }

    private BigDecimal manufacturedQuantity(UUID orderId, UUID servicingOrderId) {
        if (orderId != null) {
            return db.select(MANUFACTURING_ORDER.QUANTITY)
                    .from(MANUFACTURING_ORDER)
                    .where(MANUFACTURING_ORDER.ID.eq(orderId))
                    .fetchSingleInto(BigDecimal.class);
        } else {
            return db.select(SERVICING_ORDER.QUANTITY)
                    .from(SERVICING_ORDER)
                    .where(SERVICING_ORDER.ID.eq(servicingOrderId))
                    .fetchSingleInto(BigDecimal.class);
        }
    }

    private Tuple.Tuple3<String, String, String> product(UUID manufacturingOrderId, UUID servicingOrderId) {
        if (manufacturingOrderId != null) {
            return db.select(MATERIAL_GOOD.NAME, MATERIAL_GOOD.CODE, JooqJsonbFunctions.stringField(MATERIAL_GOOD.DETAILS, "measurementUnit").as("measurement_unit"))
                    .from(MATERIAL_GOOD)
                    .where(MATERIAL_GOOD.ID.eq(
                            select(MANUFACTURING_ORDER.PRODUCT_ID)
                                    .from(MANUFACTURING_ORDER)
                                    .where(MANUFACTURING_ORDER.ID.eq(manufacturingOrderId))))
                    .fetchOptional()
                    .map(r -> Tuple.of(r.value1(), r.value2(), r.value3()))
                    .orElseThrow();
        } else {
            return db.select(SERVICE_TEMPLATE.NAME, SERVICE_TEMPLATE.MEASUREMENT_UNIT)
                    .from(SERVICE_TEMPLATE)
                    .where(SERVICE_TEMPLATE.ID.eq(select(SERVICING_ORDER.SERVICE_ID)
                            .from(SERVICING_ORDER)
                            .where(SERVICING_ORDER.ID.eq(servicingOrderId))))
                    .fetchOptional()
                    .map(r -> Tuple.of(r.value1(), "", r.value2()))
                    .orElseThrow();
        }
    }

    private Account account(UUID id) {
        return db.selectFrom(ACCOUNT).where(ACCOUNT.ID.eq(id)).fetchSingleInto(Account.class);
    }

}
