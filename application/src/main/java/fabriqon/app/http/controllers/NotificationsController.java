package fabriqon.app.http.controllers;

import fabriqon.Notification;
import fabriqon.app.business.notifications.NotificationService;
import fabriqon.app.config.security.AccountContext;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.*;

@RestController
@RequestMapping(path = "/notifications")
public class NotificationsController {
    private final AccountContext accountContext;
    private final NotificationService notificationService;
    private final DSLContext db;

    @Autowired
    public NotificationsController(final AccountContext accountContext,
                                   final NotificationService notificationService,
                                   final DSLContext db) {
        this.accountContext = accountContext;
        this.notificationService = notificationService;
        this.db = db;
    }

    @GetMapping(path = "list")
    public Notifications list(@AuthenticationPrincipal Jwt principal,
                                          @RequestParam(value = "resolved", required = false) Boolean resolved) {
        var filter = NOTIFICATION.OWNER_ID.eq(accountContext.accountId(principal)).and(NOTIFICATION.DELETED.isFalse())
                .and(NOTIFICATION.TARGET_USER_ID.isNull().or(NOTIFICATION.TARGET_USER_ID.eq(accountContext.userId(principal))));
        if (resolved != null) {
            filter = resolved ? filter.and(NOTIFICATION.RESOLVED_AT.isNotNull()) : filter.and(NOTIFICATION.RESOLVED_AT.isNull());
        }
        return new Notifications(
                db.fetchCount(NOTIFICATION,
                        NOTIFICATION.OWNER_ID.eq(accountContext.accountId(principal)),
                        NOTIFICATION.DELETED.isFalse(),
                        NOTIFICATION.RESOLVED_AT.isNull(),
                        NOTIFICATION.TARGET_USER_ID.isNull().or(NOTIFICATION.TARGET_USER_ID.eq(accountContext.userId(principal)))),
                db.select()
                .from(NOTIFICATION)
                .where(filter)
                .orderBy(NOTIFICATION.CREATE_TIME.desc())
                .fetchInto(Notification.class)
                .stream().map(n -> details(n, accountContext.userId(principal))).toList()
        );
    }

    @PostMapping(path = "{id}/resolved")
    public void resolved(@AuthenticationPrincipal Jwt principal,
                       @PathVariable("id") UUID notificationId) {
        notificationService.resolvedBy(accountContext.accountId(principal), notificationId, accountContext.userId(principal));
    }

    @PostMapping(path = "{id}/read")
    public void read(@AuthenticationPrincipal Jwt principal,
                         @PathVariable("id") UUID notificationId) {
        notificationService.readBy(accountContext.accountId(principal), notificationId, accountContext.userId(principal));
    }

    @Transactional
    @PostMapping("firebase/register-token/{token}")
    public void registerToken(@AuthenticationPrincipal Jwt principal, @PathVariable("token") String token) {
        //clean existing tokens for user
        var userId = accountContext.userId(principal);
        db.insertInto(FIREBASE_REGISTRATION_TOKEN)
                .set(FIREBASE_REGISTRATION_TOKEN.ID, UUID.randomUUID())
                .set(FIREBASE_REGISTRATION_TOKEN.OWNER_ID, accountContext.accountId(principal))
                .set(FIREBASE_REGISTRATION_TOKEN.TOKEN, token)
                .set(FIREBASE_REGISTRATION_TOKEN.USER_ID, userId)
                .onConflict(FIREBASE_REGISTRATION_TOKEN.USER_ID).doUpdate().set(FIREBASE_REGISTRATION_TOKEN.TOKEN, token)
                .execute();
    }

    private NotificationDetails details(Notification notification, UUID userId) {
        return new NotificationDetails(
                notification.id(),
                notification.createTime(),
                notification.type(),
                notification.section(),
                notification.targetEntityId(),
                notification.resolvedAt(),
                notification.resolvedBy() != null ? resolvedBy(notification.resolvedBy()) : null,
                isReadByUser(notification.id(), userId),
                notification.triggeredBy(),
                readBy(notification.id()),
                notification.details()
        );
    }

    private List<String> readBy(UUID notificationId) {
        return db.select(USERS.NAME).from(USERS)
                .join(NOTIFICATION_READ_BY).on(NOTIFICATION_READ_BY.USER_ID.eq(USERS.ID))
                .join(NOTIFICATION).on(NOTIFICATION_READ_BY.NOTIFICATION_ID.eq(NOTIFICATION.ID))
                .where(NOTIFICATION.ID.eq(notificationId))
                .fetchInto(String.class);
    }

    private boolean isReadByUser(UUID id, UUID userId) {
        return db.fetchCount(NOTIFICATION_READ_BY, NOTIFICATION_READ_BY.NOTIFICATION_ID.eq(id).and(NOTIFICATION_READ_BY.USER_ID.eq(userId))) > 0;
    }

    private String resolvedBy(UUID resolvedBy) {
        return db.select(USERS.NAME).from(USERS).where(USERS.ID.eq(resolvedBy)).fetchSingleInto(String.class);
    }

    public record Notifications(
            int unresolved,
            List<NotificationDetails> notifications
    ) {
    }
    public record NotificationDetails(
            UUID id,
            Instant createTime,
            Notification.Type type,
            Notification.Section section,
            UUID targetEntityId,
            LocalDateTime resolvedAt,
            String resolvedBy,
            boolean read,
            String triggeredBy,
            List<String> readBy,
            Map<String, Object> details
    ) {
    }

}