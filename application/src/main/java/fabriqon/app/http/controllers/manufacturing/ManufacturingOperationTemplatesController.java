package fabriqon.app.http.controllers.manufacturing;

import fabriqon.app.business.manufacturing.ManufacturingOperationService;
import fabriqon.app.business.manufacturing.ManufacturingOperationTemplate;
import fabriqon.app.common.model.Money;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.response.Entity;
import fabriqon.misc.Tuple;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.*;
import static org.apache.commons.lang3.BooleanUtils.isTrue;

@RestController
@RequestMapping(path = "/manufacturing/operations/templates")
public class ManufacturingOperationTemplatesController {
    private final AccountContext accountContext;
    private final ManufacturingOperationService manufacturingOperationService;
    private final DSLContext db;

    @Autowired
    public ManufacturingOperationTemplatesController(final AccountContext accountContext,
                                                     final ManufacturingOperationService manufacturingOperationService,
                                                     final DSLContext db) {
        this.accountContext = accountContext;
        this.manufacturingOperationService = manufacturingOperationService;
        this.db = db;
    }

    @PostMapping(path = "create")
    public ManufacturingOperationTemplateDetails create(@AuthenticationPrincipal Jwt principal, @Validated @RequestBody OperationDefinition create) {
        return details(manufacturingOperationService.create(accountContext.accountId(principal),
                create.workstationIds, create.employees.stream().map(op -> Tuple.of(op.id, op.preferential)).toList(),
                create.name(),
                new ManufacturingOperationTemplate.Details(isTrue(create.parallelizable), create.costPerHour)));
    }

    @PostMapping(path = "{id}/update")
    public ManufacturingOperationTemplateDetails update(@AuthenticationPrincipal Jwt principal,
                                                        @PathVariable(value = "id") UUID operationId,
                                                        @Validated @RequestBody OperationDefinition update) {
        return details(manufacturingOperationService.update(accountContext.accountId(principal), operationId,
                update.workstationIds, update.employees.stream().map(op -> Tuple.of(op.id, op.preferential)).toList(),
                update.name(),
                update.parallelizable,
                update.costPerHour
                ));
    }

    @DeleteMapping(path = "{id}/delete")
    public void delete(@AuthenticationPrincipal Jwt principal,
                       @PathVariable(value = "id") UUID operationId) {
        manufacturingOperationService.delete(accountContext.accountId(principal), operationId);
    }

    @GetMapping(path = "list")
    public List<ManufacturingOperationTemplateDetails> list(@AuthenticationPrincipal Jwt principal) {
        var accountId = accountContext.accountId(principal);
        return db.select()
                .from(MANUFACTURING_OPERATION_TEMPLATE)
                .where(MANUFACTURING_OPERATION_TEMPLATE.OWNER_ID.eq(accountId), MANUFACTURING_OPERATION_TEMPLATE.DELETED.isFalse())
                .orderBy(MANUFACTURING_OPERATION_TEMPLATE.NAME)
                .fetchInto(ManufacturingOperationTemplate.class)
                .stream().map(this::details).toList();
    }

    @GetMapping(path = "{id}/details")
    public ManufacturingOperationTemplateDetails details(@AuthenticationPrincipal Jwt principal,
                                                         @PathVariable("id") UUID templateId) {
        return details(db.selectFrom(MANUFACTURING_OPERATION_TEMPLATE)
                .where(MANUFACTURING_OPERATION_TEMPLATE.OWNER_ID.eq(accountContext.accountId(principal)),
                        MANUFACTURING_OPERATION_TEMPLATE.ID.eq(templateId))
                .fetchSingleInto(ManufacturingOperationTemplate.class));
    }

    private ManufacturingOperationTemplateDetails details(ManufacturingOperationTemplate manufacturingOperationTemplate) {
        return new ManufacturingOperationTemplateDetails(
                manufacturingOperationTemplate.id(),
                manufacturingOperationTemplate.createTime(),
                manufacturingOperationTemplate.updateTime(),
                manufacturingOperationTemplate.name(),
                manufacturingOperationTemplate.details() != null ? manufacturingOperationTemplate.details().costPerHour() : null,
                workstations(manufacturingOperationTemplate.id()),
                employees(manufacturingOperationTemplate.id())
        );
    }

    private List<EmployeeDetails> employees(UUID templateId) {
        return db.select(USERS.ID, USERS.NAME, EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.PREFERENTIAL)
                .from(USERS)
                .join(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE).on(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.USER_ID.eq(USERS.ID))
                .where(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_OPERATION_TEMPLATE_ID.eq(templateId))
                .fetchInto(EmployeeDetails.class);
    }

    private List<Entity> workstations(UUID templateId) {
        return db.select(MANUFACTURING_WORKSTATION.ID, MANUFACTURING_WORKSTATION.NAME)
                .from(MANUFACTURING_WORKSTATION)
                .join(MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE).on(MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_WORKSTATION_ID.eq(MANUFACTURING_WORKSTATION.ID))
                .where(MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_OPERATION_TEMPLATE_ID.eq(templateId))
                .fetchInto(Entity.class);
    }

    public record OperationDefinition(
            String name,
            List<UUID> workstationIds,
            List<EmployeeManufacturingOperation> employees,
            Integer durationInMinutes,
            Boolean parallelizable,
            Money costPerHour
    ) {
    }

    public record EmployeeManufacturingOperation(
            UUID id,
            boolean preferential
    ) { }

    public record ManufacturingOperationTemplateDetails(
            UUID id,
            Instant createTime,
            Instant updateTime,
            String name,
            Money costPerHour,
            List<Entity> workstations,
            List<EmployeeDetails> employees
    ) {
    }

    public record EmployeeDetails(
            UUID id,
            String name,
            boolean preferential
    ) {}
}