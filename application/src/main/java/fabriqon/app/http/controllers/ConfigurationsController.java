package fabriqon.app.http.controllers;

import fabriqon.app.business.financial.SystemCurrencies;
import fabriqon.app.business.financial.fx.ExchangeRatesProvider;
import fabriqon.app.common.model.MeasurementUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static fabriqon.app.business.financial.SystemCurrencies.SYSTEM_CURRENCIES;
import static java.util.stream.Collectors.toMap;

@RestController
@RequestMapping(path = "/configurations")
public class ConfigurationsController {

    private final ExchangeRatesProvider exchangeRatesProvider;

    @Autowired
    public ConfigurationsController(ExchangeRatesProvider exchangeRatesProvider) {
        this.exchangeRatesProvider = exchangeRatesProvider;
    }

    @GetMapping(path = "measurement-units")
    public Map<String, String> measurementUnits() {
        return Arrays.stream(MeasurementUnit.values())
                .collect(toMap(Enum::name, MeasurementUnit::symbol));
    }

    @SuppressWarnings("rawtypes")
    @GetMapping(path = "currencies")
    public Map<String, Map> currencies() {
        return SYSTEM_CURRENCIES.stream()
                .collect(Collectors.toMap(Currency::getCurrencyCode,
                        currency -> Map.of(
                                "displayName", currency.getDisplayName(),
                                "symbol", currency.getSymbol(),
                                "numericCode", currency.getNumericCodeAsString(),
                                "defaultFractionDigits", currency.getDefaultFractionDigits()
                        )));
    }

    @GetMapping(path = "currencies/exchange-rates/from/RON")
    public Map<Currency, BigDecimal> currenciesExchangeRatesFromRON(
            @RequestParam(required = false) LocalDate date
    ) {
        return exchangeRatesProvider.getExchangeRates(SYSTEM_CURRENCIES.stream()
                        .filter(currency -> !currency.equals(SystemCurrencies.RON))
                        .toList(),
                date == null ? LocalDate.now() : date);
    }

    @SuppressWarnings("rawtypes")
    @GetMapping(path = "countries")
    public Map<String, String> countries(@RequestParam(required = false, defaultValue = "ro") String language) {
        var locale = new Locale(language);
        return Arrays.stream(Locale.getISOCountries())
                .collect(Collectors.toMap(
                        country -> new Locale("", country).getDisplayCountry(locale),
                        country -> country,
                        (v1, v2) -> {
                            throw new RuntimeException(String.format("Duplicate key for values %s and %s", v1, v2));
                        },
                        TreeMap::new));
    }

}
