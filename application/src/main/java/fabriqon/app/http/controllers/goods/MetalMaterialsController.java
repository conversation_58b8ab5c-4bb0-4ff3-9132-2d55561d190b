package fabriqon.app.http.controllers.goods;

import fabriqon.app.business.goods.materials.metals.MetalMaterialService;
import fabriqon.app.config.security.AccountContext;
import org.springframework.http.CacheControl;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping(path = "/configurations/materials/metals")
public class MetalMaterialsController {

    private final AccountContext accountContext;
    private final MetalMaterialService materialService;

    public MetalMaterialsController(AccountContext accountContext, MetalMaterialService materialService) {
        this.accountContext = accountContext;
        this.materialService = materialService;
    }

    @GetMapping("/list")
    public ResponseEntity<List<MetalMaterialService.MaterialWithShapes>> getMaterials(@AuthenticationPrincipal Jwt principal) {
        return ResponseEntity.ok()
                .cacheControl(CacheControl.maxAge(7, TimeUnit.DAYS)
                        .cachePublic()
                        .immutable())
                .body(materialService.getMaterialsWithShapes(accountContext.account(principal).settings().general().defaultLanguage()));
    }

}
