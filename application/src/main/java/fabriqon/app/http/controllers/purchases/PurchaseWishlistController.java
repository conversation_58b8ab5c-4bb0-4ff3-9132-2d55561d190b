package fabriqon.app.http.controllers.purchases;

import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.purchases.PurchaseOrder;
import fabriqon.app.business.purchases.PurchaseWishlistItem;
import fabriqon.app.business.purchases.PurchasesService;
import fabriqon.app.business.suppliers.SupplierService;
import fabriqon.app.common.model.Company;
import fabriqon.app.common.model.Money;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.response.Entity;
import fabriqon.app.http.response.StringEntity;
import org.jooq.DSLContext;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static fabriqon.jooq.JooqJsonbFunctions.arrayContainsJson;
import static fabriqon.jooq.classes.Tables.*;
import static java.lang.String.format;
import static java.util.stream.Collectors.toList;

@RestController
@RequestMapping(path = "/purchases/wishlist")
public class PurchaseWishlistController {

    private final AccountContext accountContext;
    private final PurchasesService purchasesService;
    private final SupplierService supplierService;
    private final DSLContext db;
    private final Sources sources;


    public PurchaseWishlistController(AccountContext accountContext, PurchasesService purchasesService, SupplierService supplierService, DSLContext db, Sources sources) {
        this.accountContext = accountContext;
        this.purchasesService = purchasesService;
        this.supplierService = supplierService;
        this.db = db;
        this.sources = sources;
    }

    @PostMapping(path = "add")
    public void addToWishlist(@AuthenticationPrincipal Jwt principal, @Validated @RequestBody List<WishlistItem> items) {
        purchasesService.addToWishlist(accountContext.accountId(principal),
                items.stream()
                        .map(e -> new PurchaseWishlistItem(
                                null, null, null, false, null,
                                e.materialGoodId, e.quantity, e.expectedPrice,
                                e.supplierId, List.of()
                        ))
                        .collect(toList()));
    }

    @GetMapping(path = "list")
    public List<WishlistItemDetails> wishlist(@AuthenticationPrincipal Jwt principal,
                                              @RequestParam(value = "supplier_id", required = false) UUID supplierId) {
        var filter = PURCHASE_WISHLIST.OWNER_ID.eq(accountContext.accountId(principal));
        if (supplierId != null) {
            filter = filter.and(PURCHASE_WISHLIST.SUPPLIER_ID.eq(supplierId));
        }
        return db.selectFrom(PURCHASE_WISHLIST)
                .where(filter)
                .orderBy(PURCHASE_WISHLIST.CREATE_TIME)
                .fetchInto(PurchaseWishlistItem.class)
                .stream().map(this::details).toList();
    }

    @DeleteMapping(path = "{id}/delete")
    public void deleteWishlistItem(@AuthenticationPrincipal Jwt principal,
                                   @PathVariable("id") UUID itemId) {
        db.deleteFrom(PURCHASE_WISHLIST)
                .where(
                        PURCHASE_WISHLIST.ID.eq(itemId),
                        PURCHASE_WISHLIST.OWNER_ID.eq(accountContext.accountId(principal))
                )
                .execute();
    }

    @PostMapping(path = "{id}/update")
    public void update(@AuthenticationPrincipal Jwt principal,
                       @PathVariable("id") UUID wishlistItemId,
                       @Validated @RequestBody WishlistItemUpdate update) {
        purchasesService.updateWishlistItem(wishlistItemId, accountContext.accountId(principal), update.quantity, update.expectedPrice);
    }

    @DeleteMapping(path = "supplier/{id}/delete")
    public void deleteWishlistItemsBySupplier(@AuthenticationPrincipal Jwt principal,
                                              @PathVariable("id") UUID supplierId) {
        db.deleteFrom(PURCHASE_WISHLIST)
                .where(
                        PURCHASE_WISHLIST.SUPPLIER_ID.eq(supplierId),
                        PURCHASE_WISHLIST.OWNER_ID.eq(accountContext.accountId(principal))
                )
                .execute();
    }

    @GetMapping(path = "suppliers/list")
    public List<SupplierWishlist> supplierWishlists(@AuthenticationPrincipal Jwt principal,
                                                    @RequestParam(value = "supplier_id", required = false) UUID supplierId) {
        var filter = PURCHASE_WISHLIST.OWNER_ID.eq(accountContext.accountId(principal));
        if (supplierId != null) {
            filter = filter.and(PURCHASE_WISHLIST.SUPPLIER_ID.eq(supplierId));
        }
        var wishlistItems = db.selectFrom(PURCHASE_WISHLIST)
                .where(filter)
                .orderBy(PURCHASE_WISHLIST.CREATE_TIME)
                .fetchInto(PurchaseWishlistItem.class);
        var suppliers = db.select(COMPANY.ID, COMPANY.COMPANY_NAME)
                .from(COMPANY)
                .join(ACCOUNT_SUPPLIER).on(COMPANY.ID.eq(ACCOUNT_SUPPLIER.SUPPLIER_ID).and(ACCOUNT_SUPPLIER.OWNER_ID.eq(accountContext.accountId(principal))))
                .where(COMPANY.DELETED.isFalse(), COMPANY.ID.in(wishlistItems.stream().map(PurchaseWishlistItem::supplierId).collect(Collectors.toSet())))
                .orderBy(COMPANY.COMPANY_NAME)
                .fetchInto(Entity.class);
        return suppliers.stream()
                .map(supplier -> new SupplierWishlist(
                        supplier.id(),
                        supplier.name(),
                        supplierService.lastOrderDeliveredIn(accountContext.accountId(principal), supplier.id()).orElse(null),
                        wishlistItems
                                .stream()
                                .filter(item -> item.supplierId().equals(supplier.id()))
                                .map(this::details)
                                .toList()))
                .toList();
    }

    @GetMapping(path = "suppliers/{id}/details")
    public SupplierWishlist supplierWishlist(@AuthenticationPrincipal Jwt principal,
                                             @PathVariable(value = "id") UUID supplierId) {
        var filter = PURCHASE_WISHLIST.OWNER_ID.eq(accountContext.accountId(principal))
                .and(PURCHASE_WISHLIST.SUPPLIER_ID.eq(supplierId));
        var wishlistItems = db.selectFrom(PURCHASE_WISHLIST)
                .where(filter)
                .orderBy(PURCHASE_WISHLIST.CREATE_TIME)
                .fetchInto(PurchaseWishlistItem.class);
        var supplier = db.select(COMPANY.ID, COMPANY.COMPANY_NAME)
                .from(COMPANY)
                .join(ACCOUNT_SUPPLIER).on(COMPANY.ID.eq(ACCOUNT_SUPPLIER.SUPPLIER_ID).and(ACCOUNT_SUPPLIER.OWNER_ID.eq(accountContext.accountId(principal))))
                .where(COMPANY.DELETED.isFalse(), COMPANY.ID.eq(supplierId))
                .orderBy(COMPANY.COMPANY_NAME)
                .fetchSingleInto(Entity.class);
        return new SupplierWishlist(supplier.id(),
                supplier.name(),
                supplierService.lastOrderDeliveredIn(accountContext.accountId(principal), supplier.id()).orElse(null),
                wishlistItems
                        .stream()
                        .map(this::details)
                        .toList());
    }

    @PostMapping(path = "suppliers/{id}/empty")
    public void deleteWishlistForSupplier(@AuthenticationPrincipal Jwt principal,
                                          @PathVariable("id") UUID supplierId) {
        db.deleteFrom(PURCHASE_WISHLIST)
                .where(
                        PURCHASE_WISHLIST.SUPPLIER_ID.eq(supplierId),
                        PURCHASE_WISHLIST.OWNER_ID.eq(accountContext.accountId(principal))
                )
                .execute();
    }

    private WishlistItemDetails details(PurchaseWishlistItem item) {
        var price = Optional.ofNullable(item.expectedPrice())
                .or(() -> getPriceFromPastOrder(item.materialGoodId(), item.supplierId()));
        return new WishlistItemDetails(
                item.id(),
                new Entity(item.supplierId(), supplierName(item.supplierId())),
                materialGood(item.materialGoodId()),
                item.quantity(),
                measurementUnit(item.materialGoodId()),
                price.orElse(null),
                price.map(money -> new Money(
                                item.quantity().multiply(BigDecimal.valueOf(money.amount())).longValue(),
                                money.currency()))
                        .orElse(null),
                sources.details(item.ownerId(), item.materialGoodId(), item.sources())
        );
    }

    private String supplierName(UUID supplierId) {
        return db.selectFrom(COMPANY)
                .where(COMPANY.ID.eq(supplierId))
                .fetchSingle().into(Company.class)
                .name();
    }

    private MaterialGoodDetails materialGood(UUID materialGoodId) {
        return db.select(MATERIAL_GOOD.ID, MATERIAL_GOOD.NAME, MATERIAL_GOOD.CODE)
                .from(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(materialGoodId))
                .fetchSingleInto(MaterialGoodDetails.class);
    }

    //search purchase orders and get the price from there
    private Optional<Money> getPriceFromPastOrder(UUID materialGoodId, UUID supplierId) {
        //should never be the case
        return db.selectFrom(PURCHASE_ORDER)
                .where(PURCHASE_ORDER.SUPPLIER_ID.eq(supplierId)
                        .and(arrayContainsJson(PURCHASE_ORDER.ITEMS, format("[{\"materialGoodId\": \"%s\"}]", materialGoodId)))
                )
                .orderBy(PURCHASE_ORDER.CREATE_TIME.desc())
                .limit(1)
                .fetchOptionalInto(PurchaseOrder.class)
                .flatMap(order -> order.items().stream()
                        .filter(item -> materialGoodId.equals(item.materialGoodId()))
                        .findFirst()
                        .map(PurchaseOrder.Item::price));
    }

    private StringEntity measurementUnit(UUID materialGoodId) {
        var commonDetails = db.select(MATERIAL_GOOD.DETAILS)
                .from(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(materialGoodId))
                .fetchSingleInto(MaterialGood.Details.class);
        return new StringEntity(commonDetails.measurementUnit().name(), commonDetails.measurementUnit().symbol());
    }

    public record WishlistItem(
            UUID materialGoodId,
            BigDecimal quantity,
            UUID supplierId,
            Money expectedPrice
    ) {

    }

    public record WishlistItemUpdate(
            BigDecimal quantity,
            Money expectedPrice
    ) {
    }

    public record WishlistItemDetails(
            UUID id,
            Entity supplier,
            MaterialGoodDetails materialGood,
            BigDecimal quantity,
            StringEntity measurementUnit,
            Money price,
            Money totalPrice,
            List<Sources.SourceDetails> sources
    ) {
    }

    public record MaterialGoodDetails(
            UUID id,
            String name,
            String code
    ) {
    }

    public record SupplierWishlist(
            UUID id,
            String name,
            Integer lastOrderDeliveredIn,
            List<WishlistItemDetails> wishlistItems
    ) {
    }

}
