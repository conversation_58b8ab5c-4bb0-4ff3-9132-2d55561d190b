package fabriqon.app.http.controllers.manufacturing;

import fabriqon.app.common.model.Money;
import fabriqon.app.http.response.Entity;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

public record MaterialIssueNoteDetails(
        UUID id,
        Instant createTime,
        UUID ownerId,
        Entity manufacturingOrder,
        String number,
        LocalDate date,
        String inventoryManager,
        String worker,
        List<Material> materials
) {
    public record Material(
            Entity material,
            BigDecimal quantity,
            Money totalCost,
            BigDecimal wastedQuantity
    ) {
    }
}
