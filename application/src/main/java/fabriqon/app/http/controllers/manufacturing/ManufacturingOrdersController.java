package fabriqon.app.http.controllers.manufacturing;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.customers.Note;
import fabriqon.app.business.goods.Dimensions;
import fabriqon.app.business.goods.MaterialCosts;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.goods.RequiredMaterial;
import fabriqon.app.business.inventory.InventoryService;
import fabriqon.app.business.manufacturing.*;
import fabriqon.app.business.manufacturing.tasks.ManufacturingTaskService;
import fabriqon.app.common.model.File;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.common.model.Money;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.controllers.FilesController;
import fabriqon.app.http.controllers.NoteDefinition;
import fabriqon.app.http.controllers.NoteDetails;
import fabriqon.app.http.controllers.util.Sorting;
import fabriqon.app.http.response.Entity;
import fabriqon.app.http.response.StringEntity;
import fabriqon.misc.Json;
import fabriqon.misc.MathUtils;
import fabriqon.misc.Tuple;
import fabriqon.pdf.HtmlToPdfConverter;
import fabriqon.templates.Localizer;
import fabriqon.templates.Templates;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.Record2;
import org.jooq.TableField;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static fabriqon.app.business.manufacturing.ManufacturingOrder.Status.ACCOUNTED;
import static fabriqon.app.business.manufacturing.ManufacturingOrder.Status.CONSUMPTION_RECORDED;
import static fabriqon.jooq.JooqJsonbFunctions.stringField;
import static fabriqon.jooq.JooqTextSearchFunctions.textSearch;
import static fabriqon.jooq.classes.Tables.*;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.poi.util.StringUtil.isBlank;
import static org.jooq.impl.DSL.*;

@Slf4j
@RestController
@RequestMapping(path = "/manufacturing/orders")
public class ManufacturingOrdersController {

    private final AccountContext accountContext;
    private final ManufacturingService manufacturingService;
    private final ManufacturingOperationService manufacturingOperationService;
    private final DSLContext db;
    private final ManufacturingTasks tasks;
    private final ManufacturingOperations operations;
    private final InventoryService inventoryService;
    private final ManufacturingTaskService manufacturingTaskService;
    private final Templates templates;
    private final HtmlToPdfConverter htmlToPdfConverter;
    private final MaterialCosts materialCosts;

    @Autowired
    public ManufacturingOrdersController(AccountContext accountContext,
                                         ManufacturingService manufacturingService,
                                         ManufacturingOperationService manufacturingOperationService,
                                         ManufacturingTasks tasks,
                                         ManufacturingOperations operations,
                                         DSLContext db, InventoryService inventoryService, ManufacturingTaskService manufacturingTaskService,
                                         Templates templates, HtmlToPdfConverter htmlToPdfConverter, MaterialCosts materialCosts) {
        this.accountContext = accountContext;
        this.manufacturingService = manufacturingService;
        this.manufacturingOperationService = manufacturingOperationService;
        this.tasks = tasks;
        this.operations = operations;
        this.db = db;
        this.inventoryService = inventoryService;
        this.manufacturingTaskService = manufacturingTaskService;
        this.templates = templates;
        this.htmlToPdfConverter = htmlToPdfConverter;
        this.materialCosts = materialCosts;
    }

    @PostMapping(path = "create")
    public fabriqon.app.business.manufacturing.ManufacturingOrder createManufacturingOrder(
            @AuthenticationPrincipal Jwt principal,
            @Validated @RequestBody CreateManufacturingOrder create) {
        return manufacturingService.orderManufacturing(accountContext.accountId(principal),
                null, create.productionDeadline, create.productId, create.serviceId, create.quantity,
                create.customProduct, create.notes, List.of(), List.of());
    }

    private static final Map<String, TableField> sortFields = Map.of(
            "ranking", MANUFACTURING_ORDER.RANKING,
            "updateTime", MANUFACTURING_ORDER.UPDATE_TIME
    );

    @GetMapping(path = "list")
    public List<ManufacturingOrderDetails> listOrders(
            @AuthenticationPrincipal Jwt principal,
            @RequestParam(value = "q", required = false) String textSearch,
            @RequestParam(value = "status", required = false) List<fabriqon.app.business.manufacturing.ManufacturingOrder.Status> statuses,
            @RequestParam(value = "salesOrderId", required = false) UUID salesOrderId,
            @RequestParam(value = "day", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate day,
            @RequestParam(value = "start", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate start,
            @RequestParam(value = "end", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate end,
            @RequestParam(value = "sort", required = false)
            @Parameter(description = "Available fields for sorting: ranking, updateTime")
            String sorting
    ) {

        if (day != null && (start != null || end != null)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "can't filter both on 'day' and 'start'/'end'");
        }
        var startTime = day != null ? day.atStartOfDay() : start != null ? start.atStartOfDay() : null;
        var endTime = day != null ? day.atTime(23, 59, 59) : end != null ? end.atTime(23, 59, 59) : null;

        var filter = MANUFACTURING_ORDER.OWNER_ID.eq(accountContext.accountId(principal))
                .and(MANUFACTURING_ORDER.DELETED.isFalse());
        if (isNotEmpty(statuses)) {
            filter = filter.and(MANUFACTURING_ORDER.STATUS.in(
                    statuses.stream()
                            .map(Enum::name)
                            .toList())
            );
        }
        if (salesOrderId != null) {
            filter = filter.and(MANUFACTURING_ORDER.SALES_ORDER_ID.eq(salesOrderId));
        }
        if (isNotBlank(textSearch)) {
            filter = filter.and(textSearch(MANUFACTURING_ORDER.TEXT_SEARCH, textSearch));
        }
        if (startTime == null) {
            return db.selectFrom(MANUFACTURING_ORDER)
                    .where(filter)
                    .orderBy(isBlank(sorting) ? List.of(MANUFACTURING_ORDER.RANKING.asc()) : Sorting.toJooqSortFields(sortFields, sorting))
                    .fetchInto(fabriqon.app.business.manufacturing.ManufacturingOrder.class)
                    .stream().map(this::orderDetails).toList();
        } else {
            //we need to refresh the tasks in order to provide accurate data for the start/end times
            manufacturingTaskService.getTasks(accountContext.accountId(principal), List.of(ManufacturingTask.Status.TODO), startTime, endTime);
            return db.select(MANUFACTURING_ORDER.fields())
                    .from(MANUFACTURING_ORDER)
                    .join(MANUFACTURING_TASK).on(MANUFACTURING_TASK.MANUFACTURING_ORDER_ID.eq(MANUFACTURING_ORDER.ID))
                    .where(filter)
                    .groupBy(MANUFACTURING_ORDER.fields())
                    .having(and(and(DSL.min(MANUFACTURING_TASK.START_TIME).gt(startTime).and(DSL.min(MANUFACTURING_TASK.START_TIME).lt(endTime)))
                            .or(DSL.max(MANUFACTURING_TASK.END_TIME).gt(startTime).and(DSL.max(MANUFACTURING_TASK.END_TIME).lt(endTime)))
                            .or(DSL.min(MANUFACTURING_TASK.START_TIME).lt(startTime).and(DSL.max(MANUFACTURING_TASK.END_TIME).gt(endTime)))))
                    .orderBy(isBlank(sorting) ? List.of(MANUFACTURING_ORDER.RANKING.asc()) : Sorting.toJooqSortFields(sortFields, sorting))
                    .fetchInto(ManufacturingOrder.class)
                    .stream().map(this::orderDetails).toList();
        }
    }

    @SuppressWarnings("rawtypes")
    @Operation(responses = {
            @ApiResponse(responseCode = "200", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = ManufacturingOrdersController.ManufacturingOrderDetails.class)),
                    @Content(mediaType = "application/pdf"),
                    @Content(mediaType = "text/html"),
            })
    })
    @GetMapping(path = "{id}/details",
            produces = {
                    MediaType.TEXT_HTML_VALUE,
                    MediaType.APPLICATION_JSON_VALUE,
                    MediaType.APPLICATION_PDF_VALUE
            })
    public ResponseEntity details(
            @AuthenticationPrincipal Jwt principal,
            @RequestHeader(HttpHeaders.ACCEPT) String acceptHeader,
            @PathVariable(value = "id") UUID orderId) {

        var ownerId = accountContext.accountId(principal);
        var orderDetails = orderDetails(db.selectFrom(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.OWNER_ID.eq(ownerId),
                        MANUFACTURING_ORDER.DELETED.isFalse(),
                        MANUFACTURING_ORDER.ID.eq(orderId))
                .orderBy(MANUFACTURING_ORDER.RANKING)
                .fetchSingleInto(fabriqon.app.business.manufacturing.ManufacturingOrder.class));
        if (acceptHeader.contains(MediaType.TEXT_HTML_VALUE)) {
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(html(ownerId, orderDetails));
        } else if (acceptHeader.contains(MediaType.APPLICATION_PDF_VALUE)) {
            var pdf = htmlToPdfConverter.convert(html(ownerId, orderDetails));
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                            + "manufacturing_order_" + orderDetails.number + "_"
                            + DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.ofInstant(orderDetails.updateTime(), ZoneId.systemDefault()))
                            + ".pdf")
                    .body(pdf);
        } else {
            return new ResponseEntity<>(orderDetails, HttpStatus.OK);
        }
    }

    @GetMapping(path = "{id}/all-required-materials")
    public List<RequiredMaterialDetails> allRequiredMaterials(@AuthenticationPrincipal Jwt principal,
                                                              @PathVariable("id") UUID orderId) {
        var accountId = accountContext.accountId(principal);
        var defaultInventoryUnitId = inventoryService.defaultInventoryUnitId(accountId);
        return allRequiredMaterials(manufacturingService.order(accountId, orderId)).stream()
                .filter(m -> m.a().quantity().compareTo(BigDecimal.ZERO) > 0)
                .map(m -> Tuple.of(operations.requiredMaterialDetails(accountId, BigDecimal.ONE, m.a(), defaultInventoryUnitId), m.b()))
                .map(t -> new RequiredMaterialDetails(
                        t.a().options(),
                        null,
                        t.a().quantity(),
                        null,
                        inventoryService.getCurrentStock(accountId, t.a().options().getFirst().id(), defaultInventoryUnitId),
                        t.b(),
                        materialCosts.materialCost(accountId, t.a().options().getFirst().id()).multiply(t.a().quantity()),
                        t.a().wastePercentage(),
                        null, null, null
                ))
                .collect(Collectors.groupingBy(
                        m -> m.materialGoods().getFirst().id(),
                        Collectors.reducing(
                                null,
                                (m1, m2) -> {
                                    if (m1 == null) return m2;
                                    var materialId = m1.materialGoods.getFirst().id();
                                    var totalRequired = m1.requiredTotal().add(m2.requiredTotal());
                                    return new RequiredMaterialDetails(
                                            m1.materialGoods,
                                            null,
                                            totalRequired,
                                            null,
                                            inventoryService.getCurrentStock(accountId, materialId, defaultInventoryUnitId),
                                            m1.allAvailable && m2.allAvailable,
                                            materialCosts.materialCost(accountId, materialId).multiply(totalRequired),
                                            m1.wastePercentage,
                                            null, null, null);
                                })
                ))
                .values()
                .stream()
                .sorted(Comparator.comparing(m -> m.materialGoods().getFirst().name()))
                .toList();
    }

    @PostMapping(path = "{id}/update")
    public ManufacturingOrderDetails update(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable(value = "id") UUID orderId,
            @Validated @RequestBody UpdateManufacturingOrder update) {
        return orderDetails(manufacturingService.updateManufacturingOrder(
                accountContext.accountId(principal),
                orderId,
                update.assignedTo,
                update.productionDeadline,
                update.quantity,
                update.manufacturingOperations,
                update.notes
        ));
    }

    @PostMapping(path = "{id}/record-consumption")
    public MaterialIssueNote recordConsumption(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable(value = "id") UUID orderId,
            @Validated @RequestBody ConsumptionDefinition consumption) {
        return manufacturingService.recordConsumption(
                        accountContext.accountId(principal), orderId,
                        consumption.date,
                        consumption.inventoryManager,
                        consumption.worker,
                        consumption.materials)
                .orElse(null);
    }

    @PostMapping(path = "{id}/accounted")
    public ManufacturingOrderDetails accounted(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable(value = "id") UUID orderId) {
        return orderDetails(manufacturingService.accounted(accountContext.accountId(principal), orderId));
    }

    @PostMapping(path = "{id}/customization-finished")
    public ManufacturingOrderDetails customizationFinished(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable(value = "id") UUID orderId) {
        return orderDetails(manufacturingService.customizationFinished(accountContext.accountId(principal), orderId));
    }

    @DeleteMapping(path = "{id}/delete")
    public void delete(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable(value = "id") UUID orderId) {
        manufacturingService.deleteOrder(accountContext.accountId(principal), orderId, true);
    }

    @PostMapping(path = "ranking")
    public void applyRanking(@AuthenticationPrincipal Jwt principal,
                             @Validated @RequestBody List<UUID> orderIds) {
        manufacturingService.applyRanking(accountContext.accountId(principal), orderIds);
    }

    @PostMapping(path = "{id}/attach-file", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ManufacturingOrderDetails attachFile(@AuthenticationPrincipal Jwt principal,
                                                @PathVariable("id") UUID orderId,
                                                @RequestParam("file") MultipartFile file) throws IOException {
        return orderDetails(manufacturingService.attach(accountContext.accountId(principal), orderId,
                file.getOriginalFilename(), file.getBytes()));
    }

    @DeleteMapping(path = "{id}/files/{fileId}/delete")
    public ManufacturingOrderDetails deleteFile(@AuthenticationPrincipal Jwt principal,
                                                @PathVariable("id") UUID orderId,
                                                @PathVariable("fileId") UUID fileId) throws IOException {
        return orderDetails(manufacturingService.deleteFile(accountContext.accountId(principal), orderId, fileId));
    }

    @GetMapping(path = "{id}/available-stock/{materialId}")
    public AvailableStock availableStockForProduct(@AuthenticationPrincipal Jwt principal,
                                                   @PathVariable("id") UUID orderId,
                                                   @PathVariable("materialId") UUID materialId) {
        var accountId = accountContext.accountId(principal);
        var orderRanking = db.select(MANUFACTURING_ORDER.RANKING)
                .from(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.OWNER_ID.eq(accountId), MANUFACTURING_ORDER.ID.eq(orderId))
                .fetchSingleInto(Integer.class);
        var reservedBefore = db.select(coalesce(sum(RESERVED_INVENTORY.QUANTITY), 0))
                .from(RESERVED_INVENTORY)
                .where(
                        RESERVED_INVENTORY.OWNER_ID.eq(accountId),
                        RESERVED_INVENTORY.MANUFACTURING_ORDER_ID.in(
                                select(MANUFACTURING_ORDER.ID).from(MANUFACTURING_ORDER)
                                        .where(MANUFACTURING_ORDER.OWNER_ID.eq(accountId),
                                                (MANUFACTURING_ORDER.RANKING.lessThan(orderRanking)
                                                        .and(MANUFACTURING_ORDER.RANKING.gt(ManufacturingOrder.UNRANKED_ORDER_VALUE)))
                                                        .or(MANUFACTURING_ORDER.STATUS.eq(ManufacturingOrder.Status.MANUFACTURED.name())))),
                        RESERVED_INVENTORY.MATERIAL_GOOD_ID.eq(materialId)
                )
                .fetchSingleInto(BigDecimal.class);
        var onStock = inventoryService.getCurrentStock(accountId, materialId);
        return new AvailableStock(materialId, onStock.subtract(reservedBefore));
    }

    @PostMapping(path = "{id}/notes/add")
    public void addNote(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable("id") UUID id,
            @Validated @RequestBody NoteDefinition note) {
        manufacturingService.addNote(accountContext.accountId(principal), id, accountContext.userId(principal), note.note());
    }

    @PostMapping(path = "{id}/add-missing-to-wishlist")
    public void addMissingToWishlist(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable("id") UUID orderId,
            @Validated @RequestBody MaterialWishlistAddition addition) {
        manufacturingService.addToWishlist(accountContext.accountId(principal), orderId,
                addition.materialId(), addition.quantity(), addition.supplierId());
    }

    @GetMapping(path = "{id}/notes/list")
    public List<NoteDetails> listNotes(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable("id") UUID id) {
        return db.select(NOTE.ID, NOTE.CREATE_TIME, NOTE.UPDATE_TIME, NOTE.DELETED, NOTE.OWNER_ID, NOTE.ADDED_BY_ID, NOTE.NOTE_)
                .from(NOTE)
                .join(MANUFACTURING_ORDER_NOTE).on(MANUFACTURING_ORDER_NOTE.NOTE_ID.eq(NOTE.ID))
                .where(
                        NOTE.OWNER_ID.eq(accountContext.accountId(principal)),
                        MANUFACTURING_ORDER_NOTE.MANUFACTURING_ORDER_ID.eq(id)
                )
                .orderBy(NOTE.CREATE_TIME.desc())
                .fetchInto(Note.class)
                .stream()
                .map(note -> new NoteDetails(note.id(), note.createTime(),
                        new Entity(note.addedById(), db.select(USERS.NAME).from(USERS).where(USERS.ID.eq(note.addedById())).fetchSingleInto(String.class)),
                        note.note()))
                .toList();
    }

    private ManufacturingOrderDetails orderDetails(fabriqon.app.business.manufacturing.ManufacturingOrder order) {
        log.info("getting details for: [{}]", order.id());
        //needs to be mutable because we will be subtracting from it
        var reservedMaterials = new HashMap<>(db.select(RESERVED_INVENTORY.MATERIAL_GOOD_ID, coalesce(sum(RESERVED_INVENTORY.QUANTITY), 0).cast(BigDecimal.class))
                .from(RESERVED_INVENTORY)
                .where(
                        RESERVED_INVENTORY.MANUFACTURING_ORDER_ID.eq(order.id()),
                        RESERVED_INVENTORY.OWNER_ID.eq(order.ownerId())
                )
                .groupBy(RESERVED_INVENTORY.MATERIAL_GOOD_ID)
                .fetch()
                .stream()
                .collect(Collectors.toMap(Record2::value1,
                        r -> r.value2().setScale(2, RoundingMode.HALF_EVEN),
                        BigDecimal::add)));

        var productDetails = product(order.productId(), order.serviceId());
        var childOrders = new ArrayList<>(db.selectFrom(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.OWNER_ID.eq(order.ownerId()), MANUFACTURING_ORDER.PARENT_ID.eq(order.id()))
                .orderBy(MANUFACTURING_ORDER.NUMBER.asc())
                .fetchInto(ManufacturingOrder.class));
        var employeeAndWorkstationCosts = manufacturingService.employeeAndWorkstationCosts(order.ownerId(), order.id());
        var manufacturingOverheadCosts = manufacturingService.manufacturingOverheadCosts(order.ownerId(), order.id());
        var manufacturingOperations = manufacturingOperations(order).stream()
                .map(op -> operations.manufacturingOperationDetails(order.ownerId(), BigDecimal.ONE, op))
                .map(op -> operationDetails(op, order, reservedMaterials, childOrders))
                .toList();
        var materialCost = materialCosts(order, employeeAndWorkstationCosts.currency());
        return new ManufacturingOrderDetails(
                order.id(),
                order.createTime(),
                order.updateTime(),
                order.parentId() != null ? db.select(MANUFACTURING_ORDER.ID, MANUFACTURING_ORDER.NUMBER).from(MANUFACTURING_ORDER).where(MANUFACTURING_ORDER.ID.eq(order.parentId())).fetchSingleInto(Entity.class) : null,
                order.salesOrderId(),
                customer(order.salesOrderId()),
                category(order.productId()),
                order.number(),
                materialCost.add(employeeAndWorkstationCosts).add(manufacturingOverheadCosts),
                order.productionDeadline(),
                order.status(),
                productDetails != null
                        ? productDetails
                        .setMaterialCosts(materialCost)
                        .setEmployeeAndWorkstationCosts(employeeAndWorkstationCosts)
                        .setManufacturingOverheadCosts(manufacturingOverheadCosts)
                        : null,
                order.quantity(),
                new StringEntity(productDetails.measurementUnit.name(), productDetails.measurementUnit.symbol()),
                order.assignedTo() != null ? new Entity(order.assignedTo(), db.select(USERS.NAME).from(USERS).where(USERS.ID.eq(order.assignedTo()), USERS.OWNER_ID.eq(order.ownerId())).fetchSingleInto(String.class)) : null,
                order.manufacturedProducts(),
                order.ranking(),
                manufacturingOperations.stream().flatMap(op -> op.materials().stream()).allMatch(availability -> availability.allAvailable),
                productionTime(manufacturingOperations, order.quantity()),
                order.customProduct(),
                manufacturingOperations,
                manufacturingTasks(order, manufacturingOperations),
                order.notes(),
                materialIssueNote(order.id(), order.number()).orElse(null),
                db.select(max(MANUFACTURING_TASK.ESTIMATED_END_TIME))
                        .from(MANUFACTURING_TASK)
                        .where(MANUFACTURING_TASK.MANUFACTURING_ORDER_ID.eq(order.id()))
                        .fetchOptionalInto(LocalDateTime.class)
                        .orElse(null),
                files(order.ownerId(), order.id())
        );
    }

    private List<File> files(UUID ownerId, UUID orderId) {
        return db.select(FILE.ID, FILE.FILE_NAME)
                .from(FILE)
                .where(FILE.ID.in(select(MANUFACTURINGORDER_FILE.FILE_ID).from(MANUFACTURINGORDER_FILE).where(MANUFACTURINGORDER_FILE.OWNER_ID.eq(ownerId), MANUFACTURINGORDER_FILE.MANUFACTURING_ORDER_ID.eq(orderId))))
                .fetch()
                .stream()
                .map(f -> new File(f.value1(), f.value2(),
                        FilesController.previewLink(f.value1()), FilesController.downloadLink(f.value1())))
                .toList();
    }

    private List<ManufacturingTaskDetails> manufacturingTasks(ManufacturingOrder order, List<ManufacturingOperationDetails> manufacturingOperations) {
        return db.selectFrom(MANUFACTURING_TASK)
                .where(
                        MANUFACTURING_TASK.DELETED.isFalse(),
                        MANUFACTURING_TASK.MANUFACTURING_ORDER_ID.eq(order.id())
                )
                .orderBy(MANUFACTURING_TASK.CREATE_TIME)
                .fetchInto(fabriqon.app.business.manufacturing.ManufacturingTask.class)
                .stream().map(tasks::toDto)
                .map(task -> new ManufacturingTaskDetails(
                        task,
                        manufacturingOperations.stream().filter(op -> op.name().equalsIgnoreCase(task.name)).findFirst()
                                .map(ManufacturingOperationDetails::materials)
                                .orElseGet(() -> {
                                    log.warn("manufacturing operation not found for task [{}]", task.id);
                                    return List.of();
                                })
                ))
                .toList();
    }

    private ManufacturingOperationDetails operationDetails(ManufacturingOperations.ManufacturingOperationDetails op,
                                                           ManufacturingOrder order,
                                                           Map<UUID, BigDecimal> reservedMaterials,
                                                           List<ManufacturingOrder> childOrders) {
        return new ManufacturingOperationDetails(
                op.operationTemplateId(),
                op.candidateWorkstations(),
                op.candidateEmployees(),
                op.name(),
                op.durationInMinutes(),
                op.parallelizable(),
                op.costPerHour(),
                op.materials().stream()
                        .map(m -> {
                            var materialId = m.options().getFirst().id();
                            var totalRequired = m.quantity().multiply(order.quantity());
                            var totalReserved = reservedMaterials.getOrDefault(materialId, BigDecimal.ZERO);
                            var neededReserved = MathUtils.min(totalReserved, totalRequired);
                            if (neededReserved.compareTo(BigDecimal.ZERO) > 0) {
                                reservedMaterials.put(materialId, totalReserved.subtract(neededReserved));
                            }
                            var allAvailable = List.of(CONSUMPTION_RECORDED, ACCOUNTED).contains(order.status()) || totalRequired.compareTo(neededReserved) == 0;
                            return new RequiredMaterialDetails(
                                    m.options(), m.quantity(), totalRequired, neededReserved,
                                    inventoryService.getCurrentStock(order.ownerId(), materialId),
                                    allAvailable,
                                    materialCosts.materialCost(order.ownerId(), materialId).multiply(totalRequired),
                                    m.wastePercentage(), m.requiredDimensions(), m.totalRequiredDimensions(),
                                    allAvailable ? null
                                            : !m.options().getFirst().produced() ? null
                                            : getChildOrder(childOrders, materialId, totalRequired)
                            );
                        })
                        .toList()
        );
    }

    private SubassemblyManufacturingOrder getChildOrder(List<ManufacturingOrder> childOrders, UUID materialId, BigDecimal quantity) {
        var childOrder = childOrders.stream().filter(co -> co.productId().equals(materialId) && co.quantity().compareTo(quantity) == 0).findFirst();
        childOrder.ifPresent(childOrders::remove);
        return childOrder.map(co -> new SubassemblyManufacturingOrder(co.id(), co.number(), co.status())).orElse(null);
    }

    private Money materialCosts(ManufacturingOrder order, Currency currency) {
        if (order.status() == CONSUMPTION_RECORDED || order.status() == ACCOUNTED) {
            return materialIssueNote(order.id(), order.number())
                    .flatMap(note -> note.materials().stream()
                            .map(MaterialIssueNoteDetails.Material::totalCost)
                            .reduce(Money::add))
                    .orElse(new Money(0, currency));
        }
        return manufacturingService.requiredMaterials(order).stream()
                .map(material -> {
                    var requiredTotal = material.quantity().multiply(order.quantity()).setScale(2, RoundingMode.HALF_EVEN);
                    return materialCosts.materialCost(order.ownerId(), material.materialIds().getFirst()).multiply(requiredTotal);
                })
                .reduce(Money::add)
                .orElse(new Money(0, currency));
    }

    private List<ManufacturingOperation> manufacturingOperations(fabriqon.app.business.manufacturing.ManufacturingOrder order) {
        return isNotEmpty(order.manufacturingOperations())
                ? manufacturingOperationService.enhance(order.manufacturingOperations())
                : (order.productId() != null ? manufacturingOperationService.enhance(
                Json.read(db.select(MATERIAL_GOOD.DETAILS)
                                .from(MATERIAL_GOOD)
                                .where(MATERIAL_GOOD.ID.eq(order.productId()))
                                .fetchSingleInto(String.class), MaterialGood.Details.class)
                        .manufacturingOperations())
                : List.of());
    }

    private int productionTime(List<ManufacturingOperationDetails> operations, BigDecimal quantity) {
        return operations.stream()
                .map(operation -> quantity.multiply(BigDecimal.valueOf(operation.durationInMinutes())))
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .intValue();
    }

    private Entity category(UUID productId) {
        if (productId != null) {
            return db.select(CATEGORY.ID, stringField(CATEGORY.DETAILS, "name"))
                    .from(CATEGORY)
                    .join(MATERIAL_GOOD).on(MATERIAL_GOOD.CATEGORY_ID.eq(CATEGORY.ID))
                    .where(MATERIAL_GOOD.ID.eq(productId))
                    .fetchOptionalInto(Entity.class)
                    .orElse(null);
        }
        return null;
    }

    private MaterialGood material(UUID materialId) {
        if (materialId != null) {
            return db.select(MATERIAL_GOOD.ID,
                            MATERIAL_GOOD.NAME,
                            MATERIAL_GOOD.CODE,
                            MATERIAL_GOOD.DETAILS)
                    .from(MATERIAL_GOOD)
                    .where(MATERIAL_GOOD.ID.eq(materialId))
                    .fetchSingleInto(MaterialGood.class);
        }
        return null;
    }

    private ProductDetails product(UUID materialId, UUID serviceId) {
        if (materialId != null) {
            return db.select(MATERIAL_GOOD.ID,
                            MATERIAL_GOOD.NAME,
                            MATERIAL_GOOD.CODE,
                            MATERIAL_GOOD.DETAILS)
                    .from(MATERIAL_GOOD)
                    .where(MATERIAL_GOOD.ID.eq(materialId))
                    .fetchSingle()
                    .map(record -> new ProductDetails(
                            record.get(MATERIAL_GOOD.ID),
                            record.get(MATERIAL_GOOD.NAME),
                            record.get(MATERIAL_GOOD.CODE),
                            Json.read(record.get(MATERIAL_GOOD.DETAILS).data(), MaterialGood.Details.class).measurementUnit(),
                            null, null, null
                    ));
        } else if (serviceId != null) {
            return db.select(SERVICE_TEMPLATE.ID,
                            SERVICE_TEMPLATE.NAME,
                            SERVICE_TEMPLATE.MEASUREMENT_UNIT)
                    .from(SERVICE_TEMPLATE)
                    .where(SERVICE_TEMPLATE.ID.eq(serviceId))
                    .fetchSingle()
                    .map(record -> new ProductDetails(
                            record.get(SERVICE_TEMPLATE.ID),
                            record.get(SERVICE_TEMPLATE.NAME),
                            null,
                            MeasurementUnit.valueOf(record.get(SERVICE_TEMPLATE.MEASUREMENT_UNIT)),
                            null, null, null
                    ));
        }
        return null;
    }

    private Entity customer(UUID salesOrderId) {
        if (salesOrderId == null) {
            return null;//the mfg order is not for a customer but own stock
        }
        return db.select(COMPANY.ID, COMPANY.COMPANY_NAME)
                .from(COMPANY)
                .join(ACCOUNT_CUSTOMER).on(ACCOUNT_CUSTOMER.CUSTOMER_ID.eq(COMPANY.ID))
                .join(SALES_ORDER).on(SALES_ORDER.CUSTOMER_ID.eq(ACCOUNT_CUSTOMER.CUSTOMER_ID))
                .where(SALES_ORDER.ID.eq(salesOrderId))
                .fetchSingleInto(Entity.class);
    }

    private Optional<MaterialIssueNoteDetails> materialIssueNote(UUID orderId, String orderNumber) {
        return db.selectFrom(MATERIAL_ISSUE_NOTE)
                .where(MATERIAL_ISSUE_NOTE.MANUFACTURING_ORDER_ID.eq(orderId))
                .fetchOptionalInto(MaterialIssueNote.class)
                .map(note -> new MaterialIssueNoteDetails(
                        note.id(),
                        note.createTime(),
                        note.ownerId(),
                        new Entity(note.manufacturingOrderId(), orderNumber),
                        note.number(),
                        note.date(),
                        note.details().inventoryManager(),
                        note.details().worker(),
                        note.materials().stream().map(m -> new MaterialIssueNoteDetails.Material(new Entity(m.id(), material(m.id()).name), m.quantity(), m.totalCost(), m.wastedQuantity())).toList()
                ));
    }

    private String html(UUID ownerId, ManufacturingOrderDetails order) {
        var account = account(ownerId);
        var localizer = new Localizer(new Locale(account.information().address().country()));

        var orderMap = new HashMap<>();
        orderMap.put("number", order.number());
        orderMap.put("saleOrderNumber", order.salesOrderId() != null
                ? db.select(SALES_ORDER.NUMBER).from(SALES_ORDER).where(SALES_ORDER.ID.eq(order.salesOrderId))
                : null);
        orderMap.put("clientName", order.customer() != null ? order.customer.name() : null);
        orderMap.put("createTime", localizer.localizeDate(order.createTime()));
        orderMap.put("productionDeadline", order.productionDeadline != null ? localizer.localizeDate(order.productionDeadline().toLocalDate()) : null);
        orderMap.put("estimatedCompletionDate", order.estimatedCompletionDate() != null ? localizer.localizeDate(order.estimatedCompletionDate().toLocalDate()) : null);

        orderMap.put("notes", order.notes);
        var taskIndexer = new AtomicInteger(0);
        orderMap.put("manufacturingTasks", order.manufacturingTasks().stream()
                .map(task -> Map.of(
                        "index", taskIndexer.incrementAndGet(),
                        "name", task.name,
                        "duration", localizer.humanReadableDuration(Duration.ofMinutes(task.durationInMinutes)),
                        "employees", task.assignedEmployees.stream().map(Entity::name).collect(Collectors.joining(",")),
                        "workstations", task.assignedWorkstations.stream().map(Entity::name).collect(Collectors.joining(","))
                ))
                .toList());

        orderMap.put("hasDimensions", order.manufacturingOperations.stream().flatMap(op -> op.materials().stream())
                .anyMatch(m -> m.requiredDimensions != null || m.totalRequiredDimensions != null));
        var materialIndexer = new AtomicInteger(0);
        orderMap.put("requiredMaterials", order.manufacturingOperations.stream().flatMap(op -> op.materials().stream())
                .map(m -> Map.of(
                        "index", materialIndexer.incrementAndGet(),
                        "name", m.materialGoods.stream().map(ManufacturingOperations.MaterialOptionDetails::name).collect(Collectors.joining("\n")),
                        "code", m.materialGoods.stream().map(ManufacturingOperations.MaterialOptionDetails::code).collect(Collectors.joining("\n")),
                        "required", m.required.stripTrailingZeros().toPlainString(),
                        "requiredTotal", m.requiredTotal.stripTrailingZeros().toPlainString(),
                        "measurementUnit", localizer.localizedValue(m.materialGoods.getFirst().measurementUnit().name()),
                        "dimensions", dimensionsForHtml(localizer, m.requiredDimensions)
                ))
                .toList()
        );

        var map = new HashMap<>();
        map.put("order", orderMap);
        var productMap = new HashMap<String, String>();
        productMap.put("name", order.product.name);
        productMap.put("code", order.product.code);
        productMap.put("quantity", order.quantity.stripTrailingZeros().toPlainString());
        productMap.put("measurementUnit", localizer.localizedValue(order.product.measurementUnit.name()));
        map.put("product", productMap);
        map.put("labels", localizer.labels());
        map.put("base64Logo", account.logo());

        return templates.render("pdf/manufacturing_order.html", map);
    }

    private String dimensionsForHtml(Localizer localizer, Dimensions requiredDimensions) {
        if (requiredDimensions == null) {
            return "";
        }
        var sb = new StringBuilder();
        if (requiredDimensions.length() != null) {
            sb.append(localizer.labels().get("length")).append(": ").append(requiredDimensions.length()).append('\n');
        }
        if (requiredDimensions.width() != null) {
            sb.append(localizer.labels().get("width")).append(": ").append(requiredDimensions.width()).append('\n');
        }
        if (requiredDimensions.height() != null) {
            sb.append(localizer.labels().get("height")).append(": ").append(requiredDimensions.height()).append('\n');
        }
        if (requiredDimensions.weight() != null) {
            sb.append(localizer.labels().get("weight")).append(": ").append(requiredDimensions.weight()).append('\n');
        }
        sb.delete(sb.length() - 1, sb.length());
        return sb.toString();
    }


    private List<Tuple.Tuple2<RequiredMaterial, Boolean>> allRequiredMaterials(ManufacturingOrder order) {
        var childOrders = db.selectFrom(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.OWNER_ID.eq(order.ownerId()),
                        MANUFACTURING_ORDER.PARENT_ID.eq(order.id()))
                .fetchInto(ManufacturingOrder.class);
        return Stream.concat(manufacturingService.requiredMaterials(order).stream()
                        .collect(Collectors.groupingBy(
                                material -> material.materialIds().getFirst(),
                                Collectors.reducing(
                                        null,
                                        (m1, m2) -> {
                                            if (m1 == null) return m2;
                                            return new RequiredMaterial(
                                                    m1.materialIds(),
                                                    m1.quantity().add(m2.quantity()),
                                                    m1.optional(),
                                                    m1.configurableWithOptions(),
                                                    m1.replaceableWithOptions(),
                                                    m1.wastePercentage(),
                                                    m1.dimensions(),
                                                    m1.totalDimensions());
                                        })
                        ))
                        .values()
                        .stream()
                        .map(m -> {
                            var quantityByChildOrders = childOrders.stream()
                                    .filter(co -> co.productId().equals(m.materialIds().getFirst()))
                                    .map(ManufacturingOrder::quantity)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            return m.setQuantity(m.quantity().multiply(order.quantity()).subtract(quantityByChildOrders));
                        })
                        .map(m -> Tuple.of(m,
                                List.of(CONSUMPTION_RECORDED, ACCOUNTED).contains(order.status())
                                        || m.quantity().compareTo(reservedQuantity(order.ownerId(), order.id(), m.materialIds().getFirst())) == 0
                        )),
                childOrders.stream().flatMap(co -> allRequiredMaterials(co).stream())
        ).toList();
    }

    private BigDecimal reservedQuantity(UUID ownerId, UUID orderId, UUID materialId) {
        return db.select(coalesce(sum(RESERVED_INVENTORY.QUANTITY), 0))
                .from(RESERVED_INVENTORY)
                .where(
                        RESERVED_INVENTORY.OWNER_ID.eq(ownerId),
                        RESERVED_INVENTORY.MANUFACTURING_ORDER_ID.eq(orderId),
                        RESERVED_INVENTORY.MATERIAL_GOOD_ID.eq(materialId)
                )
                .fetchSingleInto(BigDecimal.class);
    }

    private Account account(UUID id) {
        return db.selectFrom(ACCOUNT).where(ACCOUNT.ID.eq(id)).fetchSingleInto(Account.class);
    }

    public record CreateManufacturingOrder(
            LocalDateTime productionDeadline,
            UUID productId,
            UUID serviceId,
            BigDecimal quantity,
            String notes,
            boolean customProduct
    ) {
    }

    public record UpdateManufacturingOrder(
            UUID assignedTo,
            LocalDateTime productionDeadline,
            BigDecimal quantity,
            String notes,
            List<Material> materials,
            List<ManufacturingOperation> manufacturingOperations
    ) {
    }

    public record ConsumptionDefinition(
            LocalDate date,
            String inventoryManager,
            String worker,
            List<UsedMaterial> materials
    ) {
    }

    public record Material(
            UUID id,
            BigDecimal quantity,
            BigDecimal wastePercentage,
            Dimensions requiredDimensions,
            Dimensions totalRequiredDimensions
    ) {
    }

    public record ManufacturingOrderDetails(
            UUID id,
            Instant createTime,
            Instant updateTime,
            Entity parent,
            UUID salesOrderId,
            Entity customer,
            Entity category,
            String number,
            Money totalCost,
            LocalDateTime productionDeadline,
            fabriqon.app.business.manufacturing.ManufacturingOrder.Status status,
            ProductDetails product,
            BigDecimal quantity,
            StringEntity measurementUnit,
            Entity assignedTo,
            List<fabriqon.app.business.manufacturing.ManufacturingOrder.ManufacturedProduct> manufacturedProducts,
            Integer ranking,
            boolean allMaterialsAvailable,
            int productionTime,
            boolean customProduct,
            List<ManufacturingOperationDetails> manufacturingOperations,
            List<ManufacturingTaskDetails> manufacturingTasks,
            String notes,
            MaterialIssueNoteDetails materialIssueNote,
            LocalDateTime estimatedCompletionDate,
            List<File> files
    ) {
    }

    public record ManufacturingOperationDetails(
            UUID operationTemplateId,
            List<Entity> candidateWorkstations,
            List<Entity> candidateEmployees,
            String name,
            Integer durationInMinutes,
            Boolean parallelizable,
            Money costPerHour,
            List<RequiredMaterialDetails> materials
    ) {
    }

    public record RequiredMaterialDetails(
            List<ManufacturingOperations.MaterialOptionDetails> materialGoods,
            BigDecimal required,
            BigDecimal requiredTotal,
            BigDecimal reservedTotal,
            BigDecimal onStock,
            boolean allAvailable,
            Money cost,
            BigDecimal wastePercentage,
            Dimensions requiredDimensions,
            Dimensions totalRequiredDimensions,
            SubassemblyManufacturingOrder subassemblyManufacturingOrder
    ) {
    }

    public static class ManufacturingTaskDetails extends ManufacturingTasks.ManufacturingTask {
        public final List<RequiredMaterialDetails> materials;

        @JsonCreator
        public ManufacturingTaskDetails(
                @JsonProperty("id") UUID id,
                @JsonProperty("order") ManufacturingTasks.ManufacturingOrderDetails order,
                @JsonProperty("product") Entity product,
                @JsonProperty("status") fabriqon.app.business.manufacturing.ManufacturingTask.Status status,
                @JsonProperty("statusReason") fabriqon.app.business.manufacturing.ManufacturingTask.StatusReason statusReason,
                @JsonProperty("startTime") LocalDateTime startTime,
                @JsonProperty("estimatedStartTime") LocalDateTime estimatedStartTime,
                @JsonProperty("endTime") LocalDateTime endTime,
                @JsonProperty("estimatedEndTime") LocalDateTime estimatedEndTime,
                @JsonProperty("orderStartTime") LocalDateTime orderStartTime,
                @JsonProperty("orderEndTime") LocalDateTime orderEndTime,
                @JsonProperty("durationInMinutes") int durationInMinutes,
                @JsonProperty("actualDurationInMinutes") Integer actualDurationInMinutes,
                @JsonProperty("name") String name,
                @JsonProperty("number") String number,
                @JsonProperty("quantity") BigDecimal quantity,
                @JsonProperty("measurementUnit") StringEntity measurementUnit,
                @JsonProperty("assignedWorkstations") List<Entity> assignedWorkstations,
                @JsonProperty("assignedEmployees") List<Entity> assignedEmployees,
                @JsonProperty("numberOfAssignees") Integer numberOfAssignees,
                @JsonProperty("manuallyAssigned") boolean manuallyAssigned,
                @JsonProperty("costPerHour") Money costPerHour,
                @JsonProperty("materials") List<RequiredMaterialDetails> materials) {
            super(id, order, product, status, statusReason, startTime, estimatedStartTime, endTime, estimatedEndTime, orderStartTime, orderEndTime, durationInMinutes, actualDurationInMinutes, name, number, quantity, measurementUnit, assignedWorkstations, assignedEmployees, numberOfAssignees, manuallyAssigned, costPerHour);
            this.materials = materials;
        }

        public ManufacturingTaskDetails(ManufacturingTasks.ManufacturingTask task,
                                        List<RequiredMaterialDetails> materials) {
            super(task.id,
                    task.order,
                    task.product,
                    task.status,
                    task.statusReason,
                    task.startTime,
                    task.estimatedStartTime,
                    task.endTime,
                    task.estimatedEndTime,
                    task.orderStartTime,
                    task.orderEndTime,
                    task.durationInMinutes,
                    task.actualDurationInMinutes,
                    task.name,
                    task.number,
                    task.quantity,
                    task.measurementUnit,
                    task.assignedWorkstations,
                    task.assignedEmployees,
                    task.numberOfAssignees,
                    task.manuallyAssigned,
                    task.costPerHour);
            this.materials = materials;
        }
    }

    public record ProductDetails(
            UUID id,
            String name,
            String code,
            MeasurementUnit measurementUnit,
            Money employeeAndWorkstationCosts,
            Money materialCosts,
            Money manufacturingOverheadCosts
    ) {

        ProductDetails setEmployeeAndWorkstationCosts(Money employeeAndWorkstationCosts) {
            return new ProductDetails(id, name, code, measurementUnit, employeeAndWorkstationCosts, materialCosts, manufacturingOverheadCosts);
        }

        ProductDetails setMaterialCosts(Money materialCosts) {
            return new ProductDetails(id, name, code, measurementUnit, employeeAndWorkstationCosts, materialCosts, manufacturingOverheadCosts);
        }

        ProductDetails setManufacturingOverheadCosts(Money manufacturingOverheadCosts) {
            return new ProductDetails(id, name, code, measurementUnit, employeeAndWorkstationCosts, materialCosts, manufacturingOverheadCosts);
        }
    }

    public record SubassemblyManufacturingOrder(
            UUID id,
            String number,
            ManufacturingOrder.Status status
    ) {
    }

    public record AvailableStock(UUID id, BigDecimal quantity) {
    }

    public record MaterialWishlistAddition(
            UUID materialId,
            BigDecimal quantity,
            UUID supplierId
    ) {
    }

}
