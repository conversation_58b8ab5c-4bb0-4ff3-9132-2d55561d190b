package fabriqon.app.http.controllers;

import fabriqon.app.config.security.AccountContext;
import fabriqon.misc.Json;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping(path = "/reporting")
public class ReportsController {

    private final AccountContext accountContext;

    @Value("${fabriqon.reporting.overview.url}")
    private String overviewUrl;

    @Value("${fabriqon.reporting.production.url}")
    private String productionUrl;

    @Autowired
    public ReportsController(AccountContext accountContext) {
        this.accountContext = accountContext;
    }

    @GetMapping(value = "/{report}/link")
    public Link reportLink(@AuthenticationPrincipal Jwt principal, @PathVariable("report") Report report) {
        switch (report) {
            case production: return new Link(productionUrl + getOwnershipParam(accountContext.accountId(principal)));
            default: return new Link(overviewUrl + getOwnershipParam(accountContext.accountId(principal)));
        }
    }

    private String getOwnershipParam(UUID accountId) {
        return "?params=" + URLEncoder.encode(Json.write(Map.of("q", accountId)), StandardCharsets.UTF_8);
    }

    public enum Report { overview, production }

    public record Link (String link) {};
}
