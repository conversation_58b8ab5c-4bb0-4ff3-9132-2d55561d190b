package fabriqon.app.http.controllers.purchases;

import fabriqon.app.business.customers.Note;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.purchases.PurchaseOrder;
import fabriqon.app.business.purchases.PurchasesService;
import fabriqon.app.business.suppliers.SupplierService;
import fabriqon.app.common.model.Company;
import fabriqon.app.common.model.EmailMessage;
import fabriqon.app.common.model.Money;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.controllers.NoteDefinition;
import fabriqon.app.http.controllers.NoteDetails;
import fabriqon.app.http.controllers.inventory.InventoryReceptionsController;
import fabriqon.app.http.controllers.util.Sorting;
import fabriqon.app.http.response.Entity;
import fabriqon.app.http.response.StringEntity;
import fabriqon.misc.Tuple;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.jooq.DSLContext;
import org.jooq.TableField;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static fabriqon.jooq.JooqTextSearchFunctions.textSearch;
import static fabriqon.jooq.classes.Tables.*;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@RestController
@RequestMapping(path = "/purchases/orders")
public class PurchaseOrdersController {

    private final AccountContext accountContext;
    private final PurchasesService purchasesService;
    private final SupplierService supplierService;
    private final DSLContext db;
    private final Sources sources;


    public PurchaseOrdersController(AccountContext accountContext, PurchasesService purchasesService,
                                    SupplierService supplierService, DSLContext db, Sources sources) {
        this.accountContext = accountContext;
        this.purchasesService = purchasesService;
        this.supplierService = supplierService;
        this.db = db;
        this.sources = sources;
    }

    @PostMapping(path = "create")
    public PurchaseOrderDetails create(@AuthenticationPrincipal Jwt principal,
                                       @Valid @RequestBody CreatePurchaseOrder create) {
        return details(purchasesService.createPurchaseOrder(accountContext.accountId(principal), create.supplierId, create.status,
                create.items.stream()
                        .map(item -> new PurchaseOrder.Item(item.originalWishlistId, item.materialGoodId, item.quantity, item.price, null))
                        .collect(toList()),
                create.emailMessage, create.renderingDetails,
                accountContext.userId(principal)
        ));
    }

    @Operation(responses = {
            @ApiResponse(responseCode = "200", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = PurchaseOrderDetails.class)),
                    @Content(mediaType = "application/pdf"),
                    @Content(mediaType = "text/html"),
            })
    })
    @SuppressWarnings("rawtypes")
    @PostMapping(path = "preview",
            produces = {
                    MediaType.TEXT_HTML_VALUE,
                    MediaType.APPLICATION_JSON_VALUE,
                    MediaType.APPLICATION_PDF_VALUE,
            })
    public ResponseEntity preview(@AuthenticationPrincipal Jwt principal,
                                  @RequestHeader(HttpHeaders.ACCEPT) String acceptHeader,
                                  @Validated @RequestBody PreviewPurchaseOrder preview) {
        var account = accountContext.account(principal);
        var order = new PurchaseOrder(null, Instant.now(), Instant.now(), false,
                account.id(),
                preview.supplierId,
                null,
                isNotBlank(preview.number) ? preview.number : "DRAFT",
                purchasesService.expectedDeliveryDateConsideringWeekend(LocalDate.now(), supplierService.lastOrderDeliveredIn(account.id(), preview.supplierId).orElse(1)),
                null,
                preview.items.stream()
                        .map(item -> new PurchaseOrder.Item(item.originalWishlistId, item.materialGoodId, item.quantity, item.price, null))
                        .collect(toList()),
                preview.renderingDetails,
                null
        );
        if (acceptHeader.contains(MediaType.TEXT_HTML_VALUE)) {
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(purchasesService.html(order));
        } else if (acceptHeader.contains(MediaType.APPLICATION_PDF_VALUE)) {
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                            + "purchase_order_" + DateTimeFormatter.ofPattern("yyyy-MM-dd")
                            .withZone(ZoneId.systemDefault()).format(order.createTime()) + ".pdf")
                    .body(purchasesService.pdf(purchasesService.html(order)));
        } else {
            return new ResponseEntity<>(details(order), HttpStatus.OK);
        }
    }

    private static final Map<String, TableField> sortFields = Map.of("updateTime", PURCHASE_ORDER.UPDATE_TIME);

    @GetMapping(path = "list")
    public List<PurchaseOrderDetails> orders(
            @AuthenticationPrincipal Jwt principal,
            @RequestParam(value = "q", required = false) String textSearch,
            @RequestParam(value = "status", required = false) List<PurchaseOrder.Status> statuses,
            @RequestParam(value = "supplier", required = false) UUID supplierId,
            @RequestParam(value = "sort", required = false)
            @Parameter(description = "Available fields for sorting: updateTime")
            String sorting
    ) {
        var account = accountContext.account(principal);
        var filter = PURCHASE_ORDER.OWNER_ID.eq(account.id())
                .and(PURCHASE_ORDER.DELETED.isFalse());
        if (supplierId != null) {
            filter = filter.and(PURCHASE_ORDER.SUPPLIER_ID.eq(supplierId));
        }
        if (isNotEmpty(statuses)) {
            filter = filter.and(PURCHASE_ORDER.STATUS.in(
                    statuses.stream()
                            .map(Enum::name)
                            .toList())
            );
        }
        if (isNotBlank(textSearch)) {
            filter = filter.and(textSearch(PURCHASE_ORDER.TEXT_SEARCH, textSearch));
        }
        return db.selectFrom(PURCHASE_ORDER)
                .where(filter)
                .orderBy(isNotBlank(sorting) ? Sorting.toJooqSortFields(sortFields, sorting) : List.of(PURCHASE_ORDER.CREATE_TIME.asc()))
                .fetchInto(PurchaseOrder.class)
                .stream().map(this::details).toList();
    }

    @Operation(responses = {
            @ApiResponse(responseCode = "200", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = PurchaseOrderDetails.class)),
                    @Content(mediaType = "application/pdf"),
                    @Content(mediaType = "text/html"),
            })
    })
    @SuppressWarnings("rawtypes")
    @GetMapping(path = "{id}/details",
            produces = {
                    MediaType.TEXT_HTML_VALUE,
                    MediaType.APPLICATION_JSON_VALUE,
                    MediaType.APPLICATION_PDF_VALUE,
            })
    public ResponseEntity orderDetails(@AuthenticationPrincipal Jwt principal,
                                       @RequestHeader(HttpHeaders.ACCEPT) String acceptHeader,
                                       @PathVariable("id") UUID orderId) {
        var account = accountContext.account(principal);
        var order = db.selectFrom(PURCHASE_ORDER)
                .where(
                        PURCHASE_ORDER.OWNER_ID.eq(account.id()),
                        PURCHASE_ORDER.DELETED.isFalse(),
                        PURCHASE_ORDER.ID.eq(orderId)
                )
                .fetchSingleInto(PurchaseOrder.class);
        if (acceptHeader.contains(MediaType.TEXT_HTML_VALUE)) {
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(purchasesService.html(order));
        } else if (acceptHeader.contains(MediaType.APPLICATION_PDF_VALUE)) {
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                            + "purchase_order_" + order.number() + ".pdf")
                    .body(purchasesService.pdf(purchasesService.html(order)));
        } else {
            return new ResponseEntity<>(details(order), HttpStatus.OK);
        }
    }

    @PostMapping(path = "{id}/update")
    public PurchaseOrderDetails update(@AuthenticationPrincipal Jwt principal,
                                       @PathVariable(value = "id") UUID orderId,
                                       @Valid @RequestBody UpdatePurchaseOrder update) {
        return details(purchasesService.update(accountContext.accountId(principal), orderId, update.expectedBy, update.renderingDetails, update.managedBy,
                update.items.stream()
                .map(item -> new PurchaseOrder.Item(item.originalWishlistId, item.materialGoodId, item.quantity, item.price, null))
                .collect(toList())));
    }

    @PostMapping(path = "{id}/send-for-quote")
    public void sendForQuote(@AuthenticationPrincipal Jwt principal,
                     @PathVariable("id") UUID orderId) {
        purchasesService.sendForQuote(accountContext.accountId(principal), orderId);
    }

    @PostMapping(path = "{id}/send")
    public void send(@AuthenticationPrincipal Jwt principal,
                     @PathVariable("id") UUID orderId) {
        purchasesService.send(accountContext.accountId(principal), orderId, null, null);
    }

    @PostMapping(path = "{id}/delivered", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public void delivered(@AuthenticationPrincipal Jwt principal, @PathVariable("id") UUID orderId,
                          @RequestPart("goodsReceived") @Validated InventoryReceptionsController.GoodsReceived goodsReceived,
                          @RequestPart(name = "files", required = false) List<MultipartFile> files) {
        purchasesService.markOrderDelivered(accountContext.accountId(principal), orderId,
                goodsReceived.supplierId(),
                goodsReceived.receptionDate(),
                goodsReceived.supportingDocument(),
                goodsReceived.currency(),
                goodsReceived.notes(),
                goodsReceived.receivedBy(),
                goodsReceived.transportedBy(),
                goodsReceived.transportedWith(),
                goodsReceived.goods(),
                goodsReceived.additionalCosts(),
                ofNullable(files).orElse(List.of()).stream().map(f -> {
                    try {
                        return Tuple.of(f.getOriginalFilename(), new ByteArrayInputStream(f.getBytes()));
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }).toList()
                );
    }

    @PostMapping(path = "{id}/cancel")
    public void cancel(@AuthenticationPrincipal Jwt principal, @PathVariable("id") UUID orderId) {
        purchasesService.cancel(accountContext.accountId(principal), orderId);
    }

    @PostMapping(path = "{id}/notes/add")
    public void addNote(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable("id") UUID id,
            @Validated @RequestBody NoteDefinition note) {
        purchasesService.addNote(accountContext.accountId(principal), id, accountContext.userId(principal), note.note());
    }

    @GetMapping(path = "{id}/notes/list")
    public List<NoteDetails> listNotes(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable("id") UUID id) {
        return db.select(NOTE.ID, NOTE.CREATE_TIME, NOTE.UPDATE_TIME, NOTE.DELETED, NOTE.OWNER_ID, NOTE.ADDED_BY_ID, NOTE.NOTE_)
                .from(NOTE)
                .join(PURCHASE_ORDER_NOTE).on(PURCHASE_ORDER_NOTE.NOTE_ID.eq(NOTE.ID))
                .where(
                        NOTE.OWNER_ID.eq(accountContext.accountId(principal)),
                        PURCHASE_ORDER_NOTE.PURCHASE_ORDER_ID.eq(id)
                )
                .orderBy(NOTE.CREATE_TIME.desc())
                .fetchInto(Note.class)
                .stream()
                .map(note -> new NoteDetails(note.id(), note.createTime(),
                        new Entity(note.addedById(), db.select(USERS.NAME).from(USERS).where(USERS.ID.eq(note.addedById())).fetchSingleInto(String.class)),
                        note.note()))
                .toList();
    }

    private PurchaseOrderDetails details(PurchaseOrder purchaseOrder) {
        Money totalAmount = null;
        if (isNotEmpty(purchaseOrder.items()) && purchaseOrder.items().stream().anyMatch(item -> item.price() != null)) {
            var total = purchaseOrder.items().stream()
                    .filter(item -> item.price() != null)
                    .mapToLong(item -> item.quantity()
                            .multiply(BigDecimal.valueOf(item.price().amount()).setScale(4, RoundingMode.HALF_EVEN))
                            .longValue())
                    .sum();
            var currency = purchaseOrder.items().stream().filter(item -> item.price() != null)
                    .findFirst().map(item -> item.price().currency())
                    .orElseThrow();//this should never throw an exception because of the check in the if statement
            totalAmount = new Money(total, currency);
        }

        return new PurchaseOrderDetails(
                purchaseOrder.id(),
                purchaseOrder.createTime(),
                purchaseOrder.updateTime(),
                new Entity(purchaseOrder.supplierId(), supplierName(purchaseOrder.supplierId())),
                purchaseOrder.status(),
                purchaseOrder.number(),
                purchaseOrder.expectedBy(),
                purchaseOrder.deliveredAt(),
                totalAmount,
                purchaseOrder.items().stream()
                        .map(item -> new PurchaseOrderDetails.Item(
                                item.originalWishlistId(),
                                materialGood(item.materialGoodId()),
                                item.quantity(),
                                measurementUnit(item.materialGoodId()),
                                item.price(),
                                sources.details(purchaseOrder.ownerId(), item.materialGoodId(), item.sources())
                        ))
                        .toList(),
                receptionReceipt(purchaseOrder.id()),
                purchaseOrder.renderingDetails(),
                managedBy(purchaseOrder.managedBy())
        );
    }

    private Entity managedBy(UUID userId) {
        if (userId == null) {
            return null;
        }
        return db.select(USERS.ID, USERS.NAME).from(USERS).where(USERS.ID.eq(userId)).fetchSingleInto(Entity.class);
    }

    private StringEntity measurementUnit(UUID materialGoodId) {
        var commonDetails = db.select(MATERIAL_GOOD.DETAILS)
                .from(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(materialGoodId))
                .fetchSingleInto(MaterialGood.Details.class);
        return new StringEntity(commonDetails.measurementUnit().name(), commonDetails.measurementUnit().symbol());
    }

    private String supplierName(UUID supplierId) {
        return db.selectFrom(COMPANY)
                .where(COMPANY.ID.eq(supplierId))
                .fetchSingle().into(Company.class)
                .name();
    }

    private PurchaseOrderDetails.Item.MaterialGood materialGood(UUID materialGoodId) {
        return db.select(MATERIAL_GOOD.ID, MATERIAL_GOOD.NAME, MATERIAL_GOOD.CODE)
                .from(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(materialGoodId))
                .fetchSingleInto(PurchaseOrderDetails.Item.MaterialGood.class);
    }

    private Entity receptionReceipt(UUID id) {
        return db.select(RECEPTION_RECEIPT.ID, RECEPTION_RECEIPT.NUMBER)
                .from(RECEPTION_RECEIPT)
                .where(RECEPTION_RECEIPT.PURCHASE_ORDER_ID.eq(id))
                .fetchOneInto(Entity.class);
    }

    public record CreatePurchaseOrder(
            UUID supplierId,
            PurchaseOrder.Status status,
            @Valid
            List<Item> items,
            EmailMessage emailMessage,
            PurchaseOrder.RenderingDetails renderingDetails
    ) {
    }

    public record UpdatePurchaseOrder(
            LocalDate expectedBy,
            PurchaseOrder.RenderingDetails renderingDetails,
            UUID managedBy,
            @Valid
            List<Item> items
    ) {
    }

    public record Item(
            UUID originalWishlistId,
            @NotNull
            UUID materialGoodId,
            @NotNull
            BigDecimal quantity,
            Money price
    ) {
    }

    public record PreviewPurchaseOrder(
            String number,
            UUID supplierId,
            List<Item> items,
            EmailMessage emailMessage,
            PurchaseOrder.RenderingDetails renderingDetails
    ) {

        public record Item(
                UUID originalWishlistId,
                UUID materialGoodId,
                BigDecimal quantity,
                Money price
        ) {
        }
    }

    public record PurchaseOrderDetails(
            UUID id,
            Instant createTime,
            Instant updateTime,

            Entity supplier,
            PurchaseOrder.Status status,
            String number,
            LocalDate expectedDelivery,
            LocalDateTime deliveredAt,
            Money totalAmount,
            List<Item> items,
            Entity receptionReceipt,
            PurchaseOrder.RenderingDetails renderingDetails,
            Entity managedBy
    ) {

        public record Item(
                UUID originalWishlistId,
                MaterialGood materialGood,
                BigDecimal quantity,
                StringEntity measurementUnit,
                Money price,
                List<Sources.SourceDetails> sources
        ) {
            public record MaterialGood(
                    UUID id,
                    String name,
                    String code
            ) {
            }
        }

    }

}
