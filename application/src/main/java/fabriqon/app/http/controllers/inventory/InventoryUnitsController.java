package fabriqon.app.http.controllers.inventory;

import fabriqon.app.business.inventory.InventoryUnit;
import fabriqon.app.business.inventory.InventoryUnitService;
import fabriqon.app.config.security.AccountContext;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.INVENTORY_UNIT;

@RestController
@RequestMapping(path = "/inventory/units")
public class InventoryUnitsController {
    private final AccountContext accountContext;
    private final InventoryUnitService inventoryUnitService;
    private final DSLContext db;

    @Autowired
    public InventoryUnitsController(final AccountContext accountContext,
                                    final InventoryUnitService inventoryUnitService,
                                    final DSLContext db) {
        this.accountContext = accountContext;
        this.inventoryUnitService = inventoryUnitService;
        this.db = db;
    }

    @PostMapping(path = "create")
    public InventoryUnit create(@AuthenticationPrincipal Jwt principal, @Validated @RequestBody InventoryUnitDefinition create) {
        return inventoryUnitService.create(accountContext.accountId(principal), create.name());
    }

    @GetMapping(path = "list")
    public List<InventoryUnit> list(@AuthenticationPrincipal Jwt principal) {
        return db.select()
                .from(INVENTORY_UNIT)
                .where(INVENTORY_UNIT.OWNER_ID.eq(accountContext.accountId(principal)), INVENTORY_UNIT.DELETED.isFalse())
                .fetchInto(InventoryUnit.class);
    }

    @PostMapping(path = "{id}/update")
    public void update(@AuthenticationPrincipal Jwt principal,
                       @PathVariable("id") UUID categoryId,
                       @Validated @RequestBody InventoryUnitDefinition create) {
        inventoryUnitService.update(categoryId, accountContext.accountId(principal), create.name);
    }

    @DeleteMapping(path = "{id}/delete")
    public void delete(@AuthenticationPrincipal Jwt principal,
                       @PathVariable("id") UUID unitId) {
        inventoryUnitService.delete(unitId, accountContext.accountId(principal));
    }

    public record InventoryUnitDefinition(
            String name
    ) {
    }
}