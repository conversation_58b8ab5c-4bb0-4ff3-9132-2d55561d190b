package fabriqon.app.http.controllers.manufacturing;

import fabriqon.app.business.manufacturing.Workstation;
import fabriqon.app.business.manufacturing.WorkstationService;
import fabriqon.app.common.model.Money;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.response.Entity;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.*;

@RestController
@RequestMapping(path = "/manufacturing/workstations")
public class ManufacturingWorkstationsController {
    private final AccountContext accountContext;
    private final WorkstationService workstationService;
    private final DSLContext db;

    @Autowired
    public ManufacturingWorkstationsController(final AccountContext accountContext,
                                               final WorkstationService workstationService,
                                               final DSLContext db) {
        this.accountContext = accountContext;
        this.workstationService = workstationService;
        this.db = db;
    }

    @PostMapping(path = "create")
    public ManufacturingWorkstationDetails create(@AuthenticationPrincipal Jwt principal, @Validated @RequestBody ManufacturingWorkstationDefinition create) {
        return map(workstationService.create(accountContext.accountId(principal),
                create.manufacturingOperationTemplates,
                create.name(),
                new Workstation.Details(create.costPerHour)));
    }

    @PostMapping(path = "{id}/update")
    public ManufacturingWorkstationDetails update(@AuthenticationPrincipal Jwt principal,
                                                  @PathVariable(value = "id") UUID workstationId,
                                                  @Validated @RequestBody ManufacturingWorkstationDefinition update) {
        return map(workstationService.update(accountContext.accountId(principal), workstationId,
                update.manufacturingOperationTemplates,
                update.name(),
                update.costPerHour));
    }

    @DeleteMapping(path = "{id}/delete")
    public void delete(@AuthenticationPrincipal Jwt principal,
                                                  @PathVariable(value = "id") UUID workstationId) {
        workstationService.delete(accountContext.accountId(principal), workstationId);
    }

    @GetMapping(path = "list")
    public List<ManufacturingWorkstationDetails> list(@AuthenticationPrincipal Jwt principal) {
        var accountId = accountContext.accountId(principal);
        return db.select()
                .from(MANUFACTURING_WORKSTATION)
                .where(MANUFACTURING_WORKSTATION.OWNER_ID.eq(accountId), MANUFACTURING_WORKSTATION.DELETED.isFalse())
                .orderBy(MANUFACTURING_WORKSTATION.NAME)
                .fetchInto(Workstation.class)
                .stream().map(this::map).toList();
    }

    private ManufacturingWorkstationDetails map(Workstation workstation) {
        return new ManufacturingWorkstationDetails(
                workstation.id(),
                workstation.createTime(),
                workstation.updateTime(),
                workstation.name(),
                workstation.details().costPerHour(),
                operationTemplates(workstation.id())
        );
    }

    private List<Entity> operationTemplates(UUID workstationId) {
        return db.select(MANUFACTURING_OPERATION_TEMPLATE.ID, MANUFACTURING_OPERATION_TEMPLATE.NAME)
                .from(MANUFACTURING_OPERATION_TEMPLATE)
                .join(MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE).on(MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_OPERATION_TEMPLATE_ID.eq(MANUFACTURING_OPERATION_TEMPLATE.ID))
                .where(MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_WORKSTATION_ID.eq(workstationId), MANUFACTURING_OPERATION_TEMPLATE.DELETED.isFalse())
                .fetchInto(Entity.class);
    }

    public record ManufacturingWorkstationDefinition(
            String name,
            Money costPerHour,
            List<UUID> manufacturingOperationTemplates
    ) {
    }

    public record ManufacturingWorkstationDetails(
            UUID id,
            Instant createTime,
            Instant updateTime,
            String name,
            Money costPerHour,
            List<Entity> manufacturingOperationTemplates
    ) {
    }

}