package fabriqon.app.http.controllers.util;

import fabriqon.app.business.exceptions.BusinessException;
import org.jooq.SortField;
import org.jooq.SortOrder;
import org.jooq.TableField;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.apache.logging.log4j.util.Strings.isBlank;

public class Sorting {

    @SuppressWarnings({"unchecked", "raw"})
    public static List<SortField<?>> toJooqSortFields(Map<String, TableField> supportedSortFields, String sortString) {
        if (isBlank(sortString)) {
            return List.of();
        }
        var sortExpressions = Arrays.stream(sortString.split(","))
                .map(s -> {
                    if (s.startsWith("-")) {
                        return new SortExpression(false, s.substring(1));
                    } else {
                        return new SortExpression(true, s);
                    }
                })
                .toList();
        if (sortExpressions.stream().anyMatch(e -> !supportedSortFields.containsKey(e.name))) {
            throw new BusinessException("Invalid sort fields.", "invalid_sort_fields");
        }

        return (List) sortExpressions
                .stream()
                .map(e -> supportedSortFields.get(e.name).sort(e.ascending ? SortOrder.ASC : SortOrder.DESC))
                .toList();
    }

    private record SortExpression(
            boolean ascending,
            String name
    ) { }

}
