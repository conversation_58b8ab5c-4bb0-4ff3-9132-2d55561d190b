package fabriqon.app.http.controllers.purchases;

import fabriqon.app.business.manufacturing.ManufacturingService;
import fabriqon.app.business.purchases.Source;
import fabriqon.app.business.sales.SalesOrder;
import fabriqon.app.business.sales.SalesService;
import fabriqon.app.http.response.Entity;
import org.jooq.DSLContext;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.COMPANY;

@Component
public class Sources {

    private final DSLContext db;
    private final SalesService salesService;
    private final ManufacturingService manufacturingService;

    public Sources(DSLContext db, SalesService salesService, ManufacturingService manufacturingService) {
        this.db = db;
        this.salesService = salesService;
        this.manufacturingService = manufacturingService;
    }

    public List<SourceDetails> details(UUID ownerId, UUID materialGoodId, List<Source> sources) {
        return Optional.ofNullable(sources).orElse(List.of()).stream()
                .map(source ->
                        switch (source.section()) {
                            case SALES_ORDER -> salesSource(ownerId, materialGoodId, source.id());
                            case MANUFACTURING_ORDER -> manufacturingSource(ownerId, materialGoodId, source.id());
                        })
                .toList();
    }

    private SourceDetails salesSource(UUID ownerId, UUID materialGoodId, UUID orderId) {
        var order = salesService.order(ownerId, orderId);
        return new SourceDetails(
                Source.Section.SALES_ORDER,
                orderId,
                order.number(),
                new Entity(order.customerId(), customerName(order.customerId())),
                order.items().stream()
                        .filter(item -> materialGoodId.equals(item.productId()))
                        .map(SalesOrder.Item::quantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
        );
    }

    private SourceDetails manufacturingSource(UUID ownerId, UUID materialGoodId, UUID orderId) {
        var order = manufacturingService.order(ownerId, orderId);
        Entity customer = null;
        if (order.salesOrderId() != null) {
            var salesOrder = salesService.order(ownerId, order.salesOrderId());
            customer = new Entity(salesOrder.customerId(), customerName(salesOrder.customerId()));
        }
        return new SourceDetails(
                Source.Section.MANUFACTURING_ORDER,
                orderId,
                order.number(),
                customer,
                manufacturingService.requiredMaterials(order).stream()
                        .filter(material -> material.materialIds().contains(materialGoodId))
                        .map(material -> material.quantity().multiply(order.quantity()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
        );
    }

    private String customerName(UUID customerId) {
        return db.select(COMPANY.COMPANY_NAME)
                .from(COMPANY)
                .where(COMPANY.ID.eq(customerId))
                .fetchSingleInto(String.class);
    }

    public record SourceDetails(
            Source.Section section,
            UUID id,
            String number,
            Entity customer,
            BigDecimal quantity
    ) {
    }

}
