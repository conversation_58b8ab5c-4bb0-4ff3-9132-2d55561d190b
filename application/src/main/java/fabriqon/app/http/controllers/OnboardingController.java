package fabriqon.app.http.controllers;

import fabriqon.app.business.onboarding.OnboardingService;
import fabriqon.app.config.security.AccountContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.zip.ZipInputStream;

@RestController
@RequestMapping(path = "/onboarding")
public class OnboardingController {

    private final AccountContext accountContext;
    private final OnboardingService onboardingService;

    @Autowired
    public OnboardingController(AccountContext accountContext, OnboardingService onboardingService) {
        this.accountContext = accountContext;
        this.onboardingService = onboardingService;
    }

    @PostMapping(value = "import")
    public void importData(@AuthenticationPrincipal Jwt principal, @RequestParam("file") MultipartFile file) throws IOException {
        onboardingService.importData(accountContext.accountId(principal), new ZipInputStream(file.getInputStream()));
    }

}
