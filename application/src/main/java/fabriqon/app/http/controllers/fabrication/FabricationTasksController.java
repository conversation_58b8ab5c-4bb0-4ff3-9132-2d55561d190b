package fabriqon.app.http.controllers.fabrication;

import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.manufacturing.ManufacturingService;
import fabriqon.app.business.manufacturing.ManufacturingTask;
import fabriqon.app.business.services.ServicingOrder;
import fabriqon.app.common.model.DevicePairingTokenType;
import fabriqon.app.http.controllers.MyTasksController;
import fabriqon.app.http.response.Entity;
import fabriqon.app.http.response.StringEntity;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

import static fabriqon.jooq.classes.Tables.*;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.jooq.impl.DSL.and;

@RestController
@RequestMapping(path = "/fabrication/tasks")
public class FabricationTasksController {

    private final DSLContext db;

    private final ManufacturingService manufacturingService;

    @Autowired
    public FabricationTasksController(DSLContext db,
                                      ManufacturingService manufacturingService) {
        this.db = db;
        this.manufacturingService = manufacturingService;
    }

    @GetMapping(path = "list")
    public List<TaskDetails> list(
            @RequestHeader("X-Auth") String xAuthHeader,
            @RequestParam(value = "status", required = false) List<ManufacturingTask.Status> statuses,
            @RequestParam(value = "day", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate day) {

        var employeeId = employeeIdFromAuth(xAuthHeader);
        var filter = MANUFACTURING_TASK.DELETED.isFalse().and(MANUFACTURINGTASK_EMPLOYEE.USER_ID.eq(employeeId));
        if (isNotEmpty(statuses)) {
            filter = filter.and(MANUFACTURING_TASK.STATUS.in(
                    statuses.stream()
                            .map(status -> ManufacturingTask.Status.valueOf(status.name()))
                            .toList())
            );
        }
        if (day != null) {
            filter = filter.and(and(MANUFACTURING_TASK.START_TIME.gt(day.atStartOfDay()), MANUFACTURING_TASK.START_TIME.lt(day.plusDays(1).atStartOfDay()))
                    .or(MANUFACTURING_TASK.START_TIME.lt(day.atStartOfDay())
                            .and(MANUFACTURING_TASK.STATUS.ne(ManufacturingTask.Status.DONE.name()))));
        }
        var tasks = db.select()
                .from(MANUFACTURING_TASK)
                .join(MANUFACTURINGTASK_EMPLOYEE).on(MANUFACTURINGTASK_EMPLOYEE.MANUFACTURING_TASK_ID.eq(MANUFACTURING_TASK.ID))
                .where(filter)
                .orderBy(MANUFACTURING_TASK.START_TIME)
                .fetchInto(ManufacturingTask.class)
                .stream().map(this::details).toList();

        var serviceMos = db.selectFrom(SERVICING_ORDER)
                .where(SERVICING_ORDER.ASSIGNED_TO.eq(employeeId),
                        SERVICING_ORDER.STATUS.ne(ServicingOrder.Status.CLOSED.name()))
                .fetchInto(ServicingOrder.class).stream()
                .map(o -> new TaskDetails(
                        o.id(),
                        null,
                        MyTasksController.MyTask.Type.SERVICE,
                        null,
                        o.number(),
                        target(o.id()),
                        null, o.status().name(), null, 0,
                        o.notes(),
                        o.materials().stream()
                                .map(m -> {
                                    var material = materialRecord(m.materialIds().getFirst());
                                    return new MaterialDetails(material.id, material.name, material.code, m.quantity(), new StringEntity(material.details.measurementUnit().name(), material.details.measurementUnit().symbol()));
                                })
                                .toList()
                ));

        return Stream.concat(tasks.stream(), serviceMos).toList();
    }

    private UUID employeeIdFromAuth(String authHeader) {
        return db.select(DEVICE_PAIRING_TOKEN.USER_ID)
                .from(DEVICE_PAIRING_TOKEN)
                .where(DEVICE_PAIRING_TOKEN.TOKEN.eq(authHeader), DEVICE_PAIRING_TOKEN.TOKEN_TYPE.eq(DevicePairingTokenType.ACCESS.name()))
                .fetchSingleInto(UUID.class);
    }

    private TaskDetails details(ManufacturingTask tasks) {
        return new TaskDetails(
                tasks.id(),
                db.select(MANUFACTURING_ORDER.ID, MANUFACTURING_ORDER.NUMBER).from(MANUFACTURING_ORDER).where(MANUFACTURING_ORDER.ID.eq(tasks.manufacturingOrderId())).fetchSingleInto(Entity.class),
                MyTasksController.MyTask.Type.MANUFACTURING_TASK,
                tasks.details().name(),
                tasks.details().number(),
                target(tasks.manufacturingOrderId()),
                tasks.startTime(),
                tasks.status().name(), tasks.statusReason(),
                tasks.durationInMinutes(),
                notes(tasks),
                materials(tasks.ownerId(), tasks.manufacturingOrderId())
        );
    }

    private List<MaterialDetails> materials(UUID ownerId, UUID orderId) {
        return manufacturingService.requiredMaterials(manufacturingService.order(ownerId, orderId))
                .stream()
                .map(material -> {
                    var materialRecord = materialRecord(material.materialIds().get(0));
                    return new MaterialDetails(
                            material.materialIds().get(0),
                            materialRecord.name,
                            materialRecord.code,
                            material.quantity(),
                            new StringEntity(materialRecord.details.measurementUnit().name(), materialRecord.details.measurementUnit().symbol())
                    );
                })
                .toList();
    }

    private String notes(ManufacturingTask task) {
        return db.select(MANUFACTURING_ORDER.NOTES)
                .from(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.ID.eq(task.manufacturingOrderId()))
                .fetchSingleInto(String.class);
    }

    private MaterialGood materialRecord(UUID materialId) {
        return db.selectFrom(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(materialId))
                .fetchSingleInto(MaterialGood.class);
    }

    private Entity target(UUID orderId) {
        return db.select(MATERIAL_GOOD.ID, MATERIAL_GOOD.NAME)
                .from(MATERIAL_GOOD)
                .join(MANUFACTURING_ORDER).on(MANUFACTURING_ORDER.PRODUCT_ID.eq(MATERIAL_GOOD.ID))
                .where(MANUFACTURING_ORDER.ID.eq(orderId))
                .fetchOptionalInto(Entity.class)
                .orElseGet(() -> db.select(SERVICE_TEMPLATE.ID, SERVICE_TEMPLATE.NAME)
                        .from(SERVICE_TEMPLATE)
                        .join(SERVICING_ORDER).on(SERVICING_ORDER.SERVICE_ID.eq(SERVICE_TEMPLATE.ID))
                        .where(SERVICING_ORDER.ID.eq(orderId))
                        .fetchSingleInto(Entity.class)
                );
    }

    public record TaskDetails(
            UUID id,
            Entity parent,
            MyTasksController.MyTask.Type type,
            String name,
            String number,
            Entity target,
            LocalDateTime startTime,
            String status,
            ManufacturingTask.StatusReason statusReason,
            int durationInMinutes,
            String notes,
            List<MaterialDetails> materials
    ) {
        public enum Type {MANUFACTURING_TASK, SERVICE}
    }

    public record MaterialDetails(
            UUID id,
            String name,
            String code,
            BigDecimal quantity,
            StringEntity measurementUnit
    ) {
    }
}
