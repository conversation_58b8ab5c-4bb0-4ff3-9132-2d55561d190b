package fabriqon.app.http.controllers;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.config.security.AccountContext;
import fabriqon.misc.Json;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static fabriqon.jooq.classes.Tables.ACCOUNT;

@RestController
@RequestMapping(path = "/account/settings")
public class AccountSettingsController {

    private final AccountContext accountContext;
    private final DSLContext db;

    public AccountSettingsController(AccountContext accountContext, DSLContext db) {
        this.accountContext = accountContext;
        this.db = db;
    }

    @PostMapping(path = "update")
    public int update(@AuthenticationPrincipal Jwt principal, @Validated @RequestBody Account.Settings update) {
        return db.update(ACCOUNT)
                .set(ACCOUNT.SETTINGS, JSONB.jsonb(Json.write(update)))
                .where(ACCOUNT.ID.eq(accountContext.accountId(principal)))
                .execute();
    }

    @GetMapping
    public Account.Settings settings(@AuthenticationPrincipal Jwt principal) {
        return db.selectFrom(ACCOUNT)
                .where(ACCOUNT.ID.eq(accountContext.accountId(principal)))
                .fetchSingle().into(Account.class)
                .settings();
    }


}
