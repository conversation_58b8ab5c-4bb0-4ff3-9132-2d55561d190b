package fabriqon.app.http.controllers;

public class HttpApiConstants {

    public static final String LIMIT_QUERY = "limit";
    public static final String LIMIT_DEFAULT = "1000";//TODO this is set so high because we don't have pagination now
    public static final String OFFSET_QUERY = "offset";
    public static final String OFFSET_DEFAULT = "0";
    public static final String SORT_QUERY = "sort";

}
