package fabriqon.app.http.controllers;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.accounts.AccountService;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.response.Entity;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static fabriqon.jooq.classes.Tables.ACCOUNT;

@RestController
@RequestMapping(path = "/account/information")
@Slf4j
public class AccountInformationController {

    private final AccountContext accountContext;
    private final DSLContext db;
    private final AccountService accountService;

    public AccountInformationController(AccountContext accountContext, DSLContext db, AccountService accountService) {
        this.accountContext = accountContext;
        this.db = db;
        this.accountService = accountService;
    }


    @GetMapping(path = "name")
    public Entity name(@AuthenticationPrincipal Jwt principal) {
        log.info("account id: [{}]", accountContext.accountId(principal));
        return db.select(ACCOUNT.ID, ACCOUNT.NAME)
                .from(ACCOUNT)
                .where(ACCOUNT.ID.eq(accountContext.accountId(principal)))
                .fetchSingleInto(Entity.class);
    }

    @GetMapping
    public Account.Information accountInformation(@AuthenticationPrincipal Jwt principal) {
        return db.select(ACCOUNT.INFORMATION)
                .from(ACCOUNT)
                .where(ACCOUNT.ID.eq(accountContext.accountId(principal)))
                .fetchSingleInto(Account.Information.class);
    }

    @PutMapping(path = "update")
    public void update(@AuthenticationPrincipal Jwt principal,
                           @Validated @RequestBody Account.Information update) {
        accountService.updateAccountInformation(accountContext.accountId(principal), update);
    }


}
