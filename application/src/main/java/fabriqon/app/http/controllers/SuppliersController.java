package fabriqon.app.http.controllers;

import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.inventory.InventoryService;
import fabriqon.app.business.manufacturing.ManufacturingOrder;
import fabriqon.app.business.purchases.PurchaseOrder;
import fabriqon.app.business.sales.SalesOrder;
import fabriqon.app.business.suppliers.SupplierService;
import fabriqon.app.common.model.*;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.response.Company;
import fabriqon.app.http.response.Entity;
import fabriqon.app.http.response.StringEntity;
import fabriqon.misc.Json;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import static fabriqon.jooq.JooqJsonbFunctions.arrayContainsJson;
import static fabriqon.jooq.JooqTextSearchFunctions.textSearch;
import static fabriqon.jooq.classes.Tables.*;
import static java.lang.String.format;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.jooq.impl.DSL.*;

@RestController
@RequestMapping(path = "/suppliers")
public class SuppliersController {

    private final AccountContext accountContext;
    private final SupplierService supplierService;
    private final InventoryService inventoryService;
    private final DSLContext db;

    @Autowired
    public SuppliersController(final AccountContext accountContext,
                               final SupplierService supplierService, InventoryService inventoryService,
                               final DSLContext db) {
        this.accountContext = accountContext;
        this.supplierService = supplierService;
        this.inventoryService = inventoryService;
        this.db = db;
    }

    @PostMapping(path = "create")
    public Supplier create(@AuthenticationPrincipal Jwt principal, @Validated @RequestBody SupplierDefinition create) {
        return details(supplierService.create(accountContext.accountId(principal),
                fabriqon.app.common.model.Company.Type.LOCAL_LEGAL_ENTITY, create.name(),
                new fabriqon.app.common.model.Company.Details(create.identificationNumber, create.taxIdentificationNumber,
                        create.addresses, create.contacts, create.bankAccounts, create.notes)));
    }

    @GetMapping(path = "list")
    public List<Supplier> list(@AuthenticationPrincipal Jwt principal,
                               @RequestParam(value = "q", required = false) String textSearch) {
        var filter = COMPANY.DELETED.isFalse();
        if (isNotBlank(textSearch)) {
            filter = filter.and(textSearch(COMPANY.TEXT_SEARCH, textSearch));
        }
        return db.select().from(COMPANY)
                .join(ACCOUNT_SUPPLIER).on(COMPANY.ID.eq(ACCOUNT_SUPPLIER.SUPPLIER_ID).and(ACCOUNT_SUPPLIER.OWNER_ID.eq(accountContext.accountId(principal))))
                .where(filter)
                .fetchInto(fabriqon.app.common.model.Company.class)
                .stream().map(this::details).toList();
    }

    @GetMapping(path = "last-suppliers-for")
    public List<Entity> lastSuppliersFor(@AuthenticationPrincipal Jwt principal,
                                         @RequestParam(value = "materialGoodId", required = false) UUID materialGoodId) {
        return db.select(COMPANY.ID, COMPANY.COMPANY_NAME)
                .from(COMPANY)
                .join(ACCOUNT_SUPPLIER).on(COMPANY.ID.eq(ACCOUNT_SUPPLIER.SUPPLIER_ID).and(ACCOUNT_SUPPLIER.OWNER_ID.eq(accountContext.accountId(principal))))
                .where(COMPANY.ID.in(
                        select(INVENTORY.SUPPLIER_ID).on(INVENTORY.SUPPLIER_ID, INVENTORY.CREATE_TIME)
                                .from(INVENTORY)
                                .where(INVENTORY.MATERIAL_GOOD_ID.eq(materialGoodId), INVENTORY.SUPPLIER_ID.isNotNull())
                                .groupBy(INVENTORY.SUPPLIER_ID, INVENTORY.CREATE_TIME)
                                .orderBy(INVENTORY.CREATE_TIME.desc())
                                .limit(5)
                ))
                .fetchInto(Entity.class);
    }

    @GetMapping(path = "{id}/last-ordered-goods")
    public List<Material> lastOrderedGoods(@AuthenticationPrincipal Jwt principal,
                                           @PathVariable(value = "id", required = false) UUID id) {
        var accountId = accountContext.accountId(principal);
        return db.select(INVENTORY.MATERIAL_GOOD_ID,
                        INVENTORY.QUANTITY,
                        INVENTORY.RECEPTION_PRICE_AMOUNT,
                        INVENTORY.RECEPTION_PRICE_CURRENCY,
                        MATERIAL_GOOD.NAME,
                        MATERIAL_GOOD.CODE,
                        MATERIAL_GOOD.DETAILS
                )
                .distinctOn(INVENTORY.MATERIAL_GOOD_ID)
                .from(INVENTORY)
                .join(MATERIAL_GOOD).on(MATERIAL_GOOD.ID.eq(INVENTORY.MATERIAL_GOOD_ID))
                .where(INVENTORY.OWNER_ID.eq(accountId), INVENTORY.SUPPLIER_ID.eq(id), MATERIAL_GOOD.OWNER_ID.eq(accountId))
                .orderBy(INVENTORY.MATERIAL_GOOD_ID, INVENTORY.CREATE_TIME.desc())
                .limit(10)
                .fetch().stream()
                .map(record -> {
                    var details = Json.read(record.value7().data(), MaterialGood.Details.class);
                    return new Material(
                        record.value1(),
                        record.value5(),
                        record.value6(),
                        new StringEntity(details.measurementUnit().name(), details.measurementUnit().symbol()),
                        record.value2(),
                        new Money(record.value3(), record.value4()),
                        inventoryService.getCurrentStock(accountId, record.value1()),
                        getCommitted(accountId, record.value1()),
                        getIncoming(accountId, record.value1()),
                        details.criticalOnHand()
                );
                })
                .toList();
    }

    @GetMapping(path = "{id}/details")
    public Supplier details(@AuthenticationPrincipal Jwt principal,
                            @PathVariable("id") UUID id) {
        return details(db.select().from(COMPANY)
                .join(ACCOUNT_SUPPLIER).on(COMPANY.ID.eq(ACCOUNT_SUPPLIER.SUPPLIER_ID).and(ACCOUNT_SUPPLIER.OWNER_ID.eq(accountContext.accountId(principal))))
                .where(COMPANY.ID.eq(id))
                .fetchSingleInto(fabriqon.app.common.model.Company.class));
    }

    @PostMapping(path = "{id}/update")
    public void update(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable("id") UUID id,
            @Validated @RequestBody SupplierDefinition update) {
        supplierService.update(accountContext.accountId(principal), id, null, update.name,
                new fabriqon.app.common.model.Company.Details(update.identificationNumber, update.taxIdentificationNumber,
                        update.addresses, update.contacts, update.bankAccounts, update.notes));
    }

    @DeleteMapping(path = "{id}/delete")
    public void delete(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable("id") UUID id) {
        db.update(COMPANY)
                .set(COMPANY.DELETED, true)
                .where(COMPANY.ID.eq(id), COMPANY.OWNER_ID.eq(accountContext.accountId(principal)))
                .execute();
    }


    @PostMapping(path = "{id}/attach-file", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public Supplier attachFile(@AuthenticationPrincipal Jwt principal,
                                                   @PathVariable("id") UUID customerId,
                                                   @RequestParam("file") MultipartFile file) throws IOException {
        return details(supplierService.attach(accountContext.accountId(principal), customerId,
                file.getOriginalFilename(), file.getBytes()));
    }

    @DeleteMapping(path = "{id}/files/{fileId}/delete")
    public Supplier deleteFile(@AuthenticationPrincipal Jwt principal,
                                                   @PathVariable("id") UUID orderId,
                                                   @PathVariable("fileId") UUID fileId) throws IOException {
        return details(supplierService.deleteFile(accountContext.accountId(principal), orderId, fileId));
    }

    private Supplier details(fabriqon.app.common.model.Company company) {
        return new Supplier(
                company.id(),
                company.type(),
                company.name(),
                null,
                company.details().identificationNumber(),
                company.details().taxIdentificationNumber(),
                company.details().addresses(),
                company.details().contacts(),
                company.details().bankAccounts(),
                company.details().notes(),
                files(company.ownerId(), company.id()),
                supplierService.lastOrderDeliveredIn(company.ownerId(), company.id()).orElse(null)
        );
    }

    private BigDecimal getCommitted(UUID ownerId, UUID materialId) {
        //sales order where the product is present
        var sales = db.selectFrom(SALES_ORDER)
                .where(SALES_ORDER.DELETED.isFalse()
                        .and(SALES_ORDER.OWNER_ID.eq(ownerId))
                        .and(SALES_ORDER.STATUS.in(SalesOrder.Status.SUBMITTED.name(), SalesOrder.Status.PROCESSING.name(), SalesOrder.Status.PICKING_PACKING.name(), SalesOrder.Status.READY_TO_SHIP.name()))
                        .and(arrayContainsJson(SALES_ORDER.ITEMS, format("[{\"productId\": \"%s\"}]", materialId)))
                )
                .fetchInto(SalesOrder.class)
                .stream()
                .map(order -> order.items().stream()
                        .filter(item -> materialId.equals(item.productId()))
                        .map(SalesOrder.Item::quantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //manufacturing orders where the material is used
        var manufacturing = db.select().from(MANUFACTURING_ORDER,
                lateral(table("jsonb_array_elements(manufacturing_operations)").as("operations")),
                lateral(table("jsonb_array_elements(operations -> 'materials')").as("materials")))
                .where(MANUFACTURING_ORDER.DELETED.isFalse()
                        .and(MANUFACTURING_ORDER.STATUS.in(ManufacturingOrder.Status.SUBMITTED.name(), ManufacturingOrder.Status.MANUFACTURING.name()))
                        .and(MANUFACTURING_ORDER.OWNER_ID.eq(ownerId))
                        .and(field("materials -> 'materialIds' ->> 0", String.class).eq(materialId.toString()))
                )
                .fetchInto(ManufacturingOrder.class)
                .stream()
                .map(order -> order.manufacturingOperations().stream().flatMap(op -> op.materials().stream())
                        .filter(item -> materialId.equals(item.materialIds().get(0)))
                        .map(m -> m.quantity().multiply(order.quantity()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return sales.add(manufacturing);
    }

    private BigDecimal getIncoming(UUID accountId, UUID goodId) {
        var fromPurchases = db.selectFrom(PURCHASE_ORDER)
                .where(
                        PURCHASE_ORDER.DELETED.isFalse(),
                        PURCHASE_ORDER.OWNER_ID.eq(accountId),
                        PURCHASE_ORDER.STATUS.eq(PurchaseOrder.Status.SUBMITTED.name())
                )
                .fetchInto(PurchaseOrder.class)
                .stream()
                .map(order -> order.items().stream()
                        .filter(item -> item.materialGoodId().equals(goodId))
                        .map(PurchaseOrder.Item::quantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                )
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        var fromManufacturing = db.select(MANUFACTURING_ORDER.QUANTITY).from(MANUFACTURING_ORDER)
                .where(
                        MANUFACTURING_ORDER.DELETED.isFalse(),
                        MANUFACTURING_ORDER.OWNER_ID.eq(accountId),
                        MANUFACTURING_ORDER.STATUS.in(ManufacturingOrder.Status.SUBMITTED.name(), ManufacturingOrder.Status.MANUFACTURING.name()),
                        MANUFACTURING_ORDER.PRODUCT_ID.eq(goodId)
                )
                .fetchInto(BigDecimal.class)
                .stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return fromPurchases.add(fromManufacturing);
    }

    private List<File> files(UUID ownerId, UUID companyId) {
        return db.select(FILE.ID, FILE.FILE_NAME)
                .from(FILE)
                .where(FILE.ID.in(select(COMPANY_FILE.FILE_ID).from(COMPANY_FILE).where(COMPANY_FILE.OWNER_ID.eq(ownerId), COMPANY_FILE.COMPANY_ID.eq(companyId))))
                .fetch()
                .stream()
                .map(f -> new File(f.value1(), f.value2(),
                        FilesController.previewLink(f.value1()), FilesController.downloadLink(f.value1())))
                .toList();
    }

    public record SupplierDefinition(
            String name,
            String identificationNumber,
            String taxIdentificationNumber,
            List<Address> addresses,
            List<ContactPerson> contacts,
            List<BankAccount> bankAccounts,
            String notes,
            Integer deliveryTimeInDays
    ) {
    }

    public class Supplier extends Company {
        public final Integer lastOrderDeliveredIn;

        public Supplier(UUID id, fabriqon.app.common.model.Company.Type type, String name, String email, String identificationNumber, String taxIdentificationNumber,
                        List<Address> addresses, List<ContactPerson> contacts, List<BankAccount> bankAccounts, String notes, List<File> files,
                        Integer lastOrderDeliveredIn) {
            super(id, type, name, email, identificationNumber, taxIdentificationNumber, addresses, contacts, bankAccounts, notes, files);
            this.lastOrderDeliveredIn = lastOrderDeliveredIn;
        }
    }

    public record Material(
            UUID id,
            String name,
            String code,
            StringEntity measurementUnit,
            BigDecimal quantity,
            Money price,
            BigDecimal stock,
            BigDecimal committed,
            BigDecimal incoming,
            BigDecimal criticalOnHand
    ) {
    }

}
