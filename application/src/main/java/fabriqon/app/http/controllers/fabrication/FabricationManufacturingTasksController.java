package fabriqon.app.http.controllers.fabrication;

import fabriqon.app.business.manufacturing.ManufacturingTask;
import fabriqon.app.business.manufacturing.tasks.ManufacturingTaskService;
import fabriqon.app.common.model.DevicePairingTokenType;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

import static fabriqon.jooq.classes.Tables.DEVICE_PAIRING_TOKEN;
import static fabriqon.jooq.classes.Tables.USERS;

@RestController
@RequestMapping(path = "/fabrication/manufacturing/tasks")
public class FabricationManufacturingTasksController {

    private final DSLContext db;
    private final ManufacturingTaskService manufacturingTaskService;

    @Autowired
    public FabricationManufacturingTasksController(DSLContext db, ManufacturingTaskService manufacturingTaskService) {
        this.db = db;
        this.manufacturingTaskService = manufacturingTaskService;
    }

    @PostMapping(path = "{id}/in-progress")
    public void inProgress(@RequestHeader("X-Auth") String xAuthHeader, @PathVariable("id") UUID id) {
        manufacturingTaskService.inProgress(ownerIdForEmployee(employeeIdFromAuth(xAuthHeader)), id);
    }

    @PostMapping(path = "{id}/done")
    public void done(@RequestHeader("X-Auth") String xAuthHeader, @PathVariable("id") UUID id) {
        manufacturingTaskService.done(ownerIdForEmployee(employeeIdFromAuth(xAuthHeader)), id);
    }

    @PostMapping(path = "{id}/stopped")
    public void blocked(@RequestHeader("X-Auth") String xAuthHeader,
                        @PathVariable("id") UUID id,
                        @RequestParam("reason") ManufacturingTask.StatusReason reason) {
        var employeeId = employeeIdFromAuth(xAuthHeader);
        manufacturingTaskService.stopped(ownerIdForEmployee(employeeId), id, reason, employeeName(employeeIdFromAuth(xAuthHeader)));
    }

    private UUID employeeIdFromAuth(String authHeader) {
        return db.select(DEVICE_PAIRING_TOKEN.USER_ID)
                .from(DEVICE_PAIRING_TOKEN)
                .where(DEVICE_PAIRING_TOKEN.TOKEN.eq(authHeader), DEVICE_PAIRING_TOKEN.TOKEN_TYPE.eq(DevicePairingTokenType.ACCESS.name()))
                .fetchSingleInto(UUID.class);
    }

    private UUID ownerIdForEmployee(UUID employeeId) {
        return db.select(USERS.OWNER_ID)
                .from(USERS)
                .where(USERS.ID.eq(employeeId))
                .fetchSingleInto(UUID.class);
    }

    private String employeeName(UUID employeeId) {
        return db.select(USERS.NAME).from(USERS).where(USERS.ID.eq(employeeId)).fetchSingleInto(String.class);
    }
}
