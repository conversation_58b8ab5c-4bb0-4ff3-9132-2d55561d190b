package fabriqon.app.http.controllers.services;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.customers.Note;
import fabriqon.app.business.goods.Dimensions;
import fabriqon.app.business.goods.MaterialCosts;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.inventory.InventoryService;
import fabriqon.app.business.manufacturing.ManufacturingOperation;
import fabriqon.app.business.manufacturing.ManufacturingOperationService;
import fabriqon.app.business.manufacturing.MaterialIssueNote;
import fabriqon.app.business.manufacturing.UsedMaterial;
import fabriqon.app.business.sales.GoodsAccompanyingNote;
import fabriqon.app.business.services.ServicingOrder;
import fabriqon.app.business.services.ServicingService;
import fabriqon.app.common.model.File;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.common.model.Money;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.controllers.FilesController;
import fabriqon.app.http.controllers.GoodsAccompanyingNotesController;
import fabriqon.app.http.controllers.NoteDefinition;
import fabriqon.app.http.controllers.NoteDetails;
import fabriqon.app.http.controllers.manufacturing.ManufacturingOperations;
import fabriqon.app.http.controllers.manufacturing.MaterialIssueNoteDetails;
import fabriqon.app.http.controllers.util.Sorting;
import fabriqon.app.http.response.Entity;
import fabriqon.app.http.response.StringEntity;
import fabriqon.misc.Json;
import fabriqon.pdf.HtmlToPdfConverter;
import fabriqon.templates.Localizer;
import fabriqon.templates.Templates;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.jooq.DSLContext;
import org.jooq.TableField;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static fabriqon.jooq.JooqTextSearchFunctions.textSearch;
import static fabriqon.jooq.classes.Tables.*;
import static org.apache.commons.lang3.BooleanUtils.isTrue;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.poi.util.StringUtil.isBlank;
import static org.jooq.impl.DSL.*;

@RestController
@RequestMapping(path = "/servicing/orders")
public class ServicingOrdersController {

    private final AccountContext accountContext;
    private final ServicingService servicingService;
    private final ManufacturingOperationService manufacturingOperationService;
    private final DSLContext db;
    private final ManufacturingOperations operations;
    private final InventoryService inventoryService;
    private final Templates templates;
    private final HtmlToPdfConverter htmlToPdfConverter;
    private final MaterialCosts materialCosts;

    @Autowired
    public ServicingOrdersController(AccountContext accountContext,
                                     ServicingService servicingService,
                                     ManufacturingOperationService manufacturingOperationService,
                                     ManufacturingOperations operations,
                                     DSLContext db, InventoryService inventoryService,
                                     Templates templates, HtmlToPdfConverter htmlToPdfConverter, MaterialCosts materialCosts) {
        this.accountContext = accountContext;
        this.servicingService = servicingService;
        this.manufacturingOperationService = manufacturingOperationService;
        this.operations = operations;
        this.db = db;
        this.inventoryService = inventoryService;
        this.templates = templates;
        this.htmlToPdfConverter = htmlToPdfConverter;
        this.materialCosts = materialCosts;
    }

    @PostMapping(path = "create")
    public ServicingOrder createServicingOrder(
            @AuthenticationPrincipal Jwt principal,
            @Validated @RequestBody CreateServicingOrder create) {
        return servicingService.orderServicing(accountContext.accountId(principal),
                null, create.productionDeadline, create.serviceId, create.quantity,
                create.notes, List.of());
    }

    private static final Map<String, TableField> sortFields = Map.of(
            "ranking", SERVICING_ORDER.RANKING,
            "updateTime", SERVICING_ORDER.UPDATE_TIME
    );

    @GetMapping(path = "list")
    public List<ServicingOrderDetails> listOrders(
            @AuthenticationPrincipal Jwt principal,
            @RequestParam(value = "q", required = false) String textSearch,
            @RequestParam(value = "status", required = false) List<ServicingOrder.Status> statuses,
            @RequestParam(value = "type", required = false) OrderType type,
            @RequestParam(value = "salesOrderId", required = false) UUID salesOrderId,
            @RequestParam(value = "day", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate day,
            @RequestParam(value = "start", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate start,
            @RequestParam(value = "end", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate end,
            @RequestParam(value = "sort", required = false)
            @Parameter(description = "Available fields for sorting: ranking, updateTime")
            String sorting
    ) {
        if (day != null && (start != null || end != null)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "can't filter both on 'day' and 'start'/'end'");
        }
        var filter = SERVICING_ORDER.OWNER_ID.eq(accountContext.accountId(principal))
                .and(SERVICING_ORDER.DELETED.isFalse());
        if (isNotEmpty(statuses)) {
            filter = filter.and(SERVICING_ORDER.STATUS.in(
                    statuses.stream()
                            .map(Enum::name)
                            .toList())
            );
        }
        if (salesOrderId != null) {
            filter = filter.and(SERVICING_ORDER.SALES_ORDER_ID.eq(salesOrderId));
        }
        if (isNotBlank(textSearch)) {
            filter = filter.and(textSearch(SERVICING_ORDER.TEXT_SEARCH, textSearch));
        }
        return db.selectFrom(SERVICING_ORDER)
                .where(filter)
                .orderBy(isBlank(sorting) ? List.of(SERVICING_ORDER.RANKING.asc()) : Sorting.toJooqSortFields(sortFields, sorting))
                .fetchInto(ServicingOrder.class)
                .stream().map(this::details).toList();
    }

    @SuppressWarnings("rawtypes")
    @Operation(responses = {
            @ApiResponse(responseCode = "200", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = ServicingOrdersController.ServicingOrderDetails.class)),
                    @Content(mediaType = "application/pdf"),
                    @Content(mediaType = "text/html")
            }),
    })
    @GetMapping(path = "{id}/details",
            produces = {
                    MediaType.TEXT_HTML_VALUE,
                    MediaType.APPLICATION_JSON_VALUE,
                    MediaType.APPLICATION_PDF_VALUE
            })
    public ResponseEntity details(
            @AuthenticationPrincipal Jwt principal,
            @RequestHeader(HttpHeaders.ACCEPT) String acceptHeader,
            @PathVariable(value = "id") UUID orderId) {

        var ownerId = accountContext.accountId(principal);
        var orderDetails = details(db.selectFrom(SERVICING_ORDER)
                .where(SERVICING_ORDER.OWNER_ID.eq(ownerId),
                        SERVICING_ORDER.DELETED.isFalse(),
                        SERVICING_ORDER.ID.eq(orderId))
                .orderBy(SERVICING_ORDER.RANKING)
                .fetchSingleInto(ServicingOrder.class));
        if (acceptHeader.contains(MediaType.TEXT_HTML_VALUE)) {
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(html(ownerId, orderDetails));
        } else if (acceptHeader.contains(MediaType.APPLICATION_PDF_VALUE)) {
            var pdf = htmlToPdfConverter.convert(html(ownerId, orderDetails));
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                            + "manufacturing_order_" + orderDetails.number + "_"
                            + DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.ofInstant(orderDetails.updateTime(), ZoneId.systemDefault()))
                            + ".pdf")
                    .body(pdf);
        } else {
            return new ResponseEntity<>(orderDetails, HttpStatus.OK);
        }
    }

    @PostMapping(path = "{id}/update")
    public ServicingOrderDetails update(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable(value = "id") UUID orderId,
            @Validated @RequestBody UpdateServicingOrder update) {
        return details(servicingService.updateServicingOrder(
                accountContext.accountId(principal),
                orderId,
                update.assignedTo,
                update.productionDeadline,
                update.quantity,
                update.manufacturingOperations,
                update.materials != null
                        ? update.materials.stream().map(material -> new ServicingOrder.Material(List.of(material.id), material.quantity, material.usedQuantity)).toList()
                        : null,
                update.notes
        ));
    }

    @PostMapping(path = "{id}/in-progress")
    public void inProgress(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable(value = "id") UUID orderId) {
        servicingService.inProgress(accountContext.accountId(principal), orderId);
    }

    @PostMapping(path = "{id}/executed")
    public void executed(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable(value = "id") UUID orderId,
            @RequestBody Signatures signatures) {
        servicingService.executed(accountContext.accountId(principal), orderId,
                signatures.base64ClientSignature, signatures.clientRepresentative, signatures.base64WorkerSignature);
    }

    @PostMapping(path = "{id}/closed")
    public MaterialIssueNote closed(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable(value = "id") UUID orderId,
            @Validated @RequestBody ServicingOrderDone orderDone) {
        return servicingService.closed(
                accountContext.accountId(principal), orderId,
                orderDone.date,
                orderDone.inventoryManager,
                orderDone.worker,
                orderDone.materials);
    }

    @PostMapping(path = "{id}/blocked")
    public void blocked(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable(value = "id") UUID orderId,
            @RequestBody BlockedReasonDefinition blockedReason) {
        servicingService.blocked(accountContext.accountId(principal), orderId,
                blockedReason.blockedReason);
    }

    @DeleteMapping(path = "{id}/delete")
    public void delete(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable(value = "id") UUID orderId) {
        servicingService.deleteOrder(accountContext.accountId(principal), orderId);
    }

    @PostMapping(path = "{id}/assign-to/{employeeId}")
    public void assignTo(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable(value = "id") UUID orderId,
            @PathVariable(value = "employeeId") UUID employeeId) {
        servicingService.assignTo(accountContext.accountId(principal), orderId, employeeId);
    }

    @PostMapping(path = "ranking")
    public void applyRanking(@AuthenticationPrincipal Jwt principal,
                             @Validated @RequestBody List<UUID> orderIds) {
        servicingService.applyRanking(accountContext.accountId(principal), orderIds);
    }

    @PostMapping(path = "{id}/attach-file", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ServicingOrderDetails attachFile(@AuthenticationPrincipal Jwt principal,
                                            @PathVariable("id") UUID orderId,
                                            @RequestParam("file") MultipartFile file) throws IOException {
        return details(servicingService.attach(accountContext.accountId(principal), orderId,
                file.getOriginalFilename(), file.getBytes()));
    }

    @DeleteMapping(path = "{id}/files/{fileId}/delete")
    public ServicingOrderDetails deleteFile(@AuthenticationPrincipal Jwt principal,
                                            @PathVariable("id") UUID orderId,
                                            @PathVariable("fileId") UUID fileId) throws IOException {
        return details(servicingService.deleteFile(accountContext.accountId(principal), orderId, fileId));
    }

    @GetMapping(path = "{id}/available-stock/{materialId}")
    public AvailableStock availableStockForProduct(@AuthenticationPrincipal Jwt principal,
                                                   @PathVariable("id") UUID orderId,
                                                   @PathVariable("materialId") UUID materialId) {
        var accountId = accountContext.accountId(principal);
        var orderRanking = db.select(SERVICING_ORDER.RANKING)
                .from(SERVICING_ORDER)
                .where(SERVICING_ORDER.OWNER_ID.eq(accountId), SERVICING_ORDER.ID.eq(orderId))
                .fetchSingleInto(Integer.class);
        var reservedBefore = db.select(coalesce(sum(RESERVED_INVENTORY.QUANTITY), 0))
                .from(RESERVED_INVENTORY)
                .where(
                        RESERVED_INVENTORY.OWNER_ID.eq(accountId),
                        RESERVED_INVENTORY.SERVICING_ORDER_ID.in(
                                select(SERVICING_ORDER.ID).from(SERVICING_ORDER)
                                        .where(SERVICING_ORDER.OWNER_ID.eq(accountId),
                                                (SERVICING_ORDER.RANKING.lessThan(orderRanking)
                                                        .and(SERVICING_ORDER.RANKING.gt(ServicingOrder.UNRANKED_ORDER_VALUE)))
                                                        .or(SERVICING_ORDER.STATUS.eq(ServicingOrder.Status.EXECUTED.name())))),
                        RESERVED_INVENTORY.MATERIAL_GOOD_ID.eq(materialId)
                )
                .fetchSingleInto(BigDecimal.class);
        var onStock = inventoryService.getCurrentStock(accountId, materialId);
        return new AvailableStock(materialId, onStock.subtract(reservedBefore));
    }

    @PostMapping(path = "{id}/notes/add")
    public void addNote(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable("id") UUID id,
            @Validated @RequestBody NoteDefinition note) {
        servicingService.addNote(accountContext.accountId(principal), id, accountContext.userId(principal), note.note());
    }

    @GetMapping(path = "{id}/notes/list")
    public List<NoteDetails> listNotes(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable("id") UUID id) {
        return db.select(NOTE.ID, NOTE.CREATE_TIME, NOTE.UPDATE_TIME, NOTE.DELETED, NOTE.OWNER_ID, NOTE.ADDED_BY_ID, NOTE.NOTE_)
                .from(NOTE)
                .join(SERVICING_ORDER_NOTE).on(SERVICING_ORDER_NOTE.NOTE_ID.eq(NOTE.ID))
                .where(
                        NOTE.OWNER_ID.eq(accountContext.accountId(principal)),
                        SERVICING_ORDER_NOTE.SERVICING_ORDER_ID.eq(id)
                )
                .orderBy(NOTE.CREATE_TIME.desc())
                .fetchInto(Note.class)
                .stream()
                .map(note -> new NoteDetails(note.id(), note.createTime(),
                        new Entity(note.addedById(), db.select(USERS.NAME).from(USERS).where(USERS.ID.eq(note.addedById())).fetchSingleInto(String.class)),
                        note.note()))
                .toList();
    }

    @PostMapping(path = "{id}/goods-accompanying-note")
    public GoodsAccompanyingNotesDetails goodsAccompanyingNote(@AuthenticationPrincipal Jwt principal,
                                                               @PathVariable("id") UUID orderId,
                                                               @RequestBody @Validated GoodsAccompanyingNotesController.GoodsAccompanyingNoteDefinition create) {
        var note = servicingService.goodsAccompanyingNote(accountContext.accountId(principal), orderId,
                create.deliveryDate(), create.from(), create.to(), create.delegateId(), create.transportRegistrationNumber(), create.notes(),
                create.items().stream().map(i -> new GoodsAccompanyingNote.Item(i.id(), i.quantity(), null, null)).toList());
        return new GoodsAccompanyingNotesDetails(note.id(), note.number(), note.items());
    }

    @PostMapping(path = "{id}/preview-goods-accompanying-note", produces = MediaType.TEXT_HTML_VALUE)
    public ResponseEntity<String> previewGoodsAccompanyingNote(@AuthenticationPrincipal Jwt principal,
                                                               @PathVariable("id") UUID orderId,
                                                               @RequestBody @Validated GoodsAccompanyingNotesController.GoodsAccompanyingNoteDefinition create) {
        return ResponseEntity.ok(servicingService.previewGoodsAccompanyingNote(accountContext.accountId(principal), orderId,
                create.deliveryDate(), create.from(), create.to(), create.delegateId(), create.transportRegistrationNumber(), create.notes(),
                create.items().stream().map(i -> new GoodsAccompanyingNote.Item(i.id(), i.quantity(), null, null)).toList()));
    }

    @SuppressWarnings("rawtypes")
    @GetMapping(path = "{id}/service-report", produces = {MediaType.TEXT_HTML_VALUE, MediaType.APPLICATION_PDF_VALUE})
    public ResponseEntity serviceReport(@AuthenticationPrincipal Jwt principal,
                                        @RequestHeader(HttpHeaders.ACCEPT) String acceptHeader,
                                        @PathVariable("id") UUID orderId) {

        var serviceReport = servicingService.serviceReport(accountContext.accountId(principal), orderId);
        if (acceptHeader.contains(MediaType.TEXT_HTML_VALUE)) {
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(serviceReport.a());
        } else if (acceptHeader.contains(MediaType.APPLICATION_PDF_VALUE)) {
            var pdf = htmlToPdfConverter.convert(serviceReport.a());
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                            + "service_report_" + serviceReport.b() + "_"
                            + DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.ofInstant(serviceReport.c(), ZoneId.systemDefault()))
                            + ".pdf")
                    .body(pdf);
        }
        return ResponseEntity.status(HttpStatus.UNSUPPORTED_MEDIA_TYPE).build();
    }

    @SuppressWarnings("rawtypes")
    @PostMapping(path = "{id}/preview-service-report", produces = {MediaType.TEXT_HTML_VALUE, MediaType.APPLICATION_PDF_VALUE})
    public ResponseEntity previewServiceReport(@AuthenticationPrincipal Jwt principal,
                                                       @RequestHeader(HttpHeaders.ACCEPT) String acceptHeader,
                                                       @PathVariable("id") UUID orderId,
                                                       @RequestBody Signatures signatures) {
        var serviceReport = servicingService.previewServiceReport(accountContext.accountId(principal), orderId, signatures.base64ClientSignature, signatures.clientRepresentative, signatures.base64WorkerSignature);
        if (acceptHeader.contains(MediaType.TEXT_HTML_VALUE)) {
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(serviceReport.a());
        } else if (acceptHeader.contains(MediaType.APPLICATION_PDF_VALUE)) {
            var pdf = htmlToPdfConverter.convert(serviceReport.a());
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                            + "service_report_" + serviceReport.b() + "_"
                            + DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.ofInstant(serviceReport.c(), ZoneId.systemDefault()))
                            + ".pdf")
                    .body(pdf);
        }
        return ResponseEntity.status(HttpStatus.UNSUPPORTED_MEDIA_TYPE).build();
    }

    private ServicingOrderDetails details(ServicingOrder order) {
        var requiredMaterials = materials(order);
        var serviceDetails = service(order.serviceId());
        var productManufacturingOperations = productManufacturingOperations(order);
        var employeeAndWorkstationCosts = servicingService.employeeAndWorkstationCosts(order.ownerId(), order.id());
        var manufacturingOverheadCosts = servicingService.manufacturingOverheadCosts(order.ownerId(), order.id());
        var materialCost = requiredMaterials.stream()
                .map(material -> material.cost)
                .reduce(Money::add)
                .orElse(new Money(0, employeeAndWorkstationCosts.currency()));
        return new ServicingOrderDetails(
                order.id(),
                order.createTime(),
                order.updateTime(),
                order.salesOrderId(),
                customer(order.salesOrderId()),
                order.number(),
                materialCost.add(employeeAndWorkstationCosts).add(manufacturingOverheadCosts),
                order.executionDeadline(),
                order.status(),
                serviceDetails != null
                        ? serviceDetails
                        .setMaterialCosts(materialCost)
                        .setEmployeeAndWorkstationCosts(employeeAndWorkstationCosts)
                        .setManufacturingOverheadCosts(manufacturingOverheadCosts)
                        : null,
                order.quantity(),
                serviceDetails != null
                        ? new StringEntity(serviceDetails.measurementUnit.name(), serviceDetails.measurementUnit.symbol())
                        : null,
                order.assignedTo() != null ? new Entity(order.assignedTo(), db.select(USERS.NAME).from(USERS).where(USERS.ID.eq(order.assignedTo()), USERS.OWNER_ID.eq(order.ownerId())).fetchSingleInto(String.class)) : null,
                order.ranking(),
                productionTime(productManufacturingOperations, order.quantity()),
                requiredMaterials,
                order.operations() != null
                        ? order.operations().stream().map(op -> operations.manufacturingOperationDetails(order.ownerId(), BigDecimal.ONE, op)).toList()
                        : null,
                order.notes(),
                materialIssueNote(order.id(), order.number()),
                files(order.ownerId(), order.id()),
                goodsAccompanyingNotes(order.id())
        );
    }

    private List<File> files(UUID ownerId, UUID orderId) {
        return db.select(FILE.ID, FILE.FILE_NAME)
                .from(FILE)
                .where(FILE.ID.in(select(SERVICINGORDER_FILE.FILE_ID).from(SERVICINGORDER_FILE).where(SERVICINGORDER_FILE.OWNER_ID.eq(ownerId), SERVICINGORDER_FILE.SERVICING_ORDER_ID.eq(orderId))))
                .fetch()
                .stream()
                .map(f -> new File(f.value1(), f.value2(),
                        FilesController.previewLink(f.value1()), FilesController.downloadLink(f.value1())))
                .toList();
    }

    private List<MaterialDetails> materials(ServicingOrder order) {
        if (order.status() == ServicingOrder.Status.CLOSED) {
            return materialIssueNote(order.id(), order.number()).materials().stream()
                    .map(m -> {
                                var material = material(m.material().id());
                                return new MaterialDetails(
                                        List.of(material),
                                        m.quantity().setScale(4, RoundingMode.HALF_EVEN).divide(order.quantity(), RoundingMode.HALF_EVEN),
                                        new StringEntity(material.measurementUnit().name(), material.measurementUnit.symbol()),
                                        m.totalCost().divide(order.quantity()),
                                   null//todo
                                );
                            }
                    )
                    .toList();
        }
        return order.materials().stream()
                .map(material -> {
                    var requiredTotal = material.quantity().multiply(order.quantity()).setScale(2, RoundingMode.HALF_EVEN);
                    var materials = material.materialIds().stream().map(this::material).toList();
                    return new MaterialDetails(
                            materials,
                            material.quantity(),
                            new StringEntity(materials.getFirst().measurementUnit().name(), materials.getFirst().measurementUnit().symbol()),
                            materialCosts.materialCost(order.ownerId(), material.materialIds().getFirst()).multiply(requiredTotal),
                            material.usedQuantity()
                    );
                })
                .toList();
    }

    private List<ManufacturingOperation> productManufacturingOperations(ServicingOrder order) {
        return isNotEmpty(order.operations())
                ? manufacturingOperationService.enhance(order.operations())
                : List.of();
    }

    private int productionTime(List<ManufacturingOperation> operations, BigDecimal quantity) {
        return operations.stream()
                .map(operation -> quantity.multiply(BigDecimal.valueOf(operation.durationInMinutes())))
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .intValue();
    }

    private MaterialGoodDetails material(UUID materialId) {
        if (materialId != null) {
            return db.select(MATERIAL_GOOD.ID,
                            MATERIAL_GOOD.NAME,
                            MATERIAL_GOOD.CODE,
                            MATERIAL_GOOD.DETAILS)
                    .from(MATERIAL_GOOD)
                    .where(MATERIAL_GOOD.ID.eq(materialId))
                    .fetchSingle()
                    .map(record -> {
                        var details = Json.read(record.get(MATERIAL_GOOD.DETAILS).data(), MaterialGood.Details.class);
                        return new MaterialGoodDetails(
                                record.get(MATERIAL_GOOD.ID),
                                record.get(MATERIAL_GOOD.NAME),
                                record.get(MATERIAL_GOOD.CODE),
                                details.measurementUnit(),
                                isTrue(details.produced()),
                                details.dimensions()
                        );
                    });
        }
        return null;
    }

    private ServiceDetails service(UUID serviceId) {
        if (serviceId != null) {
            return db.select(SERVICE_TEMPLATE.ID,
                            SERVICE_TEMPLATE.NAME,
                            SERVICE_TEMPLATE.MEASUREMENT_UNIT)
                    .from(SERVICE_TEMPLATE)
                    .where(SERVICE_TEMPLATE.ID.eq(serviceId))
                    .fetchSingle()
                    .map(record -> new ServiceDetails(
                            record.get(SERVICE_TEMPLATE.ID),
                            record.get(SERVICE_TEMPLATE.NAME),
                            MeasurementUnit.valueOf(record.get(SERVICE_TEMPLATE.MEASUREMENT_UNIT)),
                            null, null, null
                    ));
        }
        return null;
    }

    private Entity customer(UUID salesOrderId) {
        if (salesOrderId == null) {
            return null;//the mfg order is not for a customer but own stock
        }
        return db.select(COMPANY.ID, COMPANY.COMPANY_NAME)
                .from(COMPANY)
                .join(ACCOUNT_CUSTOMER).on(ACCOUNT_CUSTOMER.CUSTOMER_ID.eq(COMPANY.ID))
                .join(SALES_ORDER).on(SALES_ORDER.CUSTOMER_ID.eq(ACCOUNT_CUSTOMER.CUSTOMER_ID))
                .where(SALES_ORDER.ID.eq(salesOrderId))
                .fetchSingleInto(Entity.class);
    }

    private MaterialIssueNoteDetails materialIssueNote(UUID orderId, String orderNumber) {
        return db.selectFrom(MATERIAL_ISSUE_NOTE)
                .where(MATERIAL_ISSUE_NOTE.SERVICING_ORDER_ID.eq(orderId))
                .fetchOptionalInto(MaterialIssueNote.class)
                .map(note -> new MaterialIssueNoteDetails(
                        note.id(),
                        note.createTime(),
                        note.ownerId(),
                        new Entity(note.manufacturingOrderId(), orderNumber),
                        note.number(),
                        note.date(),
                        note.details().inventoryManager(),
                        note.details().worker(),
                        note.materials().stream().map(m -> new MaterialIssueNoteDetails.Material(new Entity(m.id(), material(m.id()).name), m.quantity(), m.totalCost(), m.wastedQuantity())).toList()
                ))
                .orElse(null);
    }

    private String html(UUID ownerId, ServicingOrderDetails order) {
        var account = account(ownerId);
        var localizer = new Localizer(new Locale(account.information().address().country()));

        var orderMap = new HashMap<>();
        orderMap.put("number", order.number());
        orderMap.put("saleOrderNumber", order.salesOrderId() != null
                ? db.select(SALES_ORDER.NUMBER).from(SALES_ORDER).where(SALES_ORDER.ID.eq(order.salesOrderId))
                : null);
        orderMap.put("clientName", order.customer() != null ? order.customer.name() : null);
        orderMap.put("createTime", localizer.localizeDate(order.createTime()));
        orderMap.put("productionDeadline", order.productionDeadline != null ? localizer.localizeDate(order.productionDeadline().toLocalDate()) : null);

        orderMap.put("notes", order.notes);

        orderMap.put("hasDimensions", false);
        var materialIndexer = new AtomicInteger(0);
        orderMap.put("requiredMaterials", order.materials().stream()
                .map(m -> Map.of(
                        "index", materialIndexer.incrementAndGet(),
                        "name", m.materialGoods.stream().map(mg -> mg.name).collect(Collectors.joining("\n")),
                        "code", m.materialGoods.stream().map(mg -> mg.code).collect(Collectors.joining("\n")),
                        "required", m.required.stripTrailingZeros().toPlainString(),
                        "measurementUnit", localizer.localizedValue(m.measurementUnit.name())
                ))
                .toList()
        );

        var map = new HashMap<>();
        map.put("order", orderMap);
        var productMap = new HashMap<String, String>();
        productMap.put("name", order.service.name);
        productMap.put("quantity", order.quantity.stripTrailingZeros().toPlainString());
        productMap.put("measurementUnit", localizer.localizedValue(order.service.measurementUnit.name()));
        map.put("product", productMap);
        map.put("labels", localizer.labels());
        map.put("base64Logo", account.logo());

        return templates.render("pdf/manufacturing_order.html", map);
    }

    private String dimensionsForHtml(Localizer localizer, Dimensions requiredDimensions) {
        if (requiredDimensions == null) {
            return "";
        }
        var sb = new StringBuilder();
        if (requiredDimensions.length() != null) {
            sb.append(localizer.labels().get("length")).append(": ").append(requiredDimensions.length()).append('\n');
        }
        if (requiredDimensions.width() != null) {
            sb.append(localizer.labels().get("width")).append(": ").append(requiredDimensions.width()).append('\n');
        }
        if (requiredDimensions.height() != null) {
            sb.append(localizer.labels().get("height")).append(": ").append(requiredDimensions.height()).append('\n');
        }
        if (requiredDimensions.weight() != null) {
            sb.append(localizer.labels().get("weight")).append(": ").append(requiredDimensions.weight()).append('\n');
        }
        sb.delete(sb.length() - 1, sb.length());
        return sb.toString();
    }

    private List<GoodsAccompanyingNotesDetails> goodsAccompanyingNotes(UUID orderId) {
        return db.selectFrom(GOODS_ACCOMPANYING_NOTE)
                .where(GOODS_ACCOMPANYING_NOTE.ID.in(select(SERVICINGORDER_GOODSACCOMPANYINGNOTE.GOODS_ACCOMPANYING_NOTE_ID).from(SERVICINGORDER_GOODSACCOMPANYINGNOTE).where(SERVICINGORDER_GOODSACCOMPANYINGNOTE.SERVICING_ORDER_ID.eq(orderId))))
                .fetchInto(GoodsAccompanyingNote.class)
                .stream()
                .map(note -> new GoodsAccompanyingNotesDetails(note.id(), note.number(), note.items()))
                .toList();
    }

    private Account account(UUID id) {
        return db.selectFrom(ACCOUNT).where(ACCOUNT.ID.eq(id)).fetchSingleInto(Account.class);
    }

    public record CreateServicingOrder(
            LocalDateTime productionDeadline,
            UUID serviceId,
            BigDecimal quantity,
            String notes,
            boolean customProduct
    ) {
    }

    public record UpdateServicingOrder(
            UUID assignedTo,
            LocalDateTime productionDeadline,
            BigDecimal quantity,
            String notes,
            List<Material> materials,
            List<ManufacturingOperation> manufacturingOperations
    ) {
    }

    public record ServicingOrderDone(
            LocalDate date,
            String inventoryManager,
            String worker,
            List<UsedMaterial> materials
    ) {
    }

    public record Material(
            UUID id,
            BigDecimal quantity,
            BigDecimal usedQuantity
    ) {
    }

    public record ServicingOrderDetails(
            UUID id,
            Instant createTime,
            Instant updateTime,
            UUID salesOrderId,
            Entity customer,
            String number,
            Money totalCost,
            LocalDateTime productionDeadline,
            ServicingOrder.Status status,
            ServiceDetails service,
            BigDecimal quantity,
            StringEntity measurementUnit,
            Entity assignedTo,
            Integer ranking,
            int productionTime,
            List<MaterialDetails> materials,
            List<ManufacturingOperations.ManufacturingOperationDetails> manufacturingOperations,
            String notes,
            MaterialIssueNoteDetails materialIssueNote,
            List<File> files,
            List<GoodsAccompanyingNotesDetails> goodsAccompanyingNotes
    ) {
    }

    public record MaterialDetails(
            List<MaterialGoodDetails> materialGoods,
            BigDecimal required,
            StringEntity measurementUnit,
            Money cost,
            BigDecimal usedQuantity
    ) {
    }

    public record MaterialGoodDetails(
            UUID id,
            String name,
            String code,
            MeasurementUnit measurementUnit,
            boolean produced,
            Dimensions configuredDimensions
    ) {
    }

    public record ServiceDetails(
            UUID id,
            String name,
            MeasurementUnit measurementUnit,
            Money employeeAndWorkstationCosts,
            Money materialCosts,
            Money manufacturingOverheadCosts
    ) {

        ServiceDetails setEmployeeAndWorkstationCosts(Money employeeAndWorkstationCosts) {
            return new ServiceDetails(id, name, measurementUnit, employeeAndWorkstationCosts, materialCosts, manufacturingOverheadCosts);
        }

        ServiceDetails setMaterialCosts(Money materialCosts) {
            return new ServiceDetails(id, name, measurementUnit, employeeAndWorkstationCosts, materialCosts, manufacturingOverheadCosts);
        }

        ServiceDetails setManufacturingOverheadCosts(Money manufacturingOverheadCosts) {
            return new ServiceDetails(id, name, measurementUnit, employeeAndWorkstationCosts, materialCosts, manufacturingOverheadCosts);
        }
    }

    public record AvailableStock(UUID id, BigDecimal quantity) {
    }

    public enum OrderType {product, service}

    public record GoodsAccompanyingNotesDetails(
            UUID id,
            String number,
            List<GoodsAccompanyingNote.Item> items
    ) {
    }

    public record Signatures (String base64ClientSignature, String clientRepresentative, String base64WorkerSignature) {}

    public record BlockedReasonDefinition (ServicingOrder.BlockedReason blockedReason) {}

}
