package fabriqon.app.http.controllers;

import fabriqon.ObjectStorage;
import fabriqon.app.config.security.AccountContext;
import fabriqon.jooq.classes.tables.records.FileRecord;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.URI;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.FILE;

//@RequestMapping(path = FilesController.BASE_PATH)
@RequestMapping(path = FilesController.BASE_PATH)
@RestController
public class FilesController {

    public static final String BASE_PATH = "/files/links";
    public static final String DOWNLOAD_PATH = "/download";
    public static final String PREVIEW_PATH = "/preview";

    private final AccountContext accountContext;
    private final ObjectStorage objectStorage;
    private final DSLContext db;

    @Autowired
    public FilesController(AccountContext accountContext, ObjectStorage objectStorage, DSLContext db) {
        this.accountContext = accountContext;
        this.objectStorage = objectStorage;
        this.db = db;
    }

    @GetMapping(path = DOWNLOAD_PATH + "/{id}")
    public ResponseEntity<Void> download(@AuthenticationPrincipal Jwt principal, @PathVariable("id") UUID fileId) {
        var file = load(accountContext.accountId(principal), fileId);
        return ResponseEntity
                .status(HttpStatus.TEMPORARY_REDIRECT)
                .location(URI.create(objectStorage.getLink(file.getOwnerId(), fileId, false)))
                .build();
    }

    @GetMapping(path = PREVIEW_PATH + "/{id}")
    public ResponseEntity<Void> preview(@AuthenticationPrincipal Jwt principal, @PathVariable("id") UUID fileId) {
        var file = load(accountContext.accountId(principal), fileId);
        return ResponseEntity
                .status(HttpStatus.TEMPORARY_REDIRECT)
                .location(URI.create(objectStorage.getLink(file.getOwnerId(), fileId, true)))
                .build();
    }

    public static String downloadLink(UUID fileId) {
        return BASE_PATH + DOWNLOAD_PATH + "/" + fileId;
    }

    public static String previewLink(UUID fileId) {
        return BASE_PATH + PREVIEW_PATH + "/" + fileId;
    }


    private FileRecord load(UUID accountId, UUID fileId) {
        return db.selectFrom(FILE)
                .where(FILE.OWNER_ID.eq(accountId), FILE.ID.eq(fileId))
                .fetchSingle();
    }

}

