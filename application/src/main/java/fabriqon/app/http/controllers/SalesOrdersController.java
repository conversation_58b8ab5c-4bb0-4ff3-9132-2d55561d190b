package fabriqon.app.http.controllers;

import fabriqon.ObjectStorage;
import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.common.model.GlobalDiscount;
import fabriqon.app.business.customers.Note;
import fabriqon.app.business.exceptions.BusinessException;
import fabriqon.app.business.financial.VatCalculatorProvider;
import fabriqon.app.business.financial.fx.ExchangeRatesProvider;
import fabriqon.app.business.goods.GoodsService;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.goods.RequiredMaterial;
import fabriqon.app.business.inventory.InventoryService;
import fabriqon.app.business.invoicing.Invoice;
import fabriqon.app.business.manufacturing.ManufacturingOrder;
import fabriqon.app.business.purchases.PurchaseOrder;
import fabriqon.app.business.sales.GoodsAccompanyingNote;
import fabriqon.app.business.sales.SalesOrder;
import fabriqon.app.business.sales.SalesService;
import fabriqon.app.business.services.ServicingOrder;
import fabriqon.app.business.servicetemplates.ServiceTemplate;
import fabriqon.app.common.model.*;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.controllers.util.Sorting;
import fabriqon.app.http.response.Entity;
import fabriqon.app.http.response.StringEntity;
import fabriqon.app.http.response.StringValue;
import fabriqon.misc.Json;
import fabriqon.misc.Tuple;
import fabriqon.pdf.HtmlToPdfConverter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.TableField;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static fabriqon.app.business.sales.SalesOrder.Status.*;
import static fabriqon.app.http.controllers.HttpApiConstants.*;
import static fabriqon.jooq.JooqJsonbFunctions.arrayContainsJson;
import static fabriqon.jooq.JooqTextSearchFunctions.textSearch;
import static fabriqon.jooq.classes.Tables.*;
import static java.lang.String.format;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.lang3.BooleanUtils.isTrue;
import static org.apache.commons.lang3.ObjectUtils.isEmpty;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.jooq.impl.DSL.*;

@RestController
@RequestMapping(path = "/sales/orders")
@Slf4j
public class SalesOrdersController {

    private final AccountContext accountContext;
    private final SalesService salesService;
    private final DSLContext db;
    private final InventoryService inventoryService;
    private final GoodsService goodsService;
    private final VatCalculatorProvider vatCalculatorProvider;
    private final ExchangeRatesProvider exchangeRatesProvider;
    private final ObjectStorage objectStorage;
    private final HtmlToPdfConverter htmlToPdfConverter;

    @Autowired
    public SalesOrdersController(DSLContext db, AccountContext accountContext, SalesService salesService,
                                 InventoryService inventoryService, GoodsService goodsService,
                                 VatCalculatorProvider vatCalculatorProvider, ExchangeRatesProvider exchangeRatesProvider,
                                 ObjectStorage objectStorage, HtmlToPdfConverter htmlToPdfConverter) {
        this.db = db;
        this.accountContext = accountContext;
        this.salesService = salesService;
        this.inventoryService = inventoryService;
        this.goodsService = goodsService;
        this.vatCalculatorProvider = vatCalculatorProvider;
        this.exchangeRatesProvider = exchangeRatesProvider;
        this.objectStorage = objectStorage;
        this.htmlToPdfConverter = htmlToPdfConverter;
    }

    @PostMapping(path = "create")
    public SalesOrderDetails createSaleOrder(@AuthenticationPrincipal Jwt principal,
                                             @Validated @RequestBody CreateSalesOrder order) {
        validateItems(order.items);
        if (duplicateProductExists(order.items)) {
            throw new BusinessException("Products specified more than once", "item_specified_multiple_times");
        }
        verifyCustomizations(order.items);
        return details(salesService.createSalesOrder(accountContext.accountId(principal), order.customerId,
                order.createTime, order.deliveryDeadline,
                order.isQuotation ? IN_QUOTATION : SUBMITTED,
                order.items.stream()
                        .map(e -> new SalesOrder.Item(e.productId, e.serviceId, e.quantity, e.price, e.discount, e.customizationNote, e.customizations, null))
                        .collect(toList()),
                order.shippingAddress, order.notes, order.customerNotes, order.offerDate, order.offerExpiration, order.renderingDetails, order.globalDiscount
        ));
    }

    @Operation(responses = {
            @ApiResponse(responseCode = "200", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = SalesOrderDetails.class)),
                    @Content(mediaType = "application/pdf"),
                    @Content(mediaType = "text/html"),
            })
    })
    @SuppressWarnings("rawtypes")
    @PostMapping(path = "preview",
            produces = {
                    MediaType.TEXT_HTML_VALUE,
                    MediaType.APPLICATION_JSON_VALUE,
                    MediaType.APPLICATION_PDF_VALUE,
            })
    public ResponseEntity preview(@AuthenticationPrincipal Jwt principal,
                                  @RequestHeader(HttpHeaders.ACCEPT) String acceptHeader,
                                  @Validated @RequestBody CreateSalesOrder create) {
        var ownerId = accountContext.accountId(principal);
        var order = new SalesOrder(
                null, Instant.now(), null, false,
                ownerId, create.customerId, "DRAFT",
                null,
                create.deliveryDeadline,
                IN_QUOTATION,
                create.items.stream()
                        .map(e -> new SalesOrder.Item(e.productId, e.serviceId, e.quantity, e.price, e.discount, e.customizationNote, e.customizations, null))
                        .collect(toList()),
                create.shippingAddress, create.notes, create.customerNotes, create.offerDate, create.offerExpiration, create.renderingDetails, create.globalDiscount
        );
        if (acceptHeader.contains(MediaType.TEXT_HTML_VALUE)) {
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(salesService.html(order, create.offerDate, create.offerExpiration));
        } else if (acceptHeader.contains(MediaType.APPLICATION_PDF_VALUE)) {
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                            + "order" + DateTimeFormatter.ofPattern("yyyy-MM-dd")
                            .withZone(ZoneId.systemDefault()).format(order.createTime()) + ".pdf")
                    .body(salesService.pdf(salesService.html(order, create.offerDate, create.offerExpiration)));
        } else {
            return new ResponseEntity<>(details(order), HttpStatus.OK);
        }
    }

    private static final Map<String, TableField> sortFields = Map.of(
            "ranking", SALES_ORDER.RANKING,
            "updateTime", SALES_ORDER.UPDATE_TIME
    );

    @GetMapping(path = "list")
    public List<SalesOrderDetails> listOrders(
            @AuthenticationPrincipal Jwt principal,
            @RequestParam(value = LIMIT_QUERY, defaultValue = LIMIT_DEFAULT) Integer limit,
            @RequestParam(value = OFFSET_QUERY, defaultValue = OFFSET_DEFAULT) Integer offset,
            @RequestParam(value = "q", required = false) String textSearch,
            @RequestParam(value = "status", required = false) List<SalesOrder.Status> statuses,
            @RequestParam(value = "customer", required = false) UUID customerId,
            @RequestParam(value = "sort", required = false)
            @Parameter(description = "Available fields for sorting: ranking, updateTime")
            String sorting
    ) {
        final UUID companyId = accountContext.accountId(principal);
        var filter = SALES_ORDER.OWNER_ID.eq(companyId).and(SALES_ORDER.DELETED.isFalse());
        if (isNotEmpty(statuses)) {
            filter = filter.and(SALES_ORDER.STATUS.in(statuses.stream().map(Enum::name).toList()));
        }
        if (customerId != null) {
            filter = filter.and(SALES_ORDER.CUSTOMER_ID.eq(customerId));
        }
        if (isNotBlank(textSearch)) {
            filter = filter.and(textSearch(SALES_ORDER.TEXT_SEARCH, textSearch));
        }
        return db.selectFrom(SALES_ORDER)
                .where(filter)
                .orderBy(isBlank(sorting) ? List.of(SALES_ORDER.RANKING.asc()) : Sorting.toJooqSortFields(sortFields, sorting))
                .limit(limit).offset(offset)
                .fetchInto(SalesOrder.class)
                .stream().map(this::details)
                .toList();
    }

    @Operation(responses = {
            @ApiResponse(responseCode = "200", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = SalesOrderDetails.class)),
                    @Content(mediaType = "application/pdf"),
                    @Content(mediaType = "text/html"),
            })
    })
    @SuppressWarnings("rawtypes")
    @GetMapping(path = "{id}/details",
            produces = {
                    MediaType.TEXT_HTML_VALUE,
                    MediaType.APPLICATION_JSON_VALUE,
                    MediaType.APPLICATION_PDF_VALUE
            })
    public ResponseEntity orderDetails(@AuthenticationPrincipal Jwt principal,
                                       @RequestHeader(HttpHeaders.ACCEPT) String acceptHeader,
                                       @PathVariable("id") UUID orderId) {
        var ownerId = accountContext.accountId(principal);
        var order = db.fetchSingle(SALES_ORDER,
                        SALES_ORDER.OWNER_ID.eq(ownerId),
                        SALES_ORDER.ID.eq(orderId))
                .into(SalesOrder.class);

        if (acceptHeader.contains(MediaType.TEXT_HTML_VALUE)) {
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(salesService.html(order,
                            order.status() == IN_QUOTATION ? order.offerDate() : null,
                            order.status() == IN_QUOTATION ? order.offerExpiration() : order.deliveryDeadline().toLocalDate()));
        } else if (acceptHeader.contains(MediaType.APPLICATION_PDF_VALUE)) {
            var pdf = salesService.pdf(salesService.html(order,
                    order.status() == IN_QUOTATION ? order.offerDate() : null,
                    order.status() == IN_QUOTATION ? order.offerExpiration() : order.deliveryDeadline().toLocalDate()));
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                            + "order_" + order.number() + ".pdf")
                    .body(pdf);
        } else {
            return new ResponseEntity<>(details(order), HttpStatus.OK);
        }
    }

    @PostMapping(path = "{id}/update")
    public SalesOrderDetails update(@AuthenticationPrincipal Jwt principal,
                                    @PathVariable("id") UUID salesOrderId,
                                    @Validated @RequestBody UpdateSalesOrder update) {
        validateItems(update.items);
        if (duplicateProductExists(update.items)) {
            throw new BusinessException("Products specified more than once", "item_specified_multiple_times");
        }
        return details(salesService.updateSalesOrder(salesOrderId, accountContext.accountId(principal), new SalesOrder(null, null, null, false,
                accountContext.accountId(principal),
                update.customerId,
                null,
                null,
                update.deliveryDeadline,
                null,
                update.items != null
                        ? update.items.stream()
                        .map(e -> new SalesOrder.Item(e.productId, e.serviceId, e.quantity, e.price, e.discount, e.customizationNote, e.customizations, null))
                        .collect(toList())
                        : null,
                update.shippingAddress,
                update.notes,
                update.customerNotes,
                update.offerDate, update.offerExpiration,
                update.renderingDetails,
                update.globalDiscount
        )));
    }

    @PostMapping(path = "ranking")
    public void applyRanking(@AuthenticationPrincipal Jwt principal,
                             @Validated @RequestBody List<UUID> orderIds) {
        salesService.applyRanking(accountContext.accountId(principal), orderIds, true);
    }

    @PostMapping(path = "{id}/attach-file", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public SalesOrderDetails attachFile(@AuthenticationPrincipal Jwt principal,
                                        @PathVariable("id") UUID orderId,
                                        @RequestParam("file") MultipartFile file) throws IOException {
        return details(salesService.attach(accountContext.accountId(principal), orderId,
                file.getOriginalFilename(), file.getBytes()));
    }

    @DeleteMapping(path = "{id}/files/{fileId}/delete")
    public SalesOrderDetails deleteFile(@AuthenticationPrincipal Jwt principal,
                                        @PathVariable("id") UUID orderId,
                                        @PathVariable("fileId") UUID fileId) throws IOException {
        return details(salesService.deleteFile(accountContext.accountId(principal), orderId, fileId));
    }

    @PostMapping(path = "{id}/cancel-quote")
    public void cancelQuote(@AuthenticationPrincipal Jwt principal, @PathVariable("id") UUID orderId) {
        salesService.cancelQuote(accountContext.accountId(principal), orderId);
    }

    @PostMapping(path = "{id}/cancel")
    public void cancelOrder(@AuthenticationPrincipal Jwt principal, @PathVariable("id") UUID orderId) {
        salesService.cancelOrder(accountContext.accountId(principal), orderId);
    }

    @PostMapping(path = "{id}/on-hold")
    public void onHoldOrder(@AuthenticationPrincipal Jwt principal, @PathVariable("id") UUID orderId, @RequestBody StringValue reason) {
        salesService.onHoldOrder(accountContext.accountId(principal), accountContext.userId(principal), orderId, reason.value());
    }

    @PostMapping(path = "{id}/quote-sent")
    public void quoteSent(@AuthenticationPrincipal Jwt principal, @PathVariable("id") UUID orderId) {
        salesService.quoteSent(accountContext.accountId(principal), orderId);
    }

    @PostMapping(path = "{id}/confirm")
    public void confirmOrder(@AuthenticationPrincipal Jwt principal, @PathVariable("id") UUID orderId) {
        salesService.confirmOrder(accountContext.accountId(principal), orderId);
    }

    @PostMapping(path = "{id}/processing")
    public void processingOrder(@AuthenticationPrincipal Jwt principal, @PathVariable("id") UUID orderId) {
        salesService.processingOrder(accountContext.accountId(principal), orderId);
    }

    @PostMapping(path = "{id}/ready-to-ship")
    public void readyToShipOrder(@AuthenticationPrincipal Jwt principal, @PathVariable("id") UUID orderId) {
        salesService.readyToShipOrder(accountContext.accountId(principal), orderId);
    }

    @PostMapping(path = "{id}/pick-and-pack")
    public void pickAndPackOrder(@AuthenticationPrincipal Jwt principal, @PathVariable("id") UUID orderId) {
        salesService.pickAndPackOrder(accountContext.accountId(principal), orderId);
    }


    @PostMapping(path = "{id}/ship")
    public void shipOrder(@AuthenticationPrincipal Jwt principal, @PathVariable("id") UUID orderId) {
        salesService.shipOrder(accountContext.accountId(principal), orderId);
    }

    @PostMapping(path = "{id}/delivered")
    public void markOrderAsDelivered(@AuthenticationPrincipal Jwt principal, @PathVariable("id") UUID orderId) {
        salesService.markOrderAsDelivered(accountContext.accountId(principal), orderId);
    }

    @PostMapping(path = "{id}/manufacture-missing", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public Map<String, Object> manufactureMissing(@AuthenticationPrincipal Jwt principal,
                                                  @PathVariable("id") UUID orderId,
                                                  @RequestBody @Validated ProductManufacturingOrder productOrder) {
        return manufactureMissing(accountContext.accountId(principal), orderId, productOrder, List.of());
    }

    @PostMapping(path = "{id}/manufacture-missing", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public Map<String, Object> manufactureMissing(@AuthenticationPrincipal Jwt principal,
                                                  @PathVariable("id") UUID orderId,
                                                  @RequestPart("productOrder") String productOrder,
                                                  @RequestPart(name = "files", required = false) List<MultipartFile> files) {
        return manufactureMissing(accountContext.accountId(principal), orderId, Json.read(productOrder, ProductManufacturingOrder.class),
                ofNullable(files).orElse(List.of()).stream().map(f -> {
                    try {
                        return Tuple.of(f.getOriginalFilename(), new ByteArrayInputStream(f.getBytes()));
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }).toList());
    }

    @PostMapping(path = "{id}/add-missing-to-wishlist", consumes = {MediaType.APPLICATION_JSON_VALUE})
    public void addMissingToWishlist(@AuthenticationPrincipal Jwt principal,
                                     @PathVariable("id") UUID orderId,
                                     @RequestBody @Validated ProductWishlistAddition addition) {
        salesService.addToWishlist(accountContext.accountId(principal), orderId, addition.productId, addition.quantity, addition.supplierId);
    }

    @PostMapping(path = "{id}/invoice")
    public Invoice invoice(@AuthenticationPrincipal Jwt principal,
                           @PathVariable("id") UUID orderId,
                           @RequestBody @Validated CreateInvoice createInvoice) {
        return salesService.invoice(accountContext.accountId(principal), orderId, createInvoice.proforma,
                createInvoice.notes(), createInvoice.emailMessage != null ? createInvoice.emailMessage : null,
                createInvoice.renderingDetails);
    }

    @Operation(responses = {
            @ApiResponse(responseCode = "200", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = Invoice.class)),
                    @Content(mediaType = "application/pdf"),
                    @Content(mediaType = "text/html"),
            }),
    })
    @PostMapping(path = "{id}/invoice-preview", produces = {
            MediaType.TEXT_HTML_VALUE,
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.APPLICATION_PDF_VALUE
    })
    public ResponseEntity<Object> invoicePreview(@AuthenticationPrincipal Jwt principal,
                                                 @PathVariable("id") UUID orderId,
                                                 @RequestHeader(HttpHeaders.ACCEPT) String acceptHeader,
                                                 @RequestBody @Validated CreateInvoice createInvoice) {
        var preview = salesService.invoicePreview(accountContext.accountId(principal), orderId, createInvoice.proforma, createInvoice.notes, createInvoice.renderingDetails);
        if (acceptHeader.contains(MediaType.TEXT_HTML_VALUE)) {
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(preview.b());
        } else if (acceptHeader.contains(MediaType.APPLICATION_PDF_VALUE)) {
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                            + "invoice_preview" + DateTimeFormatter.ofPattern("yyyy-MM-dd")
                            .format(LocalDate.now()) + ".pdf")
                    .body(preview.c());
        } else {
            return new ResponseEntity<>(preview.a(), HttpStatus.OK);
        }
    }

    @PostMapping(path = "{id}/goods-accompanying-note")
    public GoodsAccompanyingNotesDetails goodsAccompanyingNote(@AuthenticationPrincipal Jwt principal,
                                                               @PathVariable("id") UUID orderId,
                                                               @RequestBody @Validated GoodsAccompanyingNotesController.GoodsAccompanyingNoteDefinition create) {
        var note = salesService.goodsAccompanyingNote(accountContext.accountId(principal), orderId,
                create.deliveryDate(), create.from(), create.to(), create.delegateId(), create.transportRegistrationNumber(), create.notes(),
                create.items().stream().map(i -> new GoodsAccompanyingNote.Item(i.id(), i.quantity(), null, null)).toList());
        return new GoodsAccompanyingNotesDetails(note.id(), note.number(), note.items());
    }

    @PostMapping(path = "{id}/preview-goods-accompanying-note", produces = MediaType.TEXT_HTML_VALUE)
    public ResponseEntity<String> previewGoodsAccompanyingNote(@AuthenticationPrincipal Jwt principal,
                                                               @PathVariable("id") UUID orderId,
                                                               @RequestBody @Validated GoodsAccompanyingNotesController.GoodsAccompanyingNoteDefinition create) {
        return ResponseEntity.ok(salesService.previewGoodsAccompanyingNote(accountContext.accountId(principal), orderId,
                create.deliveryDate(), create.from(), create.to(), create.delegateId(), create.transportRegistrationNumber(), create.notes(),
                create.items().stream().map(i -> new GoodsAccompanyingNote.Item(i.id(), i.quantity(), null, null)).toList()));
    }

    @GetMapping(path = "{id}/available-stock/{productId}")
    public AvailableStock availableStockForProduct(@AuthenticationPrincipal Jwt principal,
                                                   @PathVariable("id") UUID orderId,
                                                   @PathVariable("productId") UUID productId) {
        var accountId = accountContext.accountId(principal);
        var orderRanking = db.select(SALES_ORDER.RANKING)
                .from(SALES_ORDER)
                .where(SALES_ORDER.OWNER_ID.eq(accountId), SALES_ORDER.ID.eq(orderId))
                .fetchSingleInto(Integer.class);
        var reservedBefore = db.select(coalesce(sum(RESERVED_INVENTORY.QUANTITY), 0))
                .from(RESERVED_INVENTORY)
                .where(
                        RESERVED_INVENTORY.OWNER_ID.eq(accountId),
                        RESERVED_INVENTORY.SALES_ORDER_ID.in(
                                select(SALES_ORDER.ID).from(SALES_ORDER)
                                        .where(SALES_ORDER.OWNER_ID.eq(accountId),
                                                SALES_ORDER.RANKING.lessThan(orderRanking),
                                                SALES_ORDER.RANKING.gt(SalesOrder.UNRANKED_ORDER_VALUE))),
                        RESERVED_INVENTORY.MATERIAL_GOOD_ID.eq(productId)
                )
                .fetchSingleInto(BigDecimal.class);
        var onStock = inventoryService.getCurrentStock(accountId, productId, inventoryService.defaultInventoryUnitId(accountId));
        return new AvailableStock(productId, onStock.subtract(reservedBefore));
    }

    @PostMapping(path = "{id}/notes/add")
    public void addNote(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable("id") UUID id,
            @Validated @RequestBody NoteDefinition note) {
        salesService.addNote(accountContext.accountId(principal), id, accountContext.userId(principal), note.note());
    }

    @GetMapping(path = "{id}/notes/list")
    public List<NoteDetails> listNotes(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable("id") UUID id) {
        return db.select(NOTE.ID, NOTE.CREATE_TIME, NOTE.UPDATE_TIME, NOTE.DELETED, NOTE.OWNER_ID, NOTE.ADDED_BY_ID, NOTE.NOTE_)
                .from(NOTE)
                .join(SALES_ORDER_NOTE).on(SALES_ORDER_NOTE.NOTE_ID.eq(NOTE.ID))
                .where(
                        NOTE.OWNER_ID.eq(accountContext.accountId(principal)),
                        SALES_ORDER_NOTE.SALES_ORDER_ID.eq(id)
                )
                .orderBy(NOTE.CREATE_TIME.desc())
                .fetchInto(Note.class)
                .stream()
                .map(note -> new NoteDetails(note.id(), note.createTime(),
                        new Entity(note.addedById(), db.select(USERS.NAME).from(USERS).where(USERS.ID.eq(note.addedById())).fetchSingleInto(String.class)),
                        note.note()))
                .toList();
    }

    @PostMapping(path = "{id}/save-version")
    public Entity saveVersion(@AuthenticationPrincipal Jwt principal,
                              @PathVariable("id") UUID orderId,
                              @RequestBody VersionDetails versionDetails) {
        salesService.saveVersion(accountContext.accountId(principal), orderId, versionDetails.offerExpiration, versionDetails.customerNotes, versionDetails.renderingDetails);
        var versions = versions(accountContext.accountId(principal), orderId);
        return versions.getFirst();
    }

    @GetMapping(path = "{id}/view-version/{versionId}", produces = {
            MediaType.TEXT_HTML_VALUE,
            MediaType.APPLICATION_PDF_VALUE
    })
    public ResponseEntity viewVersion(@AuthenticationPrincipal Jwt principal,
                                      @RequestHeader(HttpHeaders.ACCEPT) String acceptHeader,
                                      @PathVariable("id") UUID orderId,
                                      @PathVariable("versionId") UUID versionId) {

        var version = objectStorage.getFile(accountContext.accountId(principal), versionId);

        if (acceptHeader.contains(MediaType.TEXT_HTML_VALUE)) {
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(version.b());
        } else if (acceptHeader.contains(MediaType.APPLICATION_PDF_VALUE)) {
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + version.a().replaceAll("\\.html", "") + ".pdf")
                    .body(htmlToPdfConverter.convert(new String(version.b(), StandardCharsets.UTF_8)));
        }
        throw new RuntimeException("Unsupported accept header: " + acceptHeader);
    }

    @PostMapping(path = "{id}/clone")
    public SalesOrderDetails clone(@AuthenticationPrincipal Jwt principal,
                                   @PathVariable("id") UUID orderId) {
        return details(salesService.clone(accountContext.accountId(principal), orderId));
    }

    private SalesOrderDetails details(SalesOrder order) {
        var vatCalculator = vatCalculatorProvider.with(order.ownerId(), order.customerId());
        var customer = customer(order.customerId());
        var isVatApplicable = vatCalculator.isVatApplicable();
        var itemDetails = order.items().stream()
                .map(item -> {
                            var product = item.productId() != null
                                    ? db.selectFrom(MATERIAL_GOOD)
                                    .where(MATERIAL_GOOD.ID.eq(item.productId()))
                                    .fetchOptionalInto(MaterialGood.class)
                                    : Optional.<MaterialGood>empty();
                            if (product.isPresent() && product.get().details != null && product.get().details.manufacturingOperations() == null) {
                                log.warn("operations and materials are null for [{}]", product.get().id);
                            }
                            var service = db.selectFrom(SERVICE_TEMPLATE)
                                    .where(SERVICE_TEMPLATE.ID.eq(item.serviceId()))
                                    .fetchOptionalInto(ServiceTemplate.class);
                            String itemName = product.isPresent() ? product.get().name : service.map(ServiceTemplate::name).orElse(null);
                            String variantName = null;
                            if (product.isPresent() && product.get().parentId != null) {
                                variantName = itemName;
                                itemName = db.selectFrom(MATERIAL_GOOD)
                                        .where(MATERIAL_GOOD.ID.eq(product.get().parentId))
                                        .fetchSingleInto(MaterialGood.class)
                                        .name;
                            }
                            var reserved = product.isPresent()
                                    ? (order.status().equals(IN_QUOTATION) ?
                                    //in the case of quotes we reference the balance of an item
                                    available(order.ownerId(), item.productId())
                                    : (//if the order is SHIPPING or DELIVERED it is already removed from the reserved list, but we need to show that it is available
                                    List.of(SalesOrder.Status.SHIPPING, SalesOrder.Status.DELIVERED).contains(order.status())
                                            ? item.quantity()
                                            : reservedQuantity(order.ownerId(), order.id(), item.productId())))
                                    : null;
                            var available = service.isPresent() ? isServiceDone(order.ownerId(), order.id(), item.serviceId()) : item.quantity().compareTo(reserved) <= 0;
                            var manufacturingOrder = manufacturingOrderDetails(order.ownerId(), order.id(), item.productId(), item.serviceId());
                            var purchaseOrder = product.isPresent()
                                    ? (!available
                                    ? purchaseOrderDetails(order.ownerId(), order.id(), item.productId())
                                    : Optional.<PurchaseOrderDetails>empty())
                                    : Optional.<PurchaseOrderDetails>empty();
                            var measurementUnit = salesService.measurementUnit(item);
                            var laborCosts = product.map(p -> goodsService.laborCosts(order.ownerId(), item.productId())).orElseGet(() -> service.map(s -> s.cost() != null ? s.cost() : null).orElse(null));
                            var materialCosts = product.map(p -> materialCosts(p, item.customizations())).orElse(null);
                            var productionOverheadCosts = product.isPresent() ? goodsService.productionOverheadCosts(order.ownerId(), item.productId()) : null;
                            var administrativeOverheadCosts = product.isPresent() ? goodsService.administrativeOverheadCosts(order.ownerId(), item.productId()) : null;
                            var totalProductionCosts = laborCosts != null ? laborCosts.addNullSafe(materialCosts).addNullSafe(productionOverheadCosts) : null;
                            var lastPurchase = product.map(p -> lastPurchase(p.id, inventoryService.lastOrderedFrom(p.ownerId, p.id))).orElse(null);
                            var currencyExchangeDate = (order.status() == SalesOrder.Status.DELIVERED || order.status() == SalesOrder.Status.SHIPPING) ?
                                    order.updateTime()
                                            //todo get the zone right for the account
                                            .atZone(ZoneId.systemDefault()).toLocalDate()
                                    : LocalDate.now();
                            return new SaleItemDetails(
                                    item.productId(),
                                    item.serviceId(),
                                    itemName,
                                    product.map(p -> p.code).orElse(null),
                                    variantName,
                                    product.map(p -> p.details.vatRate()).orElse(null),
                                    item.quantity(),
                                    new StringEntity(measurementUnit.name(), measurementUnit.symbol()),
                                    item.price(),
                                    reserved,
                                    manufacturingOrder
                                            //we need to filter out the MO if the item was stolen by another SO; that means that we don't return the MO if its finished and the product is not available
                                            .filter(mo -> available || List.of(ManufacturingOrder.Status.SUBMITTED, ManufacturingOrder.Status.MANUFACTURING).contains(mo.status())).orElse(null),
                                    servicingOrderDetails(order.ownerId(), order.id(), item.serviceId()).orElse(null),
                                    purchaseOrder.orElse(null),
                                    isVatApplicable ? item.vatValue(vatCalculator, order.globalDiscountPercentage()) : new Money(0, item.price().currency()),
                                    item.grossValue(),
                                    order.hasGlobalDiscount() ? new Money(0, item.price().currency()) : item.discountValue(),
                                    item.discount(),
                                    available ? "READY" : "NEED_SUPPLY",
                                    available ? null : manufacturingOrder.isPresent() ? manufacturingOrder.get().expectedBy() : purchaseOrder.map(po -> po.expectedBy).orElse(null),
                                    product.map(p -> p.details.produced()).orElse(null),
                                    item.customizationNote(),
                                    lastPurchase != null && lastPurchase.price != null ? lastPurchase.setPrice(exchangeRatesProvider.convert(lastPurchase.price, item.price().currency(), currencyExchangeDate)) : lastPurchase,
                                    product.map(p -> p.details.manufacturingOperations().stream().flatMap(op -> op.materials().stream()).anyMatch(RequiredMaterial::configurableWithOptions)).orElse(false),
                                    laborCosts != null ? exchangeRatesProvider.convert(laborCosts, item.price().currency(), currencyExchangeDate) : null,
                                    materialCosts != null ? exchangeRatesProvider.convert(materialCosts, item.price().currency(), currencyExchangeDate) : null,
                                    productionOverheadCosts != null ? exchangeRatesProvider.convert(productionOverheadCosts, item.price().currency(), currencyExchangeDate) : null,
                                    administrativeOverheadCosts != null ? exchangeRatesProvider.convert(administrativeOverheadCosts, item.price().currency(), currencyExchangeDate) : null,
                                    totalProductionCosts != null ? exchangeRatesProvider.convert(totalProductionCosts, item.price().currency(), currencyExchangeDate) : null,
                                    item.customizations(),
                                    isTrue(item.addedToWishlist())
                            );
                        }
                )
                .collect(toList());
        return new SalesOrderDetails(
                order.id(),
                order.createTime(),
                order.updateTime(),
                customer,
                order.number(),
                order.ranking(),
                salesService.orderSubmittedDate(order.id()).map(LocalDate::atStartOfDay).orElse(order.createTime().atZone(ZoneId.systemDefault()).toLocalDateTime()),
                order.deliveryDeadline(),
                order.status(),
                order.total(vatCalculator, isVatApplicable),
                order.subTotal(),
                isVatApplicable ? order.vat(vatCalculator) : new Money(0, order.currency()),
                order.discount(),
                itemDetails.size(),
                itemDetails.stream()
                        .filter(item -> item.reserved != null)
                        .filter(item -> item.reserved.compareTo(item.quantity) >= 0).toList().size() +
                        //add services
                        itemDetails.stream().filter(item -> item.productId == null)
                                .filter(item -> item.status.equals("READY"))
                                .toList().size(),
                itemDetails,
                order.shippingAddress(),
                order.notes(),
                order.customerNotes(),
                invoiceDetails(order.id(), false),
                invoiceDetails(order.id(), true),
                goodsAccompanyingNotes(order.id()),
                files(order.ownerId(), order.id()),
                versions(order.ownerId(), order.id()),
                order.offerDate(),
                order.offerExpiration(),
                order.globalDiscount()
        );
    }

    private Map<String, Object> manufactureMissing(UUID accountId, UUID orderId, ProductManufacturingOrder productOrder,
                                                   List<Tuple.Tuple2<String, ByteArrayInputStream>> files) {
        //todo consider doing validation here - as an example we shouldn't allow more products to be ordered than the outstanding for the sale
        if (productOrder.productId != null && productOrder.serviceId != null) {
            throw new RuntimeException("Can't specify both product and service");
        }
        var mo = salesService.manufactureMissing(accountId, orderId,
                productOrder.productId, productOrder.serviceId, productOrder.quantity, productOrder.customizationNote, files);
        var response = new HashMap<String, Object>();
        response.put("id", mo.a());
        response.put("number", mo.b());
        response.put("expectedBy", db.select(max(MANUFACTURING_TASK.ESTIMATED_END_TIME))
                .from(MANUFACTURING_TASK)
                .where(MANUFACTURING_TASK.MANUFACTURING_ORDER_ID.eq(mo.a()))
                .fetchOptionalInto(LocalDateTime.class)
                .orElse(null));
        return response;
    }

    private List<File> files(UUID ownerId, UUID orderId) {
        return db.select(FILE.ID, FILE.FILE_NAME)
                .from(FILE)
                .where(FILE.ID.in(select(SALESORDER_FILE.FILE_ID).from(SALESORDER_FILE).where(SALESORDER_FILE.OWNER_ID.eq(ownerId), SALESORDER_FILE.SALES_ORDER_ID.eq(orderId), SALESORDER_FILE.TYPE.ne(SalesService.SalesOrderFileType.VERSION.name()))))
                .orderBy(FILE.CREATE_TIME.asc())
                .fetch()
                .stream()
                .map(f -> new File(f.value1(), f.value2(),
                        FilesController.previewLink(f.value1()), FilesController.downloadLink(f.value1())))
                .toList();
    }

    private List<Entity> versions(UUID ownerId, UUID orderId) {
        return db.select(FILE.ID, FILE.FILE_NAME)
                .from(FILE)
                .where(FILE.ID.in(select(SALESORDER_FILE.FILE_ID).from(SALESORDER_FILE).where(SALESORDER_FILE.OWNER_ID.eq(ownerId), SALESORDER_FILE.SALES_ORDER_ID.eq(orderId), SALESORDER_FILE.TYPE.eq(SalesService.SalesOrderFileType.VERSION.name()))))
                .orderBy(FILE.CREATE_TIME.desc())
                .fetchInto(Entity.class)
                .stream()
                .map(e -> new Entity(e.id(), e.name().replaceAll("\\.html", "")))
                .toList();
    }

    private SalesInvoiceDetails invoiceDetails(UUID orderId, boolean proforma) {
        return db.select(INVOICE.ID, INVOICE.NUMBER, INVOICE.SENT_AT)
                .from(INVOICE)
                .where(INVOICE.SALES_ORDER_ID.eq(orderId), INVOICE.PROFORMA.eq(proforma))
                .fetchOneInto(SalesInvoiceDetails.class);
    }

    private BigDecimal reservedQuantity(UUID ownerId, UUID orderId, UUID productId) {
        return db.select(coalesce(sum(RESERVED_INVENTORY.QUANTITY), 0))
                .from(RESERVED_INVENTORY)
                .where(
                        RESERVED_INVENTORY.OWNER_ID.eq(ownerId),
                        RESERVED_INVENTORY.SALES_ORDER_ID.eq(orderId),
                        RESERVED_INVENTORY.MATERIAL_GOOD_ID.eq(productId)
                )
                .fetchSingleInto(BigDecimal.class);
    }

    private Optional<ManufacturingOrderDetails> manufacturingOrderDetails(UUID ownerId, UUID salesOrderId, UUID productId, UUID serviceId) {
        return db.selectFrom(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.SALES_ORDER_ID.eq(salesOrderId),
                        MANUFACTURING_ORDER.OWNER_ID.eq(ownerId),
                        productId != null ? MANUFACTURING_ORDER.PRODUCT_ID.eq(productId) : MANUFACTURING_ORDER.SERVICE_ID.eq(serviceId),
                        MANUFACTURING_ORDER.DELETED.isFalse()
                )
                .orderBy(MANUFACTURING_ORDER.CREATE_TIME.desc())
                .limit(1)
                .fetchOptionalInto(ManufacturingOrder.class)
                .map(order -> new ManufacturingOrderDetails(
                                order.id(),
                                order.number(),
                                order.status(),
                                order.quantity(),
                                ofNullable(order.manufacturedProducts()).orElse(List.of()).stream()
                                        .map(ManufacturingOrder.ManufacturedProduct::quantity)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add),
                                db.select(max(MANUFACTURING_TASK.ESTIMATED_END_TIME))
                                        .from(MANUFACTURING_TASK)
                                        .where(MANUFACTURING_TASK.MANUFACTURING_ORDER_ID.eq(order.id()))
                                        .fetchOptionalInto(LocalDate.class)
                                        .orElse(null),
                                order.customProduct()
                        )
                );
    }

    private Optional<ServicingOrderDetails> servicingOrderDetails(UUID ownerId, UUID salesOrderId, UUID serviceId) {
        return db.selectFrom(SERVICING_ORDER)
                .where(SERVICING_ORDER.SALES_ORDER_ID.eq(salesOrderId),
                        SERVICING_ORDER.OWNER_ID.eq(ownerId),
                        SERVICING_ORDER.SERVICE_ID.eq(serviceId),
                        SERVICING_ORDER.DELETED.isFalse()
                )
                .fetchOptionalInto(ServicingOrder.class)
                .map(order -> new ServicingOrderDetails(
                                order.id(),
                                order.number(),
                                order.status(),
                                order.quantity()
                        )
                );
    }

    private Optional<PurchaseOrderDetails> purchaseOrderDetails(UUID ownerId, UUID orderId, UUID productId) {
        var purchaseOrders = new LinkedList<>(db.selectFrom(PURCHASE_ORDER)
                .where(PURCHASE_ORDER.OWNER_ID.eq(ownerId)
                        .and(arrayContainsJson(PURCHASE_ORDER.ITEMS, format("[{\"materialGoodId\": \"%s\"}]", productId)))
                        .and(PURCHASE_ORDER.STATUS.eq(PurchaseOrder.Status.SUBMITTED.name()))
                        .and(PURCHASE_ORDER.DELETED.isFalse())
                )
                .fetchInto(PurchaseOrder.class));
        var salesOrders = new LinkedList<>(db.selectFrom(SALES_ORDER)
                .where(SALES_ORDER.OWNER_ID.eq(ownerId)
                        .and(arrayContainsJson(SALES_ORDER.ITEMS, format("[{\"productId\": \"%s\"}]", productId)))
                        .and(SALES_ORDER.STATUS.in(IN_QUOTATION.name(),
                                SUBMITTED.name(),
                                PROCESSING.name(),
                                PICKING_PACKING.name(),
                                READY_TO_SHIP.name()))
                        .and(SALES_ORDER.DELETED.isFalse())
                )
                .orderBy(SALES_ORDER.RANKING)
                .fetchInto(SalesOrder.class));

        //we allocate incoming products for the sales orders in order of their ranking

        var availableQuantity = BigDecimal.ZERO;
        while (!purchaseOrders.isEmpty()) {
            var purchaseOrder = purchaseOrders.remove();
            var product = purchaseOrder.items().stream()
                    .filter(item -> item.materialGoodId().equals(productId))
                    .findFirst()
                    .get();
            availableQuantity = availableQuantity.add(product.quantity());
            while (!salesOrders.isEmpty()) {
                if (availableQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                    break;
                }
                var salesOrder = salesOrders.remove();
                if (salesOrder.id().equals(orderId)) {
                    return Optional.of(new PurchaseOrderDetails(purchaseOrder.id(), purchaseOrder.number(), purchaseOrder.status(), purchaseOrder.expectedBy()));
                } else {
                    availableQuantity = availableQuantity
                            .subtract(salesOrder.items().stream()
                                    .filter(item -> productId.equals(item.productId()))
                                    .findFirst().get().quantity());
                }
            }
        }
        return Optional.empty();
    }

    private Customer customer(UUID customerId) {
        return db.select(COMPANY.ID, COMPANY.COMPANY_NAME, COMPANY.COMPANY_TYPE)
                .from(COMPANY)
                .where(COMPANY.ID.eq(customerId))
                .fetchSingleInto(Customer.class);
    }

    private BigDecimal available(UUID ownerId, UUID productId) {
        var inventorySettings = inventorySettings(ownerId);
        var currentStock = inventoryService.getCurrentStock(ownerId, productId, inventorySettings.unitDesignations().defaultInventoryUnit());
        var committedStock = getCommitted(ownerId, productId);
        return currentStock.subtract(committedStock);
    }

    private BigDecimal getCommitted(UUID ownerId, UUID productId) {
        //sales order where the product is present
        return db.selectFrom(SALES_ORDER)
                .where(SALES_ORDER.DELETED.isFalse()
                        .and(SALES_ORDER.OWNER_ID.eq(ownerId))
                        .and(SALES_ORDER.STATUS.in(SUBMITTED.name(), PROCESSING.name(), PICKING_PACKING.name(), READY_TO_SHIP.name()))
                        .and(arrayContainsJson(SALES_ORDER.ITEMS, format("[{\"productId\": \"%s\"}]", productId)))
                )
                .fetchInto(SalesOrder.class)
                .stream()
                .flatMap(order -> order.items().stream())
                .filter(product -> productId.equals(product.productId()))
                .map(SalesOrder.Item::quantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private boolean duplicateProductExists(List<Item> items) {
        if (isEmpty(items)) {
            return false;
        }
        var itemsWithProductsOnly = items.stream().filter(item -> item.productId != null).map(item -> item.productId).toList();
        return itemsWithProductsOnly.size() != new HashSet<>(itemsWithProductsOnly).size();
    }

    private Account.Settings.General.InventoryAccountingSettings inventorySettings(UUID ownerId) {
        return db.selectFrom(ACCOUNT)
                .where(ACCOUNT.ID.eq(ownerId))
                .fetchSingleInto(Account.class)
                .settings().general().inventoryAccountingSettings();
    }

    private void verifyCustomizations(List<Item> items) {
        if (isEmpty(items)) {
            return;
        }
        items.stream()
                .filter(item -> item.productId != null && isNotEmpty(item.customizations))
                .forEach(item -> {
                    var productDetails = productDetails(item.productId);
                    item.customizations.forEach(customization -> {
                        var customizationConfig = productDetails.manufacturingOperations().stream().flatMap(op -> op.materials().stream()).filter(m -> m.materialIds().contains(customization.materialId())).toList();
                        if (customizationConfig.size() != 1) {
                            throw new BusinessException(String.format("Invalid configurations for [%s]. More than 1 customization config matches.", customization.materialId()),
                                    "invalid_product_customizations", customization.materialId());
                        }
                        if (!customizationConfig.get(0).configurableWithOptions()) {
                            throw new BusinessException(String.format("Invalid configurations for [%s]. Item is not marked as customizable.", customization.materialId()),
                                    "product_not_customizable", customization.materialId());
                        }
                    });
                });
    }

    private MaterialGood.Details productDetails(UUID productId) {
        return db.select(MATERIAL_GOOD.DETAILS)
                .from(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(productId))
                .fetchSingleInto(MaterialGood.Details.class);
    }

    private Money materialCosts(MaterialGood product, List<SalesOrder.Item.Customization> customizations) {
        if (product.details.manufacturingOperations().stream().flatMap(op -> op.materials().stream()).anyMatch(RequiredMaterial::configurableWithOptions) && isNotEmpty(customizations)) {
            return product.details.manufacturingOperations().stream().flatMap(op -> op.materials().stream())
                    .map(material -> {
                        if (material.configurableWithOptions()) {
                            return material.materialIds().stream()
                                    .filter(option -> customizations.stream().anyMatch(customization -> customization.materialId().equals(option)))
                                    .findFirst()
                                    .flatMap(m -> customizations.stream().filter(customization -> customization.materialId().equals(m)).findFirst())
                                    .map(customization -> Tuple.of(customization.materialId(), customization.quantity()))
                                    .orElse(Tuple.of(material.materialIds().get(0), material.quantity()));
                        } else if (material.optional()) {
                            return customizations.stream().filter(customization -> customization.materialId().equals(material.materialIds().get(0)))
                                    .findFirst()
                                    .map(customization -> Tuple.of(customization.materialId(), customization.quantity()))
                                    .orElse(Tuple.of(material.materialIds().get(0), BigDecimal.ZERO));
                        } else {
                            return Tuple.of(material.materialIds().get(0), material.quantity());
                        }
                    })
                    .map(material -> new Money(inventoryService.getAverageCost(product.ownerId, material.a()).multiply(material.b()).amount(),
                            product.sellPrice.currency()))
                    .reduce(Money::add)
                    .orElse(new Money(0, Money.DEFAULT_CURRENCY));
        } else {
            return goodsService.estimatedMaterialCosts(product.ownerId, product.id);
        }
    }

    private void validateItems(List<Item> items) {
        if (isEmpty(items)) {
            return;
        }
        if (items.stream().anyMatch(item -> item.productId == null && item.serviceId == null)
                || items.stream().anyMatch(item -> item.productId != null && item.serviceId != null)) {
            throw new BusinessException("Either product id or service id must be specified", "invalid_items_definition");
        }
    }

    private boolean isServiceDone(UUID ownerId, UUID orderId, UUID serviceId) {
        return db.fetchCount(SERVICING_ORDER,
                SERVICING_ORDER.OWNER_ID.eq(ownerId),
                SERVICING_ORDER.SALES_ORDER_ID.eq(orderId),
                SERVICING_ORDER.SERVICE_ID.eq(serviceId),
                SERVICING_ORDER.STATUS.in(ServicingOrder.Status.EXECUTED.name(), ServicingOrder.Status.CLOSED.name())
        ) == 1;
    }

    private List<GoodsAccompanyingNotesDetails> goodsAccompanyingNotes(UUID orderId) {
        return db.selectFrom(GOODS_ACCOMPANYING_NOTE)
                .where(GOODS_ACCOMPANYING_NOTE.ID.in(select(SALESORDER_GOODSACCOMPANYINGNOTE.GOODS_ACCOMPANYING_NOTE_ID).from(SALESORDER_GOODSACCOMPANYINGNOTE).where(SALESORDER_GOODSACCOMPANYINGNOTE.SALES_ORDER_ID.eq(orderId))))
                .fetchInto(GoodsAccompanyingNote.class)
                .stream()
                .map(note -> new GoodsAccompanyingNotesDetails(note.id(), note.number(), note.items()))
                .toList();
    }

    private LastPurchase lastPurchase(UUID productId, Entity supplier) {
        return new LastPurchase(supplier != null ? supplier.id() : null, supplier != null ? supplier.name() : null,
                db.select(INVENTORY.RECEPTION_PRICE_AMOUNT, INVENTORY.RECEPTION_PRICE_CURRENCY)
                        .from(INVENTORY)
                        .where(INVENTORY.MATERIAL_GOOD_ID.eq(productId))
                        .orderBy(INVENTORY.CREATE_TIME.desc())
                        .limit(1)
                        .fetchOneInto(Money.class)
        );
    }

    public record CreateSalesOrder(
            @NotNull LocalDateTime createTime,
            LocalDateTime deliveryDeadline,
            @NotNull UUID customerId,
            Boolean isQuotation,
            List<Item> items,
            Address shippingAddress,
            String notes,
            String customerNotes,
            LocalDate offerDate,
            LocalDate offerExpiration,
            SalesOrder.RenderingDetails renderingDetails,
            GlobalDiscount globalDiscount
    ) {
    }

    public record UpdateSalesOrder(
            LocalDateTime deliveryDeadline,
            UUID customerId,
            List<Item> items,
            Address shippingAddress,
            String notes,
            String customerNotes,
            LocalDate offerDate,
            LocalDate offerExpiration,
            SalesOrder.RenderingDetails renderingDetails,
            GlobalDiscount globalDiscount
    ) {
    }

    public record Item(
            UUID productId, UUID serviceId, BigDecimal quantity, Money price, BigDecimal discount,
            String customizationNote, List<SalesOrder.Item.Customization> customizations
    ) {
    }

    public record CreateInvoice(
            boolean proforma,
            String notes,
            EmailMessage emailMessage,
            Invoice.RenderingDetails renderingDetails
    ) {
    }

    public record SalesOrderDetails(
            UUID id,
            Instant createTime,
            Instant updateTime,
            Customer customer,
            String number,
            Integer ranking,
            LocalDateTime submittedDate,
            LocalDateTime deliveryDeadline,
            SalesOrder.Status status,
            Money totalAmount,
            Money subTotalAmount,
            Money taxAmount,
            Money discountAmount,
            Integer numberOfItems,
            Integer numberOfAvailableItems,
            List<SaleItemDetails> items,
            Address shippingAddress,
            String notes,
            String customerNotes,
            SalesInvoiceDetails invoice,
            SalesInvoiceDetails proformaInvoice,
            List<GoodsAccompanyingNotesDetails> goodsAccompanyingNotes,
            List<File> files,
            List<Entity> versions,
            LocalDate offerDate,
            LocalDate offerExpiration,
            GlobalDiscount globalDiscount
    ) {
    }

    public record SaleItemDetails(
            UUID productId,
            UUID serviceId,
            String name,
            String code,
            String variant,
            BigDecimal vatRate,
            BigDecimal quantity,
            StringEntity measurementUnit,
            Money price,
            BigDecimal reserved,
            ManufacturingOrderDetails manufacturingOrder,
            ServicingOrderDetails servicingOrderDetails,
            PurchaseOrderDetails purchaseOrder,
            Money taxAmount,
            Money totalAmount,
            Money discountAmount,
            BigDecimal discount,
            String status,
            LocalDate expectedBy,
            Boolean produced,
            String customizationNote,
            LastPurchase lastPurchase,
            boolean customizable,
            Money laborCosts,
            Money materialCosts,
            Money productionOverheadCosts,
            Money administrativeOverheadCosts,
            Money totalProductionCosts,
            List<SalesOrder.Item.Customization> customizations,
            boolean addedToWishlist
    ) {
    }

    public record ProductManufacturingOrder(
            UUID productId,
            UUID serviceId,
            BigDecimal quantity,
            String customizationNote
    ) {
    }

    public record ProductWishlistAddition(
            UUID productId,
            BigDecimal quantity,
            UUID supplierId
    ) {
    }

    public record SalesInvoiceDetails(
            UUID id,
            String number,
            LocalDateTime sentAt
    ) {
    }

    public record GoodsAccompanyingNotesDetails(
            UUID id,
            String number,
            List<GoodsAccompanyingNote.Item> items
    ) {
    }

    public record ManufacturingOrderDetails(
            UUID id,
            String number,
            ManufacturingOrder.Status status,
            BigDecimal orderedQuantity,
            BigDecimal manufacturedQuantity,
            LocalDate expectedBy,
            boolean customProduct
    ) {
    }

    public record ServicingOrderDetails(
            UUID id,
            String number,
            ServicingOrder.Status status,
            BigDecimal orderedQuantity
    ) {
    }

    public record PurchaseOrderDetails(
            UUID id,
            String number,
            PurchaseOrder.Status status,
            LocalDate expectedBy
    ) {
    }

    public record AvailableStock(UUID id, BigDecimal quantity) {
    }

    public record LastPurchase(UUID supplierId,
                               String supplierName,
                               Money price) {

        LastPurchase setPrice(Money price) {
            return new LastPurchase(supplierId, supplierName, price);
        }

    }

    public record VersionDetails(String customerNotes, LocalDate offerExpiration, SalesOrder.RenderingDetails renderingDetails) {
    }

    public record Customer(UUID id, String name, Company.Type type) {
    }
}
