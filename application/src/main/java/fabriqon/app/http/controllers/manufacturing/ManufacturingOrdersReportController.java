package fabriqon.app.http.controllers.manufacturing;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.manufacturing.ManufacturingOperationService;
import fabriqon.app.business.manufacturing.ManufacturingOrder;
import fabriqon.app.business.manufacturing.ManufacturingTask;
import fabriqon.app.business.manufacturing.MaterialIssueNote;
import fabriqon.app.business.servicetemplates.ServiceTemplate;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.common.model.Money;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.response.Entity;
import fabriqon.pdf.HtmlToPdfConverter;
import fabriqon.templates.Localizer;
import fabriqon.templates.Templates;
import org.jooq.DSLContext;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Stream;

import static fabriqon.jooq.classes.Tables.*;
import static org.jooq.impl.DSL.select;

@RestController
@RequestMapping(path = "/manufacturing/orders/{id}/reports")
public class ManufacturingOrdersReportController {

    private final DSLContext db;
    private final Templates templates;
    private final HtmlToPdfConverter htmlToPdfConverter;
    private final AccountContext accountContext;
    private final ManufacturingOperationService manufacturingOperationService;

    public ManufacturingOrdersReportController(DSLContext db, Templates templates, HtmlToPdfConverter htmlToPdfConverter,
                                               AccountContext accountContext, ManufacturingOperationService manufacturingOperationService) {
        this.db = db;
        this.templates = templates;
        this.htmlToPdfConverter = htmlToPdfConverter;
        this.accountContext = accountContext;
        this.manufacturingOperationService = manufacturingOperationService;
    }


    @SuppressWarnings("rawtypes")
    @GetMapping(path = "production",
            produces = {
                    MediaType.TEXT_HTML_VALUE,
                    MediaType.APPLICATION_PDF_VALUE
            })
    public ResponseEntity productionReport(
            @AuthenticationPrincipal Jwt principal,
            @RequestHeader(HttpHeaders.ACCEPT) String acceptHeader,
            @PathVariable(value = "id") UUID orderId) {
        var accountId = accountContext.accountId(principal);

        var order = db.selectFrom(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.OWNER_ID.eq(accountId),
                        MANUFACTURING_ORDER.DELETED.isFalse(),
                        MANUFACTURING_ORDER.ID.eq(orderId))
                .fetchSingleInto(fabriqon.app.business.manufacturing.ManufacturingOrder.class);

        if (acceptHeader.contains(MediaType.TEXT_HTML_VALUE)) {
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(html(accountId, order));
        } else if (acceptHeader.contains(MediaType.APPLICATION_PDF_VALUE)) {
            var pdf = htmlToPdfConverter.convert(html(accountId, order));
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                            + "production_report_" + order.number() + "_"
                            + DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.ofInstant(order.updateTime(), ZoneId.systemDefault()))
                            + ".pdf")
                    .body(pdf);
        }
        return ResponseEntity.status(HttpStatus.UNSUPPORTED_MEDIA_TYPE).build();
    }

    private String html(UUID ownerId, ManufacturingOrder order) {
        var account = account(ownerId);
        var localizer = new Localizer(new Locale(account.information().address().country()));

        var orderMap = new HashMap<>();
        orderMap.put("number", order.number());
        orderMap.put("date", localizer.localizeDate(order.createTime()));
        orderMap.put("notes", order.notes());

        var materialConsumption = getMaterialConsumption(order, localizer, 0);

        var currency = account.settings().general().defaultCurrency();
        var totalLaborCost = new AtomicLong(0);
        var totalWorkstationCost = new AtomicLong(0);
        var totalIndirectCost = new AtomicLong(0);
        var taskIndexer = new AtomicInteger(0);
        var manufacturingOperations = manufacturingOperationService.enhance(order.manufacturingOperations());
        var laborAndEquipmentCosts = tasks(order.id()).stream()
                .map(task -> {
                    var assignedEmployees = assignedEmployees(task.id());
                    var assignedWorkstation = assignedWorkstation(task.id());
                    var manufacturingOperation = manufacturingOperations.stream()
                            .filter(op -> op.name().equalsIgnoreCase(task.details().name()))
                            .findFirst().orElseThrow();
                    int standardDuration = manufacturingOperation.durationInMinutes();
                    var durationInHours = BigDecimal.valueOf(standardDuration)
                            .setScale(4, RoundingMode.HALF_EVEN)
                            .multiply(order.quantity())
                            .divide(BigDecimal.valueOf(60), RoundingMode.HALF_EVEN);
                    var manufacturingOverheadCost = order.manufacturingCosts().manufacturingOverheadPerEmployeeHour().multiply(durationInHours);
                    var operationHourlyRate = manufacturingOperation.costPerHour() != null
                            ? manufacturingOperation.costPerHour() :
                            assignedEmployees.stream()
                            .map(e -> order.manufacturingCosts().employeeHourlyRates().get(e.id()))
                            .reduce(Money::add)
                            .map(money -> money.divide(BigDecimal.valueOf(assignedEmployees.size())))
                            .orElse(new Money(0, currency));
                    totalIndirectCost.addAndGet(manufacturingOverheadCost.amount());
                    var operationLaborCost = operationHourlyRate.multiply(durationInHours);
                    totalLaborCost.addAndGet(operationLaborCost.amount());
                    var workstationTotalCost = assignedWorkstation
                            .map(w -> order.manufacturingCosts().workstationHourlyRates().get(w.id()).multiply(durationInHours));
                    totalWorkstationCost.addAndGet(workstationTotalCost.map(Money::amount).orElse(0L));
                    var m = new HashMap<String, Object>();
                    m.put("index", taskIndexer.incrementAndGet());
                    m.put("taskName", task.details().name());
                    m.put("standardDuration", localizer.humanReadableDuration(Duration.ofMinutes(task.durationInMinutes())));
                    m.put("employeeHourlyCost", operationHourlyRate.displayAmount());
                    m.put("employeeTotalCost", operationLaborCost.displayAmount());
                    m.put("workstationHourlyCost", assignedWorkstation.map(w -> order.manufacturingCosts().workstationHourlyRates().get(w.id())).map(Money::displayAmount).orElse(""));
                    m.put("workstationTotalCost", workstationTotalCost.map(Money::displayAmount).orElse(""));
                    m.put("overheadCost", manufacturingOverheadCost.displayAmount());
                    m.put("totalCost", operationLaborCost
                                    .add(manufacturingOverheadCost)
                                    .add(workstationTotalCost.orElseGet(() -> new Money(0, currency)))
                                    .displayAmount()
                    );
                    return m;
                })
                .toList();

        var product = product(order.productId(), order.serviceId());
        var materialIssueNote = materialIssueNote(order.id());
        return templates.render("pdf/production_report.html",
                Map.of(
                        "order", orderMap,
                        "currency", currency,
                        "materialConsumption", materialConsumption,
                        "laborAndEquipmentCosts", laborAndEquipmentCosts,
                        "supplierName", account.name(),
                        "product", Map.of(
                                "name", product.get("name"),
                                "code", product.get("code"),
                                "quantity", order.quantity().stripTrailingZeros(),
                                "measurementUnit", localizer.localizedValue(product.get("measurementUnit"))
                        ),
                        "report", Map.of(
                                "totalMaterialCosts", materialIssueNote.materials().stream()
                                        .map(MaterialIssueNote.Material::totalCost)
                                        .reduce(Money::add)
                                        .map(Money::displayAmount)
                                        .orElse(""),
                                "totalLaborCosts", new Money(totalLaborCost.get(), currency).displayAmount(),
                                "totalWorkstationCosts", new Money(totalWorkstationCost.get(), currency).displayAmount(),
                                "totalDirectCosts", new Money(totalLaborCost.get() + totalWorkstationCost.get(), currency)
                                        .add(materialIssueNote.materials().stream()
                                                .map(MaterialIssueNote.Material::totalCost)
                                                .reduce(Money::add).orElse(new Money(0, currency)))
                                        .displayAmount(),
                                "totalIndirectCosts", new Money(totalIndirectCost.get(), currency).displayAmount(),
                                "totalCosts", new Money(totalLaborCost.get() + totalWorkstationCost.get() + totalIndirectCost.get(), currency)
                                        .add(materialIssueNote.materials().stream()
                                                .map(MaterialIssueNote.Material::totalCost)
                                                .reduce(Money::add).orElse(new Money(0, currency)))
                                        .displayAmount()
                        ),
                        "labels", localizer.labels(),
                        "base64Logo", account.logo() != null ? account.logo() : ""
                )
        );
    }

    private List<Map<String, Object>> getMaterialConsumption(ManufacturingOrder order, Localizer localizer, int level) {
        var materialIndexer = new AtomicInteger(0);
        var materialIssueNote = materialIssueNote(order.id());
        var materialIds = new HashSet<UUID>();
        var requiredMaterials = order.manufacturingOperations().stream().flatMap(op -> op.materials().stream()).toList();
        materialIds.addAll(requiredMaterials.stream().map(m -> m.materialIds().getFirst()).toList());
        materialIds.addAll(materialIssueNote.materials().stream().map(MaterialIssueNote.Material::id).toList());
        var childOrders = db.selectFrom(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.OWNER_ID.eq(order.ownerId()),
                        MANUFACTURING_ORDER.DELETED.isFalse(),
                        MANUFACTURING_ORDER.PARENT_ID.eq(order.id()))
                .orderBy(MANUFACTURING_ORDER.NUMBER.asc())
                .fetchInto(ManufacturingOrder.class);
        
        return materialIds.stream()
                .flatMap(materialId -> {
                    var material = db.selectFrom(MATERIAL_GOOD).where(MATERIAL_GOOD.ID.eq(materialId)).fetchSingleInto(MaterialGood.class);
                    var recordedData = materialIssueNote.materials().stream()
                            .filter(mat -> materialId.equals(mat.id()))
                            .findAny();
                    
                    var mainMaterial = createMaterialMap(
                            materialIndexer.incrementAndGet(), 
                            material.name, 
                            material.code,
                            getStandardConsumption(requiredMaterials, materialId, order.quantity()),
                            recordedData.map(mat -> mat.quantity().stripTrailingZeros().toPlainString()).orElse(""),
                            recordedData.map(mat -> mat.wastedQuantity().stripTrailingZeros().toPlainString()).orElse(""),
                            localizer.localizedValue(material.details.measurementUnit().name()),
                            recordedData.map(mat -> mat.totalCost().divide(mat.quantity()).displayAmount()).orElse(""),
                            recordedData.map(mat -> mat.totalCost().displayAmount()).orElse(""),
                            level, false, false
                    );

                    var childOrdersForMaterial = childOrders.stream().filter(co -> co.productId().equals(materialId)).toList();
                    
                    if (childOrdersForMaterial.isEmpty()) {
                        return Stream.concat(Stream.of(mainMaterial),
                                childOrders.stream().filter(co -> co.productId().equals(materialId))
                                        .flatMap(co -> getMaterialConsumption(co, localizer, level + 1).stream())
                        );
                    } else {
                        var totalChildQuantity = childOrdersForMaterial.stream()
                                .map(ManufacturingOrder::quantity)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                        var stockQuantity = recordedData
                                .map(mat -> mat.quantity().subtract(totalChildQuantity))
                                .orElse(BigDecimal.ZERO);

                        var childMOEntriesWithMaterials = Stream.<Map<String, Object>>builder();

                        // Add each child MO followed by its materials
                        for (int i = 0; i < childOrdersForMaterial.size(); i++) {
                            var co = childOrdersForMaterial.get(i);
                            // Add child MO header
                            var unitCost = recordedData.map(mat -> mat.totalCost().divide(mat.quantity()).displayAmount()).orElse("");
                            var totalCost = recordedData.map(mat -> {
                                var cost = mat.totalCost().divide(mat.quantity());
                                return cost.multiply(co.quantity()).displayAmount();
                            }).orElse("");
                            
                            var childMOHeader = createMaterialMap(
                                    materialIndexer.get() + "." + (i + 1),
                                    /*localizer.labels().get("child_manufacturing_order") + ": " +*/ co.number(),
                                    "",
                                    "",
                                    co.quantity().stripTrailingZeros().toPlainString(),
                                    "",
                                    localizer.localizedValue(material.details.measurementUnit().name()),
                                    unitCost,
                                    totalCost,
                                    level + 1, true, false
                            );
                            childMOEntriesWithMaterials.add(childMOHeader);

                            // Add child MO materials directly after the header with proper indexing
                            var childMaterials = getMaterialConsumption(co, localizer, level + 2);
                            var childMaterialIndex = new AtomicInteger(0);
                            var parentIndex = materialIndexer.get();
                            var childMOIndex = i + 1;
                            childMaterials.stream()
                                    .peek(childMaterial -> {
                                        var childIndex = parentIndex + "." + childMOIndex + "." + childMaterialIndex.incrementAndGet();
                                        childMaterial.put("index", childIndex);
                                    })
                                    .forEach(childMOEntriesWithMaterials::add);
                        }

                        // Add stock entry if needed
                        Stream<Map<String, Object>> stockEntry = Stream.empty();
                        if (stockQuantity.compareTo(BigDecimal.ZERO) > 0) {
                            var stockMap = createMaterialMap(
                                    materialIndexer.get() + "." + (childOrdersForMaterial.size() + 1),
                                    localizer.labels().get("from_stock"),
                                    "",
                                    "",
                                    stockQuantity.stripTrailingZeros().toPlainString(),
                                    "",
                                    localizer.localizedValue(material.details.measurementUnit().name()),
                                    recordedData.map(mat -> mat.totalCost().divide(mat.quantity()).displayAmount()).orElse(""),
                                    recordedData.map(mat -> {
                                        var unitCost = mat.totalCost().divide(mat.quantity());
                                        return unitCost.multiply(stockQuantity).displayAmount();
                                    }).orElse(""),
                                    level + 1, false, true
                            );
                            stockEntry = Stream.of(stockMap);
                        }

                        return Stream.concat(
                                Stream.of(mainMaterial),
                                Stream.concat(
                                        childMOEntriesWithMaterials.build(),
                                        stockEntry
                                )
                        );
                    }
                })
                .toList();
    }

    private Map<String, Object> createMaterialMap(Object index, String name, String code, String standardConsumption,
                                                  String actualConsumption, String wastedQuantity, String measurementUnit,
                                                  String unitCost, String totalCost, int level, boolean isChildMO, boolean isStock) {
        var map = new HashMap<String, Object>();
        map.put("index", index);
        map.put("name", name);
        map.put("code", code);
        map.put("standardConsumption", standardConsumption);
        map.put("actualConsumption", actualConsumption);
        map.put("wastedQuantity", wastedQuantity);
        map.put("measurementUnit", measurementUnit);
        map.put("unitCost", unitCost);
        map.put("totalCost", totalCost);
        map.put("level", level);
        map.put("isChildMO", isChildMO);
        map.put("isStock", isStock);
        return map;
    }

    private String getStandardConsumption(List<?> requiredMaterials, UUID materialId, BigDecimal orderQuantity) {
        return requiredMaterials.stream()
                .filter(rm -> {
                    try {
                        var method = rm.getClass().getMethod("materialIds");
                        @SuppressWarnings("unchecked")
                        List<UUID> materialIds = (List<UUID>) method.invoke(rm);
                        return materialId.equals(materialIds.getFirst());
                    } catch (Exception e) {
                        return false;
                    }
                })
                .map(rm -> {
                    try {
                        var method = rm.getClass().getMethod("quantity");
                        BigDecimal quantity = (BigDecimal) method.invoke(rm);
                        return quantity.multiply(orderQuantity);
                    } catch (Exception e) {
                        return BigDecimal.ZERO;
                    }
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .stripTrailingZeros().toPlainString();
    }

    private List<Entity> assignedEmployees(UUID task) {
        return db.select(USERS.ID, USERS.NAME).from(USERS).where(USERS.ID.in(
                select(MANUFACTURINGTASK_EMPLOYEE.USER_ID).from(MANUFACTURINGTASK_EMPLOYEE).where(MANUFACTURINGTASK_EMPLOYEE.MANUFACTURING_TASK_ID.eq(task))
        )).fetchInto(Entity.class);

    }

    private Optional<Entity> assignedWorkstation(UUID task) {
        return db.select(MANUFACTURING_WORKSTATION.ID, MANUFACTURING_WORKSTATION.NAME).from(MANUFACTURING_WORKSTATION).where(MANUFACTURING_WORKSTATION.ID.eq(
                select(MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_WORKSTATION_ID).from(MANUFACTURINGTASK_WORKSTATION).where(MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_TASK_ID.eq(task))
        )).fetchOptionalInto(Entity.class);
    }

    private List<ManufacturingTask> tasks(UUID orderId) {
        return db.selectFrom(MANUFACTURING_TASK)
                .where(MANUFACTURING_TASK.MANUFACTURING_ORDER_ID.eq(orderId))
                .orderBy(MANUFACTURING_TASK.CREATE_TIME)
                .fetchInto(ManufacturingTask.class);
    }

    private Map<String, String> product(UUID productId, UUID serviceId) {
        if (productId != null) {
            var product = db.selectFrom(MATERIAL_GOOD).where(MATERIAL_GOOD.ID.eq(productId)).fetchSingleInto(MaterialGood.class);
            return Map.of("name", product.name,
                    "code", product.code,
                    "measurementUnit", product.details.measurementUnit().name());
        }
        if (serviceId != null) {
            var serviceTemplate = db.selectFrom(SERVICE_TEMPLATE).where(SERVICE_TEMPLATE.ID.eq(serviceId)).fetchSingleInto(ServiceTemplate.class);
            return Map.of("name", serviceTemplate.name(),
                    "code", "",
                    "measurementUnit", MeasurementUnit.PIECE.name());
        }
        throw new RuntimeException("Both product and service are null");
    }

    private MaterialIssueNote materialIssueNote(UUID orderId) {
        return db.selectFrom(MATERIAL_ISSUE_NOTE).where(MATERIAL_ISSUE_NOTE.MANUFACTURING_ORDER_ID.eq(orderId)).fetchSingleInto(MaterialIssueNote.class);
    }


    private Account account(UUID accountId) {
        return db.selectFrom(ACCOUNT).where(ACCOUNT.ID.eq(accountId)).fetchSingleInto(Account.class);
    }

}
