package fabriqon.app.http.controllers.manufacturing;

import fabriqon.app.business.goods.*;
import fabriqon.app.business.inventory.InventoryService;
import fabriqon.app.business.manufacturing.ManufacturingOperation;
import fabriqon.app.common.model.Money;
import fabriqon.app.http.response.Entity;
import fabriqon.app.http.response.StringEntity;
import org.jooq.DSLContext;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.*;
import static org.jooq.impl.DSL.select;

@Component
public class ManufacturingOperations {

    private final DSLContext db;
    private final MaterialCosts materialCosts;
    private final InventoryService inventoryService;

    public ManufacturingOperations(DSLContext db, MaterialCosts materialCosts, InventoryService inventoryService) {
        this.db = db;
        this.materialCosts = materialCosts;
        this.inventoryService = inventoryService;
    }

    public ManufacturingOperationDetails manufacturingOperationDetails(UUID ownerId, BigDecimal unitOfProduction, ManufacturingOperation manufacturingOperation) {
        var defaultInventoryUnitId = inventoryService.defaultInventoryUnitId(ownerId);
        return new ManufacturingOperationDetails(
                manufacturingOperation.operationTemplateId(),
                Optional.ofNullable(manufacturingOperation.candidateWorkstationIds()).orElse(List.of()).stream().map(this::workstation).toList(),
                Optional.ofNullable(manufacturingOperation.candidateEmployeeIds()).orElse(List.of()).stream().map(this::employee).toList(),
                manufacturingOperation.name(),
                new BigDecimal(manufacturingOperation.durationInMinutes()).multiply(unitOfProduction).intValue(),
                manufacturingOperation.parallelizable(),
                manufacturingOperation.costPerHour(),
                Optional.ofNullable(manufacturingOperation.materials()).orElse(List.of())
                        .stream()
                        .map(material -> requiredMaterialDetails(ownerId, unitOfProduction, material, defaultInventoryUnitId)
                        )
                        .toList()
        );
    }

    public RequiredMaterialDetails requiredMaterialDetails(UUID ownerId, BigDecimal unitOfProduction, RequiredMaterial material, UUID defaultInventoryUnitId) {
        return new RequiredMaterialDetails(
                material.materialIds().stream().map(materialOptionId -> {
                            var m = materialGood(materialOptionId);
                            return new MaterialOptionDetails(
                                    materialOptionId,
                                    m.name,
                                    m.code,
                                    new StringEntity(m.details.measurementUnit().name(), m.details.measurementUnit().symbol()),
                                    materialCosts.materialCost(ownerId, materialOptionId).multiply(material.quantity()),
                                    material.quantity()
                                            .compareTo(inventoryService.getCurrentStock(ownerId, m.id, defaultInventoryUnitId)
                                                    .subtract(inventoryService.getReservedStock(ownerId, m.id, defaultInventoryUnitId))) <= 0,
                                    category(materialOptionId),
                                    m.details.produced(),
                                    m.details.material(),
                                    inventoryService.lastOrderedFrom(ownerId, materialOptionId)
                            );
                        })
                        .toList(),
                material.quantity().multiply(unitOfProduction).setScale(2, RoundingMode.HALF_UP),
                category(material.materialIds().getFirst()),
                material.optional(),
                material.configurableWithOptions(),
                material.replaceableWithOptions(),
                material.wastePercentage(),
                material.dimensions(),
                material.totalDimensions()
        );
    }

    private Entity workstation(UUID id) {
        return db.select(MANUFACTURING_WORKSTATION.ID, MANUFACTURING_WORKSTATION.NAME)
                .from(MANUFACTURING_WORKSTATION)
                .where(MANUFACTURING_WORKSTATION.ID.eq(id))
                .fetchSingleInto(Entity.class);
    }

    private Entity employee(UUID id) {
        return db.select(USERS.ID, USERS.NAME)
                .from(USERS)
                .where(USERS.ID.eq(id))
                .fetchSingleInto(Entity.class);
    }

    private MaterialGood materialGood(UUID materialId) {
        return db.selectFrom(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(materialId))
                .fetchSingleInto(MaterialGood.class);
    }

    private Entity category(UUID materialId) {
        var category = db.select()
                .from(CATEGORY)
                .where(CATEGORY.ID.eq(select(MATERIAL_GOOD.CATEGORY_ID).from(MATERIAL_GOOD).where(MATERIAL_GOOD.ID.eq(materialId))))
                .fetchSingleInto(Category.class);
        return new Entity(category.id(), category.details().name());
    }

    public record ManufacturingOperationDetails(
            UUID operationTemplateId,
            List<Entity> candidateWorkstations,
            List<Entity> candidateEmployees,
            String name,
            Integer durationInMinutes,
            Boolean parallelizable,
            Money costPerHour,
            List<RequiredMaterialDetails> materials
    ) {

        public ManufacturingOperationDetails setDurationInMinutes(Integer durationInMinutes) {
            return new ManufacturingOperationDetails(operationTemplateId, candidateWorkstations, candidateEmployees, name, durationInMinutes, parallelizable, costPerHour, materials);
        }

    }

    public record RequiredMaterialDetails(
            List<MaterialOptionDetails> options,
            BigDecimal quantity,
            Entity category,
            boolean optional,
            boolean configurableWithOptions,
            boolean replaceableWithOptions,
            BigDecimal wastePercentage,
            Dimensions requiredDimensions,
            Dimensions totalRequiredDimensions
    ) {
    }

    public record MaterialOptionDetails(
            UUID id,
            String name,
            String code,
            StringEntity measurementUnit,
            Money cost,
            boolean available,
            Entity category,
            boolean produced,
            MaterialGood.Material material,
            Entity lastOrderedFrom
    ) {
    }

}
