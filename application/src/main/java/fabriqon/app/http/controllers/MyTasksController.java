package fabriqon.app.http.controllers;

import fabriqon.app.business.manufacturing.ManufacturingTask;
import fabriqon.app.business.services.ServicingOrder;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.response.Entity;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

import static fabriqon.jooq.classes.Tables.*;

@RestController
@RequestMapping(path = "/users/me/tasks")
public class MyTasksController {

    final DSLContext db;
    private final AccountContext accountContext;

    @Autowired
    public MyTasksController(DSLContext db, AccountContext accountContext) {
        this.db = db;
        this.accountContext = accountContext;
    }

    @GetMapping(path = "list")
    public List<MyTask> list(@AuthenticationPrincipal Jwt principal) {
        var moTasks = db.select(MANUFACTURING_TASK.asterisk())
                .from(MANUFACTURING_TASK)
                .join(MANUFACTURINGTASK_EMPLOYEE).on(MANUFACTURING_TASK.ID.eq(MANUFACTURINGTASK_EMPLOYEE.MANUFACTURING_TASK_ID))
                .where(MANUFACTURING_TASK.OWNER_ID.eq(accountContext.accountId(principal)),
                        MANUFACTURINGTASK_EMPLOYEE.USER_ID.eq(accountContext.userId(principal)),
                        MANUFACTURING_TASK.START_TIME.lt(LocalDateTime.now().plusDays(30)),
                        MANUFACTURING_TASK.STATUS.ne(ManufacturingTask.Status.DONE.name()))
                .orderBy(MANUFACTURING_TASK.START_TIME)
                .limit(10)
                .fetchInto(ManufacturingTask.class)
                .stream()
                .map(t -> new MyTask(
                        t.id(),
                        t.manufacturingOrderId(),
                        MyTask.Type.MANUFACTURING_TASK,
                        t.details().name(),
                        t.details().number(),
                        target(t.manufacturingOrderId()),
                        t.startTime(),
                        t.status().name(), t.statusReason(),
                        t.durationInMinutes()
                        ));
        var serviceMos = db.selectFrom(SERVICING_ORDER)
                .where(SERVICING_ORDER.OWNER_ID.eq(accountContext.accountId(principal)), SERVICING_ORDER.ASSIGNED_TO.eq(accountContext.userId(principal)),
                        SERVICING_ORDER.STATUS.ne(ServicingOrder.Status.CLOSED.name()))
                .fetchInto(ServicingOrder.class).stream()
                .map(o -> new MyTask(
                        o.id(),
                        null,
                        MyTask.Type.SERVICE,
                        null,
                        o.number(),
                        target(o.id()),
                        null, o.status().name(), null, 0
                ));
        return Stream.concat(moTasks, serviceMos).toList();
    }

    private Entity target(UUID orderId) {
        return db.select(MATERIAL_GOOD.ID, MATERIAL_GOOD.NAME)
                .from(MATERIAL_GOOD)
                .join(MANUFACTURING_ORDER).on(MANUFACTURING_ORDER.PRODUCT_ID.eq(MATERIAL_GOOD.ID))
                .where(MANUFACTURING_ORDER.ID.eq(orderId))
                .fetchOptionalInto(Entity.class)
                .orElseGet(() -> db.select(SERVICE_TEMPLATE.ID, SERVICE_TEMPLATE.NAME)
                        .from(SERVICE_TEMPLATE)
                        .join(SERVICING_ORDER).on(SERVICING_ORDER.SERVICE_ID.eq(SERVICE_TEMPLATE.ID))
                        .where(SERVICING_ORDER.ID.eq(orderId))
                        .fetchSingleInto(Entity.class)
                );
    }


    public record MyTask(
            UUID id,
            UUID parentId,
            Type type,
            String name,
            String number,
            Entity target,
            LocalDateTime startTime,
            String status,
            ManufacturingTask.StatusReason statusReason,
            int durationInMinutes
    ) {
        public enum Type {MANUFACTURING_TASK, SERVICE}
    }

}
