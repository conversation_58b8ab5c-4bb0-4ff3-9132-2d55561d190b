package fabriqon.app.http.controllers.integrations;

import fabriqon.app.business.financial.VatCalculatorProvider;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.common.model.Address;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.http.controllers.InvoicesController;
import fabriqon.app.http.response.Company;
import fabriqon.templates.Templates;
import org.jooq.DSLContext;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

import static fabriqon.jooq.classes.Tables.MATERIAL_GOOD;
import static java.util.Collections.emptyMap;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

public class SagaExport {

    private final DSLContext db;
    private final VatCalculatorProvider vatCalculatorProvider;
    private final Templates templates;

    public SagaExport(DSLContext db, VatCalculatorProvider vatCalculatorProvider, Templates templates) {
        this.db = db;
        this.vatCalculatorProvider = vatCalculatorProvider;
        this.templates = templates;
    }


    public String transformToSaga(InvoicesController.InvoiceDetails invoiceDetails, Company account) {
        var dateFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy");
        var vatCalculator = vatCalculatorProvider.with(invoiceDetails.supplier().id, invoiceDetails.customer().id);
        var itemIndexer = new AtomicInteger();
        var currency = invoiceDetails.total().currency();
        return templates.render("exports/factura_saga.xml", Map.of(
                "account", account,
                "customer", Map.of(
                        "name", invoiceDetails.customer().name,
                        "taxIdentificationNumber", invoiceDetails.customer().taxIdentificationNumber,
                        "address", invoiceDetails.customer().addresses.stream().filter(address -> address.types().contains(Address.Type.BILLING)).findFirst().orElseThrow(),
                        "phone", "",//TODO
                        "email", invoiceDetails.customer().email != null ? invoiceDetails.customer().email : "",
                        "bankAccount", isNotEmpty(invoiceDetails.customer().bankAccounts) ? invoiceDetails.customer().bankAccounts.get(0) : emptyMap()
                ),
                "invoice", Map.of(
                        "number", invoiceDetails.number(),
                        "date", dateFormatter.format(invoiceDetails.creationDate()),
                        "dueDate", dateFormatter.format(invoiceDetails.dueDate()),
                        "currency", currency.getCurrencyCode(),
                        "subTotal", BigDecimal.valueOf(invoiceDetails.subTotal().amount()).movePointLeft(currency.getDefaultFractionDigits()),
                        "vat", BigDecimal.valueOf(invoiceDetails.vat().amount()).movePointLeft(currency.getDefaultFractionDigits()),
                        "total", BigDecimal.valueOf(invoiceDetails.total().amount()).movePointLeft(currency.getDefaultFractionDigits()),
                        "items", invoiceDetails.items().stream().map(item -> {
                            var unitPrice = BigDecimal.valueOf(item.unitPrice().amount()).movePointLeft(currency.getDefaultFractionDigits());
                            var subTotal = item.quantity().multiply(unitPrice).longValue();
                            var discountAmount = item.discount() != null
                                    ? item.discount().multiply(new BigDecimal(subTotal)).longValue()
                                    : 0;
                            return Map.of(
                                    "index", itemIndexer.incrementAndGet(),
                                    "name", item.name(),
                                    "measurementUnit", sagaUnit(measurementUnit(item.productId())),
                                    "quantity", item.quantity(),
                                    "unitPrice", unitPrice,
                                    "amount", subTotal - discountAmount,
                                    "vatPercentage", item.vatRate().multiply(BigDecimal.valueOf(100)).longValue(),
                                    "vatAmount", vatCalculator.calculate(subTotal - discountAmount, item.vatRate())
                            );
                        }).toList()
                )
        ));
    }

    private String sagaUnit(MeasurementUnit measurementUnit) {
        return switch (measurementUnit) {
            case PIECE -> "BUC";
            case KG -> "KG";
            case LITER -> "LITRI";
            case M -> "M";
            case G -> "GRAME";
            //todo map the other values
            default -> "BUC";
        };
    }

    private MeasurementUnit measurementUnit(UUID productId) {
        if (productId == null) {
            //this is a service so the unit is always PCS
            return MeasurementUnit.PIECE;
        }
        return db.select(MATERIAL_GOOD.DETAILS)
                .from(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(productId))
                .fetchSingleInto(MaterialGood.Details.class)
                .measurementUnit();
    }

}
