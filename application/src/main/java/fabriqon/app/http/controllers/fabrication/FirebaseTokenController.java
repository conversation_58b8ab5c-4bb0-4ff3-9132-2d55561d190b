package fabriqon.app.http.controllers.fabrication;

import fabriqon.app.common.model.DevicePairingTokenType;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

import static fabriqon.jooq.classes.Tables.*;

@RestController
@RequestMapping(path = "/fabrication/firebase")
public class FirebaseTokenController {

    private final DSLContext db;

    @Autowired
    public FirebaseTokenController(DSLContext db) {
        this.db = db;
    }

    @PostMapping("register-token/{token}")
    public void registerToken(
            @RequestHeader("X-Auth") String xAuthHeader,
            @PathVariable("token") String token) {
        var employeeId = employeeIdFromAuth(xAuthHeader);
        db.insertInto(FIREBASE_REGISTRATION_TOKEN)
                .set(FIREBASE_REGISTRATION_TOKEN.ID, UUID.randomUUID())
                .set(FIREBASE_REGISTRATION_TOKEN.OWNER_ID, ownerId(employeeId))
                .set(FIREBASE_REGISTRATION_TOKEN.TOKEN, token)
                .set(FIREBASE_REGISTRATION_TOKEN.EMPLOYEE_ID, employeeId)
                .onConflict(FIREBASE_REGISTRATION_TOKEN.EMPLOYEE_ID).doUpdate().set(FIREBASE_REGISTRATION_TOKEN.TOKEN, token)
                .execute();
    }

    private UUID ownerId(UUID employeeId) {
        return db.select(USERS.OWNER_ID)
                .from(USERS)
                .where(USERS.ID.eq(employeeId))
                .fetchSingleInto(UUID.class);
    }

    private UUID employeeIdFromAuth(String authHeader) {
        return db.select(DEVICE_PAIRING_TOKEN.USER_ID)
                .from(DEVICE_PAIRING_TOKEN)
                .where(DEVICE_PAIRING_TOKEN.TOKEN.eq(authHeader), DEVICE_PAIRING_TOKEN.TOKEN_TYPE.eq(DevicePairingTokenType.ACCESS.name()))
                .fetchSingleInto(UUID.class);
    }

}
