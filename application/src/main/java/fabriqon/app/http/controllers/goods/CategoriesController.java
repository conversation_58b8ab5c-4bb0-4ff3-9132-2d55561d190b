package fabriqon.app.http.controllers.goods;

import fabriqon.app.business.categories.CategoryService;
import fabriqon.app.business.goods.Category;
import fabriqon.app.config.security.AccountContext;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.CATEGORY;

@RestController
@RequestMapping(path = "/categories")
public class CategoriesController {
    private final AccountContext accountContext;
    private final CategoryService categoryService;
    private final DSLContext db;

    @Autowired
    public CategoriesController(final AccountContext accountContext,
                                final CategoryService categoryService,
                                final DSLContext db) {
        this.accountContext = accountContext;
        this.categoryService = categoryService;
        this.db = db;
    }

    @PostMapping(path = "create")
    public Category create(@AuthenticationPrincipal Jwt principal, @Validated @RequestBody CreateCategory create) {
        return categoryService.create(accountContext.accountId(principal), create.name());
    }

    @GetMapping(path = "list")
    public List<Category> list(@AuthenticationPrincipal Jwt principal) {
        return db.select()
                .from(CATEGORY)
                .where(CATEGORY.OWNER_ID.eq(accountContext.accountId(principal)), CATEGORY.DELETED.isFalse())
                .fetchInto(Category.class);
    }

    @PostMapping(path = "{id}/update")
    public void update(@AuthenticationPrincipal Jwt principal,
                       @PathVariable("id") UUID categoryId,
                       @Validated @RequestBody CreateCategory create) {
        categoryService.update(categoryId, accountContext.accountId(principal), create.name);
    }

    @DeleteMapping(path = "{id}/delete")
    public void delete(@AuthenticationPrincipal Jwt principal,
                       @PathVariable("id") UUID categoryId) {
        categoryService.delete(categoryId, accountContext.accountId(principal));
    }

    public record CreateCategory(
            String name
    ) {
    }
}