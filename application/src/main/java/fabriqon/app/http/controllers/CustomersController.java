package fabriqon.app.http.controllers;

import com.fasterxml.jackson.core.type.TypeReference;
import fabriqon.app.business.company.search.CompanySearchResult;
import fabriqon.app.business.company.search.CompanySearchService;
import fabriqon.app.business.customers.CustomerService;
import fabriqon.app.business.customers.Note;
import fabriqon.app.business.sales.SalesOrder;
import fabriqon.app.common.model.*;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.response.Company;
import fabriqon.app.http.response.Entity;
import fabriqon.misc.Json;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.UUID;

import static fabriqon.jooq.JooqTextSearchFunctions.textSearch;
import static fabriqon.jooq.classes.Tables.*;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.jooq.impl.DSL.select;

@RestController
@RequestMapping(path = "/customers")
public class CustomersController {

    private final AccountContext accountContext;
    private final CustomerService customerService;
    private final CompanySearchService companySearchService;
    private final DSLContext db;

    @Autowired
    public CustomersController(final AccountContext accountContext,
                               final CustomerService customerService,
                               final CompanySearchService companySearchService,
                               final DSLContext db) {
        this.accountContext = accountContext;
        this.customerService = customerService;
        this.companySearchService = companySearchService;
        this.db = db;
    }

    @PostMapping(path = "create")
    public Customer create(@AuthenticationPrincipal Jwt principal, @Validated @RequestBody CustomerDefinition create) {
        return details(customerService.create(accountContext.accountId(principal), create.type(), create.name(),
                new fabriqon.app.common.model.Company.Details(create.taxIdentificationNumber, create.taxIdentificationNumber, create.addresses, create.contacts, create.bankAccounts, create.notes)));
    }

    @GetMapping(path = "list")
    public List<Customer> list(@AuthenticationPrincipal Jwt principal,
                               @RequestParam(value = "q", required = false) String textSearch) {
        var filter = COMPANY.DELETED.isFalse();
        if (isNotBlank(textSearch)) {
            filter = filter.and(textSearch(COMPANY.TEXT_SEARCH, textSearch));
        }
        return db.select().from(COMPANY)
                .join(ACCOUNT_CUSTOMER).on(COMPANY.ID.eq(ACCOUNT_CUSTOMER.CUSTOMER_ID).and(ACCOUNT_CUSTOMER.OWNER_ID.eq(accountContext.accountId(principal))))
                .where(filter)
                .fetchInto(fabriqon.app.common.model.Company.class)
                .stream().map(this::details).toList();
    }

    @GetMapping(path = "{id}/details")
    public Customer details(@AuthenticationPrincipal Jwt principal,
                            @PathVariable("id") UUID id) {
        return details(db.select().from(COMPANY)
                .join(ACCOUNT_CUSTOMER).on(COMPANY.ID.eq(ACCOUNT_CUSTOMER.CUSTOMER_ID).and(ACCOUNT_CUSTOMER.OWNER_ID.eq(accountContext.accountId(principal))))
                .where(COMPANY.ID.eq(id))
                .fetchSingleInto(fabriqon.app.common.model.Company.class));
    }

    @PostMapping(path = "{id}/update")
    public void update(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable("id") UUID id,
            @Validated @RequestBody CustomerDefinition update) {
        customerService.update(accountContext.accountId(principal), id, update.type(), update.name(),
                new fabriqon.app.common.model.Company.Details(update.identificationNumber, update.taxIdentificationNumber,
                        update.addresses, update.contacts, update.bankAccounts, update.notes));
    }

    @DeleteMapping(path = "{id}/delete")
    public void delete(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable("id") UUID id) {
        db.update(COMPANY)
                .set(COMPANY.DELETED, true)
                .where(COMPANY.ID.eq(id), COMPANY.OWNER_ID.eq(accountContext.accountId(principal)))
                .execute();
    }

    @PostMapping(path = "{id}/notes/add")
    public void addNote(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable("id") UUID id,
            @Validated @RequestBody NoteDefinition note) {
        customerService.addNote(accountContext.accountId(principal), id, accountContext.userId(principal), note.note());
    }

    @GetMapping(path = "{id}/notes/list")
    public List<NoteDetails> listNotes(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable("id") UUID id) {
        return db.select(NOTE.ID, NOTE.CREATE_TIME, NOTE.UPDATE_TIME, NOTE.DELETED, NOTE.OWNER_ID, NOTE.ADDED_BY_ID, NOTE.NOTE_)
                .from(NOTE)
                .join(CUSTOMER_NOTE).on(CUSTOMER_NOTE.NOTE_ID.eq(NOTE.ID))
                .where(
                        NOTE.OWNER_ID.eq(accountContext.accountId(principal)),
                        CUSTOMER_NOTE.COMPANY_ID.eq(id)
                )
                .orderBy(NOTE.CREATE_TIME.desc())
                .fetchInto(Note.class)
                .stream()
                .map(note -> new NoteDetails(note.id(), note.createTime(),
                        new Entity(note.addedById(), db.select(USERS.NAME).from(USERS).where(USERS.ID.eq(note.addedById())).fetchSingleInto(String.class)),
                        note.note()))
                .toList();
    }

    @GetMapping(path = "/search/{query}")
    public List<CompanySearchResult> search(@AuthenticationPrincipal Jwt principal,
                                            @PathVariable("query") String query) {
        return companySearchService.search(query);
    }

    @PostMapping(path = "{id}/attach-file", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public Customer attachFile(@AuthenticationPrincipal Jwt principal,
                                                              @PathVariable("id") UUID customerId,
                                                              @RequestParam("file") MultipartFile file) throws IOException {
        return details(customerService.attach(accountContext.accountId(principal), customerId,
                file.getOriginalFilename(), file.getBytes()));
    }

    @DeleteMapping(path = "{id}/files/{fileId}/delete")
    public Customer deleteFile(@AuthenticationPrincipal Jwt principal,
                                                              @PathVariable("id") UUID orderId,
                                                              @PathVariable("fileId") UUID fileId) throws IOException {
        return details(customerService.deleteFile(accountContext.accountId(principal), orderId, fileId));
    }

    private Customer details(fabriqon.app.common.model.Company company) {
        return new Customer(
                company.id(),
                company.type(),
                company.name(),
                null,
                company.details().identificationNumber(),
                company.details().taxIdentificationNumber(),
                company.details().addresses(),
                company.details().contacts(),
                company.details().bankAccounts(),
                company.details().notes(),
                files(company.ownerId(), company.id()),
                lifetimeValue(company.id())
        );
    }

    private Money lifetimeValue(UUID id) {
        var orderItems = db.select(SALES_ORDER.ITEMS)
                .from(SALES_ORDER)
                .where(SALES_ORDER.CUSTOMER_ID.eq(id))
                .fetchInto(String.class)
                .stream()
                .map(s -> Json.read(s, new TypeReference<List<SalesOrder.Item>>() {
                }))
                .flatMap(Collection::stream)
                .toList();
        return orderItems.stream()
                .map(i -> {
                    var subTotal = i.price().multiply(i.quantity());
                    var discount = subTotal.multiply(i.discount() != null ? i.discount() : BigDecimal.ZERO);
                    return subTotal.subtract(discount);
                })
                .reduce(Money::add)
                .orElse(new Money(0, Money.DEFAULT_CURRENCY));
    }

    private List<File> files(UUID ownerId, UUID companyId) {
        return db.select(FILE.ID, FILE.FILE_NAME)
                .from(FILE)
                .where(FILE.ID.in(select(COMPANY_FILE.FILE_ID).from(COMPANY_FILE).where(COMPANY_FILE.OWNER_ID.eq(ownerId), COMPANY_FILE.COMPANY_ID.eq(companyId))))
                .fetch()
                .stream()
                .map(f -> new File(f.value1(), f.value2(),
                        FilesController.previewLink(f.value1()), FilesController.downloadLink(f.value1())))
                .toList();
    }

    public record CustomerDefinition(
            fabriqon.app.common.model.Company.Type type,
            String name,
            String identificationNumber,
            String taxIdentificationNumber,
            List<Address> addresses,
            List<ContactPerson> contacts,
            List<BankAccount> bankAccounts,
            String notes
    ) {
    }

    public class Customer extends Company {
        public final Money lifetimeValue;

        public Customer(UUID id, fabriqon.app.common.model.Company.Type type, String name, String email, String identificationNumber, String taxIdentificationNumber, List<Address> addresses, List<ContactPerson> contacts, List<BankAccount> bankAccounts, String notes, List<File> files, Money lifetimeValue) {
            super(id, type, name, email, identificationNumber, taxIdentificationNumber, addresses, contacts, bankAccounts, notes, files);
            this.lifetimeValue = lifetimeValue;
        }
    }

}
