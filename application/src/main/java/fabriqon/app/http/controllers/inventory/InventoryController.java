package fabriqon.app.http.controllers.inventory;

import fabriqon.app.business.goods.Category;
import fabriqon.app.business.goods.GoodsService;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.inventory.InventoryAdjustmentOrder;
import fabriqon.app.business.inventory.InventoryEntry;
import fabriqon.app.business.inventory.InventoryService;
import fabriqon.app.business.manufacturing.ManufacturingOrder;
import fabriqon.app.business.manufacturing.ManufacturingService;
import fabriqon.app.business.purchases.PurchaseOrder;
import fabriqon.app.business.sales.SalesOrder;
import fabriqon.app.business.services.ServicingOrder;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.common.model.Money;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.response.Entity;
import fabriqon.app.http.response.StringEntity;
import fabriqon.jooq.classes.tables.records.InventoryItemsViewRecord;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Stream;

import static fabriqon.app.http.controllers.HttpApiConstants.*;
import static fabriqon.jooq.JooqJsonbFunctions.arrayContainsJson;
import static fabriqon.jooq.JooqTextSearchFunctions.textSearch;
import static fabriqon.jooq.classes.Tables.*;
import static java.lang.String.format;
import static org.apache.commons.lang3.BooleanUtils.isTrue;
import static org.apache.commons.lang3.ObjectUtils.isEmpty;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.jooq.impl.DSL.*;

@RestController
@RequestMapping(path = "/inventory")
public class InventoryController {

    private final AccountContext accountContext;
    private final DSLContext db;
    private final InventoryService inventoryService;
    private final ManufacturingService manufacturingService;
    private final GoodsService productsService;

    @Autowired
    public InventoryController(AccountContext accountContext,
                               DSLContext db,
                               InventoryService inventoryService,
                               ManufacturingService manufacturingService,
                               GoodsService productsService) {
        this.accountContext = accountContext;
        this.db = db;
        this.inventoryService = inventoryService;
        this.manufacturingService = manufacturingService;
        this.productsService = productsService;
    }

    @GetMapping(path = "list")
    public List<Item> list(
            @AuthenticationPrincipal Jwt principal,
            @RequestParam(value = "q", required = false) String textSearch,
            @RequestParam(value = "inventoryUnitFilter", required = false) List<UUID> inventoryUnitFilter,
            @RequestParam(value = "categoryIds", required = false) List<UUID> categoryIds,
            @RequestParam(value = "ids", required = false) List<UUID> ids,
            @RequestParam(value = "onlyOnStockItems", required = false) Boolean onlyOnStockItems,
            @RequestParam(value = LIMIT_QUERY, defaultValue = LIMIT_DEFAULT) Integer limit,
            @RequestParam(value = OFFSET_QUERY, defaultValue = OFFSET_DEFAULT) Integer offset
    ) {
        final var account = accountContext.account(principal);
        final UUID accountId = account.id();
        final var defaultInventoryUnit = account.settings().general().inventoryAccountingSettings().unitDesignations().defaultInventoryUnit();
        var whereClause = INVENTORY_ITEMS_VIEW.OWNER_ID.eq(accountId);
        if (isNotBlank(textSearch)) {
            whereClause = whereClause.and(textSearch(INVENTORY_ITEMS_VIEW.TEXT_SEARCH, textSearch));
        }
        if (isNotEmpty(categoryIds)) {
            whereClause = whereClause.and(INVENTORY_ITEMS_VIEW.CATEGORY_ID.in(categoryIds));
        }
        if (isNotEmpty(inventoryUnitFilter)) {
            whereClause = whereClause.and(INVENTORY_ITEMS_VIEW.INVENTORY_UNIT_ID.in(inventoryUnitFilter));
        }
        if (isNotEmpty(ids)) {
            whereClause = whereClause.and(INVENTORY_ITEMS_VIEW.MATERIAL_GOOD_ID.in(ids));
        }
        if (isTrue(onlyOnStockItems)) {
            whereClause = whereClause.and(INVENTORY_ITEMS_VIEW.QUANTITY.gt(BigDecimal.ZERO));
        } else if (isEmpty(inventoryUnitFilter)) {
            whereClause = whereClause.and(INVENTORY_ITEMS_VIEW.INVENTORY_UNIT_ID.eq(defaultInventoryUnit)
                    .or(INVENTORY_ITEMS_VIEW.INVENTORY_UNIT_ID.ne(defaultInventoryUnit).and(INVENTORY_ITEMS_VIEW.QUANTITY.gt(BigDecimal.ZERO))));
        }
        return db.select(INVENTORY_ITEMS_VIEW.asterisk().except(INVENTORY_ITEMS_VIEW.TEXT_SEARCH))
                .from(INVENTORY_ITEMS_VIEW)
                .where(whereClause)
                .orderBy(INVENTORY_ITEMS_VIEW.NAME)
                .limit(limit).offset(offset)
                .fetchInto(InventoryItemsViewRecord.class)
                .stream()
                .map(this::mapItem)
                .toList();
    }

    @GetMapping(path = "stats")
    public Map<String, Object> stats(
            @AuthenticationPrincipal Jwt principal,
            @RequestParam(value = "q", required = false) String textSearch,
            @RequestParam(value = "inventoryUnitFilter", required = false) List<UUID> inventoryUnitFilter,
            @RequestParam(value = "categoryIds", required = false) List<UUID> categoryIds,
            @RequestParam(value = "onlyCriticalItems", required = false, defaultValue = "false") boolean onlyCritical
    ) {
        var filter = INVENTORY_ITEMS_VIEW.OWNER_ID.eq(accountContext.accountId(principal));
        if (isNotEmpty(inventoryUnitFilter)) {
            filter = filter.and(INVENTORY_ITEMS_VIEW.INVENTORY_UNIT_ID.in(inventoryUnitFilter));
        }
        if (isNotEmpty(categoryIds)) {
            filter = filter.and(INVENTORY_ITEMS_VIEW.CATEGORY_ID.in(categoryIds));
        }
        if (onlyCritical) {
            filter = filter.and(INVENTORY_ITEMS_VIEW.CRITICAL_ON_HAND.gt(BigDecimal.ZERO))
                    .and(INVENTORY_ITEMS_VIEW.QUANTITY.lt(INVENTORY_ITEMS_VIEW.CRITICAL_ON_HAND));
        }
        if (isNotBlank(textSearch)) {
            filter = filter.and(textSearch(INVENTORY_ITEMS_VIEW.TEXT_SEARCH, textSearch));
        }
        var accountCurrency = accountContext.account(principal).settings().general().defaultCurrency();
        var potentialRevenue = db.select(coalesce(sum(INVENTORY_ITEMS_VIEW.QUANTITY.multiply(INVENTORY_ITEMS_VIEW.SELL_PRICE_AMOUNT)), 0)).from(INVENTORY_ITEMS_VIEW).where(filter).fetchSingleInto(Long.class);
        return Map.of(
                "count", db.select(countDistinct(INVENTORY_ITEMS_VIEW.MATERIAL_GOOD_ID)).from(INVENTORY_ITEMS_VIEW).where(filter).fetchSingleInto(Integer.class),
                "value", new Money(db.select(coalesce(sum(INVENTORY_ITEMS_VIEW.TOTAL_VALUE), 0)).from(INVENTORY_ITEMS_VIEW).where(filter).fetchSingleInto(Long.class), accountCurrency),
                "potentialRevenue", new Money(potentialRevenue, accountCurrency),
                "potentialProfit", new Money(potentialRevenue - db.select(coalesce(sum(INVENTORY_ITEMS_VIEW.TOTAL_VALUE), 0)).from(INVENTORY_ITEMS_VIEW).where(filter.and(INVENTORY_ITEMS_VIEW.SELL_PRICE_AMOUNT.gt(0L))).fetchSingleInto(Long.class), accountCurrency)
        );
    }

    @GetMapping(path = "list/critical")
    public List<Item> critical(
            @AuthenticationPrincipal Jwt principal,
            @RequestParam(value = "q", required = false) String textSearch,
            @RequestParam(value = "inventoryUnitFilter", required = false) List<UUID> inventoryUnitFilter,
            @RequestParam(value = "categoryIds", required = false) List<UUID> categoryIds,
            @RequestParam(value = LIMIT_QUERY, defaultValue = LIMIT_DEFAULT) Integer limit,
            @RequestParam(value = OFFSET_QUERY, defaultValue = OFFSET_DEFAULT) Integer offset
    ) {
        final var account = accountContext.account(principal);
        final UUID accountId = account.id();
        final var defaultInventoryUnit = account.settings().general().inventoryAccountingSettings().unitDesignations().defaultInventoryUnit();
        var filter = INVENTORY_ITEMS_VIEW.OWNER_ID.eq(accountId)
                .and(INVENTORY_ITEMS_VIEW.CRITICAL_ON_HAND.gt(BigDecimal.ZERO))
                .and(INVENTORY_ITEMS_VIEW.QUANTITY.lt(INVENTORY_ITEMS_VIEW.CRITICAL_ON_HAND));
        if (isNotEmpty(inventoryUnitFilter)) {
            filter = filter.and(INVENTORY_ITEMS_VIEW.INVENTORY_UNIT_ID.in(inventoryUnitFilter));
        } else {
            filter = filter.and(INVENTORY_ITEMS_VIEW.INVENTORY_UNIT_ID.eq(defaultInventoryUnit));
        }
        if (isNotEmpty(categoryIds)) {
            filter = filter.and(INVENTORY_ITEMS_VIEW.CATEGORY_ID.in(categoryIds));
        }
        if (isNotBlank(textSearch)) {
            filter = filter.and(textSearch(INVENTORY_ITEMS_VIEW.TEXT_SEARCH, textSearch));
        }
        return db.select(INVENTORY_ITEMS_VIEW.asterisk().except(INVENTORY_ITEMS_VIEW.TEXT_SEARCH))
                .from(INVENTORY_ITEMS_VIEW)
                .where(filter)
                .orderBy(INVENTORY_ITEMS_VIEW.NAME)
                .limit(limit).offset(offset)
                .fetchInto(InventoryItemsViewRecord.class)
                .stream()
                .map(this::mapItem)
                .toList();
    }

    private Item mapItem(InventoryItemsViewRecord item) {
        var costPerItem = new Money(item.getCost(), item.getSellPriceCurrency());
        if (costPerItem.amount() == 0 && isTrue(item.getProduced())) {
            costPerItem = productsService.laborCosts(item.getOwnerId(), item.getMaterialGoodId())
                    .add(productsService.estimatedMaterialCosts(item.getOwnerId(), item.getMaterialGoodId()));
        }
        var currentStock = item.getQuantity();
        var committedStock = item.getCommittedQuantity();
        var incoming = item.getIncomingQuantity();
        var measurementUnit = MeasurementUnit.valueOf(item.getMeasurementUnit());
        return new Item(
                item.getMaterialGoodId(),
                item.getName(),
                item.getCode(),
                item.getDescription(),
                item.getSupplierId() != null ? new Entity(item.getSupplierId(), item.getSupplierName()) : null,
                item.getCategoryId() != null ? new Entity(item.getCategoryId(), item.getCategoryName()) : null,
                new Entity(item.getInventoryUnitId(), item.getInventoryUnitName()),
                new StringEntity(measurementUnit.name(), measurementUnit.symbol()),
                currentStock,
                committedStock,
                incoming,
                currentStock.subtract(committedStock).add(incoming),
                currentStock.subtract(committedStock),
                item.getCriticalOnHand(),
                item.getProduced(),
                item.getUnitOfProduction(),
                new Money(item.getTotalValue(), item.getCurrency() != null ? Currency.getInstance(item.getCurrency()) : item.getSellPriceCurrency()),
                costPerItem,
                new Money(item.getSellPriceAmount(), item.getSellPriceCurrency()),
                new Money(item.getSellPriceAmount() - costPerItem.amount(), item.getSellPriceCurrency()),
                costPerItem.amount() == 0 ?
                        BigDecimal.ZERO
                        : item.getSellPriceAmount() != 0
                        ? BigDecimal.valueOf(item.getSellPriceAmount() - costPerItem.amount()).setScale(2, RoundingMode.HALF_EVEN)
                        .divide(BigDecimal.valueOf(costPerItem.amount()), RoundingMode.HALF_EVEN)
                        : BigDecimal.ZERO
        );
    }

    @GetMapping(path = "{id}/history")
    public ItemHistory history(
            @AuthenticationPrincipal Jwt principal,
            @PathVariable("id") UUID id,
            @RequestParam(value = "start", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate start,
            @RequestParam(value = "end", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate end,
            @RequestParam(value = "stockHistoryEntryType", required = false) StockHistoryEntryType stockHistoryEntryType
    ) {
        var accountId = accountContext.accountId(principal);
        var materialGood = db.selectFrom(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.OWNER_ID.eq(accountId), MATERIAL_GOOD.ID.eq(id))
                .fetchSingleInto(MaterialGood.class);
        var currentStock = inventoryService.getCurrentStock(accountId, materialGood.id);
        var committed = getCommitted(accountId, materialGood.id);
        var committedStock = committed.stream().map(item -> item.quantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        var incoming = inventoryService.getIncomingStock(accountId, materialGood.id);
        return new ItemHistory(
                currentStock,
                committedStock,
                incoming,
                materialGood.details.criticalOnHand(),
                currentStock.subtract(committedStock).add(incoming),
                stockHistoryEntries(db.selectFrom(INVENTORY)
                                .where(INVENTORY.OWNER_ID.eq(accountContext.accountId(principal)),
                                        INVENTORY.MATERIAL_GOOD_ID.eq(id),
                                        INVENTORY.CREATE_TIME.lt(end != null ? end.plusDays(1).atStartOfDay() : LocalDateTime.now()))
                                .orderBy(INVENTORY.CREATE_TIME.asc())
                                .fetchInto(InventoryEntry.class),
                        start != null ? start : LocalDate.ofEpochDay(0), stockHistoryEntryType),
                committed.stream().map(record ->
                        new Committed(
                                record.salesOrder,
                                record.manufacturingOrder,
                                record.servicingOrder,
                                record.salesOrder != null ? customerForSalesOrder(record.salesOrder.id())
                                        : record.manufacturingOrder != null
                                        ? customerForManufacturingOrder(record.manufacturingOrder.id())
                                        : customerForServicingOrder(record.servicingOrder.id()),
                                record.quantity()
                        )).toList(),
                Stream.concat(
                                db.selectFrom(PURCHASE_ORDER)
                                        .where(PURCHASE_ORDER.DELETED.isFalse()
                                                .and(PURCHASE_ORDER.OWNER_ID.eq(accountId))
                                                .and(PURCHASE_ORDER.STATUS.in(PurchaseOrder.Status.SUBMITTED.name(), PurchaseOrder.Status.SENT_FOR_QUOTE.name(), PurchaseOrder.Status.SENT.name()))
                                                .and(arrayContainsJson(PURCHASE_ORDER.ITEMS, format("[{\"materialGoodId\": \"%s\"}]", id)))
                                        )
                                        .fetchInto(PurchaseOrder.class)
                                        .stream().map(order -> incomingDetails(id, order)),
                                db.selectFrom(MANUFACTURING_ORDER)
                                        .where(MANUFACTURING_ORDER.DELETED.isFalse(),
                                                MANUFACTURING_ORDER.PRODUCT_ID.eq(id),
                                                MANUFACTURING_ORDER.STATUS.in(ManufacturingOrder.Status.SUBMITTED.name(), ManufacturingOrder.Status.MANUFACTURING.name()),
                                                MANUFACTURING_ORDER.OWNER_ID.eq(accountId)
                                        )
                                        .fetchInto(ManufacturingOrder.class)
                                        .stream().map(this::incomingDetails))
                        .toList()
        );
    }

    private List<Committed> getCommitted(UUID ownerId, UUID materialId) {
        return Stream.concat(
                Stream.concat(
                        //sales order where the product is present
                        db.selectFrom(SALES_ORDER)
                                .where(SALES_ORDER.DELETED.isFalse()
                                        .and(SALES_ORDER.OWNER_ID.eq(ownerId))
                                        .and(SALES_ORDER.STATUS.in(SalesOrder.Status.SUBMITTED.name(), SalesOrder.Status.PROCESSING.name(), SalesOrder.Status.PICKING_PACKING.name(), SalesOrder.Status.READY_TO_SHIP.name()))
                                        .and(arrayContainsJson(SALES_ORDER.ITEMS, format("[{\"productId\": \"%s\"}]", materialId)))
                                )
                                .orderBy(SALES_ORDER.RANKING.asc())
                                .fetchInto(SalesOrder.class)
                                .stream()
                                .map(order -> committedDetails(order, materialId)),
                        //manufacturing orders where the material is used
                        db.selectDistinct(MANUFACTURING_ORDER.asterisk()).from(MANUFACTURING_ORDER,
                                        lateral(table("jsonb_array_elements(manufacturing_operations)").as("operations")),
                                        lateral(table("jsonb_array_elements(operations -> 'materials')").as("materials")))
                                .where(MANUFACTURING_ORDER.DELETED.isFalse()
                                        .and(MANUFACTURING_ORDER.STATUS.in(ManufacturingOrder.Status.SUBMITTED.name(), ManufacturingOrder.Status.MANUFACTURING.name(), ManufacturingOrder.Status.MANUFACTURED.name()))
                                        .and(MANUFACTURING_ORDER.OWNER_ID.eq(ownerId))
                                        .and(field("materials -> 'materialIds' ->> 0", String.class).eq(materialId.toString())))
                                .orderBy(MANUFACTURING_ORDER.RANKING.asc())
                                .fetchInto(ManufacturingOrder.class)
                                .stream().map(order -> committedDetails(order, materialId))),
                        //servicing orders where the material is used
                        db.select().from(SERVICING_ORDER, lateral(table("jsonb_array_elements(materials)").as("ms")))
                                .where(SERVICING_ORDER.DELETED.isFalse()
                                        .and(SERVICING_ORDER.STATUS.in(ServicingOrder.Status.SUBMITTED.name(), ServicingOrder.Status.IN_PROGRESS.name(), ServicingOrder.Status.EXECUTED.name()))
                                        .and(SERVICING_ORDER.OWNER_ID.eq(ownerId))
                                        .and(field("ms -> 'materialIds' ->> 0", String.class).eq(materialId.toString())))
                                .orderBy(SERVICING_ORDER.RANKING.asc())
                                .fetchInto(ServicingOrder.class)
                                .stream().map(order -> committedDetails(order, materialId))
                )
                .toList();
    }

    private Committed committedDetails(SalesOrder salesOrder, UUID productId) {
        return new Committed(new Entity(salesOrder.id(), salesOrder.number()), null, null, null,
                salesOrder.items().stream()
                        .filter(product -> productId.equals(product.productId()))
                        .map(SalesOrder.Item::quantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
    }

    private Committed committedDetails(ManufacturingOrder manufacturingOrder, UUID materialId) {
        return new Committed(null, new Entity(manufacturingOrder.id(), manufacturingOrder.number()), null, null,
                manufacturingService.requiredMaterials(manufacturingOrder).stream()
                        .filter(material -> material.materialIds().get(0).equals(materialId))
                        .map(material -> material.quantity().multiply(manufacturingOrder.quantity()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
    }

    private Committed committedDetails(ServicingOrder servicingOrder, UUID materialId) {
        return new Committed(null, null, new Entity(servicingOrder.id(), servicingOrder.number()), null,
                servicingOrder.materials().stream()
                        .filter(material -> material.materialIds().get(0).equals(materialId))
                        .map(material -> material.quantity().multiply(servicingOrder.quantity()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
    }


    private List<ItemHistory.StockHistoryEntry> stockHistoryEntries(List<InventoryEntry> entries,
                                                                    LocalDate from,
                                                                    StockHistoryEntryType stockHistoryEntryType) {
        AtomicReference<BigDecimal> currentStock = new AtomicReference<>(BigDecimal.ZERO);
        var processedEntries = new LinkedList<InventoryEntry>();
        var history = new LinkedList<>(entries.stream()
                .map(entry -> {
                    processedEntries.add(entry);
                    currentStock.set(currentStock.get().add(entry.quantity()));
                    return new ItemHistory.StockHistoryEntry(
                            entry.id(),
                            entry.purchaseDate() != null ? entry.purchaseDate().atZone(ZoneId.systemDefault()).toInstant() : entry.createTime(),
                            inventoryAdjustmentOrder(entry.inventoryAdjustmentOrderId()),
                            salesOrder(entry.salesOrderId()),
                            manufacturingOrder(entry.manufacturingOrderId()),
                            purchaseOrder(entry.receptionReceiptId()),
                            accompanyingDocument(entry),
                            entry.receptionPrice(),
                            entry.quantity(),
                            averageCostAfter(processedEntries),
                            currentStock.get()
                    );
                })
                .filter(entry -> entry.createTime().isAfter(from.atStartOfDay().toInstant(ZoneOffset.UTC)))
                .filter(entry -> {
                    if (StockHistoryEntryType.INCOMING.equals(stockHistoryEntryType)) {
                        return entry.quantity.compareTo(BigDecimal.ZERO) > 0;
                    } else if (StockHistoryEntryType.OUTGOING.equals(stockHistoryEntryType)) {
                        return entry.quantity.compareTo(BigDecimal.ZERO) < 0;
                    }
                    return true;
                })
                .toList());
        Collections.reverse(history);
        return history;
    }

    private Incoming incomingDetails(UUID materialGoodId, PurchaseOrder purchaseOrder) {
        var itemDetails = purchaseOrder.items().stream().filter(item -> item.materialGoodId().equals(materialGoodId))
                .findFirst().orElseThrow();
        return new Incoming(
                new Entity(purchaseOrder.id(), purchaseOrder.number()),
                null,
                db.select(COMPANY.ID, COMPANY.COMPANY_NAME)
                        .from(COMPANY)
                        .where(COMPANY.ID.eq(purchaseOrder.supplierId()))
                        .fetchSingleInto(Entity.class),
                itemDetails.quantity(),
                itemDetails.price(),
                purchaseOrder.expectedBy()
        );
    }

    private Incoming incomingDetails(ManufacturingOrder manufacturingOrder) {
        return new Incoming(
                null,
                new Entity(manufacturingOrder.id(), manufacturingOrder.number()),
                null,
                manufacturingOrder.quantity(),
                null,
                manufacturingOrder.productionDeadline() != null ? manufacturingOrder.productionDeadline().toLocalDate() : null
        );
    }

    private Entity customerForSalesOrder(UUID salesOrderId) {
        return db.select(COMPANY.ID, COMPANY.COMPANY_NAME)
                .from(COMPANY)
                .join(ACCOUNT_CUSTOMER).on(ACCOUNT_CUSTOMER.CUSTOMER_ID.eq(COMPANY.ID))
                .join(SALES_ORDER).on(SALES_ORDER.CUSTOMER_ID.eq(ACCOUNT_CUSTOMER.CUSTOMER_ID))
                .where(SALES_ORDER.ID.eq(salesOrderId))
                .fetchOneInto(Entity.class);
    }

    private Entity customerForManufacturingOrder(UUID manufacturingOrderId) {
        return db.select(COMPANY.ID, COMPANY.COMPANY_NAME)
                .from(COMPANY)
                .join(ACCOUNT_CUSTOMER).on(ACCOUNT_CUSTOMER.CUSTOMER_ID.eq(COMPANY.ID))
                .join(SALES_ORDER).on(SALES_ORDER.CUSTOMER_ID.eq(ACCOUNT_CUSTOMER.CUSTOMER_ID))
                .join(MANUFACTURING_ORDER).on(MANUFACTURING_ORDER.SALES_ORDER_ID.eq(SALES_ORDER.ID))
                .where(MANUFACTURING_ORDER.ID.eq(manufacturingOrderId))
                .fetchOneInto(Entity.class);
    }

    private Entity customerForServicingOrder(UUID servicingOrderId) {
        return db.select(COMPANY.ID, COMPANY.COMPANY_NAME)
                .from(COMPANY)
                .join(ACCOUNT_CUSTOMER).on(ACCOUNT_CUSTOMER.CUSTOMER_ID.eq(COMPANY.ID))
                .join(SALES_ORDER).on(SALES_ORDER.CUSTOMER_ID.eq(ACCOUNT_CUSTOMER.CUSTOMER_ID))
                .join(SERVICING_ORDER).on(SERVICING_ORDER.SALES_ORDER_ID.eq(SALES_ORDER.ID))
                .where(SERVICING_ORDER.ID.eq(servicingOrderId))
                .fetchOneInto(Entity.class);
    }

    //todo use a join instead of reading one-by-one
    private String categoryName(UUID categoryId) {
        return db.select(CATEGORY.DETAILS)
                .from(CATEGORY)
                .where(CATEGORY.ID.eq(categoryId))
                .fetchSingle()
                .into(Category.Details.class)
                .name();
    }


    private Entity inventoryAdjustmentOrder(UUID inventoryAdjustmentOrderId) {
        if (inventoryAdjustmentOrderId == null) return null;
        return db.select(INVENTORY_ADJUSTMENT_ORDER.ID, INVENTORY_ADJUSTMENT_ORDER.NUMBER)
                .from(INVENTORY_ADJUSTMENT_ORDER)
                .where(INVENTORY_ADJUSTMENT_ORDER.ID.eq(inventoryAdjustmentOrderId))
                .fetchSingleInto(Entity.class);
    }

    private Entity salesOrder(UUID salesOrderId) {
        if (salesOrderId == null) return null;
        return db.select(SALES_ORDER.ID, SALES_ORDER.NUMBER)
                .from(SALES_ORDER)
                .where(SALES_ORDER.ID.eq(salesOrderId))
                .fetchSingleInto(Entity.class);
    }


    private Entity manufacturingOrder(UUID manufacturingOrderId) {
        if (manufacturingOrderId == null) return null;
        return db.select(MANUFACTURING_ORDER.ID, MANUFACTURING_ORDER.NUMBER)
                .from(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.ID.eq(manufacturingOrderId))
                .fetchSingleInto(Entity.class);
    }

    private Entity purchaseOrder(UUID receptionReceiptId) {
        if (receptionReceiptId == null) return null;
        return db.select(PURCHASE_ORDER.ID, PURCHASE_ORDER.NUMBER)
                .from(PURCHASE_ORDER)
                .where(PURCHASE_ORDER.ID.eq(
                        select(RECEPTION_RECEIPT.PURCHASE_ORDER_ID)
                                .from(RECEPTION_RECEIPT)
                                .where(RECEPTION_RECEIPT.ID.eq(receptionReceiptId))))
                .fetchOneInto(Entity.class);
    }

    private Document accompanyingDocument(InventoryEntry entry) {
        if (entry.receptionReceiptId() != null) {
            return db.select(RECEPTION_RECEIPT.ID, RECEPTION_RECEIPT.NUMBER)
                    .from(RECEPTION_RECEIPT)
                    .where(RECEPTION_RECEIPT.OWNER_ID.eq(entry.ownerId()), RECEPTION_RECEIPT.ID.eq(entry.receptionReceiptId()))
                    .fetchOptional()
                    .map(r -> new Document(r.value1(), r.value2(), Document.Type.RECEPTION_RECEIPT))
                    .orElse(null);
        } else if (entry.manufacturingOrderId() != null) {
            return db.select(MATERIAL_ISSUE_NOTE.ID, MATERIAL_ISSUE_NOTE.NUMBER)
                    .from(MATERIAL_ISSUE_NOTE)
                    .where(MATERIAL_ISSUE_NOTE.OWNER_ID.eq(entry.ownerId()), MATERIAL_ISSUE_NOTE.MANUFACTURING_ORDER_ID.eq(entry.manufacturingOrderId()))
                    .fetchOptional()
                    .map(r -> new Document(r.value1(), r.value2(), Document.Type.MATERIAL_ISSUE_NOTE))
                    .orElse(null);
        } else if (entry.inventoryAdjustmentOrderId() != null) {
            return db.select(INVENTORY_ADJUSTMENT_ORDER.ID, INVENTORY_ADJUSTMENT_ORDER.NUMBER, INVENTORY_ADJUSTMENT_ORDER.TYPE)
                    .from(INVENTORY_ADJUSTMENT_ORDER)
                    .where(INVENTORY_ADJUSTMENT_ORDER.OWNER_ID.eq(entry.ownerId()), INVENTORY_ADJUSTMENT_ORDER.ID.eq(entry.inventoryAdjustmentOrderId()))
                    .fetchOptional()
                    .filter(r -> InventoryAdjustmentOrder.Type.TYPE_CHANGE.name().equals(r.value3()))
                    .map(r -> new Document(r.value1(), r.value2(), Document.Type.INVENTORY_MOVE_ADJUSTMENT))
                    .orElse(null);
        }
        return null;
    }

    private Money averageCostAfter(LinkedList<InventoryEntry> processedEntries) {
        if (processedEntries.isEmpty()) {
            return new Money(0, Money.DEFAULT_CURRENCY);
        }
        var currentStock = processedEntries.stream()
                .map(InventoryEntry::quantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (currentStock.compareTo(BigDecimal.ZERO) == 0) {
            return new Money(0, Money.DEFAULT_CURRENCY);
        }
        return new Money(
                processedEntries.stream()
                        .map(entry -> entry.quantity().multiply(BigDecimal.valueOf(entry.receptionPrice().amount())))
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .setScale(4, RoundingMode.HALF_EVEN)
                        .divide(currentStock, RoundingMode.HALF_EVEN)
                        .longValue(),
                processedEntries.get(0).receptionPrice().currency());
    }

    public record Item(
            UUID materialGoodId, String name, String code, String description, Entity lastOrderedFrom, Entity category,
            Entity inventoryUnit,
            StringEntity measurementUnit,
            BigDecimal quantity,
            BigDecimal committed, BigDecimal incoming, BigDecimal balance, BigDecimal available,
            BigDecimal criticalOnHand,
            Boolean produced, BigDecimal unitOfProduction,
            Money stockValue, Money cost, Money sellingPrice, Money profit, BigDecimal margin
    ) {
    }

    public record ItemHistory(
            BigDecimal currentStock,
            BigDecimal reservedStock,
            BigDecimal incomingStock,
            BigDecimal criticalOnHand,
            BigDecimal balance,
            List<StockHistoryEntry> stockHistory,
            List<Committed> committedTo,
            List<Incoming> incomingOrders
    ) {
        public record StockHistoryEntry(
                UUID id,
                Instant createTime,
                Entity inventoryAdjustmentOrder,
                Entity salesOrder,
                Entity manufacturingOrder,
                Entity purchaseOrder,
                Document document,
                Money price,
                BigDecimal quantity,
                Money averageCostAfter,
                BigDecimal stockAfter
        ) {
        }
    }

    public record Committed(Entity salesOrder,
                            Entity manufacturingOrder,
                            Entity servicingOrder,
                            Entity customer,
                            BigDecimal quantity) {
    }

    public record Incoming(
            Entity purchaseOrder,
            Entity manufacturingOrder,
            Entity supplier,
            BigDecimal quantity,
            Money price,
            LocalDate expectedBy
    ) {
    }

    public record Document(UUID id, String name, Type type) {
        public enum Type {RECEPTION_RECEIPT, MATERIAL_ISSUE_NOTE, INVENTORY_MOVE_ADJUSTMENT}
    }

    public enum StockHistoryEntryType {INCOMING, OUTGOING}


}
