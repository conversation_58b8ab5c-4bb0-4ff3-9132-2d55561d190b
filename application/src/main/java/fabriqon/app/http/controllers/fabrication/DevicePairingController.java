package fabriqon.app.http.controllers.fabrication;

import fabriqon.app.common.model.DevicePairingTokenType;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

import static fabriqon.jooq.classes.Tables.*;

@RestController
@RequestMapping(path = "/fabrication/devices/pairing")
public class DevicePairingController {

    private final DSLContext db;

    @Autowired
    public DevicePairingController(DSLContext db) {
        this.db = db;
    }

    @GetMapping("initiate/{token}")
    public PairingInit initiatePairing(@PathVariable("token") String token) {
        var initiationTokenDetails = db.selectFrom(DEVICE_PAIRING_TOKEN)
                .where(DEVICE_PAIRING_TOKEN.TOKEN.eq(token), DEVICE_PAIRING_TOKEN.TOKEN_TYPE.eq(DevicePairingTokenType.INITIATION.name()))
                .fetchSingle();
        //TODO introduce token expiration
        var confirmationToken = randomToken();
        db.insertInto(DEVICE_PAIRING_TOKEN)
                .set(DEVICE_PAIRING_TOKEN.TOKEN, confirmationToken)
                .set(DEVICE_PAIRING_TOKEN.TOKEN_TYPE, DevicePairingTokenType.CONFIRMATION.name())
                .set(DEVICE_PAIRING_TOKEN.USER_ID, initiationTokenDetails.getUserId())
                .execute();
        return new PairingInit(
                db.select(USERS.NAME).from(USERS)
                        .where(USERS.ID.eq(initiationTokenDetails.getUserId()))
                        .fetchSingleInto(String.class),
                confirmationToken);
    }

    @PostMapping("confirm")
    public AccessToken confirmPairing(@Validated @RequestBody PairingRequest pairingRequest) {
        var confirmationTokenDetails = db.selectFrom(DEVICE_PAIRING_TOKEN)
                .where(
                        DEVICE_PAIRING_TOKEN.TOKEN.eq(pairingRequest.token),
                        DEVICE_PAIRING_TOKEN.TOKEN_TYPE.eq(DevicePairingTokenType.CONFIRMATION.name())
                )
                .fetchSingle();
        //TODO introduce token expiration
        db.deleteFrom(DEVICE_PAIRING_TOKEN)
                .where(DEVICE_PAIRING_TOKEN.USER_ID.eq(confirmationTokenDetails.getUserId()),
                        DEVICE_PAIRING_TOKEN.TOKEN.ne("ANDROID_TOKEN"),
                        DEVICE_PAIRING_TOKEN.TOKEN.ne("IOS_TOKEN")
                )
                .execute();
        var accessToken = randomToken();
        db.insertInto(DEVICE_PAIRING_TOKEN)
                .set(DEVICE_PAIRING_TOKEN.TOKEN, accessToken)
                .set(DEVICE_PAIRING_TOKEN.TOKEN_TYPE, DevicePairingTokenType.ACCESS.name())
                .set(DEVICE_PAIRING_TOKEN.USER_ID, confirmationTokenDetails.getUserId())
                .execute();

        return new AccessToken(accessToken);
    }

    @PostMapping("remove")
    public void removePairing(@Validated @RequestBody PairingRequest pairingRequest) {
        var accessTokenDetails = db.selectFrom(DEVICE_PAIRING_TOKEN)
                .where(
                        DEVICE_PAIRING_TOKEN.TOKEN.eq(pairingRequest.token),
                        DEVICE_PAIRING_TOKEN.TOKEN_TYPE.eq(DevicePairingTokenType.ACCESS.name())
                )
                .fetchSingle();
        db.deleteFrom(DEVICE_PAIRING_TOKEN)
                .where(
                        DEVICE_PAIRING_TOKEN.USER_ID.eq(accessTokenDetails.getUserId()),
                        //exceptions for testing
                        DEVICE_PAIRING_TOKEN.TOKEN.ne("ANDROID_TOKEN"),
                        DEVICE_PAIRING_TOKEN.TOKEN.ne("IOS_TOKEN")
                        )
                .execute();
    }

    private String randomToken() {
        return UUID.randomUUID().toString();
    }

    public record PairingRequest(
            String token
    ) {
    }

    public record PairingInit(
            String employeeName,
            String confirmationToken
    ) {
    }

    public record AccessToken(
            String token
    ) {
    }

}
