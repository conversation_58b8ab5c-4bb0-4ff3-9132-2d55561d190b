package fabriqon.app.http.controllers.inventory;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.inventory.reception.*;
import fabriqon.app.common.model.File;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.common.model.Money;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.controllers.FilesController;
import fabriqon.app.http.controllers.integrations.WinmentorNirImporter;
import fabriqon.app.http.controllers.manufacturing.ManufacturingOrdersController;
import fabriqon.app.http.response.Entity;
import fabriqon.app.http.response.StringEntity;
import fabriqon.misc.Json;
import fabriqon.misc.Tuple;
import fabriqon.pdf.HtmlToPdfConverter;
import fabriqon.templates.Localizer;
import fabriqon.templates.Templates;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.jooq.DSLContext;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static fabriqon.jooq.JooqTextSearchFunctions.textSearch;
import static fabriqon.jooq.classes.Tables.*;
import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.jooq.impl.DSL.*;

@RestController
@RequestMapping(path = "/inventory/receptions")
public class InventoryReceptionsController {

    private static final String PLACEHOLDER_STRING = "__________________";

    private final DSLContext db;
    private final AccountContext accountContext;
    private final ReceptionService receptionService;
    private final Templates templates;
    private final HtmlToPdfConverter htmlToPdfConverter;
    private final WinmentorNirImporter winmentorNirImporter;

    public InventoryReceptionsController(DSLContext db, AccountContext accountContext, ReceptionService receptionService, Templates templates, HtmlToPdfConverter htmlToPdfConverter, WinmentorNirImporter winmentorNirImporter) {
        this.db = db;
        this.accountContext = accountContext;
        this.receptionService = receptionService;
        this.templates = templates;
        this.htmlToPdfConverter = htmlToPdfConverter;
        this.winmentorNirImporter = winmentorNirImporter;
    }

    @PostMapping(path = "receive")
    public ReceptionReceiptDetails receive(@AuthenticationPrincipal Jwt principal, @Validated @RequestBody GoodsReceived goodsReceived) {
        return map(receptionService.receive(accountContext.accountId(principal),
                goodsReceived.supplierId,
                goodsReceived.purchaseOrderId,
                goodsReceived.documentNumber,
                goodsReceived.receptionDate,
                goodsReceived.supportingDocument,
                goodsReceived.currency,
                goodsReceived.notes,
                goodsReceived.receivedBy,
                goodsReceived.transportedBy,
                goodsReceived.transportedWith,
                goodsReceived.goods,
                goodsReceived.additionalCosts,
                List.of()
        ));
    }

    @PostMapping(path = "receive", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ReceptionReceiptDetails receiveWithFiles(@AuthenticationPrincipal Jwt principal,
                                                    @RequestPart("goodsReceived") @Validated InventoryReceptionsController.GoodsReceived goodsReceived,
                                                    @RequestPart(name = "files", required = false) List<MultipartFile> files) {
        return map(receptionService.receive(accountContext.accountId(principal),
                goodsReceived.supplierId,
                goodsReceived.purchaseOrderId,
                goodsReceived.documentNumber,
                goodsReceived.receptionDate,
                goodsReceived.supportingDocument,
                goodsReceived.currency,
                goodsReceived.notes,
                goodsReceived.receivedBy,
                goodsReceived.transportedBy,
                goodsReceived.transportedWith,
                goodsReceived.goods,
                goodsReceived.additionalCosts,
                ofNullable(files).orElse(List.of()).stream().map(f -> {
                    try {
                        return Tuple.of(f.getOriginalFilename(), new ByteArrayInputStream(f.getBytes()));
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }).toList()
        ));
    }

    @SuppressWarnings("rawtypes")
    @GetMapping(path = "list",
            produces = {
                    MediaType.APPLICATION_JSON_VALUE,
                    MediaType.APPLICATION_PDF_VALUE + "+zip"
            })
    public ResponseEntity list(@AuthenticationPrincipal Jwt principal,
                                              @RequestHeader(HttpHeaders.ACCEPT) String acceptHeader,
                                              @RequestParam(value = "start", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate start,
                                              @RequestParam(value = "end", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate end,
                                              @RequestParam(value = "q", required = false) String textSearch) throws IOException {

        var filter = RECEPTION_RECEIPT.OWNER_ID.eq(accountContext.accountId(principal));
        var s = start != null ? start.atStartOfDay() : null;
        var e = end != null ? end.atTime(23, 59, 59) : null;
        if (s != null) {
            filter = filter.and(RECEPTION_RECEIPT.RECEPTION_DATE.gt(s));
        }
        if (e != null) {
            filter = filter.and(RECEPTION_RECEIPT.RECEPTION_DATE.lt(e));
        }
        if (isNotBlank(textSearch)) {
            filter = filter.and(exists(selectOne()
                    .from("jsonb_array_elements(" + RECEPTION_RECEIPT.GOODS.getName() + ") as arr")
                    .where(field("(arr->>'materialGoodId')::uuid")
                            .in(select(MATERIAL_GOOD.ID)
                                    .from(MATERIAL_GOOD)
                                    .where(MATERIAL_GOOD.OWNER_ID.eq(accountContext.accountId(principal))
                                            .and(textSearch(MATERIAL_GOOD.TEXT_SEARCH, textSearch)))))
            ));
        }
        var receipts = db.selectFrom(RECEPTION_RECEIPT)
                .where(filter)
                .orderBy(RECEPTION_RECEIPT.RECEPTION_DATE.desc())
                .fetchInto(ReceptionReceipt.class);

        if (acceptHeader.contains(MediaType.APPLICATION_PDF_VALUE + "+zip")) {
            var zip = new ByteArrayOutputStream();
            ZipOutputStream zipOut = new ZipOutputStream(zip);
            receipts.forEach(receipt -> {
                try {
                    ZipEntry zipEntry = new ZipEntry("reception_receipt_" +
                            DateTimeFormatter.ofPattern("yyyy-MM-dd").format(receipt.receptionDate()) + "_" + receipt.number() + ".pdf");
                    zipOut.putNextEntry(zipEntry);
                    zipOut.write(pdf(html(receipt)));
                } catch (IOException ex) {
                    throw new RuntimeException(ex);
                }
            });
            zipOut.close();
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=reception_receipts.zip")
                    .body(zip.toByteArray());
        } else {
            return new ResponseEntity<>(receipts.stream().map(this::map).toList(), HttpStatus.OK);
        }
    }

    @Operation(responses = {
            @ApiResponse(responseCode = "200", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = ReceptionReceiptDetails.class)),
                    @Content(mediaType = "application/pdf"),
                    @Content(mediaType = "text/html"),
            })
    })
    @SuppressWarnings("rawtypes")
    @GetMapping(path = "{id}/details",
            produces = {
                    MediaType.TEXT_HTML_VALUE,
                    MediaType.APPLICATION_JSON_VALUE,
                    MediaType.APPLICATION_PDF_VALUE
            }
    )
    public ResponseEntity details(@AuthenticationPrincipal Jwt principal,
                                  @RequestHeader(HttpHeaders.ACCEPT) String acceptHeader,
                                  @PathVariable("id") UUID receiptId
    ) {
        var receptionReceipt = db.selectFrom(RECEPTION_RECEIPT)
                .where(RECEPTION_RECEIPT.OWNER_ID.eq(accountContext.accountId(principal)), RECEPTION_RECEIPT.ID.eq(receiptId))
                .fetchSingleInto(ReceptionReceipt.class);
        if (acceptHeader.contains(MediaType.TEXT_HTML_VALUE)) {
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(html(receptionReceipt));
        } else if (acceptHeader.contains(MediaType.APPLICATION_PDF_VALUE)) {
            var pdf = pdf(html(receptionReceipt));
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                            + "reception_receipt_" + DateTimeFormatter.ofPattern("yyyy-MM-dd")
                            .format(LocalDate.ofInstant(receptionReceipt.createTime(), ZoneId.systemDefault())) + ".pdf")
                    .body(pdf);
        } else {
            return new ResponseEntity<>(map(receptionReceipt), HttpStatus.OK);
        }
    }

    @PostMapping(value = "import/winmentor-xls/process")
    public ReceptionReceiptDetails processWinmentorXls(@AuthenticationPrincipal Jwt principal, @RequestParam("file") MultipartFile file) throws IOException {
        return map(winmentorNirImporter.processXls(accountContext.accountId(principal), file.getInputStream()));
    }

    @PostMapping(path = "{id}/attach-file", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ReceptionReceiptDetails attachFile(@AuthenticationPrincipal Jwt principal,
                           @PathVariable("id") UUID receiptId,
                           @RequestParam("file") MultipartFile file) throws IOException {
        var receipt = db.selectFrom(RECEPTION_RECEIPT)
                .where(RECEPTION_RECEIPT.OWNER_ID.eq(accountContext.accountId(principal)), RECEPTION_RECEIPT.ID.eq(receiptId))
                .fetchSingleInto(ReceptionReceipt.class);
        receptionService.attach(accountContext.accountId(principal), receiptId,
                file.getOriginalFilename(), file.getBytes());
        return map(receipt);
    }

    private ReceptionReceiptDetails map(ReceptionReceipt receipt) {
        return new ReceptionReceiptDetails(
                receipt.id(),
                receipt.number(),
                supplier(receipt.supplierId()),
                purchaseOrder(receipt.purchaseOrderId()),
                receipt.receptionDate(),
                receipt.supportingDocument(),
                receipt.details().currency(),
                receipt.details().notes(),
                receipt.details().receivedBy(),
                receipt.details().transportedBy(),
                receipt.details().transportedWith(),
                receipt.goods().stream().map(good -> new ReceivedGoodDetails(
                                material(good.materialGoodId()),
                                good.orderedQuantity(),
                                good.receivedQuantity(),
                                good.price(),
                                inventoryUnit(good.inventoryUnitId()),
                                good.additionalCostCustomValue(),
                                good.calculatedAdditionalCost(),
                                good.price().multiply(good.receivedQuantity()).add(good.calculatedAdditionalCost())
                        )
                ).toList(),
                receipt.details().additionalCosts(),
                files(receipt.ownerId(), receipt.id())
        );
    }

    private List<File> files(UUID ownerId, UUID receiptId) {
        return db.select(FILE.ID, FILE.FILE_NAME)
                .from(FILE)
                .where(FILE.ID.in(select(RECEPTIONRECEIPT_FILE.FILE_ID).from(RECEPTIONRECEIPT_FILE).where(RECEPTIONRECEIPT_FILE.OWNER_ID.eq(ownerId), RECEPTIONRECEIPT_FILE.RECEPTION_RECEIPT_ID.eq(receiptId))))
                .fetch()
                .stream()
                .map(f -> new File(f.value1(), f.value2(),
                        FilesController.previewLink(f.value1()), FilesController.downloadLink(f.value1())))
                .toList();
    }

    private String html(ReceptionReceipt receipt) {
        var account = account(receipt.ownerId());
        var localizer = new Localizer(new Locale(account.information().address().country()));
        var currency = receipt.goods().stream()
                .filter(item -> item.price() != null)
                .map(item -> item.price().currency())
                .findFirst().orElse(account.settings().general().defaultCurrency());
        var itemIndexer = new AtomicInteger();

        var receiptMap = new HashMap<>();
        receiptMap.put("number", receipt.number());
        receiptMap.put("date", localizer.localizeDate(receipt.receptionDate()));
        receiptMap.put("transportedWith", isNotBlank(receipt.details().transportedWith()) ? receipt.details().transportedWith() : PLACEHOLDER_STRING);
        receiptMap.put("transportedBy", isNotBlank(receipt.details().transportedBy()) ? receipt.details().transportedBy() : PLACEHOLDER_STRING);
        receiptMap.put("documentType", localizer.localizedValue(receipt.supportingDocument().type().name()));
        receiptMap.put("documentNumber", receipt.supportingDocument().type() != SupportingDocument.Type.NONE
                ? isNotBlank(receipt.supportingDocument().number())
                    ? receipt.supportingDocument().number() : PLACEHOLDER_STRING
                : PLACEHOLDER_STRING);
        receiptMap.put("documentDate", receipt.supportingDocument().type() != SupportingDocument.Type.NONE
                ? receipt.supportingDocument().date() != null
                ? localizer.localizeDate(receipt.supportingDocument().date()) : PLACEHOLDER_STRING
                : PLACEHOLDER_STRING);
        receiptMap.put("goods", receipt.goods().stream()
                .map(item -> {
                    return Map.of(
                            "index", itemIndexer.incrementAndGet(),
                            "name", material(item.materialGoodId()).name(),
                            "measurementUnit", localizer.localizedValue(measurementUnit(item.materialGoodId()).symbol()),
                            "orderedQuantity", item.orderedQuantity(),
                            "receivedQuantity", item.receivedQuantity(),
                            "price", item.price().displayAmount(),
                            "additionalCosts", item.calculatedAdditionalCost() != null ? item.calculatedAdditionalCost().displayAmount() : "",
                            "value", item.price().multiply(item.receivedQuantity())
                                    .add((item.calculatedAdditionalCost() != null ? item.calculatedAdditionalCost().amount() : 0))
                                    .displayAmount()
                    );})
                .toList());
        receiptMap.put("totalValue", receipt.goods().stream()
                .map(item -> item.price().multiply(item.receivedQuantity())
                        .add((item.calculatedAdditionalCost() != null ? item.calculatedAdditionalCost().amount() : 0)))
                .reduce(Money::add).orElse(new Money(0, currency)).displayAmount());
        receiptMap.put("receivedBy", receipt.details().receivedBy());

        var map = new HashMap<>();
        map.put("receipt", receiptMap);
        map.put("supplier", supplier(receipt.supplierId()));
        map.put("labels", localizer.labels());
        map.put("base64Logo", account.logo());
        map.put("currency", currency);
        return templates.render("pdf/reception_receipt.html", map);
    }

    private byte[] pdf(String html) {
        return htmlToPdfConverter.convert(html);
    }

    private Entity supplier(UUID supplierId) {
        if (supplierId == null) {
            return null;
        }
        return db.select(COMPANY.ID, COMPANY.COMPANY_NAME)
                .from(COMPANY)
                .where(COMPANY.ID.eq(supplierId))
                .fetchSingleInto(Entity.class);
    }


    private Entity purchaseOrder(UUID purchaseOrderId) {
        if (purchaseOrderId == null) {
            return null;
        }
        return db.select(PURCHASE_ORDER.ID, PURCHASE_ORDER.NUMBER)
                .from(PURCHASE_ORDER)
                .where(PURCHASE_ORDER.ID.eq(purchaseOrderId))
                .fetchSingleInto(Entity.class);
    }

    private ReceivedGoodDetails.MaterialGoodDetails material(UUID materialGoodId) {
        var mg = db.selectFrom(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(materialGoodId))
                .fetchSingleInto(MaterialGood.class);
        return new ReceivedGoodDetails.MaterialGoodDetails(materialGoodId, mg.name, mg.code,
                new StringEntity(mg.details.measurementUnit().name(), mg.details.measurementUnit().symbol()));
    }

    private Entity inventoryUnit(UUID inventoryUnitId) {
        return db.select(INVENTORY_UNIT.ID, INVENTORY_UNIT.NAME)
                .from(INVENTORY_UNIT)
                .where(INVENTORY_UNIT.ID.eq(inventoryUnitId))
                .fetchSingleInto(Entity.class);
    }

    private MeasurementUnit measurementUnit(UUID materialId) {
        return db.select(MATERIAL_GOOD.DETAILS)
                .from(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(materialId))
                .fetchSingleInto(MaterialGood.Details.class)
                .measurementUnit();
    }

    private Account account(UUID id) {
        return db.selectFrom(ACCOUNT).where(ACCOUNT.ID.eq(id)).fetchSingleInto(Account.class);
    }

    public record GoodsReceived(
            UUID supplierId,
            UUID purchaseOrderId,
            String documentNumber,
            LocalDate receptionDate,
            SupportingDocument supportingDocument,
            Currency currency,
            String notes,
            String receivedBy,
            String transportedBy,
            String transportedWith,
            List<ReceivedGood> goods,
            List<AdditionalCost> additionalCosts
    ) {
    }

    public record ReceptionReceiptDetails(
            UUID id,
            String number,
            Entity supplier,
            Entity purchaseOrder,
            LocalDate receptionDate,
            SupportingDocument supportingDocument,
            Currency currency,
            String notes,
            String receivedBy,
            String transportedBy,
            String transportedWith,
            List<ReceivedGoodDetails> goods,
            List<AdditionalCost> additionalCosts,
            List<File> files
    ) {
    }

    public record ReceivedGoodDetails(
            MaterialGoodDetails materialGood,
            BigDecimal orderedQuantity,
            BigDecimal receivedQuantity,
            Money price,
            Entity inventoryUnit,
            Money additionalCostCustomValue,
            Money calculatedAdditionalCost,
            Money totalValue) {

        public record MaterialGoodDetails(
                UUID id,
                String name,
                String code,
                StringEntity measurementUnit
        ) {
        }
    }

}
