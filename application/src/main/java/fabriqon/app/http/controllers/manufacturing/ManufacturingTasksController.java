package fabriqon.app.http.controllers.manufacturing;

import fabriqon.app.business.manufacturing.ManufacturingTask;
import fabriqon.app.business.manufacturing.tasks.ManufacturingTaskService;
import fabriqon.app.config.security.AccountContext;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.MANUFACTURING_TASK;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

@RestController
@RequestMapping(path = "/manufacturing/tasks")
public class ManufacturingTasksController {

    private final AccountContext accountContext;

    private final DSLContext db;
    private final ManufacturingTasks tasks;
    private final ManufacturingTaskService manufacturingTaskService;

    @Autowired
    public ManufacturingTasksController(AccountContext accountContext, DSLContext db, ManufacturingTasks tasks,
                                        ManufacturingTaskService manufacturingTaskService) {
        this.accountContext = accountContext;
        this.db = db;
        this.tasks = tasks;
        this.manufacturingTaskService = manufacturingTaskService;
    }

    @GetMapping(path = "list")
    public List<ManufacturingTasks.ManufacturingTask> list(
            @AuthenticationPrincipal Jwt principal,
            @RequestParam(value = "status", required = false) List<ManufacturingTask.Status> statuses,
            @RequestParam(value = "day", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate day,
            @RequestParam(value = "start", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate start,
            @RequestParam(value = "end", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate end
            ) {
        if (day != null && (start != null || end != null)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "can't filter both on 'day' and 'start'/'end'");
        }
        var s = day != null ? day.atStartOfDay() : start != null ? start.atStartOfDay() : null;
        var e = day != null ? day.atTime(23, 59, 59) : end != null ? end.atTime(23, 59, 59) : null;
        return manufacturingTaskService.getTasks(accountContext.accountId(principal),
                        isNotEmpty(statuses)
                                ? statuses.stream()
                                .map(status -> ManufacturingTask.Status.valueOf(status.name()))
                                .toList()
                                : List.of(),
                        s, e)
                .stream().map(tasks::toDto).toList();
    }

    @GetMapping(path = "{id}/details")
    public ManufacturingTasks.ManufacturingTask details(@AuthenticationPrincipal Jwt principal,
                                                        @PathVariable("id") UUID taskId) {
        return tasks.toDto(db.selectFrom(MANUFACTURING_TASK)
                .where(MANUFACTURING_TASK.OWNER_ID.eq(accountContext.accountId(principal)),
                        MANUFACTURING_TASK.ID.eq(taskId),
                        MANUFACTURING_TASK.DELETED.isFalse())
                .fetchSingleInto(ManufacturingTask.class));
    }

    @PostMapping(path = "{id}/employees")
    public void updateEmployees(@AuthenticationPrincipal Jwt principal,
                                @PathVariable("id") UUID taskId,
                                @RequestBody @Validated List<UUID> employees) {
        manufacturingTaskService.updateEmployees(accountContext.accountId(principal), taskId, employees);
    }

    @PostMapping(path = "{id}/workstations")
    public void updateWorkstations(@AuthenticationPrincipal Jwt principal,
                                   @PathVariable("id") UUID taskId,
                                   @RequestBody @Validated List<UUID> workstations) {
        manufacturingTaskService.updateWorkstations(accountContext.accountId(principal), taskId, workstations);
    }

    @PostMapping(path = "{id}/in-progress")
    public void inProgress(@AuthenticationPrincipal Jwt principal, @PathVariable("id") UUID id) {
        manufacturingTaskService.inProgress(accountContext.accountId(principal), id);
    }

    @PostMapping(path = "{id}/done")
    public void done(@AuthenticationPrincipal Jwt principal, @PathVariable("id") UUID id) {
        manufacturingTaskService.done(accountContext.accountId(principal), id);
    }

    @PostMapping(path = "{id}/stopped")
    public void stopped(@AuthenticationPrincipal Jwt principal, @PathVariable("id") UUID id, @RequestParam("reason") ManufacturingTask.StatusReason reason) {
        manufacturingTaskService.stopped(accountContext.accountId(principal), id, reason, accountContext.user(principal).name());
    }

    @PostMapping(path = "{id}/change-assignee-count")
    public void changeNumberOfAssignees(@AuthenticationPrincipal Jwt principal, @PathVariable("id") UUID id,
                                        @RequestBody NumberOfAssignees numberOfAssignees) {
        manufacturingTaskService.changeNumberOfAssignees(accountContext.accountId(principal), id, numberOfAssignees.numberOfAssignees);
    }

    public record NumberOfAssignees(int numberOfAssignees) {}

}
