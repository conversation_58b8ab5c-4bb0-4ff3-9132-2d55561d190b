package fabriqon.app.http.controllers;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.financial.VatCalculatorProvider;
import fabriqon.app.business.invoicing.Invoice;
import fabriqon.app.business.invoicing.InvoiceService;
import fabriqon.app.common.model.Address;
import fabriqon.app.common.model.EmailMessage;
import fabriqon.app.common.model.Money;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.controllers.integrations.SagaExport;
import fabriqon.app.http.controllers.integrations.WinmentorExport;
import fabriqon.app.http.response.Company;
import fabriqon.misc.Tuple;
import fabriqon.templates.Templates;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static fabriqon.jooq.classes.Tables.*;
import static java.lang.String.format;
import static java.util.stream.Collectors.toList;

@RestController
@RequestMapping(path = "/invoices")
public class InvoicesController {
    private final AccountContext accountContext;
    private final DSLContext db;
    private final Templates templates;
    private final InvoiceService invoiceService;
    private final VatCalculatorProvider vatCalculatorProvider;

    private static final String MEDIA_TYPE_FACTURA_SAGA = "application/factura-saga+xml";
    private static final String MEDIA_TYPE_FACTURA_SAGA_ZIP = "application/factura-saga+zip";
    private static final String MEDIA_TYPE_FACTURA_WINMENTOR = "application/factura-winmentor+txt";
    private static final String MEDIA_TYPE_FACTURA_WINMENTOR_ZIP = "application/factura-winmentor+zip";

    @Autowired
    public InvoicesController(AccountContext accountContext,
                              DSLContext db,
                              Templates templates,
                              InvoiceService invoiceService, VatCalculatorProvider vatCalculatorProvider) {
        this.accountContext = accountContext;
        this.db = db;
        this.templates = templates;
        this.invoiceService = invoiceService;
        this.vatCalculatorProvider = vatCalculatorProvider;
    }

    @SuppressWarnings("rawtypes")
    @GetMapping(path = "list",
            produces = {
                    MediaType.APPLICATION_JSON_VALUE,
                    MEDIA_TYPE_FACTURA_SAGA_ZIP,
                    MEDIA_TYPE_FACTURA_WINMENTOR_ZIP,
                    MediaType.APPLICATION_PDF_VALUE + "+zip"
            }
    )
    public ResponseEntity list(@AuthenticationPrincipal Jwt principal,
                               @RequestHeader(HttpHeaders.ACCEPT) String acceptHeader,
                               @RequestParam(value = "proforma", required = false) Boolean proforma,
                               @RequestParam(value = "start", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate start,
                               @RequestParam(value = "end", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate end)
            throws IOException {
        var accountId = accountContext.accountId(principal);
        var account = supplier(accountId);

        var whereClause = INVOICE.OWNER_ID.eq(accountId).and(INVOICE.DELETED.isFalse());
        var s = start != null ? start.atStartOfDay() : null;
        var e = end != null ? end.atTime(23, 59, 59) : null;
        if (s != null) {
            whereClause = whereClause.and(INVOICE.CREATE_TIME.gt(s));
        }
        if (e != null) {
            whereClause = whereClause.and(INVOICE.CREATE_TIME.lt(e));
        }
        if (proforma != null) {
            whereClause = whereClause.and(INVOICE.PROFORMA.eq(proforma));
        }
        var invoices = db.select()
                .from(INVOICE)
                .where(whereClause)
                .fetchInto(Invoice.class);
        var invoiceDetails = invoices.stream().map(this::details).toList();
        if (acceptHeader.contains(MEDIA_TYPE_FACTURA_SAGA_ZIP)) {
            var zip = new ByteArrayOutputStream();
            ZipOutputStream zipOut = new ZipOutputStream(zip);
            invoiceDetails.forEach(invoice -> {
                try {
                    var sagaXml = new SagaExport(db, vatCalculatorProvider, templates).transformToSaga(invoice, account);
                    var fileName = format("F_%1$s_%2$s_%3$s.xml",
                            account.taxIdentificationNumber, invoice.number(), DateTimeFormatter.ofPattern("dd-MM-yyyy").format(invoice.creationDate));
                    ZipEntry zipEntry = new ZipEntry(fileName);
                    zipOut.putNextEntry(zipEntry);
                    zipOut.write(sagaXml.getBytes());
                } catch (IOException ex) {
                    throw new RuntimeException(ex);
                }
            });
            zipOut.close();
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=invoices.zip")
                    .body(zip.toByteArray());

        } else if (acceptHeader.contains(MEDIA_TYPE_FACTURA_WINMENTOR_ZIP)) {
            var zip = new ByteArrayOutputStream();
            ZipOutputStream zipOut = new ZipOutputStream(zip);
            invoiceDetails.stream()
                    .collect(Collectors.groupingBy(note -> Tuple.of(note.creationDate().getYear(), note.creationDate().getMonth()), toList()))
                    .forEach((key, list) -> {
                        var export = new WinmentorExport(db, vatCalculatorProvider, templates).transformInvoices(list);
                        var fileName = format("facturi_winmentor_%1$s_%2$s.txt", key.a(), key.b());
                        try {
                            ZipEntry zipEntry = new ZipEntry(fileName);
                            zipOut.putNextEntry(zipEntry);
                            zipOut.write(export.getBytes());
                        } catch (IOException ex) {
                            throw new RuntimeException(ex);
                        }
                    });
            zipOut.close();
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=facturi_winmentor.zip")
                    .body(zip.toByteArray());

        } else if (acceptHeader.contains(MediaType.APPLICATION_PDF_VALUE + "+zip")) {
            var zip = new ByteArrayOutputStream();
            ZipOutputStream zipOut = new ZipOutputStream(zip);
            invoices.forEach(invoice -> {
                var fileName = "invoice_" + invoice.number() + "_" + DateTimeFormatter.ofPattern("yyyy-MM-dd")
                        .format(LocalDate.ofInstant(invoice.createTime(), ZoneId.systemDefault())) + ".pdf";
                try {
                    ZipEntry zipEntry = new ZipEntry(fileName);
                    zipOut.putNextEntry(zipEntry);
                    zipOut.write(invoiceService.pdf(invoiceService.html(invoice)));
                } catch (IOException ex) {
                    throw new RuntimeException(ex);
                }
            });
            zipOut.close();
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=invoices.zip")
                    .body(zip.toByteArray());

        } else {
            return new ResponseEntity<>(invoiceDetails, HttpStatus.OK);
        }
    }

    @Operation(responses = {
            @ApiResponse(responseCode = "200", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = InvoiceDetails.class)),
                    @Content(mediaType = "application/pdf"),
                    @Content(mediaType = "text/html"),
                    @Content(mediaType = "application/factura-saga+xml")
            }),
    })
    @SuppressWarnings("rawtypes")
    @GetMapping(path = "{id}/details",
            produces = {
                    MediaType.TEXT_HTML_VALUE,
                    MediaType.APPLICATION_JSON_VALUE,
                    MediaType.APPLICATION_PDF_VALUE,
                    MEDIA_TYPE_FACTURA_SAGA,
                    MEDIA_TYPE_FACTURA_WINMENTOR
            }
    )
    public ResponseEntity details(@AuthenticationPrincipal Jwt principal,
                                  @RequestHeader(HttpHeaders.ACCEPT) String acceptHeader,
                                  @PathVariable("id") UUID invoiceId
    ) {
        var accountId = accountContext.accountId(principal);
        var account = supplier(accountId);
        var invoice = db.select()
                .from(INVOICE)
                .where(INVOICE.OWNER_ID.eq(accountId), INVOICE.ID.eq(invoiceId))
                .fetchSingleInto(Invoice.class);
        if (acceptHeader.contains(MediaType.TEXT_HTML_VALUE)) {
            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(invoiceService.html(invoice));
        } else if (acceptHeader.contains(MediaType.APPLICATION_PDF_VALUE)) {
            var pdf = invoiceService.pdf(invoiceService.html(invoice));
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                            + "invoice_" + DateTimeFormatter.ofPattern("yyyy-MM-dd")
                            .format(LocalDate.ofInstant(invoice.createTime(), ZoneId.systemDefault())) + ".pdf")
                    .body(pdf);
        } else if (acceptHeader.contains(MEDIA_TYPE_FACTURA_SAGA)) {
            var invoiceDetails = details(invoice);
            var sagaXml = new SagaExport(db, vatCalculatorProvider, templates).transformToSaga(invoiceDetails, account);
            var fileName = format("F_%1$s_%2$s_%3$s.xml",
                    account.taxIdentificationNumber, invoice.number(), DateTimeFormatter.ofPattern("dd-MM-yyyy").format(invoiceDetails.creationDate));
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName)
                    .body(sagaXml.getBytes());
        } else if (acceptHeader.contains(MEDIA_TYPE_FACTURA_WINMENTOR)) {
            var invoiceDetails = details(invoice);
            var export = new WinmentorExport(db, vatCalculatorProvider, templates).transformInvoices(List.of(invoiceDetails));
            var fileName = format("facturi_winmentor_%1$s_%2$s.txt",
                    invoiceDetails.creationDate().getYear(), invoiceDetails.creationDate().getMonth());
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName)
                    .body(export.getBytes());
        } else {
            return new ResponseEntity<>(details(invoice), HttpStatus.OK);
        }
    }

    @PostMapping(path = "{id}/send")
    public void send(@AuthenticationPrincipal Jwt principal,
                     @PathVariable("id") UUID invoiceId,
                     @RequestBody @Validated EmailMessage emailMessage) {
        invoiceService.send(accountContext.accountId(principal), invoiceId, emailMessage);
    }

    @PostMapping(path = "{id}/mark-as-paid")
    public void markAsPaid(@AuthenticationPrincipal Jwt principal,
                           @PathVariable("id") UUID invoiceId) {
        invoiceService.markAsPaid(accountContext.accountId(principal), invoiceId);
    }

    private InvoiceDetails details(Invoice invoice) {
        var vatCalculator = vatCalculatorProvider.with(invoice.ownerId(), invoice.customerId());
        return new InvoiceDetails(
                invoice.id(),
                invoice.number(),
                LocalDate.ofInstant(invoice.createTime(), ZoneId.systemDefault()),
                invoice.dueDate(),
                invoice.sentAt(),
                customer(invoice.customerId()),
                supplier(invoice.ownerId()),
                invoice.subTotal(),
                invoice.discount(),
                invoice.vat(vatCalculator),
                invoice.total(vatCalculator),
                invoice.paid() ? InvoiceDetails.Status.PAID : invoice.dueDate().isAfter(LocalDate.now()) ? InvoiceDetails.Status.DUE : InvoiceDetails.Status.OVERDUE,
                invoice.proforma(),
                invoice.notes(),
                invoice.items()
        );
    }

    private Company supplier(UUID ownerId) {
        var account = db.selectFrom(ACCOUNT).where(ACCOUNT.ID.eq(ownerId)).fetchSingleInto(Account.class);
        return new Company(
                account.id(),
                fabriqon.app.common.model.Company.Type.LOCAL_LEGAL_ENTITY,
                account.name(),
                account.information().email(),
                account.information().identificationNumber(),
                account.information().taxIdentificationNumber(),
                //should only return one entry anyway
                List.of(account.information().address()),
                null,
                account.information().bankAccounts(),
                null,
                List.of()
        );
    }

    private Company customer(UUID customerId) {
        var company = db.selectFrom(COMPANY)
                .where(COMPANY.ID.eq(customerId))
                .fetchSingleInto(fabriqon.app.common.model.Company.class);
        return new Company(
                company.id(),
                company.type(),
                company.name(),
                null,
                company.details().identificationNumber(),
                company.details().taxIdentificationNumber(),
                //should only return one entry anyway
                company.details().addresses().stream().filter(address -> address.types().contains(Address.Type.BILLING)).toList(),
                company.details().contacts(),
                company.details().bankAccounts(),
                company.details().notes(),
                List.of()
        );
    }

    public record InvoiceDetails(
            UUID id,
            String number,
            LocalDate creationDate,
            LocalDate dueDate,
            LocalDate sentAt,
            Company customer,
            Company supplier,
            Money subTotal,
            Money discount,
            Money vat,
            Money total,
            Status status,
            boolean proforma,
            String notes,
            List<Invoice.Item> items
    ) {
        public enum Status {PAID, DUE, OVERDUE}
    }

}