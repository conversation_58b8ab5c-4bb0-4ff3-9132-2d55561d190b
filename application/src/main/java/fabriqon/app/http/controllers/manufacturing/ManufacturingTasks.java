package fabriqon.app.http.controllers.manufacturing;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.manufacturing.ManufacturingOperation;
import fabriqon.app.business.manufacturing.ManufacturingOperationService;
import fabriqon.app.business.manufacturing.ManufacturingOrder;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.common.model.Money;
import fabriqon.app.http.response.Entity;
import fabriqon.app.http.response.StringEntity;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.*;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.jooq.impl.DSL.max;
import static org.jooq.impl.DSL.min;

/**
 * Helper class to be used in the controllers
 */
@Component
public class ManufacturingTasks {

    private final DSLContext db;
    private final ManufacturingOperationService manufacturingOperationService;

    @Autowired
    public ManufacturingTasks(DSLContext db, ManufacturingOperationService manufacturingOperationService) {
        this.db = db;
        this.manufacturingOperationService = manufacturingOperationService;
    }

    public ManufacturingTask toDto(fabriqon.app.business.manufacturing.ManufacturingTask task) {
        var order = order(task.manufacturingOrderId());
        var product = order.productId() != null ? product(order.productId()) : null;
        var service = order.serviceId() != null ? service(order.serviceId()) : null;
        var assignedEmployees = assignedEmployees(task);
        var settings = settings(task.ownerId());
        var operations = manufacturingOperationService.enhance(order.manufacturingOperations());
        return new ManufacturingTasks.ManufacturingTask(
                task.id(),
                orderDetails(order),
                product != null ? new Entity(product.id, product.name) : service,
                task.status(),
                task.statusReason(),
                task.startTime(),
                task.estimatedStartTime(),
                task.endTime(),
                task.estimatedEndTime(),
                orderStartTime(task.manufacturingOrderId()),
                orderEndTime(task.manufacturingOrderId()),
                task.durationInMinutes(),
                task.status() == fabriqon.app.business.manufacturing.ManufacturingTask.Status.DONE
                        ? actualDurationInMinutes(settings.manufacturing() ,task.startTime(), task.endTime())
                        : task.status() == fabriqon.app.business.manufacturing.ManufacturingTask.Status.IN_PROGRESS
                        ? actualDurationInMinutes(settings.manufacturing(), task.startTime(), LocalDateTime.now(settings.general().defaultTimeZone().toZoneId()))
                        : null,
                task.details().name(),
                task.details().number(),
                quantity(task.manufacturingOrderId()),
                product != null
                        ? new StringEntity(product.details.measurementUnit().name(), product.details.measurementUnit().symbol())
                        : new StringEntity(MeasurementUnit.PIECE.name(), MeasurementUnit.PIECE.symbol()),
                assignedWorkstations(task),
                assignedEmployees,
                assignedEmployees.size(),
                operations.stream()
                        .filter(op -> op.name().equals(task.details().name()))
                        .anyMatch(o -> isNotEmpty(o.manuallyAssignedEmployees())),
                operations.stream()
                        .filter(op -> op.name().equals(task.details().name()))
                        .findFirst()
                        .map(ManufacturingOperation::costPerHour)
                        .orElse(null)
        );
    }

    private LocalDateTime orderStartTime(UUID orderId) {
        return db.select(min(MANUFACTURING_TASK.START_TIME))
                .from(MANUFACTURING_TASK)
                .where(MANUFACTURING_TASK.MANUFACTURING_ORDER_ID.eq(orderId))
                .fetchSingleInto(LocalDateTime.class);
    }

    private LocalDateTime orderEndTime(UUID orderId) {
        return db.select(max(MANUFACTURING_TASK.END_TIME))
                .from(MANUFACTURING_TASK)
                .where(MANUFACTURING_TASK.MANUFACTURING_ORDER_ID.eq(orderId))
                .fetchSingleInto(LocalDateTime.class);
    }

    private ManufacturingOrderDetails orderDetails(ManufacturingOrder order) {
        var client = order.salesOrderId() != null ?
                db.select(COMPANY.ID, COMPANY.COMPANY_NAME)
                        .from(COMPANY)
                        .join(SALES_ORDER).on(SALES_ORDER.CUSTOMER_ID.eq(COMPANY.ID))
                        .where(SALES_ORDER.ID.eq(order.salesOrderId()))
                        .fetchSingleInto(Entity.class)
                : null;
        return new ManufacturingOrderDetails(order.id(), order.number(), order.ranking(), client);
    }

    private List<Entity> assignedWorkstations(fabriqon.app.business.manufacturing.ManufacturingTask task) {
        return db.select(MANUFACTURING_WORKSTATION.ID, MANUFACTURING_WORKSTATION.NAME)
                .from(MANUFACTURING_WORKSTATION)
                .join(MANUFACTURINGTASK_WORKSTATION).on(MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_WORKSTATION_ID.eq(MANUFACTURING_WORKSTATION.ID))
                .where(MANUFACTURINGTASK_WORKSTATION.OWNER_ID.eq(task.ownerId()), MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_TASK_ID.eq(task.id()))
                .fetchInto(Entity.class);
    }

    private List<Entity> assignedEmployees(fabriqon.app.business.manufacturing.ManufacturingTask task) {
        return db.select(USERS.ID, USERS.NAME)
                .from(USERS)
                .join(MANUFACTURINGTASK_EMPLOYEE).on(MANUFACTURINGTASK_EMPLOYEE.USER_ID.eq(USERS.ID))
                .where(MANUFACTURINGTASK_EMPLOYEE.OWNER_ID.eq(task.ownerId()), MANUFACTURINGTASK_EMPLOYEE.MANUFACTURING_TASK_ID.eq(task.id()))
                .fetchInto(Entity.class);
    }

    private MaterialGood product(UUID productId) {
        return db.select()
                .from(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(productId))
                .fetchSingleInto(MaterialGood.class);
    }

    private Entity service(UUID serviceId) {
        return db.select(SERVICE_TEMPLATE.ID, SERVICE_TEMPLATE.NAME)
                .from(SERVICE_TEMPLATE)
                .where(SERVICE_TEMPLATE.ID.eq(serviceId))
                .fetchSingleInto(Entity.class);
    }

    private BigDecimal quantity(UUID manufacturingOrderId) {
        return db.select(MANUFACTURING_ORDER.QUANTITY)
                .from(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.ID.eq(manufacturingOrderId))
                .fetchSingleInto(BigDecimal.class);
    }

    private ManufacturingOrder order(UUID manufacturingOrderId) {
        return db.selectFrom(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.ID.eq(manufacturingOrderId))
                .fetchSingleInto(ManufacturingOrder.class);
    }

    private Integer actualDurationInMinutes(Account.Settings.Manufacturing settings, LocalDateTime start, LocalDateTime end) {
        if (start.isAfter(end)) {
            return 0;
        }
        if (start.toLocalDate().equals(end.toLocalDate())) {
            return (int) ChronoUnit.MINUTES.between(start, end);
        }
        long totalWorkingHours = 0;
        // Calculate working hours for the start date
        if (isWorkingDay(settings, start)) {
            totalWorkingHours += calculateDailyWorkingHours(settings, start, settings.workDayEndTime());
        }
        // Calculate working hours for the end date
        if (isWorkingDay(settings, start)) {
            totalWorkingHours += calculateDailyWorkingHours(settings, settings.workDayStartTime(), end);
        }
        // Calculate full working days between start and end dates
        LocalDate currentDate = start.toLocalDate().plusDays(1);
        while (currentDate.isBefore(end.toLocalDate())) {
            if (isWorkingDay(settings, currentDate.atStartOfDay())) {
                totalWorkingHours += ChronoUnit.MINUTES.between(settings.workDayStartTime(), settings.workDayEndTime());
            }
            currentDate = currentDate.plusDays(1);
        }
        return (int) totalWorkingHours;
    }

    private static long calculateDailyWorkingHours(Account.Settings.Manufacturing settings, LocalDateTime startDateTime, LocalTime endTime) {
        LocalTime startTime = startDateTime.toLocalTime();
        if (startTime.isAfter(settings.workDayEndTime())) {
            return 0;
        }
        if (startTime.isBefore(settings.workDayStartTime())) {
            startTime = settings.workDayStartTime();
        }
        return ChronoUnit.MINUTES.between(startTime, endTime);
    }

    private static long calculateDailyWorkingHours(Account.Settings.Manufacturing settings, LocalTime startTime, LocalDateTime endDateTime) {
        LocalTime endTime = endDateTime.toLocalTime();
        if (endTime.isBefore(settings.workDayStartTime())) {
            return 0;
        }
        if (endTime.isAfter(settings.workDayEndTime())) {
            endTime = settings.workDayEndTime();
        }
        return ChronoUnit.MINUTES.between(startTime, endTime);
    }

    private boolean isWorkingDay(Account.Settings.Manufacturing settings, LocalDateTime date) {
        return settings.workingDays().contains(date.getDayOfWeek().getValue());
    }

    private Account.Settings settings(UUID ownerId) {
        return db.selectFrom(ACCOUNT)
                .where(ACCOUNT.ID.eq(ownerId))
                .fetchSingleInto(Account.class)
                .settings();
    }

    public static class ManufacturingTask{
        public final UUID id;
        public final ManufacturingOrderDetails order;
        public final Entity product;
        public final fabriqon.app.business.manufacturing.ManufacturingTask.Status status;
        public final fabriqon.app.business.manufacturing.ManufacturingTask.StatusReason statusReason;
        public final LocalDateTime startTime;
        public final LocalDateTime estimatedStartTime;
        public final LocalDateTime endTime;
        public final LocalDateTime estimatedEndTime;
        public final LocalDateTime orderStartTime;
        public final LocalDateTime orderEndTime;
        public final int durationInMinutes;
        public final Integer actualDurationInMinutes;
        public final String name;
        public final String number;
        public final List<Entity> assignedWorkstations;
        public final List<Entity> assignedEmployees;
        public final BigDecimal quantity;
        public final StringEntity measurementUnit;
        public final Integer numberOfAssignees;
        public final boolean manuallyAssigned;
        public final Money costPerHour;

        public ManufacturingTask(UUID id,
                                 ManufacturingOrderDetails order,
                                 Entity product,
                                 fabriqon.app.business.manufacturing.ManufacturingTask.Status status,
                                 fabriqon.app.business.manufacturing.ManufacturingTask.StatusReason statusReason,
                                 LocalDateTime startTime,
                                 LocalDateTime estimatedStartTime,
                                 LocalDateTime endTime,
                                 LocalDateTime estimatedEndTime,
                                 LocalDateTime orderStartTime,
                                 LocalDateTime orderEndTime,
                                 int durationInMinutes,
                                 Integer actualDurationInMinutes,
                                 String name,
                                 String number,
                                 BigDecimal quantity,
                                 StringEntity measurementUnit,
                                 List<Entity> assignedWorkstations,
                                 List<Entity> assignedEmployees,
                                 Integer numberOfAssignees,
                                 boolean manuallyAssigned,
                                 Money costPerHour) {
            this.id = id;
            this.order = order;
            this.product = product;
            this.status = status;
            this.statusReason = statusReason;
            this.startTime = startTime;
            this.estimatedStartTime = estimatedStartTime;
            this.endTime = endTime;
            this.estimatedEndTime = estimatedEndTime;
            this.orderStartTime = orderStartTime;
            this.orderEndTime = orderEndTime;
            this.durationInMinutes = durationInMinutes;
            this.actualDurationInMinutes = actualDurationInMinutes;
            this.name = name;
            this.number = number;
            this.quantity = quantity;
            this.measurementUnit = measurementUnit;
            this.assignedWorkstations = assignedWorkstations;
            this.assignedEmployees = assignedEmployees;
            this.numberOfAssignees = numberOfAssignees;
            this.manuallyAssigned = manuallyAssigned;
            this.costPerHour = costPerHour;
        }

        @JsonCreator
        public ManufacturingTask(
                @JsonProperty("id") UUID id,
                @JsonProperty("order") ManufacturingOrderDetails order,
                @JsonProperty("product") Entity product,
                @JsonProperty("status") fabriqon.app.business.manufacturing.ManufacturingTask.Status status,
                @JsonProperty("statusReason") fabriqon.app.business.manufacturing.ManufacturingTask.StatusReason statusReason,
                @JsonProperty("startTime") LocalDateTime startTime,
                @JsonProperty("estimatedStartTime") LocalDateTime estimatedStartTime,
                @JsonProperty("endTime") LocalDateTime endTime,
                @JsonProperty("estimatedEndTime") LocalDateTime estimatedEndTime,
                @JsonProperty("orderStartTime") LocalDateTime orderStartTime,
                @JsonProperty("orderEndTime") LocalDateTime orderEndTime,
                @JsonProperty("durationInMinutes") int durationInMinutes,
                @JsonProperty("actualDurationInMinutes") Integer actualDurationInMinutes,
                @JsonProperty("name") String name,
                @JsonProperty("number") String number,
                @JsonProperty("assignedWorkstations") List<Entity> assignedWorkstations,
                @JsonProperty("assignedEmployees") List<Entity> assignedEmployees,
                @JsonProperty("quantity") BigDecimal quantity,
                @JsonProperty("measurementUnit") StringEntity measurementUnit,
                @JsonProperty("numberOfAssignees") Integer numberOfAssignees,
                @JsonProperty("manuallyAssigned") boolean manuallyAssigned,
                @JsonProperty("costPerHour") Money costPerHour) {
            this.id = id;
            this.order = order;
            this.product = product;
            this.status = status;
            this.statusReason = statusReason;
            this.startTime = startTime;
            this.estimatedStartTime = estimatedStartTime;
            this.endTime = endTime;
            this.estimatedEndTime = estimatedEndTime;
            this.orderStartTime = orderStartTime;
            this.orderEndTime = orderEndTime;
            this.durationInMinutes = durationInMinutes;
            this.actualDurationInMinutes = actualDurationInMinutes;
            this.name = name;
            this.number = number;
            this.assignedWorkstations = assignedWorkstations;
            this.assignedEmployees = assignedEmployees;
            this.quantity = quantity;
            this.measurementUnit = measurementUnit;
            this.numberOfAssignees = numberOfAssignees;
            this.manuallyAssigned = manuallyAssigned;
            this.costPerHour = costPerHour;
        }
    }

    public record ManufacturingOrderDetails(UUID id,
                                            String number,
                                            int ranking,
                                            Entity customer) { }
}


