package fabriqon.app.http.controllers.integrations;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.company.search.CompanySearchService;
import fabriqon.app.business.exceptions.BusinessException;
import fabriqon.app.business.goods.GoodsService;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.inventory.InventoryService;
import fabriqon.app.business.inventory.reception.ReceivedGood;
import fabriqon.app.business.inventory.reception.ReceptionReceipt;
import fabriqon.app.business.inventory.reception.ReceptionService;
import fabriqon.app.business.inventory.reception.SupportingDocument;
import fabriqon.app.business.suppliers.SupplierService;
import fabriqon.app.common.model.Company;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.common.model.Money;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static fabriqon.jooq.JooqJsonbFunctions.stringField;
import static fabriqon.jooq.classes.Tables.*;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Component
public class WinmentorNirImporter {

    private final Map<String, MeasurementUnit> measurementUnits = Map.of(
            "buc", MeasurementUnit.PIECE
    );

    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("dd.MM.yyyy");

    private final DSLContext db;
    private final ReceptionService receptionService;
    private final InventoryService inventoryService;
    private final CompanySearchService companySearchService;
    private final SupplierService supplierService;
    private final GoodsService productsService;

    @Autowired
    public WinmentorNirImporter(DSLContext db, ReceptionService receptionService, InventoryService inventoryService,
                                CompanySearchService companySearchService,
                                SupplierService supplierService,
                                GoodsService productsService) {
        this.db = db;
        this.receptionService = receptionService;
        this.inventoryService = inventoryService;
        this.companySearchService = companySearchService;
        this.supplierService = supplierService;
        this.productsService = productsService;
    }

    public ReceptionReceipt processXls(UUID ownerId, InputStream xls) {

        try (Workbook workbook = new HSSFWorkbook(xls)) {

            Sheet sheet = workbook.getSheetAt(0); // Assuming data is in the first sheet

            // Extract specific lines
            String documentNumber = sheet.getRow(7).getCell(0).getStringCellValue().split("nr.")[1].trim();
            String supplierName = sheet.getRow(9).getCell(0).getStringCellValue().split("furnizate de")[1].trim();
            String supplierTaxId = sheet.getRow(10).getCell(0).getStringCellValue().split("cod fiscal")[1].split("din")[0].trim();
            String documentDetails = sheet.getRow(10).getCell(0).getStringCellValue().split("documentul")[1].trim();
            documentDetails = documentDetails.split(" ")[0]; // Keep only the document number part
            String dateTime = sheet.getRow(11).getCell(0).getStringCellValue().trim();
            String date = dateTime.split(" ")[0].trim();

            var currency = accountCurrency(ownerId);
            var defaultInventoryUnitId = inventoryService.defaultInventoryUnitId(ownerId);
            var goods = new ArrayList<ReceivedGood>();
            var startRowIndex = 17;
            while (true) {
                Row row = sheet.getRow(startRowIndex++);
                if (row == null || row.getCell(0) == null || !row.getCell(0).getCellType().equals(CellType.NUMERIC)) {
                    break;
                }
                var good = material(ownerId,
                        row.getCell(3).getStringCellValue().trim(),//code
                        row.getCell(2).getStringCellValue().trim(),//name
                        row.getCell(4).getStringCellValue().trim()//measurement unit
                );
                goods.add(new ReceivedGood(
                        good.id,
                        BigDecimal.valueOf(row.getCell(5).getNumericCellValue()),
                        BigDecimal.valueOf(row.getCell(7).getNumericCellValue()),
                        new Money(BigDecimal.valueOf(row.getCell(11).getNumericCellValue()).movePointRight(currency.getDefaultFractionDigits()).longValue(), currency),
                        defaultInventoryUnitId,
                        null,
                        new Money(0, currency)
                ));
            }

            return new ReceptionReceipt(null, null, null, false,
                    ownerId,
                    supplierId(ownerId, supplierName, supplierTaxId),
                    null,
                    documentNumber,
                    LocalDate.parse(date, DATE_FORMAT),
                    new SupportingDocument(SupportingDocument.Type.INVOICE,
                            documentDetails.substring(0, documentDetails.lastIndexOf("/")),
                            LocalDate.parse(documentDetails.substring(documentDetails.lastIndexOf("/") + 1), DATE_FORMAT)
                    ),
                    new ReceptionReceipt.Details(currency, null, null, null, null, List.of()),
                    goods
            );
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private Currency accountCurrency(UUID ownerId) {
        return db.selectFrom(ACCOUNT)
                .where(ACCOUNT.ID.eq(ownerId))
                .fetchSingleInto(Account.class)
                .settings().general().defaultCurrency();
    }

    private UUID supplierId(UUID ownerId, String supplierName, String supplierTaxId) {
        return db.select(COMPANY.ID)
                .from(COMPANY)
                .where(COMPANY.OWNER_ID.eq(ownerId), COMPANY.COMPANY_NAME.eq(supplierName), stringField(COMPANY.DETAILS, "taxIdentificationNumber").eq(supplierTaxId))
                .fetchOptionalInto(UUID.class)
                .orElseGet(() -> newSupplier(ownerId, supplierName, supplierTaxId));
    }

    private UUID newSupplier(UUID ownerId, String supplierName, String supplierTaxId) {
        var searchResult = companySearchService.search(supplierTaxId);
        if (searchResult.size() != 1 || !searchResult.get(0).name().equalsIgnoreCase(supplierName)) {
            throw new BusinessException("Cannot find supplier with tax id: " + supplierTaxId, "invalid_supplier", supplierTaxId);
        }
        var company = searchResult.get(0);
        return supplierService.create(ownerId,
                        company.address().country().equalsIgnoreCase("romania") ? Company.Type.LOCAL_LEGAL_ENTITY : Company.Type.FOREIGN_LEGAL_ENTITY,
                        supplierName,
                        new Company.Details(
                                null,
                                supplierTaxId,
                                List.of(company.address()),
                                List.of(),
                                List.of(),
                                null
                        )
                )
                .id();
    }

    private MaterialGood material(UUID ownerId, String code, String name, String measurementUnit) {
        return db.selectFrom(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.OWNER_ID.eq(ownerId),
                        isNotBlank(code)
                                ? MATERIAL_GOOD.CODE.eq(code)
                                : MATERIAL_GOOD.NAME.eq(name)
                )
                .fetchOptionalInto(MaterialGood.class)
                .orElseGet(() -> productsService.create(ownerId, new MaterialGood(null, null, null, false,
                                ownerId,
                                name,
                                "",
                                "",
                                null,
                                null,
                                null,
                                null,
                                new MaterialGood.Details(
                                        new BigDecimal(0),
                                        measurementUnits.get(measurementUnit.toLowerCase()),
                                        List.of(),
                                        List.of(),
                                        List.of(),
                                        false, null, null, null, null, null
                                )
                        ))
                );
    }

}
