package fabriqon.app.http.response;

import fabriqon.app.common.model.*;

import java.util.List;
import java.util.UUID;

public class Company {
    public final UUID id;
    public final fabriqon.app.common.model.Company.Type type;
    public final String name;
    public final String email;
    public final String identificationNumber;
    public final String taxIdentificationNumber;
    public final List<Address> addresses;
    public final List<ContactPerson> contacts;
    public final List<BankAccount> bankAccounts;
    public final String notes;
    public final List<File> files;

    public Company(UUID id, fabriqon.app.common.model.Company.Type type, String name, String email, String identificationNumber, String taxIdentificationNumber, List<Address> addresses, List<ContactPerson> contacts, List<BankAccount> bankAccounts, String notes, List<File> files) {
        this.id = id;
        this.type = type;
        this.name = name;
        this.email = email;
        this.identificationNumber = identificationNumber;
        this.taxIdentificationNumber = taxIdentificationNumber;
        this.addresses = addresses;
        this.contacts = contacts;
        this.bankAccounts = bankAccounts;
        this.notes = notes;
        this.files = files;
    }
}
