package fabriqon.app.config.security;

import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Component;

@Component
public class ClaimUtils {

    public String customClaim(Jwt jwt, CustomClaim claim) {
        return jwt.getClaimAsString(claim.claim);
    }

    public enum CustomClaim {
        ACCOUNT_ID("accountId");

        String claim;

        CustomClaim(String claim) {
            this.claim = claim;
        }
    }


}
