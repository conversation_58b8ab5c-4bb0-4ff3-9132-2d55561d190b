package fabriqon.app.config.security;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.accounts.AccountService;
import fabriqon.app.business.users.User;
import fabriqon.app.business.users.UserService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Component;

import java.util.UUID;

import static java.lang.String.format;

@Slf4j
@Component
public class AccountContext {

    private final AccountService accountService;
    private final UserService userService;
    private final ClaimUtils claimUtils;

    @Autowired
    public AccountContext(final AccountService accountService,
                          final UserService userService,
                          final ClaimUtils claimUtils) {
        this.accountService = accountService;
        this.userService = userService;
        this.claimUtils = claimUtils;
    }

    public Account account(Jwt jwt) {
        try {
            String accountId = claimUtils.customClaim(jwt, ClaimUtils.CustomClaim.ACCOUNT_ID);
            MDC.put("account_id", accountId);
            return accountService.load(UUID.fromString(accountId))
                    .orElseThrow(() -> {
                        log.info("could not load account");
                        return new BadCredentialsException(format("Cannot find account with id [%s]", accountId));
                    });
        } catch (Exception e) {
            log.error("can't load account", e);
            throw new BadCredentialsException("Can't find account");
        }
    }

    public UUID accountId(Jwt principal) {
        return account(principal).id();
    }

    public User user(Jwt jwt) {
        var user = userService.loadOrPersist(
                UUID.fromString(claimUtils.customClaim(jwt, ClaimUtils.CustomClaim.ACCOUNT_ID)),
                jwt.getClaimAsString("sub"));
        MDC.put("user_id", user.id().toString());
        return user;
    }

    public UUID userId(Jwt jwt) {
        return user(jwt).id();
    }
}
