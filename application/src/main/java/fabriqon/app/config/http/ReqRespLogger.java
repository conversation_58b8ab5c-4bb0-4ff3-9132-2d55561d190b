package fabriqon.app.config.http;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@Order(0)
@Slf4j
public class ReqRespLogger implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        HttpServletResponse res = (HttpServletResponse) response;
        log.info("request  [{}] [{}] [{}]", req.getMethod(), req.getRequestURI(), req.getQueryString());
        try {
            chain.doFilter(request, response);
        } finally {
            log.info("response [{}] [{}] [{}] [{}]", res.getStatus(), req.getMethod(), req.getRequestURI(), req.getQueryString());
            MDC.clear();
        }
    }
}

