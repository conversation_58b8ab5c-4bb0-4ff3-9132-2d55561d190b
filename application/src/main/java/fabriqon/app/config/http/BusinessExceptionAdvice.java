package fabriqon.app.config.http;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.exceptions.BusinessException;
import fabriqon.app.business.exceptions.ResourceNotFoundException;
import fabriqon.app.config.security.AccountContext;
import fabriqon.app.http.response.ErrorResponse;
import fabriqon.jooq.JooqJsonbFunctions;
import fabriqon.misc.Json;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.exception.NoDataFoundException;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.text.MessageFormat;
import java.util.*;

import static fabriqon.jooq.JooqJsonbFunctions.stringField;
import static fabriqon.jooq.classes.Tables.ACCOUNT;
import static fabriqon.jooq.classes.Tables.USERS;
import static org.jooq.impl.DSL.select;
import static org.springframework.http.HttpStatus.BAD_REQUEST;
import static org.springframework.http.HttpStatus.NOT_FOUND;

@Slf4j
@Order(Ordered.HIGHEST_PRECEDENCE + 1)
@ControllerAdvice
public class BusinessExceptionAdvice {

    final DSLContext db;

    final Map<String, ResourceBundle> loadedBundles = new HashMap<>();

    @Autowired
    public BusinessExceptionAdvice(DSLContext db) {
        this.db = db;
    }

    @ResponseStatus(BAD_REQUEST)
    @ResponseBody
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<?> businessException(BusinessException ex) {
        //todo: we should remove this as these are user errors
        log.info("got exception", ex);
        return ResponseEntity.badRequest().body(new ErrorResponse(ex.getMessage(), ex.code, getLocalizedMessage(ex.code, ex.params)));
    }

    @ResponseStatus(NOT_FOUND)
    @ResponseBody
    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<?> resourceNotFoundException(ResourceNotFoundException ex) {
        log.info("got exception", ex);
        return ResponseEntity.notFound().build();
    }

    @ResponseStatus(NOT_FOUND)
    @ResponseBody
    @ExceptionHandler(NoDataFoundException.class)
    public ResponseEntity<?> noDataFound(NoDataFoundException ex) {
        log.info("got exception", ex);
        return ResponseEntity.notFound().build();
    }


    private String getLocalizedMessage(String code, Object... args) {
        var language = Json.read(
                        db.select(stringField(ACCOUNT.SETTINGS, "general"))
                                .from(ACCOUNT)
                                .where(ACCOUNT.ID.eq(select(USERS.OWNER_ID).from(USERS).where(USERS.ID.eq(UUID.fromString(MDC.get("user_id"))))))
                                .fetchSingleInto(String.class), Account.Settings.General.class)
                .defaultLanguage();
        var bundle = loadedBundles.computeIfAbsent(language, l -> ResourceBundle.getBundle("BusinessExceptions", Locale.forLanguageTag(language)));
        return MessageFormat.format(bundle.getString(code), args);
    }

}
