package fabriqon.app.config.http;

import fabriqon.app.http.response.ErrorResponse;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.List;

import static org.springframework.http.HttpStatus.BAD_REQUEST;

@Order(Ordered.HIGHEST_PRECEDENCE)
@ControllerAdvice
public class ValidationAdvice {

    @ResponseStatus(BAD_REQUEST)
    @ResponseBody
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<?> methodArgumentNotValidException(MethodArgumentNotValidException ex) {
        BindingResult result = ex.getBindingResult();

        List<FieldError> fieldErrors = result.getFieldErrors();

        if (fieldErrors.size() == 1) {
            FieldError error = fieldErrors.get(0);
            String errorMessage = String.format("Field '%s' %s", error.getField(), error.getDefaultMessage());
            return ResponseEntity.badRequest().body(new ErrorResponse(errorMessage, "validation_error"));
        } else {
            StringBuilder errorMessage = new StringBuilder("Validation failed for fields: ");
            fieldErrors.forEach(error -> 
                errorMessage.append(String.format("'%s' %s; ", error.getField(), error.getDefaultMessage())));
            return ResponseEntity.badRequest().body(new ErrorResponse(errorMessage.toString().trim(), "validation_error"));
        }
    }

}
