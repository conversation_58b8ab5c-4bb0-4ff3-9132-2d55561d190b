package fabriqon.app.common.model;

import java.util.Set;

public record Address(
        String name,
        String country,
        String city,
        String state,
        String address1,
        String address2,
        String zip,
        Set<Type> types
) {

    @Override
    public String toString() {
        return address1 + (address2 != null ? address2 : "") + ", " + city + ", " + state + " " + zip + ", " + country;
    }

    public enum Type {BILLING, SHIPPING, OTHER}

    public static final Address EMPTY = new Address(null, null, null, null, null, null, null, Set.of());
}
