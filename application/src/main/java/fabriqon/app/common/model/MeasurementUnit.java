package fabriqon.app.common.model;

public enum MeasurementUnit {

    //length
    MM("mm"),
    CM("cm"),
    M("m"),
    KM("km"),

    //area
    CM2("cm2"),
    M2("m2"),

    //volume
    CM3("cm3"),
    M3("m3"),

    LITER("l"),
    MILLI_LITER("ml"),

    //weight
    G("g"),
    KG("kg"),
    TONNE("t"),

    PIECE("pcs"),
    SET("set"),
    ROLL("rol"),
    UN("UN"),//see what is this and remove if we can
    PACK("pac"),//maps to Pachet
    BOX("box"),//maps to Cutie
    BAG("bag"),//maps to Punga
    SACK("sack"),//maps to Sac
    PAIR("pair"),//maps to Pereche
    KWH("kwh"),//Kilowatt Hour
    HOUR("h"),//Hour
    MINUTE("min"),
    DAY("day"),
    CAN("can"),//maps to Doze
    SERVICE("service"),//maps to Serviciu
    PLATE("plate"),//maps to Placa
    PALLET("pallet"),//maps to Palet
    LINEAR_METER("linear_meter")//maps to ML


    ;

    private String symbol;

    MeasurementUnit(String symbol) {
        this.symbol = symbol;
    }

    public String symbol() {
        return symbol;
    }
}
