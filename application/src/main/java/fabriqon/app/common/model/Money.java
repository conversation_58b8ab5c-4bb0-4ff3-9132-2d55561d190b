package fabriqon.app.common.model;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.Currency;

public record Money(
        long amount,
        Currency currency
) {

    //TODO should be removed; see how to solve the cases where this would be required
    public static final Currency DEFAULT_CURRENCY = Currency.getInstance("RON");

    @Override
    public String toString() {
        return currency().getSymbol() + " "
                + BigDecimal.valueOf(amount).movePointLeft(currency.getDefaultFractionDigits());
    }

    public Money multiply(int v) {
        return new Money(amount * v, currency);
    }

    public Money multiply(BigDecimal v) {
        return new Money(v.multiply(BigDecimal.valueOf(amount)).longValue(), currency);
    }

    public Money divide(BigDecimal v) {
        return new Money(BigDecimal.valueOf(amount).setScale(4, RoundingMode.HALF_EVEN).divide(v, RoundingMode.HALF_EVEN).longValue(), currency);
    }

    public Money divide(int v) {
        return new Money(BigDecimal.valueOf(amount).setScale(4, RoundingMode.HALF_EVEN).divide(BigDecimal.valueOf(v), RoundingMode.HALF_EVEN).longValue(), currency);
    }

    public Money add(Money money) {
        return new Money(amount + money.amount, currency);
    }

    public Money add(long addedAmount) {
        return new Money(amount + addedAmount, currency);
    }

    public Money addNullSafe(Money money) {
        if (money == null) {
            return this;
        }
        return add(money);
    }

    public Money subtract(Money money) {
        return new Money(this.amount - money.amount, this.currency);
    }

    public static Money sum(Collection<Money> moneys) {
        if (moneys.isEmpty()) {
            return new Money(0, DEFAULT_CURRENCY);
        }
        return moneys.stream().reduce(new Money(0, moneys.iterator().next().currency), Money::add);
    }

    public String displayAmount() {
        return BigDecimal.valueOf(amount).movePointLeft(currency.getDefaultFractionDigits()).toString();
    }

    public BigDecimal toBigDecimal() {
        return BigDecimal.valueOf(amount);
    }
}
