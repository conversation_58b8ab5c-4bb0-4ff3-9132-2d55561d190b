package fabriqon.app.common.model;

import fabriqon.misc.Json;

import java.beans.ConstructorProperties;
import java.time.Instant;
import java.util.List;
import java.util.UUID;

public record Company(
        UUID id,
        Instant createTime,
        Instant updateTime,
        boolean deleted,

        UUID ownerId,
        Type type,
        String name,
        Details details
) {

    @ConstructorProperties({"id", "create_time", "update_time", "deleted", "owner_id", "company_type", "company_name", "details"})
    public Company(UUID id, Instant createTime, Instant updateTime, boolean deleted, UUID ownerId, Type type, String name, String details) {
        this(id, createTime, updateTime, deleted, ownerId, type, name, Json.read(details, Details.class));
    }

    public record Details(
            String identificationNumber,
            String taxIdentificationNumber,
            List<Address> addresses,
            List<ContactPerson> contacts,
            List<BankAccount> bankAccounts,
            String notes
    ) {
    }

    public enum Type {INDIVIDUAL, LOCAL_LEGAL_ENTITY, FOREIGN_LEGAL_ENTITY}

}
