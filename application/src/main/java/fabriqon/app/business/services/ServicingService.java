package fabriqon.app.business.services;

import fabriqon.Notification;
import fabriqon.ObjectStorage;
import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.accounts.AccountService;
import fabriqon.app.business.customers.Note;
import fabriqon.app.business.employees.Employee;
import fabriqon.app.business.exceptions.BusinessException;
import fabriqon.app.business.inventory.InventoryService;
import fabriqon.app.business.manufacturing.*;
import fabriqon.app.business.notifications.UserTaggingProcessor;
import fabriqon.app.business.sales.GoodsAccompanyingNote;
import fabriqon.app.business.sales.GoodsAccompanyingNotesService;
import fabriqon.app.business.sales.SalesOrder;
import fabriqon.app.business.sequence.Sequences;
import fabriqon.app.common.model.Address;
import fabriqon.app.common.model.Money;
import fabriqon.app.http.controllers.manufacturing.ServiceReportGenerator;
import fabriqon.events.Events;
import fabriqon.misc.Json;
import fabriqon.misc.MathUtils;
import fabriqon.misc.Tuple;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.Record2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static fabriqon.app.business.services.ServicingOrder.Status.IN_PROGRESS;
import static fabriqon.app.business.services.ServicingOrder.Status.SUBMITTED;
import static fabriqon.app.business.services.ServicingOrder.UNRANKED_ORDER_VALUE;
import static fabriqon.jooq.JooqJsonbFunctions.stringField;
import static fabriqon.jooq.classes.Tables.*;
import static java.lang.String.join;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.jooq.impl.DSL.count;
import static org.jooq.impl.DSL.max;

@Slf4j
@Component
@Transactional
public class ServicingService {

    private final AccountService accountService;
    private final Sequences sequences;
    private final DSLContext db;
    private final InventoryService inventory;
    private final ManufacturingOperationService manufacturingOperationService;
    private final MaterialIssueService materialIssueService;
    private final ObjectStorage objectStorage;
    private final GoodsAccompanyingNotesService goodsAccompanyingNotesService;
    private final ServiceReportGenerator serviceReportGenerator;
    private final ManufacturingService manufacturingService;
    private final UserTaggingProcessor userTaggingProcessor;

    private static final Map<String, Object> keyLocks = new ConcurrentHashMap<>();
    private final Events events;

    @Autowired
    public ServicingService(AccountService accountService, Sequences sequences, DSLContext db, InventoryService inventory,
                            ManufacturingOperationService manufacturingOperationService,
                            MaterialIssueService materialIssueService, ObjectStorage objectStorage,
                            GoodsAccompanyingNotesService goodsAccompanyingNotesService,
                            ServiceReportGenerator serviceReportGenerator, ManufacturingService manufacturingService, UserTaggingProcessor userTaggingProcessor, Events events) {
        this.sequences = sequences;
        this.db = db;
        this.inventory = inventory;
        this.manufacturingOperationService = manufacturingOperationService;
        this.materialIssueService = materialIssueService;
        this.objectStorage = objectStorage;
        this.accountService = accountService;
        this.goodsAccompanyingNotesService = goodsAccompanyingNotesService;
        this.serviceReportGenerator = serviceReportGenerator;
        this.manufacturingService = manufacturingService;
        this.userTaggingProcessor = userTaggingProcessor;
        this.events = events;
    }

    public ServicingOrder orderServicing(UUID ownerId, UUID salesOrderId,
                                         LocalDateTime executionDeadline,
                                         UUID serviceId, BigDecimal quantity,
                                         String notes, List<Tuple.Tuple2<String, ByteArrayInputStream>> files) {
        var orderId = UUID.randomUUID();
        var ranking = db.select(max(SERVICING_ORDER.RANKING))
                .from(SERVICING_ORDER)
                .where(SERVICING_ORDER.OWNER_ID.eq(ownerId))
                .fetchOptionalInto(Integer.class)
                .orElse(UNRANKED_ORDER_VALUE);
        var orderNumber = salesOrderId == null
                ? sequences.nextSequenceForServicingOrder(ownerId)
                : getNumberBySalesOrder(salesOrderId);
        db.insertInto(SERVICING_ORDER)
                .set(SERVICING_ORDER.ID, orderId)
                .set(SERVICING_ORDER.OWNER_ID, ownerId)
                .set(SERVICING_ORDER.SALES_ORDER_ID, salesOrderId)
                .set(SERVICING_ORDER.NUMBER, orderNumber)
                .set(SERVICING_ORDER.EXECUTION_DEADLINE, executionDeadline)
                .set(SERVICING_ORDER.STATUS, SUBMITTED.name())
                .set(SERVICING_ORDER.SERVICE_ID, serviceId)
                .set(SERVICING_ORDER.QUANTITY, quantity)
                .set(SERVICING_ORDER.RANKING, ++ranking)
                .set(SERVICING_ORDER.NOTES, notes)
                .set(SERVICING_ORDER.MATERIALS, JSONB.jsonb(Json.write(List.of())))
                .set(SERVICING_ORDER.OPERATIONS, JSONB.jsonb(Json.write(List.of())))
                .execute();

        files.forEach(f -> attach(ownerId, orderId, f.a(), f.b().readAllBytes()));
        return new ServicingOrder(
                orderId, null, null, false,
                ownerId, salesOrderId, orderNumber, executionDeadline, null, SUBMITTED,
                serviceId, quantity, ranking, notes, List.of(), List.of(),
                null, null, null, null, null
        );
    }

    public ServicingOrder updateServicingOrder(UUID ownerId, UUID orderId,
                                               UUID assignedTo, LocalDateTime productionDeadline,
                                               BigDecimal quantity,
                                               List<ManufacturingOperation> operations,
                                               List<ServicingOrder.Material> materials, String notes) {

        var ownershipCriteria = SERVICING_ORDER.ID.eq(orderId).and(SERVICING_ORDER.OWNER_ID.eq(ownerId));
        var order = order(ownerId, orderId);

        var status = db.select(SERVICING_ORDER.STATUS).from(SERVICING_ORDER).where(ownershipCriteria)
                .fetchSingleInto(ServicingOrder.Status.class);
        if (ServicingOrder.Status.CLOSED == status) {
            throw new BusinessException("Order cannot be modified in current status: " + status, "incorrect_status", status);
        }

        var updateStatement = db.update(SERVICING_ORDER)
                .set(SERVICING_ORDER.OWNER_ID, ownerId);
        if (assignedTo != null) {
            updateStatement = updateStatement.set(SERVICING_ORDER.ASSIGNED_TO, assignedTo);
        }
        if (productionDeadline != null) {
            updateStatement = updateStatement.set(SERVICING_ORDER.EXECUTION_DEADLINE, productionDeadline);
        }
        if (quantity != null) {
            updateStatement = updateStatement.set(SERVICING_ORDER.QUANTITY, quantity);
        }
        if (operations != null) {
            if (operations.stream().anyMatch(Objects::isNull)) {
                throw new BusinessException("operation is null for update", "operation_is_null");
            }
            updateStatement = updateStatement.set(SERVICING_ORDER.OPERATIONS, JSONB.valueOf(Json.write(operations)));
        }
        if (materials != null) {
            var hasDifferences = hasMaterialDifferences(order.materials(), materials);
            if (hasDifferences) {
                inventory.clearReservedForServicingOrder(ownerId, orderId);
                var notAvailableItems = inventory.reserveStockForServicingOrder(ownerId, orderId,
                        materials.stream()
                                .map(m -> new InventoryService.Stock(m.materialIds().getFirst(),
                                        m.usedQuantity() != null ? m.usedQuantity() : m.quantity()))
                                .toList());
                if (isNotEmpty(notAvailableItems)) {
                    throw new BusinessException("not enough stock to reserve", "not_enough_stock", join(", ", db.select(MATERIAL_GOOD.NAME).from(MATERIAL_GOOD).where(MATERIAL_GOOD.ID.in(notAvailableItems.keySet())).fetchInto(String.class)));
                }
                manufacturingService.reDistributeMaterials(ownerId);
            }
            updateStatement = updateStatement.set(SERVICING_ORDER.MATERIALS, JSONB.valueOf(Json.write(materials.stream()
                    .map(m -> {
                        if (m.usedQuantity() != null && m.quantity().compareTo(m.usedQuantity()) != 0) {
                            return m.setQuantity(MathUtils.max(m.usedQuantity(), m.quantity()));
                        }
                        return m;
                    })
                    .toList())));
        }
        if (notes != null) {
            updateStatement = updateStatement.set(SERVICING_ORDER.NOTES, notes);
        }

        updateStatement.where(SERVICING_ORDER.ID.eq(orderId), SERVICING_ORDER.OWNER_ID.eq(ownerId)).execute();
        return db.selectFrom(SERVICING_ORDER)
                .where(ownershipCriteria)
                .fetchSingleInto(ServicingOrder.class);
    }

    private boolean hasMaterialDifferences(List<ServicingOrder.Material> oldMaterials, List<ServicingOrder.Material> newMaterials) {
        //first check that all the materials from notes and others are present in the new materials
        if (oldMaterials.stream().anyMatch(m -> newMaterials.stream().noneMatch(p -> p.materialIds().getFirst().equals(m.materialIds().getFirst())))) {
            throw new RuntimeException("old materials are not present in the new materials");
        }
        if (oldMaterials.stream().map(m -> m.materialIds().getFirst()).collect(Collectors.toSet()).size()
                != newMaterials.stream().map(m -> m.materialIds().getFirst()).collect(Collectors.toSet()).size()) {
            return true;
        }
        var oldUsage = oldMaterials.stream().collect(Collectors.toMap(m -> m.materialIds().getFirst(), m -> m.usedQuantity() != null ? m.usedQuantity() : m.quantity()));
        var newUsage = newMaterials.stream().collect(Collectors.toMap(m -> m.materialIds().getFirst(), m -> m.usedQuantity() != null ? m.usedQuantity() : m.quantity()));
        return oldUsage.entrySet().stream().anyMatch(e -> !newUsage.containsKey(e.getKey()) || !e.getValue().equals(newUsage.get(e.getKey())));
    }

    public void deleteOrder(UUID ownerId, UUID orderId) {
        if (db.fetchCount(SERVICINGORDER_GOODSACCOMPANYINGNOTE, SERVICINGORDER_GOODSACCOMPANYINGNOTE.SERVICING_ORDER_ID.eq(orderId)) > 0) {
            throw new BusinessException("can't delete order as it has a goods accompanying note", "order_has_goods_accompanying_note");
        }
        inventory.clearReservedForServicingOrder(ownerId, orderId);
        db.select(SERVICINGORDER_FILE.FILE_ID)
                .from(SERVICINGORDER_FILE)
                .where(SERVICINGORDER_FILE.OWNER_ID.eq(ownerId), SERVICINGORDER_FILE.SERVICING_ORDER_ID.eq(orderId))
                .forEach(r -> {
                    db.deleteFrom(SERVICINGORDER_FILE)
                            .where(SERVICINGORDER_FILE.OWNER_ID.eq(ownerId),
                                    SERVICINGORDER_FILE.SERVICING_ORDER_ID.eq(orderId),
                                    SERVICINGORDER_FILE.FILE_ID.eq(r.value1()))
                            .execute();
                    objectStorage.delete(ownerId, r.value1());
                });
        db.deleteFrom(SERVICING_ORDER)
                .where(SERVICING_ORDER.OWNER_ID.eq(ownerId), SERVICING_ORDER.ID.eq(orderId))
                .execute();
    }

    public void inProgress(UUID ownerId, UUID manufacturingOrderId) {
        var order = order(ownerId, manufacturingOrderId);
        if (order.serviceId() == null) {
            throw new BusinessException("order is not a service. cannot change status", "not_a_service");
        }
        int updated = db.update(SERVICING_ORDER)
                .set(SERVICING_ORDER.STATUS, ServicingOrder.Status.IN_PROGRESS.name())
                .set(SERVICING_ORDER.RANKING, ServicingOrder.UNRANKED_ORDER_VALUE)//so it falls off the radar
                .where(SERVICING_ORDER.OWNER_ID.eq(ownerId), SERVICING_ORDER.ID.eq(manufacturingOrderId),
                        SERVICING_ORDER.STATUS.in(SUBMITTED.name()))
                .execute();
        if (updated != 1) {
            throw new BusinessException("order is not in the correct state", "invalid_order_state");
        }
    }

    public void executed(UUID ownerId, UUID orderId, String base64ClientSignature, String clientRepresentative, String base64WorkerSignature) {
        var order = order(ownerId, orderId);
        if (order.serviceId() == null) {
            throw new BusinessException("order is not a service. cannot change status", "not_a_service");
        }
        int updated = db.update(SERVICING_ORDER)
                .set(SERVICING_ORDER.STATUS, ServicingOrder.Status.EXECUTED.name())
                .set(SERVICING_ORDER.RANKING, ServicingOrder.UNRANKED_ORDER_VALUE)//so it falls off the radar
                .set(SERVICING_ORDER.BASE64_CLIENT_SIGNATURE, base64ClientSignature)
                .set(SERVICING_ORDER.CLIENT_REPRESENTATIVE, clientRepresentative)
                .set(SERVICING_ORDER.BASE64_WORKER_SIGNATURE, base64WorkerSignature)
                .where(SERVICING_ORDER.OWNER_ID.eq(ownerId), SERVICING_ORDER.ID.eq(orderId),
                        SERVICING_ORDER.STATUS.in(SUBMITTED.name(), IN_PROGRESS.name()))
                .execute();
        if (updated != 1) {
            throw new BusinessException("order is not in the correct state", "invalid_order_state");
        }
        events.publish(new ServiceOrderCompleted(new ServiceOrderCompleted.Data(
                ownerId, order.salesOrderId(), orderId
        )));
    }

    public MaterialIssueNote closed(UUID ownerId,
                                    UUID orderId,
                                    LocalDate date,
                                    String inventoryManager,
                                    String worker,
                                    List<UsedMaterial> usedMaterials) {

        db.update(SERVICING_ORDER)
                .set(SERVICING_ORDER.STATUS, ServicingOrder.Status.CLOSED.name())
                .set(SERVICING_ORDER.SERVICING_COSTS, JSONB.jsonb(Json.write(new ServicingOrder.ServicingCosts(
                        employeeRates(ownerId, orderId),
                        workstationRates(ownerId, orderId),
                        manufacturingOverheadPerEmployeeHour(ownerId)
                ))))
                .where(SERVICING_ORDER.OWNER_ID.eq(ownerId), SERVICING_ORDER.ID.eq(orderId))
                .execute();
        var order = order(ownerId, orderId);

        var salesOrderItem = salesOrder(ownerId, order.salesOrderId()).items().stream()
                .filter(item -> order.serviceId().equals(item.serviceId())).findFirst().orElseThrow();
        db.insertInto(EXECUTED_SERVICES)
                .set(EXECUTED_SERVICES.OWNER_ID, ownerId)
                .set(EXECUTED_SERVICES.SALES_ORDER_ID, order.salesOrderId())
                .set(EXECUTED_SERVICES.SERVICING_ORDER_ID, orderId)
                .set(EXECUTED_SERVICES.SERVICE_ID, order.serviceId())
                .set(EXECUTED_SERVICES.QUANTITY, order.quantity())
                .set(EXECUTED_SERVICES.SALE_AMOUNT, salesOrderItem.price().amount())
                .set(EXECUTED_SERVICES.SERVICE_CURRENCY, salesOrderItem.price().currency())
                .execute();
        events.publish(new ServiceOrderCompleted(new ServiceOrderCompleted.Data(ownerId, order.salesOrderId(), orderId)));

        inventory.clearReservedForServicingOrder(ownerId, orderId);
        var materialIssueNote = materialIssueService.issueForServicing(ownerId,
                orderId,
                date,
                inventoryManager,
                worker,
                usedMaterials,
                employeeAndWorkstationCosts(ownerId, orderId).add(manufacturingOverheadCosts(ownerId, orderId)));
        //we re-distribute any unused materials for MOs
        manufacturingService.reDistributeMaterials(ownerId);
        return materialIssueNote;
    }

    public void blocked(UUID ownerId, UUID orderId, ServicingOrder.BlockedReason blockedReason) {
        var updated = db.update(SERVICING_ORDER)
                .set(SERVICING_ORDER.STATUS, ServicingOrder.Status.BLOCKED.name())
                .set(SERVICING_ORDER.BLOCKED_REASON, blockedReason.name())
                .where(SERVICING_ORDER.OWNER_ID.eq(ownerId), SERVICING_ORDER.ID.eq(orderId))
                .execute();
        if (updated != 1) {
            throw new BusinessException("order is not in the correct state", "invalid_order_state");
        }
    }

    /**
     * Method used to change the ranking/priority of the  orders. This can only be performed on orders
     * that are still 'active' (i.e. not done).
     */
    public void applyRanking(UUID ownerId, List<UUID> orderIds) {
        if (db.fetchCount(SERVICING_ORDER,
                SERVICING_ORDER.STATUS.notIn(SUBMITTED.name(), ServicingOrder.Status.CUSTOMIZATION_NEEDED.name()),
                SERVICING_ORDER.ID.in(orderIds)) > 0) {
            throw new BusinessException("You can only reorder ranking for orders that are 'SUBMITTED'", "invalid_order_statuses_for_ranking");
        }
        rePlan(ownerId, filterChangedRanking(ownerId, orderIds));
    }

    public void assignTo(UUID ownerId, UUID orderId, UUID employeeId) {
        int updated = db.update(SERVICING_ORDER)
                .set(SERVICING_ORDER.ASSIGNED_TO, employeeId)
                .where(SERVICING_ORDER.OWNER_ID.eq(ownerId), SERVICING_ORDER.ID.eq(orderId))
                .execute();
        if (updated != 1) {
            throw new BusinessException("order is not in the correct state", "invalid_order_state");
        }
    }

    public Money employeeAndWorkstationCosts(UUID ownerId, UUID orderId) {
        var order = order(ownerId, orderId);
        var currency = accountService.defaultCurrency(ownerId);
        if (order.status() == ServicingOrder.Status.CUSTOMIZATION_NEEDED) {//in this case there are no tasks created so we need to calculate
            return new Money(manufacturingOperationService.enhance(order.operations()).stream()
                    .mapToLong(operation -> {
                        var value = BigDecimal.ZERO;
                        BigDecimal durationByHour = BigDecimal.valueOf(operation.durationInMinutes()).setScale(4, RoundingMode.HALF_EVEN)
                                .divide(BigDecimal.valueOf(60), RoundingMode.HALF_EVEN);
                        if (isNotEmpty((operation.candidateEmployeeIds()))) {
                            value = value.add(
                                    //todo: average the employee rate
                                    employeeHourlyRate(operation.candidateEmployeeIds().getFirst()).multiply(durationByHour)
                            );
                        }
                        if (isNotEmpty((operation.candidateWorkstationIds()))) {
                            value = value.add(
                                    //todo: average the workstation rate
                                    workstationHourlyRate(operation.candidateWorkstationIds().getFirst()).multiply(durationByHour)
                            );
                        }
                        return value.longValue();
                    })
                    .sum(),
                    currency);
        } else {
            var employeeRates = order.status() == ServicingOrder.Status.CLOSED && order.servicingCosts() != null
                    ? order.servicingCosts().employeeHourlyRates()
                    : employeeRates(ownerId, orderId);
            return new Money(order.operations()
                    .stream()
                    .mapToLong(operation -> {
                        var durationInHours = new BigDecimal(operation.durationInMinutes()).setScale(4, RoundingMode.HALF_EVEN).divide(new BigDecimal(60), RoundingMode.HALF_EVEN);
                        var employeeCosts = employeeRates.getOrDefault(order.assignedTo(), new Money(0, currency)).multiply(durationInHours);
                        return employeeCosts.amount();
                    })
                    .sum(),
                    currency);
        }
    }

    public Money manufacturingOverheadCosts(UUID ownerId, UUID orderId) {
        var order = order(ownerId, orderId);
        var manufacturingOverheadPerEmployeeHour = order.status() == ServicingOrder.Status.CLOSED && order.servicingCosts() != null
                ? order.servicingCosts().manufacturingOverheadPerEmployeeHour()
                : manufacturingOverheadPerEmployeeHour(ownerId);
        var currency = manufacturingOverheadPerEmployeeHour.currency();
        if (order.status() == ServicingOrder.Status.CUSTOMIZATION_NEEDED) {
            return new Money(manufacturingOperationService.enhance(order.operations()).stream()
                    .mapToLong(operation -> {
                        BigDecimal durationInHours = BigDecimal.valueOf(operation.durationInMinutes()).setScale(4, RoundingMode.HALF_EVEN)
                                .divide(BigDecimal.valueOf(60), RoundingMode.HALF_EVEN);
                        var overheadCosts = manufacturingOverheadPerEmployeeHour
                                .multiply(durationInHours);
                        return overheadCosts.amount();
                    }).sum(),
                    currency);
        } else {
            return new Money(order.operations()
                    .stream()
                    .mapToLong(operation -> {
                        var durationInHours = new BigDecimal(operation.durationInMinutes()).setScale(4, RoundingMode.HALF_EVEN).divide(new BigDecimal(60), RoundingMode.HALF_EVEN);
                        var overheadCosts = manufacturingOverheadPerEmployeeHour.multiply(durationInHours);
                        return overheadCosts.amount();
                    })
                    .sum(),
                    currency);
        }
    }

    public ServicingOrder attach(UUID ownerId, UUID orderId, String name, byte[] bytes) {
        var order = order(ownerId, orderId);//we do this here to validate access
        var fileId = objectStorage.store(ownerId, name, bytes);
        db.insertInto(SERVICINGORDER_FILE)
                .set(SERVICINGORDER_FILE.OWNER_ID, ownerId)
                .set(SERVICINGORDER_FILE.SERVICING_ORDER_ID, orderId)
                .set(SERVICINGORDER_FILE.FILE_ID, fileId)
                .execute();
        return order;
    }

    public ServicingOrder deleteFile(UUID ownerId, UUID orderId, UUID fileId) {
        var order = order(ownerId, orderId);//we do this here to validate access
        db.deleteFrom(SERVICINGORDER_FILE)
                .where(SERVICINGORDER_FILE.OWNER_ID.eq(ownerId), SERVICINGORDER_FILE.SERVICING_ORDER_ID.eq(orderId), SERVICINGORDER_FILE.FILE_ID.eq(fileId))
                .execute();
        objectStorage.delete(ownerId, fileId);
        return order;
    }

    public Note addNote(UUID ownerId, UUID orderId, UUID userId, String note) {
        var id = UUID.randomUUID();
        db.insertInto(NOTE)
                .set(NOTE.ID, id)
                .set(NOTE.OWNER_ID, ownerId)
                .set(NOTE.ADDED_BY_ID, userId)
                .set(NOTE.NOTE_, note)
                .execute();
        db.insertInto(SERVICING_ORDER_NOTE)
                .set(SERVICING_ORDER_NOTE.NOTE_ID, id)
                .set(SERVICING_ORDER_NOTE.SERVICING_ORDER_ID, orderId)
                .execute();
        userTaggingProcessor.notify(ownerId, Notification.Section.SERVICING, orderId, userId, id, note, number(orderId));
        return new Note(id, Instant.now(), Instant.now(), false, ownerId, userId, note);
    }

    public GoodsAccompanyingNote goodsAccompanyingNote(UUID ownerId, UUID orderId, LocalDate deliveryDate,
                                                       Address from, Address to, UUID delegateId,
                                                       String transportRegistrationNumber, String notes, List<GoodsAccompanyingNote.Item> items) {
        var order = order(ownerId, orderId);
        var note = goodsAccompanyingNotesService.create(ownerId, deliveryDate, from, to,
                delegateId, transportRegistrationNumber, notes,
                items.stream()
                        .map(item -> new GoodsAccompanyingNote.Item(item.id(), item.quantity(), inventory.getAverageCost(ownerId, item.id()), null))
                        .toList());
        db.insertInto(SERVICINGORDER_GOODSACCOMPANYINGNOTE)
                .set(SERVICINGORDER_GOODSACCOMPANYINGNOTE.SERVICING_ORDER_ID, orderId)
                .set(SERVICINGORDER_GOODSACCOMPANYINGNOTE.GOODS_ACCOMPANYING_NOTE_ID, note.id())
                .execute();

        //reserve materials for this servicing order; that means that we reserve all materials and if some are not available
        // we throw an exception; then we re-allocate the remainder to the MOs in their ranking order
        db.deleteFrom(RESERVED_INVENTORY)
                .where(RESERVED_INVENTORY.OWNER_ID.eq(ownerId),
                        RESERVED_INVENTORY.MANDATORY_FOR_ORDER.isFalse(),
                        RESERVED_INVENTORY.SALES_ORDER_ID.isNull()
                )
                .execute();
        var notAvailableItems = inventory.reserveStockForServicingOrder(ownerId, orderId,
                items.stream().map(item -> new InventoryService.Stock(item.id(), item.quantity())).toList());
        if (isNotEmpty(notAvailableItems)) {
            throw new BusinessException("not enough stock to reserve", "not_enough_stock", join(", ", db.select(MATERIAL_GOOD.NAME).from(MATERIAL_GOOD).where(MATERIAL_GOOD.ID.in(notAvailableItems.keySet())).fetchInto(String.class)));
        }
        //update the order material list
        db.update(SERVICING_ORDER)
                .set(SERVICING_ORDER.MATERIALS,
                        JSONB.jsonb(Json.write(
                                Stream.concat(order.materials().stream(),
                                        items.stream().map(item -> new ServicingOrder.Material(List.of(item.id()), item.quantity(), null)))
                                        .collect(Collectors.toMap(m -> m.materialIds().getFirst(), ServicingOrder.Material::quantity, BigDecimal::add))
                                        .entrySet().stream()
                                        .map(entry -> new ServicingOrder.Material(List.of(entry.getKey()), entry.getValue(), order.materials().stream().filter(m -> m.materialIds().contains(entry.getKey())).findFirst().map(ServicingOrder.Material::usedQuantity).orElse(null)))
                                .toList()
                        )))
                .where(SERVICING_ORDER.ID.eq(orderId), SERVICING_ORDER.OWNER_ID.eq(ownerId))
                .execute();

        manufacturingService.reDistributeMaterials(ownerId);
        return note;
    }

    public String previewGoodsAccompanyingNote(UUID ownerId, UUID orderId, LocalDate deliveryDate,
                                               Address from, Address to, UUID delegateId,
                                               String transportRegistrationNumber, String notes, List<GoodsAccompanyingNote.Item> items) {
        var order = order(ownerId, orderId);
        return goodsAccompanyingNotesService.html(order.salesOrderId(),
                new GoodsAccompanyingNote(
                        null, Instant.now(), null, false,
                        ownerId,
                        sequences.nextSequenceForGoodsAccompanyingNote(ownerId),
                        deliveryDate,
                        from, to,
                        delegateId,
                        transportRegistrationNumber,
                        notes,
                        items.stream()
                                .map(i -> new GoodsAccompanyingNote.Item(i.id(), i.quantity(), inventory.getAverageCost(ownerId, i.id()), null))
                                .toList()
                ));
    }

    public Tuple.Tuple3<String, String, Instant> serviceReport(UUID ownerId, UUID orderId) {
        return serviceReportHtml(ownerId, orderId, null, null, null);
    }

    public Tuple.Tuple3<String, String, Instant> previewServiceReport(UUID ownerId, UUID orderId, String base64ClientSignature, String clientRepresentative, String base64WorkerSignature) {
        return serviceReportHtml(ownerId, orderId, base64ClientSignature, clientRepresentative, base64WorkerSignature);
    }

    private void rePlan(UUID ownerId, Tuple.Tuple2<List<UUID>, Integer> orderIds) {
        synchronized (keyLocks.computeIfAbsent(ownerId.toString(), k -> new Object())) {
            log.info("changing ranking for servicing orders [{}]", ownerId);
            IntStream.range(0, orderIds.a().size()).forEach(
                    index -> db.update(SERVICING_ORDER)
                            .set(SERVICING_ORDER.RANKING, orderIds.b() + index)
                            .where(SERVICING_ORDER.ID.eq(orderIds.a().get(index)))
                            .execute()
            );
            log.info("finished replanning for [{}]", ownerId);
        }
    }


    /**
     * returns a sublist starting with the first order that is diverges from the original ranking.
     * this way we don't change anything for the first orders that didn't have a ranking change
     */
    private Tuple.Tuple2<List<UUID>, Integer> filterChangedRanking(UUID ownerId, List<UUID> orderIds) {
        var ordersRanked = db.select(SERVICING_ORDER.ID)
                .from(SERVICING_ORDER)
                .where(SERVICING_ORDER.OWNER_ID.eq(ownerId), SERVICING_ORDER.ID.in(orderIds))
                .orderBy(SERVICING_ORDER.RANKING.asc())
                .fetchInto(UUID.class);
        for (int i = 0; i < orderIds.size(); i++) {
            if (!(ordersRanked.get(i).equals(orderIds.get(i)))) {
                return Tuple.of(orderIds.subList(i, orderIds.size()), i);
            }
        }
        return Tuple.of(orderIds, 0);
    }

    public ServicingOrder order(UUID owner, UUID manufacturingOrder) {
        return db.selectFrom(SERVICING_ORDER)
                .where(SERVICING_ORDER.ID.eq(manufacturingOrder), SERVICING_ORDER.OWNER_ID.eq(owner))
                .fetchSingle().into(ServicingOrder.class);
    }

    private String getNumberBySalesOrder(UUID salesOrderId) {
        var salesOrderNumber = db.select(SALES_ORDER.NUMBER)
                .from(SALES_ORDER)
                .where(SALES_ORDER.ID.eq(salesOrderId))
                .fetchSingleInto(String.class);
        var servicingOrdersForSalesOrderCount = db.select(count())
                .from(SERVICING_ORDER)
                .where(SERVICING_ORDER.SALES_ORDER_ID.eq(salesOrderId))
                .fetchOptionalInto(Integer.class).orElse(0);

        return salesOrderNumber + " / " + ++servicingOrdersForSalesOrderCount;
    }

    private Money manufacturingOverheadPerEmployeeHour(UUID accountId) {
        var account = db.selectFrom(ACCOUNT)
                .where(ACCOUNT.ID.eq(accountId))
                .fetchSingleInto(Account.class);
        var overhead = account.settings().manufacturing().manufacturingOverheadPerEmployeeHour();
        return overhead != null ? overhead : new Money(0, account.settings().general().defaultCurrency());
    }

    //return all hourly rates for the employees that worked on this order
    private Map<UUID, Money> employeeRates(UUID ownerId, UUID orderId) {
        var order = order(ownerId, orderId);
        return db.select(USERS.ID, stringField(USERS.DETAILS, "hourlyRate"))
                .from(USERS)
                .where(USERS.OWNER_ID.eq(ownerId), USERS.ID.eq(order.assignedTo()))
                .fetchMap(Record2::value1, r -> isBlank(r.value2()) ? new Money(0, Money.DEFAULT_CURRENCY) : Json.read(r.value2(), Money.class));
    }

    //return all hourly rates for the employees that worked on this order
    private Map<UUID, Money> workstationRates(UUID ownerId, UUID orderId) {
        return db.select(MANUFACTURING_WORKSTATION.ID, stringField(MANUFACTURING_WORKSTATION.DETAILS, "costPerHour"))
                .from(MANUFACTURING_WORKSTATION)
                /*.where(MANUFACTURING_WORKSTATION.OWNER_ID.eq(ownerId), MANUFACTURING_WORKSTATION.ID.in(
                        select(MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_WORKSTATION_ID).from(MANUFACTURINGTASK_WORKSTATION)
                                .where(MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_TASK_ID.in(
                                        select(MANUFACTURING_TASK.ID).from(MANUFACTURING_TASK).where(MANUFACTURING_TASK.SERVICING_ORDER_ID.eq(orderId))
                                ))
                ))*///TODO
                .fetchMap(Record2::value1,
                        r -> isBlank(r.value2()) ? new Money(0, Money.DEFAULT_CURRENCY) : Json.read(r.value2(), Money.class));
    }

    private BigDecimal employeeHourlyRate(UUID id) {
        var employee = db.selectFrom(USERS)
                .where(USERS.ID.eq(id))
                .fetchSingleInto(Employee.class);
        return new BigDecimal(employee.details().hourlyRate() != null
                ? employee.details().hourlyRate().amount()
                : 0
        );
    }

    private BigDecimal workstationHourlyRate(UUID id) {
        var workstation = db.selectFrom(MANUFACTURING_WORKSTATION)
                .where(MANUFACTURING_WORKSTATION.ID.eq(id))
                .fetchSingleInto(Workstation.class);
        return new BigDecimal(workstation.details().costPerHour() != null
                ? workstation.details().costPerHour().amount()
                : 0
        );
    }

    private SalesOrder salesOrder(UUID ownerId, UUID salesOrder) {
        return db.selectFrom(SALES_ORDER)
                .where(SALES_ORDER.OWNER_ID.eq(ownerId), SALES_ORDER.ID.eq(salesOrder))
                .fetchSingleInto(SalesOrder.class);
    }


    private Tuple.Tuple3<String, String, Instant> serviceReportHtml(UUID ownerId, UUID orderId,
                                                                    String base64ClientSignature, String clientRepresentative, String base64WorkerSignature) {
        var order = order(ownerId, orderId);
        return Tuple.of(serviceReportGenerator.html(order, base64ClientSignature, clientRepresentative, base64WorkerSignature), order.number(), order.updateTime());
    }

    private String number(UUID orderId) {
        return db.select(SERVICING_ORDER.NUMBER)
                .from(SERVICING_ORDER)
                .where(SERVICING_ORDER.ID.eq(orderId))
                .fetchSingleInto(String.class);
    }

}
