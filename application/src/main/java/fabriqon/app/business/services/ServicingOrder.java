package fabriqon.app.business.services;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.type.TypeReference;
import fabriqon.app.business.manufacturing.ManufacturingOperation;
import fabriqon.app.common.model.Money;
import fabriqon.misc.Json;

import java.beans.ConstructorProperties;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public record ServicingOrder
        (
                UUID id,
                Instant createTime,
                Instant updateTime,
                boolean deleted,

                UUID ownerId,
                UUID salesOrderId,
                String number,
                LocalDateTime executionDeadline,
                UUID assignedTo,
                Status status,
                UUID serviceId,
                BigDecimal quantity,
                Integer ranking,
                String notes,
                List<Material> materials,
                List<ManufacturingOperation> operations,
                ServicingCosts servicingCosts,
                String base64ClientSignature,
                String clientRepresentative,
                String base64WorkerSignature,
                BlockedReason blockedReason
        ) {

    @ConstructorProperties({"id", "create_time", "update_time", "deleted", "owner_id", "sales_order_id", "number",
            "execution_deadline", "assigned_to", "status", "service_id", "quantity", "ranking", "notes",
            "materials", "operations", "servicing_costs", "base64_client_signature", "client_representative", "base64_worker_signature", "blocked_reason"})
    public ServicingOrder(UUID id, Instant createTime, Instant updateTime, boolean deleted, UUID ownerId,
                          UUID salesOrderId, String number, LocalDateTime executionDeadline, UUID assignedTo, Status status,
                          UUID serviceId, BigDecimal quantity,
                          Integer ranking, String notes, String materials,
                          String operations, String servicingCosts, String base64ClientSignature, String clientRepresentative, String base64WorkerSignature,
                          BlockedReason blockedReason) {
        this(id, createTime, updateTime, deleted, ownerId, salesOrderId, number, executionDeadline, assignedTo, status,
                serviceId, quantity, ranking, notes,
                Json.readNullSafe(materials, new TypeReference<List<Material>>() {
                }),
                Json.readNullSafe(operations, new TypeReference<List<ManufacturingOperation>>() {
                }),
                Json.readNullSafe(servicingCosts, ServicingCosts.class),
                base64ClientSignature,
                clientRepresentative,
                base64WorkerSignature,
                blockedReason
        );
    }

    public enum Status {
        CUSTOMIZATION_NEEDED,
        SUBMITTED,
        IN_PROGRESS,
        EXECUTED,
        CLOSED,
        BLOCKED
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public record Material(List<UUID> materialIds,
                           BigDecimal quantity,
                           BigDecimal usedQuantity) {

        public Material setUsedQuantity(BigDecimal usedQuantity) {
            return new Material(materialIds, quantity, usedQuantity);
        }

        public Material setQuantity(BigDecimal quantity) {
            return new Material(materialIds, quantity, usedQuantity);
        }
    }

    public record ServicingCosts(
            Map<UUID, Money> employeeHourlyRates,
            Map<UUID, Money> workstationHourlyRates,
            Money manufacturingOverheadPerEmployeeHour
    ) {
    }

    public static final int UNRANKED_ORDER_VALUE = -1;

    public enum BlockedReason {
        AWAITING_PARTS,
        AWAITING_CLIENT_APPROVAL,
        RESTRICTED_ACCESS,
        EQUIPMENT_FAILURE,
        AWAITING_TECHNICAL_DECISION
    }
}