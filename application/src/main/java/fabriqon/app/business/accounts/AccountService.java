package fabriqon.app.business.accounts;

import fabriqon.app.common.model.Money;
import fabriqon.misc.Json;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Currency;
import java.util.Optional;
import java.util.UUID;

import static fabriqon.app.business.accounts.Account.Settings.General.InventoryAccountingSettings.Method.FIFO;
import static fabriqon.jooq.classes.Tables.ACCOUNT;

@Component
@Transactional
public class AccountService {

    private final DSLContext db;

    @Autowired
    public AccountService(final DSLContext db) {
        this.db = db;
    }

    public Optional<Account> load(final UUID id) {
        return db.selectFrom(ACCOUNT)
                .where(ACCOUNT.ID.eq(id), ACCOUNT.DELETED.isFalse())
                .fetchOptionalInto(Account.class);
    }

    public Account.Settings.General.InventoryAccountingSettings.Method accountingMethod(UUID ownerId) {
        return load(ownerId).map(
                account -> account.settings() != null && account.settings().general().inventoryAccountingSettings() != null
                        ? account.settings().general().inventoryAccountingSettings().method() : FIFO
        ).orElseThrow(() -> new RuntimeException("account with id [" + ownerId + "] not found"));
    }

    public Currency defaultCurrency(UUID ownerId) {
        return load(ownerId).map(
                account -> account.settings() != null && account.settings().general().defaultCurrency() != null
                        ? account.settings().general().defaultCurrency() : Money.DEFAULT_CURRENCY
        ).orElseThrow(() -> new RuntimeException("account with id [" + ownerId + "] not found"));
    }

    public void updateAccountInformation(UUID accountId, Account.Information update) {
        db.update(ACCOUNT)
                .set(ACCOUNT.INFORMATION, JSONB.valueOf(Json.write(update)))
                .where(ACCOUNT.ID.eq(accountId))
                .execute();
    }

}
