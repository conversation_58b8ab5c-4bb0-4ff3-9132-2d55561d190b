package fabriqon.app.business.accounts;

import com.fasterxml.jackson.annotation.JsonEnumDefaultValue;
import fabriqon.app.common.model.Address;
import fabriqon.app.common.model.BankAccount;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.common.model.Money;
import fabriqon.misc.Json;

import java.beans.ConstructorProperties;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Currency;
import java.util.List;
import java.util.TimeZone;
import java.util.UUID;

public record Account(
        UUID id,
        Instant createTime,
        Instant updateTime,
        boolean deleted,

        String name,
        Information information,
        Settings settings,
        String logo//in base64
) {

    @ConstructorProperties({"id", "create_time", "update_time", "deleted", "name", "information", "settings", "logo"})
    public Account(UUID id, Instant createTime, Instant updateTime, boolean deleted, String name, String information, String settings, String logo) {
        this(id, createTime, updateTime, deleted, name, Json.read(information, Information.class), Json.read(settings, Settings.class), logo);
    }

    public record Information(
            Integer socialCapital,
            boolean includeSocialCapitalOnDocuments,
            String identificationNumber,
            boolean includeIdentificationNumberOnDocuments,
            String taxIdentificationNumber,
            boolean includeTaxIdentificationNumberOnDocuments,
            Address address,
            boolean includeAddressOnDocuments,
            String phone,
            boolean includePhoneOnDocuments,
            String email,
            boolean includeEmailOnDocuments,
            List<BankAccount> bankAccounts,
            List<Boolean> includeBankAccountOnDocuments
    ) {
    }

    public record Settings(
            General general,
            List<MeasurementUnit> enabledMeasurementUnits,
            BigDecimal defaultVAT,
            Manufacturing manufacturing
    ) {

        public record General(
                InventoryAccountingSettings inventoryAccountingSettings,
                Integer defaultDeliveryTimeForSalesOrders,//in days
                String defaultLanguage,
                Currency defaultCurrency,
                TimeZone defaultTimeZone
        ) {
            public record InventoryAccountingSettings(
                    Method method,
                    UnitDesignations unitDesignations
            ) {

                public enum Method {
                    //TODO clean this deserialization hack up
                    @JsonEnumDefaultValue FIFO,
                    WEIGHTED_AVERAGE
                }

                public record UnitDesignations(
                        UUID defaultInventoryUnit
                ) {
                }
            }
        }

        public record Manufacturing(
                LocalTime workDayStartTime,
                LocalTime workDayEndTime,
                List<Integer> workingDays,
                Money manufacturingOverheadPerEmployeeHour,
                Money administrativeOverheadPerEmployeeHour
        ) {
        }

    }

    public LocalDateTime nowForAccount() {
        return LocalDateTime.now(settings.general().defaultTimeZone().toZoneId());
    }
}
