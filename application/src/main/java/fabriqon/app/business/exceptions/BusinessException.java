package fabriqon.app.business.exceptions;

/**
 * Extension of exception with a <i>code</i> that can be relied on to translate errors in the backend to the frontend
 */
public class BusinessException extends RuntimeException {

    public final String code;
    public final Object[] params;

    public BusinessException(String message, String code, Object... params) {
        super(message);
        this.code = code;
        this.params = params;
    }

}
