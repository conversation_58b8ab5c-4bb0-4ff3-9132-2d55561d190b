package fabriqon.app.business.notifications;

import fabriqon.Notification;
import fabriqon.Notifier;
import fabriqon.Recipient;
import fabriqon.app.business.exceptions.ResourceNotFoundException;
import fabriqon.misc.Json;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.*;

@Component
@Transactional
public class NotificationService {

    final DSLContext db;
    final Notifier notifier;

    @Autowired
    public NotificationService(DSLContext db,
                               Notifier notifier) {
        this.db = db;
        this.notifier = notifier;
    }

    public Notification create(UUID ownerId,
                               Notification.Type type,
                               Notification.Section section,
                               UUID targetEntityId,
                               String triggeredBy,
                               Map<String, Object> details
    ) {
        return create(ownerId, type, section, targetEntityId, triggeredBy, details, null);
    }

    public Notification create(UUID ownerId,
                               Notification.Type type,
                               Notification.Section section,
                               UUID targetEntityId,
                               String triggeredBy,
                               Map<String, Object> details,
                               UUID targetUserId
    ) {
        var id = UUID.randomUUID();
        db.insertInto(NOTIFICATION)
                .set(NOTIFICATION.ID, id)
                .set(NOTIFICATION.OWNER_ID, ownerId)
                .set(NOTIFICATION.TYPE, type.name())
                .set(NOTIFICATION.SECTION, section.name())
                .set(NOTIFICATION.TARGET_ENTITY_ID, targetEntityId)
                .set(NOTIFICATION.TRIGGERED_BY, triggeredBy)
                .set(NOTIFICATION.TARGET_USER_ID, targetUserId)
                .set(NOTIFICATION.DETAILS, JSONB.jsonb(Json.write(details)))
                .execute();
        var notification = new Notification(id, Instant.now(), Instant.now(), false, ownerId, type, section,
                targetEntityId, null, null, triggeredBy, details, targetUserId);
        notifier.send(notification, targetUserId != null ? List.of(new Recipient(targetUserId)) : recipients(notification));
        return notification;
    }

    public void resolvedBy(UUID ownerId, UUID id, UUID userId) {
        db.update(NOTIFICATION)
                .set(NOTIFICATION.RESOLVED_BY, userId)
                .set(NOTIFICATION.RESOLVED_AT, LocalDateTime.now())
                .where(NOTIFICATION.OWNER_ID.eq(ownerId), NOTIFICATION.ID.eq(id))
                .execute();
        var notification = new Notification(id, null, null, false, ownerId,
                Notification.Type.UPDATE, Notification.Section.NOTIFICATION, id, null, null, null, null, null);
        notifier.send(notification, recipients(notification).stream().filter(r -> !userId.equals(r.userId())).toList());
    }

    public void readBy(UUID ownerId, UUID id, UUID userId) {
        //check access
        if (db.fetchCount(NOTIFICATION, NOTIFICATION.OWNER_ID.eq(ownerId).and(NOTIFICATION.ID.eq(id))) != 1) {
            throw new ResourceNotFoundException("notification not found", "notification_not_found");
        }
        db.insertInto(NOTIFICATION_READ_BY)
                .set(NOTIFICATION_READ_BY.NOTIFICATION_ID, id)
                .set(NOTIFICATION_READ_BY.USER_ID, userId)
                .onConflictDoNothing()
                .execute();
    }

    private List<Recipient> recipients(Notification notification) {
        //todo: we'll have to consider the notification props but for now we go with users only
        // (and not employees) for the first use case
        return db.select(USERS.ID).from(USERS).where(USERS.OWNER_ID.eq(notification.ownerId()))
                .fetchInto(UUID.class)
                .stream().map(Recipient::new).toList();
    }

}
