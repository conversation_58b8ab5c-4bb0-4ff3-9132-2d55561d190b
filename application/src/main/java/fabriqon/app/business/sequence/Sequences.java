package fabriqon.app.business.sequence;

import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.SEQUENCE;

@Slf4j
@Component
@Transactional
public class Sequences {

    private static final Map<Sequence.Section, String> defaultPatterns = new HashMap<>(2);

    static {
        defaultPatterns.put(Sequence.Section.INVENTORY_ADJUSTMENT_ORDER, "AS-0000");
        defaultPatterns.put(Sequence.Section.SALES_ORDER, "CV-0000");
        defaultPatterns.put(Sequence.Section.MANUFACTURING_ORDER, "CP-0000");
        defaultPatterns.put(Sequence.Section.SERVICING_ORDER, "CS-0000");
        defaultPatterns.put(Sequence.Section.PURCHASE_ORDER, "CA-0000");
        defaultPatterns.put(Sequence.Section.INVOICE, "FA-0000");
        defaultPatterns.put(Sequence.Section.INVOICE_PROFORMA, "FP-0000");
        defaultPatterns.put(Sequence.Section.RECEPTION_RECEIPT, "NIR-0000");
        defaultPatterns.put(Sequence.Section.MATERIAL_ISSUE_NOTE, "BC-0000");
        defaultPatterns.put(Sequence.Section.GOODS_ACCOMPANYING_NOTE, "AVIZ-0000");
    }

    private final DSLContext db;

    @Autowired
    public Sequences(final DSLContext db) {
        this.db = db;
    }

    public String nextSequenceForSalesOrder(UUID owner) {
        return getNextSequenceFor(owner, Sequence.Section.SALES_ORDER);
    }

    public String nextSequenceForManufacturingOrder(UUID owner) {
        return getNextSequenceFor(owner, Sequence.Section.MANUFACTURING_ORDER);
    }

    public String nextSequenceForServicingOrder(UUID owner) {
        return getNextSequenceFor(owner, Sequence.Section.SERVICING_ORDER);
    }

    public String nextSequenceForPurchaseOrder(UUID owner) {
        return getNextSequenceFor(owner, Sequence.Section.PURCHASE_ORDER);
    }

    public String nextSequenceForInventoryAdjustmentOrder(UUID owner) {
        return getNextSequenceFor(owner, Sequence.Section.INVENTORY_ADJUSTMENT_ORDER);
    }

    public String nextSequenceForInvoice(UUID owner) {
        return getNextSequenceFor(owner, Sequence.Section.INVOICE);
    }

    public String nextSequenceForProformaInvoice(UUID owner) {
        return getNextSequenceFor(owner, Sequence.Section.INVOICE_PROFORMA);
    }

    public String nextSequenceForReceptionReceipt(UUID owner) {
        return getNextSequenceFor(owner, Sequence.Section.RECEPTION_RECEIPT);
    }

    public String nextSequenceForMaterialIssueNote(UUID owner) {
        return getNextSequenceFor(owner, Sequence.Section.MATERIAL_ISSUE_NOTE);
    }

    public String nextSequenceForGoodsAccompanyingNote(UUID owner) {
        return getNextSequenceFor(owner, Sequence.Section.GOODS_ACCOMPANYING_NOTE);
    }

    private String getNextSequenceFor(UUID owner, Sequence.Section section) {
        return increment(loadForUpdate(owner, section)
                .orElseGet(() -> createDefault(owner, section)))
                .render();
    }

    private Sequence increment(final Sequence sequence) {
        final Sequence updated = sequence.setValue(sequence.value() + 1);
        final int updatedRows = db.update(SEQUENCE)
                .set(SEQUENCE.VALUE, updated.value())
                .where(SEQUENCE.ID.eq(updated.id()))
                .execute();
        if (updatedRows != 1) {
            throw new RuntimeException("Couldn't increment sequence [" + sequence.id() + "].");
        }
        return updated;
    }

    private Optional<Sequence> loadForUpdate(final UUID ownerId, final Sequence.Section section) {
        return db.selectFrom(SEQUENCE)
                .where(SEQUENCE.OWNER_ID.eq(ownerId)
                        .and(SEQUENCE.SECTION.eq(section.name()))
                        .and(SEQUENCE.DELETED.isFalse())
                )
                .forUpdate()
                .fetchOptionalInto(Sequence.class);
    }

    private Sequence createDefault(final UUID owner, final Sequence.Section section) {
        final Sequence defaultSequence = new Sequence(UUID.randomUUID(), null, null, false,
                owner, section, defaultPatterns.getOrDefault(section, "00000000"), 0L);
        db.insertInto(SEQUENCE)
                .set(SEQUENCE.ID, defaultSequence.id())
                .set(SEQUENCE.OWNER_ID, defaultSequence.ownerId())
                .set(SEQUENCE.SECTION, defaultSequence.section().name())
                .set(SEQUENCE.PATTERN, defaultSequence.pattern())
                .set(SEQUENCE.VALUE, defaultSequence.value())
                .execute();
        return defaultSequence;
    }

}
