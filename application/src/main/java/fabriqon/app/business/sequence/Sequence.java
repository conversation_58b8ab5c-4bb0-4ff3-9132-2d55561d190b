package fabriqon.app.business.sequence;

import org.apache.commons.lang3.StringUtils;

import java.time.Instant;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public record Sequence(
        UUID id,
        Instant createTime,
        Instant updateTime,
        boolean deleted,

        UUID ownerId,
        Section section,
        String pattern,
        Long value
) {

    private static final Pattern NUMBER_PATTERN = Pattern.compile("([0-9]+)");

    public Sequence setValue(long newValue) {
        return new Sequence(
                this.id,
                this.createTime,
                this.updateTime,
                this.deleted,
                this.ownerId,
                this.section,
                this.pattern,
                newValue
        );
    }

    public String render() {
        Matcher matcher = NUMBER_PATTERN.matcher(pattern);

        String newSequence;
        String initial;
        if (matcher.find()) {
            do {
                initial = matcher.group();
            } while (matcher.find());
            newSequence = pattern;
        } else {
            throw new RuntimeException("Incorrect pattern for sequence.");
        }

        long nextValue = Long.parseLong(initial) + value;

        return newSequence.replace(initial, StringUtils.leftPad(String.valueOf(nextValue), initial.length(), "0"));
    }

    public enum Section {
        INVENTORY_ADJUSTMENT_ORDER,
        SALES_ORDER,
        MANUFACTURING_ORDER,
        SERVICING_ORDER,
        PURCHASE_ORDER,
        INVOICE,
        INVOICE_PROFORMA,
        RECEPTION_RECEIPT,
        MATERIAL_ISSUE_NOTE,
        GOODS_ACCOMPANYING_NOTE,
    }


}
