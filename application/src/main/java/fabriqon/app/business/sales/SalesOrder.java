package fabriqon.app.business.sales;

import com.fasterxml.jackson.core.type.TypeReference;
import fabriqon.app.business.common.model.GlobalDiscount;
import fabriqon.app.business.financial.VatCalculatorProvider;
import fabriqon.app.common.model.Address;
import fabriqon.app.common.model.Money;
import fabriqon.misc.Json;

import java.beans.ConstructorProperties;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Currency;
import java.util.List;
import java.util.UUID;

public record SalesOrder
        (
                UUID id,
                Instant createTime,
                Instant updateTime,
                boolean deleted,

                UUID ownerId,
                UUID customerId,
                String number,
                Integer ranking,
                LocalDateTime deliveryDeadline,
                Status status,
                List<Item> items,
                Address shippingAddress,
                String notes,
                String customerNotes,
                LocalDate offerDate,
                LocalDate offerExpiration,
                RenderingDetails renderingDetails,
                GlobalDiscount globalDiscount
        ) {

    @ConstructorProperties({"id", "create_time", "update_time", "deleted", "owner_id", "customer_id", "number", "ranking",
            "delivery_deadline", "status", "items", "shippingAddress", "notes", "customer_notes", "offer_date", "offer_expiration", "rendering_details", "global_discount"})
    public SalesOrder(UUID id, Instant createTime, Instant updateTime, boolean deleted, UUID ownerId, UUID customerId,
                      String number, Integer ranking, LocalDateTime deliveryDeadline, Status status, String items,
                      String shippingAddress, String notes, String customerNotes, LocalDate offerDate, LocalDate offerExpiration, String renderingDetails, String globalDiscount) {
        this(id, createTime, updateTime, deleted, ownerId, customerId, number, ranking,  deliveryDeadline, status,
                Json.read(items, new TypeReference<List<Item>>() {}),
                Json.read(shippingAddress, Address.class),
                notes, customerNotes,
                offerDate, offerExpiration,
                Json.readNullSafe(renderingDetails, RenderingDetails.class),
                Json.readNullSafe(globalDiscount, GlobalDiscount.class)
        );
    }

    public static final int UNRANKED_ORDER_VALUE = -1;

    public SalesOrder setRenderingDetails(RenderingDetails renderingDetails) {
        return new SalesOrder(id, createTime, updateTime, deleted, ownerId, customerId, number, ranking, deliveryDeadline, status, items, shippingAddress, notes, customerNotes, offerDate, offerExpiration, renderingDetails, globalDiscount);
    }

    public SalesOrder setCustomerNotes(String customerNotes) {
        return new SalesOrder(id, createTime, updateTime, deleted, ownerId, customerId, number, ranking, deliveryDeadline, status, items, shippingAddress, notes, customerNotes, offerDate, offerExpiration, renderingDetails, globalDiscount);
    }

    public boolean hasGlobalDiscount() {
        return globalDiscount != null && globalDiscount.applicable();
    }

    public Currency currency() {
        return items.stream()
                .filter(item -> item.price != null)
                .map(item -> item.price.currency())
                .findFirst()
                .orElseThrow(() -> new RuntimeException("No currency found in invoice items"));
    }

    public Money discount() {
        return hasGlobalDiscount()
                ? globalDiscount.amount() != null ? globalDiscount.amount() : subTotal().multiply(globalDiscount.percentage())
                : discountFromItems();
    }

    private Money discountFromItems() {
        return items.stream()
                .filter(item -> item.discount() != null)
                .map(item -> item.grossValue().multiply(item.discount()))
                .reduce(Money::add)
                .orElse(new Money(0, currency()));

    }

    public Money vat(VatCalculatorProvider.Calculator vatCalculator) {
        return hasGlobalDiscount() ? globalVat(vatCalculator) : perItemVat(vatCalculator);
    }

    private Money globalVat(VatCalculatorProvider.Calculator vatCalculator) {
        var globalDiscountPercentage = globalDiscountPercentage();
        return new Money(items.stream()
                .map(item -> vatCalculator.calculate(item.grossValue().subtract(item.grossValue().multiply(globalDiscountPercentage)), vatCalculator.itemVatRate(item.productId())))
                .mapToLong(v -> v)
                .sum(), currency());
    }

    public BigDecimal globalDiscountPercentage() {
        if (globalDiscount == null || !globalDiscount.applicable()) {
            return null;
        }
        var subTotal = subTotal();
        return globalDiscount.amount() != null
                ? BigDecimal.valueOf(globalDiscount.amount().amount()).divide(BigDecimal.valueOf(subTotal.amount()), 6, RoundingMode.HALF_EVEN)
                : globalDiscount.percentage();
    }

    private Money perItemVat(VatCalculatorProvider.Calculator vatCalculator) {
        return items.stream()
                .map(item -> item.vatValue(vatCalculator, globalDiscountPercentage()))
                .reduce(Money::add)
                .orElse(new Money(0, currency()));
    }

    public Money subTotal() {
        return items.stream()
                .map(SalesOrder.Item::grossValue)
                .reduce(Money::add)
                .orElse(new Money(0, currency()));
    }

    public Money total(VatCalculatorProvider.Calculator vatCalculator, boolean includeVat) {
        var v = subTotal().subtract(discount());
        return includeVat ? v.add(vat(vatCalculator)) : v;
    }

    public enum Status {
        IN_QUOTATION,
        QUOTE_SENT,
        QUOTE_CANCELED,
        CANCELED,
        SUBMITTED,
        PROCESSING,
        PICKING_PACKING,
        READY_TO_SHIP,
        SHIPPING,
        DELIVERED,
        ON_HOLD
    }

    /**
     * If product id is specified it represents a product otherwise a service
     */
    public record Item(
            UUID productId, UUID serviceId, BigDecimal quantity, Money price, BigDecimal discount, String customizationNote,
            List<Customization> customizations, Boolean addedToWishlist
    ) {
        public record Customization(
                UUID materialId,
                BigDecimal quantity
        ) {}

        public Item addedToWishlist(Boolean addedToWishlist) {
            return new Item(productId, serviceId, quantity, price, discount, customizationNote,
                    customizations, addedToWishlist
            );
        }

        public Money grossValue() {
            return price.multiply(quantity);
        }

        public Money discountValue() {
            return discount != null ? grossValue().multiply(discount) : new Money(0, price.currency());
        }

        public Money vatValue(VatCalculatorProvider.Calculator vatCalculator, BigDecimal globalDiscountPercentage) {
            var discountValue = globalDiscountPercentage != null ? grossValue().multiply(globalDiscountPercentage) : discountValue();
            return new Money(vatCalculator.calculate(grossValue().subtract(discountValue), vatCalculator.itemVatRate(productId)), price.currency());
        }

    }

    public record RenderingDetails(
            String language,
            List<HideableColumn> columnsToHide
    ) {
        public enum HideableColumn { DISCOUNT, VAT_COLUMNS }
    }

}
