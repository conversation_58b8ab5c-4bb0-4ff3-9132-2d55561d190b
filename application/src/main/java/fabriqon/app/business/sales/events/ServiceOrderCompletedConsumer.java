package fabriqon.app.business.sales.events;

import fabriqon.app.business.sales.SalesService;
import fabriqon.app.business.services.ServiceOrderCompleted;
import fabriqon.events.EventConsumer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ServiceOrderCompletedConsumer extends EventConsumer<ServiceOrderCompleted> {

    private final SalesService salesService;

    @Autowired
    public ServiceOrderCompletedConsumer(SalesService salesService) {
        this.salesService = salesService;
    }

    @Override
    public void process(ServiceOrderCompleted event) {
        salesService.tryMarkOrderAsDelivered(event.data.ownerId(), event.data.salesOrderId(), false);
    }

}
