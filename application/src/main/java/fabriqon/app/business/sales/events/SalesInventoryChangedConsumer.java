package fabriqon.app.business.sales.events;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.inventory.InventoryChangedEvent;
import fabriqon.app.business.inventory.InventoryEntry;
import fabriqon.app.business.inventory.InventoryService;
import fabriqon.app.business.inventory.ManufacturingContext;
import fabriqon.app.business.sales.SalesOrder;
import fabriqon.app.business.sales.SalesService;
import fabriqon.events.EventConsumer;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;
import java.util.Queue;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;

import static fabriqon.app.business.sales.SalesOrder.Status.*;
import static fabriqon.jooq.JooqJsonbFunctions.arrayContainsJson;
import static fabriqon.jooq.classes.Tables.*;
import static fabriqon.misc.MathUtils.min;
import static java.lang.String.format;
import static org.jooq.impl.DSL.coalesce;
import static org.jooq.impl.DSL.sum;

@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class SalesInventoryChangedConsumer extends EventConsumer<InventoryChangedEvent> {

    private final DSLContext db;
    private final InventoryService inventory;
    private final SalesService salesService;

    @Autowired
    public SalesInventoryChangedConsumer(DSLContext db, InventoryService inventory, SalesService salesService) {
        this.db = db;
        this.inventory = inventory;
        this.salesService = salesService;
    }

    @Override
    public void process(InventoryChangedEvent event) {
        var inventoryEntry = inventoryEntry(event.data.ownerId(), event.data.inventoryEntry());
        //check if this affects the sales orders
        if (inventoryEntry.salesOrderId() != null) {
            return;//no processing required as this only registers that the entry was used for a sales order
        }
        if (!inventorySettings(event.data.ownerId()).unitDesignations().defaultInventoryUnit().equals(inventoryEntry.unitId())) {
            return;//no processing required as it is not for the sales inventory type
        }
        if (inventoryEntry.quantity().compareTo(BigDecimal.ZERO) > 0) {
            if (event.data.manufacturingContext() != null) {
                handleManufacturedProduct(inventoryEntry, event.data.manufacturingContext());
            } else {
                distributeStockForOrders(inventoryEntry.ownerId(), inventoryEntry.materialGoodId(), inventoryEntry.quantity());
            }
        } else {
            var availableStock = inventory.getCurrentStock(inventoryEntry.ownerId(), inventoryEntry.materialGoodId(), inventoryEntry.unitId());
            var reservedStock = inventory.getReservedStock(inventoryEntry.ownerId(), inventoryEntry.materialGoodId(), inventoryEntry.unitId());
            var unassigned = availableStock.subtract(reservedStock);
            if (unassigned.compareTo(BigDecimal.ZERO) < 0) {
                removeReservedStockForOrders(inventoryEntry, unassigned);
            }
        }
    }

    private void distributeStockForOrders(UUID ownerId, UUID productId, BigDecimal quantity) {
        AtomicReference<BigDecimal> available = new AtomicReference<>(quantity);
        //get orders that need the product in ranking order
        Queue<SalesOrder> orders = new LinkedList<>(db.selectFrom(SALES_ORDER)
                .where(SALES_ORDER.DELETED.isFalse()
                        .and(SALES_ORDER.OWNER_ID.eq(ownerId))
                        .and(SALES_ORDER.STATUS.in(SUBMITTED.name(), PROCESSING.name()))
                        .and(arrayContainsJson(SALES_ORDER.ITEMS, format("[{\"productId\": \"%s\"}]", productId)))
                )
                .orderBy(SALES_ORDER.RANKING.asc())
                .fetchInto(SalesOrder.class));

        while (available.get().compareTo(BigDecimal.ZERO) > 0 && !orders.isEmpty()) {
            var order = orders.remove();
            var reservedQuantity = reservedQuantity(order, productId);
            var requiredQuantity = requiredQuantity(order, productId);
            if (requiredQuantity.compareTo(reservedQuantity) > 0) {
                //reserve what is available
                var newlyReserved = min(available.get(), requiredQuantity.subtract(reservedQuantity));
                inventory.reserveStockForSalesOrder(order.ownerId(), order.id(),
                        List.of(new InventoryService.Stock(productId, newlyReserved)));
                available.set(available.get().subtract(newlyReserved));
                if (salesService.allItemsAvailable(order.ownerId(), order.id()) && order.items().stream().anyMatch(item -> item.productId() != null)) {
                    salesService.setPickAndPackStatus(order.ownerId(), order.id());
                }
            }
        }
    }

    private void removeReservedStockForOrders(InventoryEntry inventoryEntry, BigDecimal toRemove) {
        //get orders that need the product in ranking order
        Queue<SalesOrder> orders = new LinkedList<>(db.selectFrom(SALES_ORDER)
                .where(SALES_ORDER.DELETED.isFalse()
                        .and(SALES_ORDER.OWNER_ID.eq(inventoryEntry.ownerId()))
                        .and(SALES_ORDER.STATUS.in(SUBMITTED.name(), PROCESSING.name(), PICKING_PACKING.name(), READY_TO_SHIP.name()))
                        .and(arrayContainsJson(SALES_ORDER.ITEMS, format("[{\"productId\": \"%s\"}]", inventoryEntry.materialGoodId())))
                )
                .orderBy(SALES_ORDER.RANKING.desc())
                .fetchInto(SalesOrder.class));

        while (toRemove.compareTo(BigDecimal.ZERO) < 0 && !orders.isEmpty()) {
            var order = orders.remove();
            var reservedQuantity = reservedQuantity(order, inventoryEntry.materialGoodId());
            if (reservedQuantity.compareTo(BigDecimal.ZERO) > 0) {
                var toRemoveReserved = min(toRemove.abs(), reservedQuantity);
                inventory.clearReserved(order.id(), null, order.ownerId(),
                        new InventoryService.Stock(inventoryEntry.materialGoodId(), toRemoveReserved));
                toRemove = toRemove.add(toRemoveReserved);
                if (order.status() == READY_TO_SHIP || order.status() == PICKING_PACKING) {
                    //we reset the status to PROCESSING as the order doesn't have the necessary stock reserved anymore
                    db.update(SALES_ORDER)
                            .set(SALES_ORDER.STATUS, PROCESSING.name())
                            .where(SALES_ORDER.ID.eq(order.id()))
                            .execute();
                }
            }
        }
    }

    private void handleManufacturedProduct(InventoryEntry inventoryEntry, ManufacturingContext context) {
        // Case 1: Product manufactured for parent manufacturing order; handled by ManufacturingInventoryChangedConsumer
        if (context.parentManufacturingOrderId() != null) {
            return;
        }

        // Case 2: Product manufactured for specific sales order
        if (context.targetSalesOrderId() != null) {
            handleSpecificSalesOrderAllocation(inventoryEntry, context);
            return;
        }

        // Case 3: General manufactured product - distribute normally
        distributeStockForOrders(inventoryEntry.ownerId(), inventoryEntry.materialGoodId(), inventoryEntry.quantity());
    }

    private void handleSpecificSalesOrderAllocation(InventoryEntry inventoryEntry, ManufacturingContext manufacturingContext) {
        var productId = inventoryEntry.materialGoodId();
        var ownerId = inventoryEntry.ownerId();
        var order = salesOrder(manufacturingContext.targetSalesOrderId());
        var requiredQuantity = requiredQuantity(order, productId);
        var currentlyReserved = reservedQuantity(order, productId);
        if (requiredQuantity.compareTo(inventoryEntry.quantity()) == 0) {
            //we might have something reserved so we clear it
            db.deleteFrom(RESERVED_INVENTORY)
                    .where(RESERVED_INVENTORY.MATERIAL_GOOD_ID.eq(productId),
                            RESERVED_INVENTORY.SALES_ORDER_ID.eq(order.id()),
                            RESERVED_INVENTORY.OWNER_ID.eq(ownerId))
                    .execute();
            inventory.reserveStockForSalesOrder(ownerId, order.id(),
                    List.of(new InventoryService.Stock(productId, inventoryEntry.quantity())),
                    manufacturingContext.customProduct(), manufacturingContext.sourceManufacturingOrderId()
            );
            if (currentlyReserved.compareTo(BigDecimal.ZERO) > 0) {//redistribute what was left
                distributeStockForOrders(ownerId, productId, currentlyReserved);
            }
        } else {
            if (currentlyReserved.add(inventoryEntry.quantity()).compareTo(requiredQuantity) >= 0) {//we will have some left over
                var remainder = currentlyReserved.add(inventoryEntry.quantity()).subtract(requiredQuantity);
                inventory.reserveStockForSalesOrder(ownerId, order.id(),
                        List.of(new InventoryService.Stock(productId, inventoryEntry.quantity().subtract(remainder))),
                        manufacturingContext.customProduct(), manufacturingContext.sourceManufacturingOrderId()
                );
                if (remainder.compareTo(BigDecimal.ZERO) > 0) {//redistribute what was left
                    distributeStockForOrders(ownerId, productId, remainder);
                }
            } else {
                inventory.reserveStockForSalesOrder(ownerId, order.id(),
                        List.of(new InventoryService.Stock(productId, inventoryEntry.quantity())),
                        manufacturingContext.customProduct(), manufacturingContext.sourceManufacturingOrderId()
                );
            }
        }
        if (salesService.allItemsAvailable(ownerId, order.id()) && order.items().stream().anyMatch(item -> item.productId() != null)) {
            salesService.pickAndPackOrder(ownerId, order.id());
        }
    }

    private BigDecimal reservedQuantity(SalesOrder order, UUID productId) {
        return db.select(coalesce(sum(RESERVED_INVENTORY.QUANTITY), 0))
                .from(RESERVED_INVENTORY)
                .where(RESERVED_INVENTORY.OWNER_ID.eq(order.ownerId()),
                        RESERVED_INVENTORY.SALES_ORDER_ID.eq(order.id()),
                        RESERVED_INVENTORY.MATERIAL_GOOD_ID.eq(productId)
                )
                .fetchOptionalInto(BigDecimal.class).orElse(BigDecimal.ZERO);
    }

    private BigDecimal requiredQuantity(SalesOrder order, UUID productId) {
        return order.items().stream()
                .filter(product -> productId.equals(product.productId()))
                .map(SalesOrder.Item::quantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private SalesOrder salesOrder(UUID salesOrderId) {
        return db.selectFrom(SALES_ORDER)
                .where(SALES_ORDER.ID.eq(salesOrderId))
                .fetchSingleInto(SalesOrder.class);
    }

    private InventoryEntry inventoryEntry(UUID ownerId, UUID entryId) {
        return db.selectFrom(INVENTORY).where(INVENTORY.OWNER_ID.eq(ownerId), INVENTORY.ID.eq(entryId)).fetchSingleInto(InventoryEntry.class);
    }

    private Account.Settings.General.InventoryAccountingSettings inventorySettings(UUID ownerId) {
        return db.selectFrom(ACCOUNT)
                .where(ACCOUNT.ID.eq(ownerId))
                .fetchSingleInto(Account.class)
                .settings().general().inventoryAccountingSettings();
    }
}
