package fabriqon.app.business.sales;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.financial.VatCalculatorProvider;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.sequence.Sequences;
import fabriqon.app.common.model.Address;
import fabriqon.app.common.model.Company;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.common.model.Money;
import fabriqon.app.http.response.Entity;
import fabriqon.misc.Json;
import fabriqon.misc.Tuple;
import fabriqon.pdf.HtmlToPdfConverter;
import fabriqon.templates.Localizer;
import fabriqon.templates.Templates;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static fabriqon.jooq.classes.Tables.*;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.jooq.impl.DSL.select;

@Component
@Transactional
public class GoodsAccompanyingNotesService {

    private final Sequences sequences;
    private final DSLContext db;
    private final Templates templates;
    private final HtmlToPdfConverter htmlToPdfConverter;
    private final VatCalculatorProvider vatCalculatorProvider;

    @Autowired
    public GoodsAccompanyingNotesService(Sequences sequences, DSLContext db,
                                         Templates templates, HtmlToPdfConverter htmlToPdfConverter,
                                         VatCalculatorProvider vatCalculatorProvider) {
        this.sequences = sequences;
        this.db = db;
        this.templates = templates;
        this.htmlToPdfConverter = htmlToPdfConverter;
        this.vatCalculatorProvider = vatCalculatorProvider;
    }

    public GoodsAccompanyingNote create(UUID ownerId,
                                        LocalDate deliveryDate,
                                        Address from, Address to,
                                        UUID delegateId,
                                        String transportRegistrationNumber,
                                        String notes,
                                        List<GoodsAccompanyingNote.Item> items
    ) {
        var id = UUID.randomUUID();
        final GoodsAccompanyingNote note = new GoodsAccompanyingNote(
                id, null, null, false, ownerId,
                sequences.nextSequenceForGoodsAccompanyingNote(ownerId),
                deliveryDate,
                from, to,
                delegateId,
                transportRegistrationNumber,
                notes,
                items
        );

        db.insertInto(GOODS_ACCOMPANYING_NOTE)
                .set(GOODS_ACCOMPANYING_NOTE.ID, note.id())
                .set(GOODS_ACCOMPANYING_NOTE.OWNER_ID, note.ownerId())
                .set(GOODS_ACCOMPANYING_NOTE.NUMBER, note.number())
                .set(GOODS_ACCOMPANYING_NOTE.DELIVERY_DATE, deliveryDate.atStartOfDay())
                .set(GOODS_ACCOMPANYING_NOTE.FROM_ADDRESS, JSONB.jsonb(Json.write(from)))
                .set(GOODS_ACCOMPANYING_NOTE.TO_ADDRESS, JSONB.jsonb(Json.write(to)))
                .set(GOODS_ACCOMPANYING_NOTE.DELEGATE_ID, delegateId)
                .set(GOODS_ACCOMPANYING_NOTE.TRANSPORT_REGISTRATION_NUMBER, transportRegistrationNumber)
                .set(GOODS_ACCOMPANYING_NOTE.NOTES, notes)
                .set(GOODS_ACCOMPANYING_NOTE.ITEMS, JSONB.valueOf(Json.write(note.items())))
                .execute();
        return note;
    }

    public GoodsAccompanyingNote update(UUID ownerId, UUID id, GoodsAccompanyingNote update) {
        var ownershipCriteria = GOODS_ACCOMPANYING_NOTE.ID.eq(id).and(GOODS_ACCOMPANYING_NOTE.OWNER_ID.eq(ownerId));
        var note = db.selectFrom(GOODS_ACCOMPANYING_NOTE)
                .where(ownershipCriteria)
                .fetchSingleInto(GoodsAccompanyingNote.class);

        var updateStatement = db.update(GOODS_ACCOMPANYING_NOTE)
                .set(GOODS_ACCOMPANYING_NOTE.OWNER_ID, ownerId);

        if (update.deliveryDate() != null) {
            updateStatement = updateStatement.set(GOODS_ACCOMPANYING_NOTE.DELIVERY_DATE, update.deliveryDate().atStartOfDay());
        }
        if (update.from() != null) {
            updateStatement = updateStatement.set(GOODS_ACCOMPANYING_NOTE.FROM_ADDRESS, JSONB.valueOf(Json.write(update.from())));
        }
        if (update.to() != null) {
            updateStatement = updateStatement.set(GOODS_ACCOMPANYING_NOTE.TO_ADDRESS, JSONB.valueOf(Json.write(update.to())));
        }
        if (update.delegateId() != null) {
            updateStatement = updateStatement.set(GOODS_ACCOMPANYING_NOTE.DELEGATE_ID, update.delegateId());
        }
        if (update.transportRegistrationNumber() != null) {
            updateStatement = updateStatement.set(GOODS_ACCOMPANYING_NOTE.TRANSPORT_REGISTRATION_NUMBER, update.transportRegistrationNumber());
        }
        if (update.notes() != null) {
            updateStatement = updateStatement.set(GOODS_ACCOMPANYING_NOTE.NOTES, update.notes());
        }
        if (update.items() != null) {
            var salesOrder = db.selectFrom(SALES_ORDER)
                    .where(SALES_ORDER.ID.eq(select(SALESORDER_GOODSACCOMPANYINGNOTE.SALES_ORDER_ID).from(SALESORDER_GOODSACCOMPANYINGNOTE).where(SALESORDER_GOODSACCOMPANYINGNOTE.GOODS_ACCOMPANYING_NOTE_ID.eq(note.id()))))
                    .fetchSingleInto(SalesOrder.class);
            updateStatement = updateStatement.set(GOODS_ACCOMPANYING_NOTE.ITEMS, JSONB.valueOf(Json.write(
                    update.items().stream()
                            .map(i -> new GoodsAccompanyingNote.Item(i.id(), i.quantity(),
                                    salesOrder.items().stream().filter(oi -> oi.productId().equals(i.id())).findFirst().map(SalesOrder.Item::price).orElseThrow(),
                                    salesOrder.items().stream().filter(oi -> oi.productId().equals(i.id())).findFirst().map(SalesOrder.Item::discount).orElse(null)))
                            .toList()
            )));
        }
        updateStatement
                .where(GOODS_ACCOMPANYING_NOTE.ID.eq(id), GOODS_ACCOMPANYING_NOTE.OWNER_ID.eq(ownerId))
                .execute();

        return db.selectFrom(GOODS_ACCOMPANYING_NOTE)
                .where(ownershipCriteria)
                .fetchSingleInto(GoodsAccompanyingNote.class);
    }

    public String html(UUID salesOrderId, GoodsAccompanyingNote note) {
        var account = account(note.ownerId());
        var customer = customer(salesOrderId, note.id());
        var vatCalculator = vatCalculatorProvider.with(account, (Address) null);
        var localizer = new Localizer(new Locale(account.information().address().country()));
        var currency = note.items().stream()
                .filter(item -> item.price() != null)
                .map(item -> item.price().currency())
                .findFirst().orElse(account.settings().general().defaultCurrency());
        var delegate = note.delegateId() != null ? db.select(USERS.ID, USERS.NAME).from(USERS).where(USERS.ID.eq(note.delegateId())).fetchSingleInto(Entity.class) : Map.of();
        var itemIndexer = new AtomicInteger();
        var noteMap = new HashMap<>();
        noteMap.put("number", note.number());
        noteMap.put("date", localizer.localizeDate(note.createTime()));
        noteMap.put("deliveryDate", localizer.localizeDate(note.deliveryDate()));
        noteMap.put("from", note.from());
        noteMap.put("to", note.to());
        noteMap.put("delegate", delegate);
        noteMap.put("transportRegistrationNumber", note.transportRegistrationNumber());
        noteMap.put("notes", note.notes());
        noteMap.put("items", note.items().stream()
                .map(item -> {
                    var itemSubtotal = item.quantity().multiply(BigDecimal.valueOf(item.price().amount())).longValue();
                    var discountAmount = item.discount() != null ? item.discount().multiply(item.quantity().multiply(BigDecimal.valueOf(item.price().amount()))).longValue() : 0;
                    var materialGood = materialGood(item.id());
                    var vatRate = vatCalculator.itemVatRate(item.id());
                    return Map.of(
                            "index", itemIndexer.incrementAndGet(),
                            "name", materialGood.a(),
                            "quantity", item.quantity(),
                            "measurementUnit", localizer.localizedValue(materialGood.b().symbol()),
                            "price", item.price().displayAmount(),
                            "discount", new Money(discountAmount, currency).displayAmount(),
                            "value", new Money(itemSubtotal - discountAmount, currency).displayAmount(),
                            "VAT", vatRate.multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_EVEN) + "%",
                            "vatAmount", new Money(vatCalculator.calculate(itemSubtotal - discountAmount, vatRate), currency).displayAmount()
                    );
                })
                .toList());
        return templates.render("pdf/goods_accompanying_note.html",
                Map.of(
                        "note", noteMap,
                        "supplier", Map.of(
                                "name", account.name(),
                                "identificationNumber", account.information().identificationNumber() != null ? account.information().identificationNumber() : "",
                                "taxIdentificationNumber", account.information().taxIdentificationNumber() != null ? account.information().taxIdentificationNumber() : "",
                                "address", account.information().address(),
                                "email", account.information().email() != null ? account.information().email() : ""
                        ),
                        "client", customer != null ? Map.of(
                                "name", customer.name(),
                                "identificationNumber", customer.details().identificationNumber() != null ? customer.details().identificationNumber() : "",
                                "taxIdentificationNumber", customer.details().taxIdentificationNumber() != null ? customer.details().taxIdentificationNumber() : "",
                                "address", customer.details().addresses()
                                        .stream()
                                        .filter(address -> address.types().contains(Address.Type.BILLING))
                                        .findFirst()
                                        .orElse(Address.EMPTY)
                        ) : Map.of(),
                        "labels", localizer.labels(),
                        "base64Logo", account.logo() != null ? account.logo() : "",
                        "currency", isNotEmpty(note.items()) ? note.items().get(0).price().currency() : ""
                )
        );
    }

    private Tuple.Tuple2<String, MeasurementUnit> materialGood(UUID id) {
        return db.select(MATERIAL_GOOD.NAME, MATERIAL_GOOD.DETAILS)
                .from(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(id))
                .fetchSingle()
                .map(r -> Tuple.of(
                        r.getValue(MATERIAL_GOOD.NAME, String.class),
                        r.getValue(MATERIAL_GOOD.DETAILS, MaterialGood.Details.class).measurementUnit()
                ));
    }

    public byte[] pdf(String html) {
        return htmlToPdfConverter.convert(html);
    }

    private Account account(UUID id) {
        return db.selectFrom(ACCOUNT).where(ACCOUNT.ID.eq(id)).fetchSingleInto(Account.class);
    }

    private Company customer(UUID salesOrderId, UUID noteId) {
        //see if it has an SO associated
        UUID soId;
        if (salesOrderId == null) {
            soId = db.select(SALESORDER_GOODSACCOMPANYINGNOTE.SALES_ORDER_ID)
                    .from(SALESORDER_GOODSACCOMPANYINGNOTE)
                    .where(SALESORDER_GOODSACCOMPANYINGNOTE.GOODS_ACCOMPANYING_NOTE_ID.eq(noteId))
                    .fetchOptionalInto(UUID.class)
                    .orElseGet(() -> db.select(SERVICING_ORDER.SALES_ORDER_ID)
                            .from(SERVICING_ORDER)
                            .where(SERVICING_ORDER.ID.eq(select(SERVICINGORDER_GOODSACCOMPANYINGNOTE.SERVICING_ORDER_ID).from(SERVICINGORDER_GOODSACCOMPANYINGNOTE).where(SERVICINGORDER_GOODSACCOMPANYINGNOTE.GOODS_ACCOMPANYING_NOTE_ID.eq(noteId))))
                            .fetchOptionalInto(UUID.class)
                            .orElse(null)
                    );
        } else {
            soId = salesOrderId;
        }
        if (soId == null) {
            return null;
        }
        return db.select(SALES_ORDER.CUSTOMER_ID)
                .from(SALES_ORDER)
                .where(SALES_ORDER.ID.eq(soId))
                .fetchOptionalInto(UUID.class)
                .map(customerId -> db.selectFrom(COMPANY)
                        .where(COMPANY.ID.eq(customerId))
                        .fetchSingleInto(Company.class))
                .orElseThrow();
    }

}
