package fabriqon.app.business.sales;

import com.fasterxml.jackson.core.type.TypeReference;
import fabriqon.app.common.model.Address;
import fabriqon.app.common.model.Money;
import fabriqon.misc.Json;
import jakarta.validation.constraints.NotNull;

import java.beans.ConstructorProperties;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

public record GoodsAccompanyingNote(
        UUID id,
        Instant createTime,
        Instant updateTime,
        boolean deleted,

        UUID ownerId,
        String number,
        LocalDate deliveryDate,
        Address from,
        Address to,
        UUID delegateId,
        String transportRegistrationNumber,
        String notes,
        List<Item> items
) {

    @ConstructorProperties({"id", "create_time", "update_time", "deleted", "owner_id", "number", "delivery_date",
            "from_address", "to_address", "delegate_id", "transport_registration_number", "notes",  "items"})
    public GoodsAccompanyingNote(UUID id, Instant createTime, Instant updateTime, boolean deleted, UUID ownerId,
                                 String number, LocalDate deliveryDate,
                                 Address from, Address to,
                                 UUID delegateId, String transportRegistrationNumber,
                                 String notes,
                                 String items) {
        this(id, createTime, updateTime, deleted, ownerId, number,
                deliveryDate, from, to, delegateId, transportRegistrationNumber, notes,
                Json.read(items, new TypeReference<List<Item>>() {
                })
        );
    }

    public record Item(
            @NotNull
            UUID id,
            @NotNull
            BigDecimal quantity,
            @NotNull
            Money price,
            BigDecimal discount
    ) {

    }
}
