package fabriqon.app.business.systemevents;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface SystemEventGenerator {
    public static final String CREATE_TRANSITION = "CREATE";

    SystemEvent.Section section();
    String transition() default CREATE_TRANSITION;
}
