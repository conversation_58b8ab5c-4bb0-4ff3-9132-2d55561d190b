package fabriqon.app.business.systemevents;

import fabriqon.app.config.security.AccountContext;
import lombok.SneakyThrows;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Component;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Around;

import org.springframework.security.core.Authentication;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.SYSTEM_EVENT;

@Aspect
@Component
public class SystemEventLoggingAspect {

    private static final ThreadLocal<UUID> currentUserId = new ThreadLocal<>();
    private static final ThreadLocal<UUID> currentAccountId = new ThreadLocal<>();

    private final AccountContext accountContext;
    private final DSLContext db;

    @Autowired
    public SystemEventLoggingAspect(AccountContext accountContext, DSLContext db) {
        this.accountContext = accountContext;
        this.db = db;
    }

    // Pointcut for all controller methods
    @Pointcut("within(@org.springframework.web.bind.annotation.RestController *)")
    public void controllerLayer() {
    }

    @Before("controllerLayer()")
    public void setUserAccountDetailsFromSecurityContext() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() && authentication.getPrincipal() instanceof Jwt) {
            currentUserId.set(accountContext.userId((org.springframework.security.oauth2.jwt.Jwt) authentication.getPrincipal()));
            currentAccountId.set(accountContext.accountId((org.springframework.security.oauth2.jwt.Jwt) authentication.getPrincipal()));
        }
    }

    @Around("@annotation(systemEventGenerator)")
    public Object logSystemEvent(ProceedingJoinPoint joinPoint, SystemEventGenerator systemEventGenerator) throws Throwable {
        try {
            Object result = joinPoint.proceed();
            db.insertInto(SYSTEM_EVENT)
                    .set(SYSTEM_EVENT.ID, UUID.randomUUID())
                    .set(SYSTEM_EVENT.OWNER_ID, currentAccountId.get())
                    .set(SYSTEM_EVENT.USER_ID, currentUserId.get())
                    .set(SYSTEM_EVENT.TARGET_ENTITY_ID,
                            getTarget(systemEventGenerator.transition(), result, ((MethodSignature) (joinPoint.getSignature())).getMethod(), joinPoint.getArgs()))
                    .set(SYSTEM_EVENT.SECTION, systemEventGenerator.section().name())
                    .set(SYSTEM_EVENT.TRANSITION, systemEventGenerator.transition())
                    .execute();
            return result;
        } finally {
            // Clear ThreadLocal after request completion
            currentUserId.remove();
            currentAccountId.remove();
        }
    }

    @SneakyThrows
    private UUID getTarget(String transition, Object result, Method method, Object[] args) {
        if (transition.equalsIgnoreCase(SystemEventGenerator.CREATE_TRANSITION)) {
            //in this case we always have an entity returned with an id field that is a UUID (no contract, but it should be applicable everywhere)
            var idField = result.getClass().getDeclaredField("id");
            idField.setAccessible(true);
            return (UUID) idField.get(result);
        } else {
            Annotation[][] parameterAnnotations = method.getParameterAnnotations();
            for (int i = 0; i < parameterAnnotations.length; i++) {
                for (Annotation annotation : parameterAnnotations[i]) {
                    if (annotation instanceof SystemEventTargetEntityId) {
                        // Return the value of the parameter annotated with @SystemEventTargetEntityId
                        return (UUID) args[i];
                    }
                }
            }
        }
        return null;
    }
}