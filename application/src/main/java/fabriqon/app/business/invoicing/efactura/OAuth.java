package fabriqon.app.business.invoicing.efactura;

import fabriqon.app.business.exceptions.BusinessException;
import fabriqon.misc.Tuple;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientException;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;

@Component
@Slf4j
public class OAuth {

    @Value("${efactura.client.id:}")
    private String clientId;
    @Value("${efactura.client.secret:}")
    private String clientSecret;
    @Value("${efactura.callback.url:}")
    private String callbackUrl;

    private static String ANAF_IDP_AUTH_URL = "https://logincert.anaf.ro/anaf-oauth2/v1/authorize";
    private static String ANAF_IDP_TOKEN_ISSUANCE_URL = "https://logincert.anaf.ro/anaf-oauth2/v1/token";
    private static String ANAF_IDP_TOKEN_REVOCATION_URL = "https://logincert.anaf.ro/anaf-oauth2/v1/revoke";

    public String getAuthLink() {
        return ANAF_IDP_AUTH_URL
                + "?response_type=code"
                + "&client_id=" + clientId
                + "&redirect_uri=" + URLEncoder.encode(callbackUrl, StandardCharsets.UTF_8)
                + "&token_content_type=jwt"
                ;
    }

    /**
     * Returns token, refresh token and expiration information
     */
    public Token exchangeCode(String code) {
        try {
            var response = WebClient.create(ANAF_IDP_TOKEN_ISSUANCE_URL)
                    .post()
                    .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                    .header("Authorization", "Basic " + Base64.getEncoder().encodeToString((clientId + ":" + clientSecret).getBytes()))
                    .body(BodyInserters.fromFormData("token_content_type", "jwt")
                            .with("code", code)
                            .with("grant_type", "authorization_code")
                            .with("redirect_uri", callbackUrl)
                    )
                    .retrieve()
                    .toEntity(Map.class)
                    .block();
            if (response == null || response.getBody() == null) {
                throw new RuntimeException("Cannot read from response");
            }
            return new Token(
                    (String) response.getBody().get("access_token"),
                    (String) response.getBody().get("refresh_token"),
                    (Integer) response.getBody().get("expires_in"),
                    (String) response.getBody().get("scope"));
        } catch (WebClientException e) {
            log.error("Can't retrieve token", e);
            throw (e);
        }
    }

    public record Token(
            String token,
            String refreshToken,
            Integer expiresIn,
            String scope
    ) {
    }

    public static void main(String[] args) {
        var oauth = new OAuth();
        oauth.callbackUrl = "https://localhost/callback/auth/efactura";
        oauth.clientId = "d70483c47dc545b5d0e6aab378327e8a7e3ee71d3ac3c266";
        oauth.clientSecret = "eab9e986eced5fad96f0e79fb6902a70aa9003e22e3a7e8a7e3ee71d3ac3c266";

//            System.out.println(oauth.getAuthLink());
        System.out.println(oauth.exchangeCode("56234f568c78fce52a726e6fdf4084d34825128f39ce774a09b8e2392d486693"));

    }

}
