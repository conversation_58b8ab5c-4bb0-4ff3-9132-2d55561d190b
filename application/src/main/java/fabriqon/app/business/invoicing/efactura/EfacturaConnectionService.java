//package fabriqon.app.business.invoicing.efactura;
//
//import org.jooq.DSLContext;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.UUID;
//
//import static fabriqon.jooq.classes.Tables.OAUTH_TOKEN;
//
//@Component
//public class EfacturaConnectionService {
//
//    private final DSLContext db;
//    private final OAuth oauth;
//
//    @Autowired
//    public EfacturaConnectionService(DSLContext db, OAuth oauth) {
//        this.db = db;
//        this.oauth = oauth;
//    }
//
//    public String initiateConnection(UUID ownerId) {
//        return oauth.getAuthLink();
//    }
//
//    public void exchangeCodeForToken(UUID ownerId, UUID userId, String code) {
//        var token = oauth.exchangeCode(code);
//        db.insertInto(OAUTH_TOKEN)
//                .set(OAUTH_TOKEN.ID, UUID.randomUUID())
//                .set(OAUTH_TOKEN.OWNER_ID, ownerId)
//                .set(OAUTH_TOKEN.USER_ID, userId)
//                .set(OAUTH_TOKEN.SERVICE, EfacturaService.NAME)
//                .set(OAUTH_TOKEN.TOKEN, token.token())
//                .set(OAUTH_TOKEN.REFRESH_TOKEN, token.refreshToken())
//                .set(OAUTH_TOKEN.EXPIRES_IN, token.expiresIn())
//                .set(OAUTH_TOKEN.SCOPE, token.scope())
//                .execute();
//    }
//
//}
