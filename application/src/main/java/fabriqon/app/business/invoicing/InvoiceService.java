package fabriqon.app.business.invoicing;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.common.model.GlobalDiscount;
import fabriqon.app.business.financial.VatCalculatorProvider;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.sequence.Sequences;
import fabriqon.app.common.model.Company;
import fabriqon.app.common.model.ContactPerson;
import fabriqon.app.common.model.EmailMessage;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.email.Email;
import fabriqon.email.EmailSender;
import fabriqon.misc.Json;
import fabriqon.pdf.HtmlToPdfConverter;
import fabriqon.templates.Localizer;
import fabriqon.templates.Templates;
import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static fabriqon.jooq.classes.Tables.*;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

@Component
@Transactional
public class InvoiceService {

    private final Sequences sequences;
    private final DSLContext db;
    private final EmailSender emailSender;
    private final Templates templates;
    private final HtmlToPdfConverter htmlToPdfConverter;
    private final VatCalculatorProvider vatCalculatorProvider;

    public InvoiceService(Sequences sequences, DSLContext db, EmailSender emailSender, Templates templates, HtmlToPdfConverter htmlToPdfConverter, VatCalculatorProvider vatCalculatorProvider) {
        this.sequences = sequences;
        this.db = db;
        this.emailSender = emailSender;
        this.templates = templates;
        this.htmlToPdfConverter = htmlToPdfConverter;
        this.vatCalculatorProvider = vatCalculatorProvider;
    }


    public Invoice create(UUID ownerId, UUID customerId, UUID salesOrderId, boolean proforma, String notes,
                          List<Invoice.Item> items, EmailMessage emailMessage, Invoice.RenderingDetails renderingDetails,
                          GlobalDiscount globalDiscount) {
        var invoiceId = UUID.randomUUID();
        var vatCalculator = vatCalculatorProvider.with(ownerId, customerId);
        final Invoice invoice = new Invoice(
                invoiceId, null, null, false,
                ownerId,
                proforma ? sequences.nextSequenceForProformaInvoice(ownerId) : sequences.nextSequenceForInvoice(ownerId),
                customerId,
                //TODO make this configurable per account
                LocalDate.now().plus(30, ChronoUnit.DAYS),
                false,
                proforma,
                null,
                notes,
                items.stream()
                        .map(item -> item.setVatRate(vatCalculator.itemVatRate(item.productId())))
                        .toList(),
                renderingDetails,
                globalDiscount
        );

        db.insertInto(INVOICE)
                .set(INVOICE.ID, invoice.id())
                .set(INVOICE.OWNER_ID, invoice.ownerId())
                .set(INVOICE.CUSTOMER_ID, invoice.customerId())
                .set(INVOICE.SALES_ORDER_ID, salesOrderId)
                .set(INVOICE.NUMBER, invoice.number())
                .set(INVOICE.DUE_DATE, invoice.dueDate().atStartOfDay())
                .set(INVOICE.PAID, invoice.paid())
                .set(INVOICE.PROFORMA, invoice.proforma())
                .set(INVOICE.NOTES, invoice.notes())
                .set(INVOICE.ITEMS, JSONB.valueOf(Json.write(invoice.items())))
                .set(INVOICE.RENDERING_DETAILS, JSONB.valueOf(Json.write(invoice.renderingDetails())))
                .execute();

        if (emailMessage != null) {
            send(ownerId, invoice.id(), emailMessage);
        }

        return invoice;
    }

    public void updateFromSalesOrder(UUID ownerId, UUID salesOrderId, Optional<GlobalDiscount> globalDiscount, List<Invoice.Item> items) {
        var vatCalculator = vatCalculatorProvider.with(ownerId, db.select(SALES_ORDER.CUSTOMER_ID)
                .from(SALES_ORDER)
                .where(SALES_ORDER.ID.eq(salesOrderId))
                .fetchSingleInto(UUID.class));
        db.update(INVOICE)
                .set(INVOICE.ITEMS, JSONB.valueOf(Json.write(
                        items.stream()
                                .map(item -> item.setVatRate(vatCalculator.itemVatRate(item.productId())))
                                .toList()
                )))
                .where(INVOICE.OWNER_ID.eq(ownerId), INVOICE.SALES_ORDER_ID.eq(salesOrderId))
                .execute();
    }

    public void send(UUID ownerId, UUID invoiceId, EmailMessage emailMessage) {
        var invoice = db.selectFrom(INVOICE)
                .where(
                        INVOICE.OWNER_ID.eq(ownerId),
                        INVOICE.DELETED.isFalse(),
                        INVOICE.ID.eq(invoiceId)
                )
                .fetchSingleInto(Invoice.class);
        var customer = customer(invoice.customerId());
        final byte[] pdf = pdf(html(invoice));
        emailSender.send(new Email(
                "<EMAIL>",
                customer.details() != null && isNotEmpty(customer.details().contacts())
                        ? customer.details().contacts().stream()
                        .map(ContactPerson::email)
                        .filter(StringUtils::isNotBlank)
                        .toList()
                        : List.of(),
                List.of(),
                emailMessage.subject(),
                templates.render("email/base_template.html", Map.of(
                        "title", "Invoice",
                        "content", emailMessage.body()
                )),
                Map.of("invoice_"+ invoice.number() + ".pdf", pdf)
        ));
        db.update(INVOICE)
                .set(INVOICE.SENT_AT, LocalDateTime.now())
                .where(INVOICE.OWNER_ID.eq(ownerId), INVOICE.ID.eq(invoiceId))
                .execute();
    }

    public String html(Invoice invoice) {
        var account = account(invoice.ownerId());
        var customer = customer(invoice.customerId());
        var renderingDetails = invoice.renderingDetails();
        var localizer = new Localizer((renderingDetails != null && renderingDetails.language() != null) ? new Locale(renderingDetails.language()) : new Locale(account.information().address().country()));
        var vatCalculator = vatCalculatorProvider.with(account, customer);
        var itemIndexer = new AtomicInteger();

        var invoiceMap = new HashMap<>();
        invoiceMap.put("proforma", invoice.proforma());
        invoiceMap.put("number", invoice.number());
        invoiceMap.put("date", localizer.localizeDate(Instant.now()));
        invoiceMap.put("dueDate", localizer.localizeDate(invoice.dueDate()));
        invoiceMap.put("notes", invoice.notes());
        invoiceMap.put("items", invoice.items().stream()
                        .map(item -> Map.of(
                                "index", itemIndexer.incrementAndGet(),
                                "name", item.name(),
                                "quantity", item.quantity(),
                                "measurementUnit", localizer.localizedValue(item.measurementUnit() != null ? item.measurementUnit().symbol() : measurementUnit(item.productId()).symbol()),
                                "price", item.unitPrice().displayAmount(),
                                "discount", (!invoice.hasGlobalDiscount() && item.discount() != null) ? item.discount().multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_EVEN) + "%" : "",
                                "VAT", item.vatRate().multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_EVEN) + "%",
                                "vatAmount", item.vatValue(vatCalculator, invoice.globalDiscountPercentage()).displayAmount(),
                                "value", invoice.hasGlobalDiscount() ? item.grossValue().displayAmount() : item.grossValue().subtract(item.discountValue()).displayAmount()
                        ))
                        .toList());

        invoiceMap.put("subTotalAmount", invoice.subTotal().displayAmount());
        invoiceMap.put("discountAmount", invoice.discount().displayAmount());
        invoiceMap.put("taxAmount", invoice.vat(vatCalculator).displayAmount());
        invoiceMap.put("totalAmount", invoice.total(vatCalculator).displayAmount());
        invoiceMap.put("isVatApplicable", vatCalculator.isVatApplicable());

        var supplierMap = new HashMap<>();
        supplierMap.put("name", account.name());
        if (account.information().includeIdentificationNumberOnDocuments()) {
            supplierMap.put("identificationNumber", account.information().identificationNumber());
        }
        if (account.information().includeTaxIdentificationNumberOnDocuments()) {
            supplierMap.put("taxIdentificationNumber", account.information().taxIdentificationNumber());
        }
        if (account.information().includeSocialCapitalOnDocuments()) {
            supplierMap.put("socialCapital", account.information().socialCapital());
        }
        if (account.information().includeSocialCapitalOnDocuments() && account.information().address() != null) {
            supplierMap.put("address", account.information().address().toString());
        }
        if (isNotEmpty(account.information().bankAccounts()) && account.information().includeBankAccountOnDocuments().getFirst()) {
            supplierMap.put("bankAccount", account.information().bankAccounts().getFirst());
        }
        if (account.information().includeEmailOnDocuments()) {
            supplierMap.put("email", account.information().email());
        }
        if (account.information().includePhoneOnDocuments()) {
            supplierMap.put("phone", account.information().phone());
        }
        var map = new HashMap<>();
        map.put("invoice", invoiceMap);
        map.put("supplier", supplierMap);
        map.put("client", Map.of(
                "name", customer.name(),
                "identificationNumber", customer.details().identificationNumber() != null ? customer.details().identificationNumber() : "",
                "taxIdentificationNumber", customer.details().taxIdentificationNumber(),
                "address", isNotEmpty(customer.details().addresses()) ? customer.details().addresses().getFirst().toString() : ""
        ));
        map.put("labels", localizer.labels());
        map.put("base64Logo", account.logo());
        map.put("currency", invoice.currency());
        map.put("showDiscount", renderingDetails == null || renderingDetails.columnsToHide() == null || !renderingDetails.columnsToHide().contains(Invoice.RenderingDetails.HideableColumn.DISCOUNT));
        return templates.render("pdf/invoice.html", map);
    }

    public void markAsPaid(UUID ownerId, UUID invoiceId) {
        db.update(INVOICE)
                .set(INVOICE.PAID, true)
                .where(
                        INVOICE.OWNER_ID.eq(ownerId),
                        INVOICE.DELETED.isFalse(),
                        INVOICE.ID.eq(invoiceId)
                )
                .execute();
    }

    // fixme: we are storing these in the db from now on but we'd need to go back and fix old data so it contains the info
    private MeasurementUnit measurementUnit(UUID productId) {
        if (productId == null) {//this is a service
            return MeasurementUnit.PIECE;
        }
        return db.select(MATERIAL_GOOD.DETAILS)
                .from(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(productId))
                .fetchSingleInto(MaterialGood.Details.class)
                .measurementUnit();
    }

    public byte[] pdf(String html) {
        return htmlToPdfConverter.convert(html);
    }

    private Account account(UUID id) {
        return db.selectFrom(ACCOUNT).where(ACCOUNT.ID.eq(id)).fetchSingleInto(Account.class);
    }

    private Company customer(UUID customerId) {
        return db.selectFrom(COMPANY)
                .where(COMPANY.ID.eq(customerId))
                .fetchSingleInto(Company.class);
    }

}
