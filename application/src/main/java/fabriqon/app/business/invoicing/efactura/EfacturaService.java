package fabriqon.app.business.invoicing.efactura;

import fabriqon.app.business.invoicing.Invoice;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class EfacturaService {

    public static final String NAME = "EFACTURA";

    @Value("${efactura.service.url:}")
    String serviceUrl;

    public void sendInvoice(Invoice invoice) {
        //send invoice to efactura
    }

}
