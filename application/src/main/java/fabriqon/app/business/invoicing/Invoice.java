package fabriqon.app.business.invoicing;

import com.fasterxml.jackson.core.type.TypeReference;
import fabriqon.app.business.common.model.GlobalDiscount;
import fabriqon.app.business.financial.VatCalculatorProvider;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.common.model.Money;
import fabriqon.misc.Json;

import java.beans.ConstructorProperties;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Currency;
import java.util.List;
import java.util.UUID;

public record Invoice
        (
                UUID id,
                Instant createTime,
                Instant updateTime,
                boolean deleted,

                UUID ownerId,
                String number,
                UUID customerId,
                LocalDate dueDate,
                boolean paid,
                boolean proforma,
                LocalDate sentAt,
                String notes,
                List<Item> items,
                RenderingDetails renderingDetails,
                GlobalDiscount globalDiscount
        ) {
    @ConstructorProperties({"id", "create_time", "update_time", "deleted", "owner_id", "number", "customer_id",
            "due_date", "paid", "proforma", "sent_at", "notes", "items", "rendering_details", "global_discount"})
    public Invoice(UUID id, Instant createTime, Instant updateTime, boolean deleted, UUID ownerId, String number, UUID customerId,
                   LocalDate dueDate, boolean paid, boolean proforma, LocalDate sentAt, String notes, String items,
                   String renderingDetails, String globalDiscount) {
        this(id, createTime, updateTime, deleted, ownerId, number, customerId, dueDate, paid, proforma, sentAt, notes,
                Json.read(items, new TypeReference<List<Item>>() {}),
                Json.readNullSafe(renderingDetails, RenderingDetails.class),
                Json.readNullSafe(globalDiscount, GlobalDiscount.class)
                );
    }

    public record Item(
            UUID productId,
            UUID serviceId,
            String name,
            BigDecimal quantity,
            MeasurementUnit measurementUnit,
            Money unitPrice,
            BigDecimal discount,
            BigDecimal vatRate
    ) {
        public Item setVatRate(BigDecimal vatRate) {
            return new Item(productId, serviceId, name, quantity, measurementUnit, unitPrice, discount, vatRate);
        }

        public Money grossValue() {
            return unitPrice.multiply(quantity);
        }

        public Money discountValue() {
            return discount != null ? grossValue().multiply(discount) : new Money(0, unitPrice.currency());
        }

        public Money vatValue(VatCalculatorProvider.Calculator vatCalculator, BigDecimal globalDiscountPercentage) {
            var discountValue = globalDiscountPercentage != null ? grossValue().multiply(globalDiscountPercentage) : discountValue();
            return new Money(vatCalculator.calculate(grossValue().subtract(discountValue), vatRate), unitPrice.currency());
        }

    }


    public record RenderingDetails(
            String language,
            List<HideableColumn> columnsToHide
    ) {
        public enum HideableColumn { DISCOUNT }
    }

    public boolean hasGlobalDiscount() {
        return globalDiscount != null && globalDiscount.applicable();
    }

    public Currency currency() {
        return items.stream()
                .filter(item -> item.unitPrice() != null)
                .map(item -> item.unitPrice().currency())
                .findFirst()
                .orElseThrow(() -> new RuntimeException("No currency found in invoice items"));
    }

    public Money discount() {
        return hasGlobalDiscount()
                ? globalDiscount.amount() != null ? globalDiscount.amount() : subTotal().multiply(globalDiscount.percentage())
                : discountFromItems();
    }

    private Money discountFromItems() {
        return items.stream()
                .filter(item -> item.discount() != null)
                .map(item -> item.grossValue().multiply(item.discount()))
                .reduce(Money::add)
                .orElse(new Money(0, currency()));

    }

    public Money vat(VatCalculatorProvider.Calculator vatCalculator) {
        return hasGlobalDiscount() ? globalVat(vatCalculator) : perItemVat(vatCalculator);
    }

    private Money globalVat(VatCalculatorProvider.Calculator vatCalculator) {
        var globalDiscountPercentage = globalDiscountPercentage();
        return new Money(items.stream()
                .map(item -> vatCalculator.calculate(item.grossValue().subtract(item.grossValue().multiply(globalDiscountPercentage)), item.vatRate()))
                .mapToLong(v -> v)
                .sum(), currency());
    }

    public BigDecimal globalDiscountPercentage() {
        if (globalDiscount == null || !globalDiscount.applicable()) {
            return null;
        }
        var subTotal = subTotal();
        return globalDiscount.amount() != null
                ? BigDecimal.valueOf(subTotal.subtract(globalDiscount.amount()).amount()).divide(BigDecimal.valueOf(subTotal.amount()), 6, RoundingMode.HALF_EVEN)
                : globalDiscount.percentage();
    }

    private Money perItemVat(VatCalculatorProvider.Calculator vatCalculator) {
        return items.stream()
                .map(item -> item.vatValue(vatCalculator, globalDiscountPercentage()))
                .reduce(Money::add)
                .orElse(new Money(0, currency()));
    }

    public Money subTotal() {
        return items.stream()
                .map(Item::grossValue)
                .reduce(Money::add)
                .orElse(new Money(0, currency()));
    }

    public Money total(VatCalculatorProvider.Calculator vatCalculator) {
        return subTotal().subtract(discount()).add(vat(vatCalculator));
    }

}