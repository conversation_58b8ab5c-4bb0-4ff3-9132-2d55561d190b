package fabriqon.app.business.suppliers;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.company.CompanyService;
import fabriqon.app.business.purchases.PurchaseOrder;
import fabriqon.app.common.model.Company;
import fabriqon.misc.Json;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.temporal.ChronoUnit;
import java.util.Optional;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.*;
import static java.time.Instant.now;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Component
@Transactional
public class SupplierService {

    private final DSLContext db;
    private final CompanyService companyService;

    @Autowired
    public SupplierService(final DSLContext db, CompanyService companyService) {
        this.db = db;
        this.companyService = companyService;
    }

    public Company create(UUID owner,
                          Company.Type type,
                          String name,
                          Company.Details details) {
        var company = companyService.create(owner, type, name, details);
        db.insertInto(ACCOUNT_SUPPLIER)
                .set(ACCOUNT_SUPPLIER.SUPPLIER_ID, company.id())
                .set(ACCOUNT_SUPPLIER.OWNER_ID, owner)
                .execute();
        return company;
    }

    public void update(UUID ownerId, UUID id, Company.Type type, String name, Company.Details details) {
        companyService.update(ownerId, id, type, name, details);
    }

    /**
     * return the no of days the last order from the supplier was delivered in
     */
    public Optional<Integer> lastOrderDeliveredIn(UUID ownerId, UUID supplierId) {
        return db.selectFrom(PURCHASE_ORDER)
                .where(PURCHASE_ORDER.OWNER_ID.eq(ownerId),
                        PURCHASE_ORDER.SUPPLIER_ID.eq(supplierId),
                        PURCHASE_ORDER.STATUS.eq(PurchaseOrder.Status.DELIVERED.name()))
                .orderBy(PURCHASE_ORDER.DELIVERED_AT.desc())
                .limit(1)
                .fetchOptionalInto(PurchaseOrder.class)
                .map(order -> {
                    var accountSettings = accountSettings(ownerId);
                    return (int) ChronoUnit.DAYS.between(
                            order.createTime().atZone(accountSettings.general().defaultTimeZone().toZoneId()).toLocalDateTime(),
                            order.deliveredAt()) + 1;
                });
    }

    public Company attach(UUID ownerId, UUID customerId, String originalFilename, byte[] bytes) {
        return companyService.attach(ownerId, customerId, originalFilename, bytes);
    }

    public Company deleteFile(UUID ownerId, UUID customerId, UUID fileId) {
        return companyService.deleteFile(ownerId, customerId, fileId);
    }

    private Account.Settings accountSettings(UUID accountId) {
        return db.selectFrom(ACCOUNT)
                .where(ACCOUNT.ID.eq(accountId))
                .fetchSingleInto(Account.class)
                .settings();
    }
}
