package fabriqon.app.business.servicetemplates;

import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.common.model.Money;

import java.beans.ConstructorProperties;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Currency;
import java.util.UUID;

public record ServiceTemplate (
        UUID id,
        Instant createTime,
        Instant updateTime,
        boolean deleted,

        UUID ownerId,
        String name,
        BigDecimal vatRate,
        MeasurementUnit measurementUnit,
        Money sellPrice,
        Money cost
) {

    @ConstructorProperties({"id", "create_time", "update_time", "deleted", "owner_id", "name", "vat_rate",
            "measurement_unit", "sell_price_amount", "sell_price_currency", "cost_amount", "cost_currency"})
    public ServiceTemplate(UUID id, Instant createTime, Instant updateTime, boolean deleted, UUID ownerId, String name,
                      BigDecimal vatRate, MeasurementUnit measurementUnit, int sellPriceAmount, Currency sellPriceCurrency,
                           int costAmount, Currency costCurrency) {
        this(id, createTime, updateTime, deleted, ownerId, name, vatRate, measurementUnit,
                sellPriceCurrency != null ? new Money(sellPriceAmount, sellPriceCurrency) : null, costCurrency != null ? new Money(costAmount, costCurrency) : null);
    }
}
