package fabriqon.app.business.users;

import fabriqon.misc.Json;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.Map;

@Component("auth0")
public class Auth0Impl implements Auth0 {

    @Value("${auth0.management.url}")
    private String auth0ManagementUrl;

    @Value("${auth0.management.client.id}")
    private String auth0ClientId;

    @Value("${auth0.management.client.secret}")
    private String auth0ClientSecret;

    public UserData userData(String userId) {
        return WebClient.create(auth0ManagementUrl + "/api/v2/users/" + userId).get()
                .header("authorization", "Bearer " + token())
                .retrieve()
                .bodyToMono(String.class)
                .map(s -> Json.read(s, UserData.class))
                .block();
    }


    private String token() {
        return WebClient.create(auth0ManagementUrl + "/oauth/token").post()
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(Json.write(Map.of(
                        "client_id", auth0ClientId,
                        "client_secret", auth0ClientSecret,
                        "audience", auth0ManagementUrl + "/api/v2/",
                        "grant_type", "client_credentials"
                )))
                .retrieve()
                .bodyToMono(String.class)
                .map(s -> Json.read(s, Map.class))
                .map(m -> (String) m.get("access_token"))
                .block();
    }

}
