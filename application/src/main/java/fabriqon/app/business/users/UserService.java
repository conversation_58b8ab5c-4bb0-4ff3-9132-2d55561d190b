package fabriqon.app.business.users;

import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

import static fabriqon.jooq.classes.Tables.USERS;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Slf4j
@Component
@Transactional
public class UserService {

    private final Auth0 auth0;
    private final DSLContext db;

    @Autowired
    public UserService(Auth0 auth0, DSLContext db) {
        this.auth0 = auth0;
        this.db = db;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public User loadOrPersist(UUID ownerId, String externalId) {
        return db.select(USERS.ID, USERS.CREATE_TIME, USERS.UPDATE_TIME, USERS.DELETED, USERS.OWNER_ID, USERS.EXTERNAL_ID, USERS.NAME)
                .from(USERS)
                .where(USERS.OWNER_ID.eq(ownerId), USERS.EXTERNAL_ID.eq(externalId))
                .fetchOptionalInto(User.class)
                .orElseGet(() -> {
                    var userId = UUID.randomUUID();
                    try {
                        var userData = auth0.userData(externalId);
                        db.insertInto(USERS)
                                .set(USERS.ID, userId)
                                .set(USERS.OWNER_ID, ownerId)
                                .set(USERS.EXTERNAL_ID, externalId)
                                .set(USERS.NAME, userData.name())
                                .set(USERS.HIDDEN, shouldHideUser(userData.name()))
                                .set(USERS.DETAILS, JSONB.valueOf("{}"))
                                .execute();
                        return new User(userId, null, null, false, ownerId, externalId, null);
                    } catch (DuplicateKeyException e) {//race conditions
                        log.info("exception when persisting user [{}]", externalId, e);
                        return db.selectFrom(USERS)
                                .where(USERS.OWNER_ID.eq(ownerId), USERS.EXTERNAL_ID.eq(externalId))
                                .fetchSingleInto(User.class);
                    }
                });
    }

    /**
     * these users we use a lot in production to test account setup for customers but they should not be displayed within
     * the application so we automatically mark them as hidden
     */
    private boolean shouldHideUser(String username) {
        return isNotBlank(username) && (
                username.contains("lokee103")
                || username.contains("evotech3")
                || (username.contains("gellert") && username.contains("szeles"))
                );
    }

}
