package fabriqon.app.business.users;

import java.beans.ConstructorProperties;
import java.time.Instant;
import java.util.UUID;

public record User(
        UUID id,
        Instant createTime,
        Instant updateTime,
        boolean deleted,

        UUID ownerId,
        String externalId,
        String name
) {

    @ConstructorProperties({"id", "create_time", "update_time", "deleted", "owner_id", "external_id", "name", "details", "hidden", "text_search"})
    public User(UUID id, Instant createTime, Instant updateTime, boolean deleted, UUID ownerId, String externalId, String name,
                //we declare this so jooq can deserialize it but it's not needed in the code
                String details, boolean hidden, String textSearch) {
        this(id, createTime, updateTime, deleted, ownerId, externalId, name);
    }

}
