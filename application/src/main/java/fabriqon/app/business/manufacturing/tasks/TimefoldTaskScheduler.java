package fabriqon.app.business.manufacturing.tasks;

import ai.timefold.solver.core.api.domain.entity.PlanningEntity;
import ai.timefold.solver.core.api.domain.lookup.PlanningId;
import ai.timefold.solver.core.api.domain.solution.*;
import ai.timefold.solver.core.api.domain.valuerange.ValueRangeProvider;
import ai.timefold.solver.core.api.domain.variable.*;
import ai.timefold.solver.core.api.score.buildin.hardsoft.HardSoftScore;
import ai.timefold.solver.core.api.score.director.ScoreDirector;
import ai.timefold.solver.core.api.score.stream.Constraint;
import ai.timefold.solver.core.api.score.stream.ConstraintFactory;
import ai.timefold.solver.core.api.score.stream.ConstraintProvider;
import ai.timefold.solver.core.api.score.stream.Joiners;
import ai.timefold.solver.core.api.solver.SolutionManager;
import ai.timefold.solver.core.api.solver.SolverFactory;
import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.manufacturing.ManufacturingOperation;
import fabriqon.app.business.manufacturing.ManufacturingOperationService;
import fabriqon.app.business.manufacturing.ManufacturingOrder;
import fabriqon.app.business.manufacturing.tasks.timefold.TimefoldInitializer;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static fabriqon.jooq.classes.Tables.*;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

@Slf4j
@Component
public class TimefoldTaskScheduler {

    private static final SecureRandom secureRandom = new SecureRandom();

    private final int SOLUTION_DURATION = 15;

    private final DSLContext db;
    private final ManufacturingOperationService operationService;

    @Autowired
    public TimefoldTaskScheduler(DSLContext db, ManufacturingOperationService operationService) {
        this.db = db;
        this.operationService = operationService;
    }

    public ManufacturingSchedule schedule(List<ManufacturingOrder> orders) {
        Account account = fetchAccount(orders.getFirst().ownerId());
        var workstations = prepareWorkstations(account.id());
        var operations = prepareOperations(orders, workstations);

        var timeUtil = new TimeUtil(account.settings());

        // Create employees with empty assignment lists
        var employees = prepareEmployees(account.id());

        // Create employee assignments
        var assignments = prepareEmployeeAssignments(operations);

        Map<UUID, TimefoldTaskScheduler.Employee> employeeMap = new HashMap<>();
        for (TimefoldTaskScheduler.Employee employee : employees) {
            employeeMap.put(employee.getId(), employee);
        }

        assignments.forEach(ea -> {
            var operation = ea.getOperation();
            var employee = employeeMap.get(operation.getCandidateEmployeeIds().getFirst());
            log.info("Assigning {} to {}", operation.getName(), employee.getName());
            employee.getAssignments().add(ea);
        });

        var initializedSolution = new ManufacturingSchedule(
                db,
                timeUtil,
                account.id(),
                employees,
                workstations,
                operations,
                assignments,
                orders
        );

        log.info("Created initialized solution with {} employees, {} operations, {} assignments",
                employees.size(), operations.size(), assignments.size());


        var solverConfig = TimefoldInitializer.getSolverConfig(SOLUTION_DURATION);
        var solverFactory = SolverFactory.create(solverConfig);
        var solutionManager = SolutionManager.create(solverFactory);
        solutionManager.update(initializedSolution);
        var solver = solverFactory.buildSolver();
        ManufacturingSchedule solution = (ManufacturingSchedule) solver.solve(initializedSolution);

        var solutionAnalysis = solutionManager.analyze(solution);
        log.info("solution analysis [{}]", solutionAnalysis);
        solutionAnalysis.constraintMap().values().stream()
                .filter(c -> ((Integer) c.weight().toLevelNumbers()[0]) <= -1 && isNotEmpty(c.matches()))
                .forEach(c -> log.info("constraint [{}] with violations [{}] and matches:\n{}",
                        c.constraintName(), c.matchCount(),
                        c.matches().stream().map(Object::toString).collect(Collectors.joining(",\n"))));

        return solution;
    }

    private List<Employee> prepareEmployees(UUID ownerId) {
        return db.select(USERS.ID, USERS.NAME).from(USERS).where(USERS.OWNER_ID.eq(ownerId))
                .fetch()
                .map(r -> new Employee(r.value1(), r.value2(), new ArrayList<>()));
    }

    private List<EmployeeAssignment> prepareEmployeeAssignments(List<Operation> operations) {
        return operations.stream()
                .flatMap(op -> op.employeeAssignments.stream())
                .toList();
    }

    private List<Workstation> prepareWorkstations(UUID ownerId) {
        return db.select(MANUFACTURING_WORKSTATION.ID, MANUFACTURING_WORKSTATION.NAME).from(MANUFACTURING_WORKSTATION).where(MANUFACTURING_WORKSTATION.OWNER_ID.eq(ownerId))
                .fetch()
                .map(r -> new Workstation(r.value1(), r.value2()));
    }

    private List<Operation> prepareOperations(List<ManufacturingOrder> orders, List<Workstation> workstations) {
        var timeUtil = new TimeUtil(fetchAccount(orders.getFirst().ownerId()).settings());
        var referenceTime = timeUtil.localDateTimeForAccount().truncatedTo(ChronoUnit.MINUTES);
        return orders.stream()
                .flatMap(order -> {
                    var sequenceCounter = new AtomicInteger(0);
                    var previousOperationParallel = new AtomicBoolean(true);
                    var operations = operationService.enhance(order.manufacturingOperations()).stream()
                            .map(op -> {
                                var sequence = previousOperationParallel.get() && op.parallelizable() ? sequenceCounter.get() : sequenceCounter.incrementAndGet();
                                previousOperationParallel.set(op.parallelizable());
                                return createOperation(referenceTime, order, op, timeUtil, sequence, workstations);
                            })
                            .toList();
                    operations.forEach(op -> op.setPreviousOperations(operations.stream().filter(o -> o.taskSequence == op.taskSequence - 1).toList()));
                    operations.forEach(op -> op.setNextOperations(operations.stream().filter(o -> o.taskSequence == op.taskSequence + 1).toList()));
                    return operations.stream();
                })
                .toList();
    }

    private Operation createOperation(LocalDateTime referenceTime,
                                      ManufacturingOrder order,
                                      ManufacturingOperation op,
                                      TimeUtil timeUtil,
                                      int sequence,
                                      List<Workstation> workstations) {
        var numAssignees = op.numberOfAssignees() != null ? op.numberOfAssignees() : 1;
        var operation = new Operation(
                UUID.randomUUID(),
                referenceTime,
                timeUtil,
                op.candidateWorkstationIds().stream()
                        .map(w -> workstations.stream().filter(workstation -> workstation.id.equals(w)).findFirst().orElseThrow())
                        .toList(),
                op.candidateEmployeeIds(),
                op.name(),
                op.durationInMinutes() > 60 ? op.durationInMinutes() / 3 : op.durationInMinutes() / 2,
                numAssignees,
                order.id(),
                order.ranking(),
                op.parallelizable(),
                sequence,
                List.of(),
                List.of(),
                null
        );
        operation.setEmployeeAssignments(IntStream.range(0, numAssignees).mapToObj(i -> new EmployeeAssignment(operation)).collect(Collectors.toSet()));
        return operation;
    }

    // Constraint Provider
    public static class ManufacturingConstraintProvider implements ConstraintProvider {
        @Override
        public Constraint[] defineConstraints(ConstraintFactory constraintFactory) {
            return new Constraint[]{
                    candidateEmployeeConstraint(constraintFactory),
                    employeeAssignmentSequenceConstraint(constraintFactory),
                    taskSequencingConstraint(constraintFactory),
                    noOverlapForEmployeesConstraint(constraintFactory),
//                    noOverlapForWorkstationsConstraint(constraintFactory),
                    operationStartTimeSynchronization(constraintFactory),

                    //soft constraints
//                    promoteParallelizationConstraint(constraintFactory),
//                    balanceEmployeeWorkloadConstraint(constraintFactory),
//                    employeeTaskContinuityConstraint(constraintFactory),
//                    prioritizeHighRankingOrdersConstraint(constraintFactory),
                    prioritizeEarlyOrderCompletion(constraintFactory)
            };
        }

        private Constraint candidateEmployeeConstraint(ConstraintFactory constraintFactory) {
            return constraintFactory.forEach(Employee.class)
                    .filter(e -> e.getAssignments().stream().anyMatch(a -> !a.getOperation().candidateEmployeeIds.contains(e.id)))
                    .penalize(HardSoftScore.ONE_HARD)
                    .asConstraint("Candidate employee constraint");
        }

        private Constraint noOverlapForEmployeesConstraint(ConstraintFactory constraintFactory) {
            return constraintFactory.forEach(EmployeeAssignment.class)
                    .join(EmployeeAssignment.class,
                            Joiners.equal(EmployeeAssignment::getEmployeeId), // Same employee
                            Joiners.lessThanOrEqual(EmployeeAssignment::getStartTime), // Ensure logical ordering
                            Joiners.filtering((ea1, ea2) -> !ea1.equals(ea2)) // Avoid self-comparison
                    )
                    .filter((ea1, ea2) -> ea1.getEndTime().isAfter(ea2.getStartTime())) // Detect overlaps
                    .penalize(HardSoftScore.ONE_HARD)
                    .asConstraint("No overlap for employees constraint");
        }

        Constraint employeeAssignmentSequenceConstraint(ConstraintFactory factory) {
            return factory
                    .forEach(Employee.class)
                    .filter(employee -> employee.getAssignments() != null && !employee.getAssignments().isEmpty())
                    .filter(employee -> IntStream.range(0, employee.getAssignments().size())
                            .anyMatch(i -> employee.getAssignments().subList(0, i).stream()
                                    .anyMatch(a -> a.getOperation().getManufacturingOrderId().equals(employee.getAssignments().get(i).getOperation().getManufacturingOrderId()) &&
                                            a.getOperation().getTaskSequence() > employee.getAssignments().get(i).getOperation().getTaskSequence())))
                    .penalize(HardSoftScore.ONE_HARD)
                    .asConstraint("Employee assignments must respect task sequence within an order");
        }

        private Constraint taskSequencingConstraint(ConstraintFactory constraintFactory) {
            return constraintFactory.forEach(EmployeeAssignment.class)
                    .join(Operation.class, Joiners.equal(ea -> ea.operation.manufacturingOrderId, op -> op.manufacturingOrderId))
                    .groupBy((ea, op) -> op)
                    // Join with other operations in the same manufacturing order
                    .join(Operation.class,
                            Joiners.equal(Operation::getManufacturingOrderId), // Same order
                            Joiners.lessThanOrEqual(Operation::getTaskSequence),  // Ensure the sequence is respected
                            Joiners.filtering((earlierOperation, laterOperation) -> !earlierOperation.equals(laterOperation))) // Exclude self
                    .filter((earlierOperation, laterOperation) -> {
                        if (earlierOperation.getTaskSequence() == laterOperation.getTaskSequence()) {
                            // Parallelizable operations in the same sequence can start simultaneously
                            return !earlierOperation.isParallelizable() || !laterOperation.isParallelizable();
                        } else {
                            // Ensure earlier operations finish before later ones start
                            LocalDateTime earlierEnd = earlierOperation.getEndTime();
                            LocalDateTime laterStart = laterOperation.getStartTime();
                            return earlierEnd != null && laterStart != null && earlierEnd.isAfter(laterStart);
                        }
                    })
                    .penalize(HardSoftScore.ONE_HARD)
                    .asConstraint("Task sequencing constraint");
        }

        private Constraint operationStartTimeSynchronization(ConstraintFactory constraintFactory) {
            return constraintFactory.forEach(EmployeeAssignment.class)
                    .join(EmployeeAssignment.class,
                            Joiners.equal(EmployeeAssignment::getOperationId), // Belong to the same operation
                            Joiners.filtering((ea1, ea2) -> !ea1.equals(ea2)) // Avoid self-joins
                    )
                    .filter((ea1, ea2) -> !ea1.getStartTime().equals(ea2.getStartTime())) // Start times mismatch
                    .penalize(HardSoftScore.ONE_HARD)
                    .asConstraint("Operation start time synchronization");
        }

        //soft constraints
        private Constraint promoteParallelizationConstraint(ConstraintFactory constraintFactory) {
            return constraintFactory.forEach(Operation.class)
                    .join(Operation.class,
                            Joiners.equal(Operation::getManufacturingOrderId),
                            Joiners.equal(Operation::getTaskSequence),
                            Joiners.filtering((op1, op2) -> op1.isParallelizable() && op2.isParallelizable())
                    )
                    .filter((op1, op2) -> op1.getStartTime() != null && op2.getStartTime() != null)
                    .penalize(HardSoftScore.ONE_SOFT, (op1, op2) -> (int) Math.abs(Duration.between(op1.getStartTime(), op2.getStartTime()).toMinutes())
                    )
                    .asConstraint("Promote parallelization for tasks in the same sequence");
        }

        /*private Constraint balanceEmployeeWorkloadConstraint(ConstraintFactory constraintFactory) {
            return constraintFactory.forEach(EmployeeAssignment.class)
                    // Group by Employee and calculate total workload (in minutes)
                    .groupBy(
                            EmployeeAssignment::getEmployeeId,
                            ConstraintCollectors.sum(assignment ->
                                            (int) assignment.getOperation().getStartTime()
                                            .until(assignment.getOperation().getEndTime(), ChronoUnit.MINUTES))
                    )
                    // Penalize deviations from the average workload dynamically
                    .penalize(
                            HardSoftScore.ONE_SOFT,
                            (employeeId, workload) -> {
                                // Calculate the average dynamically within this penalty function
                                // (Would require external calculation if this doesn't meet performance needs)
                                double averageWorkload = computeAverageWorkload();
                                return (int) Math.abs(workload - averageWorkload);
                            }
                    )
                    .asConstraint("Balance employee workload to avoid overloading");
        }*/

        private Constraint employeeTaskContinuityConstraint(ConstraintFactory constraintFactory) {
            return constraintFactory.forEach(EmployeeAssignment.class)
                    .join(EmployeeAssignment.class,
                            Joiners.equal(EmployeeAssignment::getEmployeeId),
                            Joiners.equal(assignment -> assignment.getOperation().getManufacturingOrderId()))
                    .filter((a1, a2) -> a1.getStartTime() != null && a2.getStartTime() != null)
                    .penalize(HardSoftScore.ONE_SOFT,
                            (assignment1, assignment2) -> 10
                            /*(int) Math.abs(Duration.between(assignment1.getEndTime(), assignment2.getStartTime()).toMinutes())*/)
                    .asConstraint("Employee task continuity");
        }

//        private Constraint prioritizeEarlyOrderCompletion(ConstraintFactory constraintFactory) {
//            return constraintFactory.forEach(EmployeeAssignment.class)
//                    .join(Operation.class, Joiners.equal(ea -> ea.operation.manufacturingOrderId, op -> op.manufacturingOrderId))
//                    .groupBy((ea, op) -> op)
//                    .filter(op -> op.getEndTime() != null)
//                    .penalize(HardSoftScore.ONE_SOFT, (op) -> {
////                        log.info("order [{}] op [{}]", op.manufacturingOrderId, op);
//                        return (int) Math.max(0, ChronoUnit.MINUTES.between(op.getReferenceTime(), op.getEndTime()));
//                    })
//                    .asConstraint("Prioritize earlier completion of all orders");
//        }
            private Constraint prioritizeEarlyOrderCompletion(ConstraintFactory constraintFactory) {
                // This constraint must depend directly on the shadow variable
                return constraintFactory.forEach(Operation.class)
                        .filter(op -> op.getStartTime() != null)
                        .penalize(HardSoftScore.ONE_SOFT,
                                op -> (int) Math.max(0, ChronoUnit.MINUTES.between(op.getReferenceTime(), op.getEndTime())))
                        .asConstraint("Prioritize earlier completion of all orders");
            }

        /*private Constraint prioritizeHighRankingOrdersConstraint(ConstraintFactory constraintFactory) {
            return constraintFactory.forEach(Operation.class)
                    .filter(op -> op.getEndTime() != null)
                    .penalize(HardSoftScore.ONE_SOFT,
                            operation -> operation.getManufacturingOrderRanking()
                                    * (int) Math.max(1, ChronoUnit.MINUTES.between(operation.getEndTime(), LocalDateTime.now())))
                    .asConstraint("Prioritize higher ranking orders and encourage early completion");
        }*/

    }

    // Helper Method
    private Account fetchAccount(UUID accountId) {
        return db.selectFrom(ACCOUNT).where(ACCOUNT.ID.eq(accountId)).fetchSingleInto(Account.class);
    }


    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @PlanningEntity
    public static class Employee {

        @PlanningId
        private UUID id;
        private String name;

        @PlanningListVariable(valueRangeProviderRefs = "employeeAssignmentsProvider")
        private List<EmployeeAssignment> assignments;

        @Override
        public boolean equals(Object o) {
            if (!(o instanceof Employee employee)) return false;
            return Objects.equals(id, employee.id);
        }

        @Override
        public int hashCode() {
            return Objects.hashCode(id);
        }

        @Override
        public String toString() {
            return "Employee{" +
                    "id=" + id +
                    ", name='" + name + '\'' +
                    '}';
        }

    }

    @Getter
    @Setter
    @NoArgsConstructor
    @PlanningEntity
    public static class EmployeeAssignment {

        @PlanningId
        private UUID id;

        @ProblemFactProperty
        private Operation operation;

        @InverseRelationShadowVariable(sourceVariableName = "assignments")
        private Employee employee;

        @PreviousElementShadowVariable(sourceVariableName = "assignments")
        private EmployeeAssignment previousAssignment;

        @NextElementShadowVariable(sourceVariableName = "assignments")
        private EmployeeAssignment nextAssignment;

        @ShadowVariable(variableListenerClass = StartTimeUpdatingListener.class, sourceVariableName = "employee")
        @ShadowVariable(variableListenerClass = StartTimeUpdatingListener.class, sourceVariableName = "previousAssignment")
        private LocalDateTime startTime;

        @ShadowVariable(variableListenerClass = WorkstationUpdatingListener.class, sourceVariableName = "employee")
        private Workstation workstation;

        public EmployeeAssignment(Operation operation) {
            this.id = UUID.randomUUID();
            this.operation = operation;
        }

        public LocalDateTime getEndTime() {
            if (startTime == null) {
                return null;
            }
            return operation.getTimeUtil().getEndTime(startTime, operation.getDurationInMinutes());
        }

        public UUID getOperationId() {
            return operation.getId();
        }

        public UUID getEmployeeId() {
            return employee != null ? employee.getId() : null;
        }

        @Override
        public boolean equals(Object o) {
            if (!(o instanceof EmployeeAssignment assignment)) return false;
            return Objects.equals(id, assignment.id);
        }

        @Override
        public int hashCode() {
            return Objects.hashCode(id);
        }

        @Override
        public String toString() {
            return "EA{" +
                    "op=" + operation.name +
                    ", emp=" + (employee != null ? employee.name : "") +
                    '}';
        }

    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Workstation {

        @PlanningId
        private UUID id;
        private String name;

        @Override
        public boolean equals(Object o) {
            if (!(o instanceof Employee employee)) return false;
            return Objects.equals(id, employee.id);
        }

        @Override
        public int hashCode() {
            return Objects.hashCode(id);
        }

        @Override
        public String toString() {
            return "Employee{" +
                    "id=" + id +
                    ", name='" + name + '\'' +
                    '}';
        }
    }


    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @PlanningEntity
    public static class Operation {

        @PlanningId
        private UUID id;

        private LocalDateTime referenceTime;
        private TimeUtil timeUtil;
        private List<Workstation> candidateWorkstations;
        private List<UUID> candidateEmployeeIds;
        private String name;
        private Integer durationInMinutes;
        private int numberOfAssignees;
        private UUID manufacturingOrderId;
        private int manufacturingOrderRanking;
        private boolean parallelizable;
        private int taskSequence;
        private List<Operation> previousOperations;
        private List<Operation> nextOperations;

        @ShadowVariable(variableListenerClass = EmployeeAssignmentUpdatingListener.class, sourceEntityClass = EmployeeAssignment.class, sourceVariableName = "employee")
        private Set<EmployeeAssignment> employeeAssignments;

        public LocalDateTime getStartTime() {
            return employeeAssignments.stream()
                    .map(EmployeeAssignment::getStartTime)
                    .filter(Objects::nonNull)
                    .max(LocalDateTime::compareTo)
                    .orElse(null);
        }

        /*@ShadowVariable(variableListenerClass = OperationStartTimeUpdatingListener.class, sourceEntityClass = EmployeeAssignment.class, sourceVariableName = "startTime")
        private LocalDateTime startTime;*/

        public LocalDateTime getEndTime() {
            if (getStartTime() == null) return null;
            return timeUtil.getEndTime(getStartTime(), durationInMinutes);
        }


        @Override
        public boolean equals(Object o) {
            if (!(o instanceof Operation operation)) return false;
            return Objects.equals(id, operation.id);
        }

        @Override
        public int hashCode() {
            return Objects.hashCode(id);
        }

        @Override
        public String toString() {
            return "Operation{" +
                    "id=" + id +
                    ", name='" + name + '\'' +
                    '}';
        }
    }

    @SuppressWarnings("NullableProblems")
    public static class EmployeeAssignmentUpdatingListener implements VariableListener<ManufacturingSchedule, EmployeeAssignment> {

        @Override
        public void beforeVariableChanged(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
        }

        @Override
        public void afterVariableChanged(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
            var operation = assignment.getOperation();
            Set<EmployeeAssignment> updatedAssignments = new HashSet<>(operation.getEmployeeAssignments());
            updatedAssignments.removeIf(ea -> ea.id.equals(assignment.id)); // Remove old version
            updatedAssignments.add(assignment); // Add updated assignment

            scoreDirector.beforeVariableChanged(operation, "employeeAssignments");
            operation.setEmployeeAssignments(updatedAssignments);
            scoreDirector.afterVariableChanged(operation, "employeeAssignments");
        }

        @Override
        public void beforeEntityAdded(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
        }

        @Override
        public void afterEntityAdded(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
        }

        @Override
        public void beforeEntityRemoved(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
        }

        @Override
        public void afterEntityRemoved(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
        }
    }

    /*@SuppressWarnings("NullableProblems")
    public static class OperationStartTimeUpdatingListener implements VariableListener<ManufacturingSchedule, EmployeeAssignment> {

        @Override
        public void beforeVariableChanged(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
            // No action needed
        }

        @Override
        public void afterVariableChanged(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
            // Only update the operation if this assignment's start time has changed
            Operation operation = assignment.getOperation();

            // Recalculate the operation's start time
            LocalDateTime earliestStartTime = calculateOperationStartTime(operation);

            // Only update if there's a change
            if (!Objects.equals(earliestStartTime, operation.getStartTime())) {
                scoreDirector.beforeVariableChanged(operation, "startTime");
                operation.setStartTime(earliestStartTime);
                scoreDirector.afterVariableChanged(operation, "startTime");
            }
        }

        @Override
        public void beforeEntityAdded(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
            // No action needed
        }

        @Override
        public void afterEntityAdded(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
            // No action needed
        }

        @Override
        public void beforeEntityRemoved(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
            // No action needed
        }

        @Override
        public void afterEntityRemoved(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
            // No action needed
        }

        *//**
         * Calculates the start time of an operation based on all its assignments.
         * The operation starts when the earliest assignment starts.
         *//*
        private LocalDateTime calculateOperationStartTime(Operation operation) {
            if (operation.getEmployeeAssignments() == null || operation.getEmployeeAssignments().isEmpty()) {
                return null;
            }

            // The operation starts when the earliest assignment starts
            return operation.getEmployeeAssignments().stream()
                    .map(EmployeeAssignment::getStartTime)
                    .filter(Objects::nonNull)
                    .min(LocalDateTime::compareTo)
                    .orElse(null);
        }
    }*/



    @SuppressWarnings("NullableProblems")
    public static class StartTimeUpdatingListener implements VariableListener<ManufacturingSchedule, EmployeeAssignment> {
        static final String START_TIME = "startTime";

        static ThreadLocal<EmployeeAssignment> nextEmployeeAssignment = new ThreadLocal<>();

        @Override
        public void beforeVariableChanged(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
            nextEmployeeAssignment.set(assignment.getNextAssignment());
        }

        @Override
        public void afterVariableChanged(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
            if (assignment.getEmployee() == null) {
                if (assignment.getStartTime() != null) {
                    scoreDirector.beforeVariableChanged(assignment, START_TIME);
                    assignment.setStartTime(null);
                    scoreDirector.afterVariableChanged(assignment, START_TIME);
                    updateNextOperations(assignment.getOperation().nextOperations, scoreDirector);
                    updateImpactedAssignments(nextEmployeeAssignment.get(), scoreDirector);
                    nextEmployeeAssignment.remove();
                } else {
                    log.info("null employee null start time case for [{}]", assignment);
                    nextEmployeeAssignment.remove();
                }
                return;
            }
            //cleanup
            nextEmployeeAssignment.remove();

            //update own start time
            updateImpactedAssignments(assignment, scoreDirector);
        }

        @Override
        public void beforeEntityAdded(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
        }

        @Override
        public void afterEntityAdded(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
        }

        @Override
        public void beforeEntityRemoved(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
        }

        @Override
        public void afterEntityRemoved(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
        }

        private void updateImpactedAssignments(EmployeeAssignment assignment, ScoreDirector<ManufacturingSchedule> scoreDirector) {
            if (assignment == null) {
                return;
            }
            if (assignment.getStartTime() != null && getStartTime(assignment).isEqual(assignment.getStartTime())) {
                return; //break the updates as nothing has changed
            }
            try {
                Thread.sleep(5);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            setStartTime(scoreDirector, assignment);
            updateNextOperations(assignment.getOperation().nextOperations, scoreDirector);
            if (assignment.nextAssignment != null && !assignment.getOperation().nextOperations.contains(assignment.nextAssignment.operation)) {//this was already updated
                updateImpactedAssignments(assignment.nextAssignment, scoreDirector);
            }
        }

        private void updateNextOperations(List<Operation> nextOperations, ScoreDirector<ManufacturingSchedule> scoreDirector) {
            if (isEmpty(nextOperations)) {
                return;
            }
            nextOperations.stream()
                    .flatMap(operation -> operation.getEmployeeAssignments().stream())
                    .filter(ea -> ea.employee != null)
                    .forEach(ea -> updateImpactedAssignments(ea, scoreDirector));
        }

        private void setStartTime(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
            scoreDirector.beforeVariableChanged(assignment, START_TIME);
            assignment.setStartTime(getStartTime(assignment));
            scoreDirector.afterVariableChanged(assignment, START_TIME);
        }

        public LocalDateTime getStartTime(EmployeeAssignment assignment) {
            if (assignment.employee == null) {
                return null;
            }
            var previousAssignment = assignment.getPreviousAssignment();
            var operation = assignment.getOperation();
            return Stream.of(previousAssignment != null && previousAssignment.getEndTime() != null ? previousAssignment.getEndTime() : getEarliestStartTimeForNow(operation),
                            getStartTimeFromPreviousOperations(operation))
                    .max(LocalDateTime::compareTo)
                    .orElseGet(() -> getEarliestStartTimeForNow(operation));
        }

        private LocalDateTime getStartTimeFromPreviousOperations(Operation currentOperation) {
            return currentOperation.previousOperations.stream()
                    .filter(o -> o.getEndTime() != null)
                    .max(Comparator.comparing(Operation::getEndTime))
                    .map(Operation::getEndTime)
                    .orElseGet(() -> getEarliestStartTimeForNow(currentOperation));
        }

        private LocalDateTime getEarliestStartTimeForNow(Operation operation) {
            return operation.timeUtil.getStartTime(operation.timeUtil.localDateTimeForAccount()).truncatedTo(ChronoUnit.MINUTES);
        }
    }

    @SuppressWarnings("NullableProblems")
    public static class WorkstationUpdatingListener implements VariableListener<ManufacturingSchedule, EmployeeAssignment> {
        static final String WORKSTATION = "workstation";

        @Override
        public void beforeVariableChanged(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
        }

        @Override
        public void afterVariableChanged(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
            if (assignment.getEmployee() == null) {
                if (assignment.getWorkstation() != null) {
                    scoreDirector.beforeVariableChanged(assignment, WORKSTATION);
                    assignment.setWorkstation(null);
                    scoreDirector.afterVariableChanged(assignment, WORKSTATION);
                }
                return;
            }

            scoreDirector.beforeVariableChanged(assignment, WORKSTATION);
            assignment.setWorkstation(getWorkstation(assignment));
            scoreDirector.afterVariableChanged(assignment, WORKSTATION);
        }

        @Override
        public void beforeEntityAdded(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
        }

        @Override
        public void afterEntityAdded(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
        }

        @Override
        public void beforeEntityRemoved(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
        }

        @Override
        public void afterEntityRemoved(ScoreDirector<ManufacturingSchedule> scoreDirector, EmployeeAssignment assignment) {
        }

        public Workstation getWorkstation(EmployeeAssignment ea) {
            return (ea.operation != null && isNotEmpty(ea.operation.candidateWorkstations))
                    ? ea.operation.employeeAssignments.size() > 1
                    ? getWorkstationFromOtherAssignments(ea, ea.operation)
                    : randomWorkstation(ea.operation)
                    : null;
        }

        private Workstation getWorkstationFromOtherAssignments(EmployeeAssignment employeeAssignment, Operation operation) {
            return operation.employeeAssignments.stream()
                    .filter(ea -> !ea.id.equals(employeeAssignment.id) && ea.workstation != null)
                    .findFirst()
                    .map(ea -> ea.workstation)
                    .orElseGet(() -> randomWorkstation(operation));
        }

        private Workstation randomWorkstation(Operation operation) {
//            return operation.candidateWorkstations.get(secureRandom.nextInt(operation.candidateWorkstations.size()));
            return operation.candidateWorkstations.getFirst();
        }

    }


    @Getter
    @PlanningSolution
    public static class ManufacturingSchedule {
        @ProblemFactCollectionProperty
        private List<ManufacturingOrder> manufacturingOrders;

        @PlanningEntityCollectionProperty
        private List<Employee> employees;

        @ProblemFactCollectionProperty
        private List<Workstation> workstations;

        @PlanningEntityCollectionProperty
        private List<EmployeeAssignment> employeeAssignments;

        @PlanningEntityCollectionProperty
        private List<Operation> operations;

        @PlanningScore
        private HardSoftScore score;

        private DSLContext db;
        private TimeUtil timeUtil;
        private UUID accountId;

        public ManufacturingSchedule() {
        }

        public ManufacturingSchedule(
                DSLContext db,
                TimeUtil timeUtil,
                UUID accountId,
                List<Employee> employees,
                List<Workstation> workstations,
                List<Operation> operations,
                List<EmployeeAssignment> employeeAssignments,
                List<ManufacturingOrder> manufacturingOrders) {
            this.db = db;
            this.timeUtil = timeUtil;
            this.accountId = accountId;
            this.employees = employees;
            this.workstations = workstations;
            this.employeeAssignments = employeeAssignments;
            this.operations = operations;
            this.manufacturingOrders = manufacturingOrders;
        }

        @ValueRangeProvider(id = "employeeAssignmentsProvider")
        public List<EmployeeAssignment> getEmployeeAssignments() {
            return employeeAssignments;
        }

    }
}