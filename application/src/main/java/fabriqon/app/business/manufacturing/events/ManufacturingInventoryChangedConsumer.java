package fabriqon.app.business.manufacturing.events;

import fabriqon.app.business.inventory.InventoryChangedEvent;
import fabriqon.app.business.inventory.InventoryEntry;
import fabriqon.app.business.inventory.InventoryService;
import fabriqon.app.business.manufacturing.ManufacturingService;
import fabriqon.events.EventConsumer;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.INVENTORY;

@Component
public class ManufacturingInventoryChangedConsumer extends EventConsumer<InventoryChangedEvent> {

    private final DSLContext db;
    private final InventoryService inventory;
    private final ManufacturingService manufacturingService;

    @Autowired
    public ManufacturingInventoryChangedConsumer(DSLContext db, InventoryService inventory, ManufacturingService manufacturingService) {
        this.db = db;
        this.inventory = inventory;
        this.manufacturingService = manufacturingService;
    }

    @Override
    public void process(InventoryChangedEvent event) {
        var inventoryEntry = inventoryEntry(event.data.ownerId(), event.data.inventoryEntry());
        var context = event.data.manufacturingContext();

        // Skip if allocated to sales order
        if (inventoryEntry.salesOrderId() != null) {
            return;//no processing required for these types of entries
        }

        // Manufactured for a parent product so we reserve it as such
        if (context != null && context.parentManufacturingOrderId() != null) {
            inventory.reserveStockForManufacturingOrder(
                    inventoryEntry.ownerId(),
                    context.parentManufacturingOrderId(),
                    List.of(new InventoryService.Stock(inventoryEntry.materialGoodId(), inventoryEntry.quantity())),
                    true,
                    context.sourceManufacturingOrderId()
            );
            return;
        }

        manufacturingService.reDistributeMaterials(inventoryEntry.ownerId());
    }

    private InventoryEntry inventoryEntry(UUID ownerId, UUID entryId) {
        return db.selectFrom(INVENTORY).where(INVENTORY.OWNER_ID.eq(ownerId), INVENTORY.ID.eq(entryId)).fetchSingleInto(InventoryEntry.class);
    }
}
