package fabriqon.app.business.manufacturing.events;

import fabriqon.events.Event;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

public class TaskCompleted extends Event {

    public final TaskCompleted.Data data;

    public TaskCompleted(TaskCompleted.Data data) {
        this.data = data;
    }

    public record Data(
            UUID ownerId,
            UUID orderId,
            BigDecimal quantity
    ) {
    }

}
