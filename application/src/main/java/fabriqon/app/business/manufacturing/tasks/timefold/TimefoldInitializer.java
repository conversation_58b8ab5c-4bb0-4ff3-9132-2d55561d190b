package fabriqon.app.business.manufacturing.tasks.timefold;

import ai.timefold.solver.core.api.score.buildin.hardsoft.HardSoftScore;
import ai.timefold.solver.core.api.score.director.ScoreDirector;
import ai.timefold.solver.core.config.heuristic.selector.entity.EntitySelectorConfig;
import ai.timefold.solver.core.config.heuristic.selector.list.DestinationSelectorConfig;
import ai.timefold.solver.core.config.heuristic.selector.move.generic.list.ListChangeMoveSelectorConfig;
import ai.timefold.solver.core.config.heuristic.selector.value.ValueSelectorConfig;
import ai.timefold.solver.core.config.localsearch.LocalSearchPhaseConfig;
import ai.timefold.solver.core.config.phase.PhaseConfig;
import ai.timefold.solver.core.config.phase.custom.CustomPhaseConfig;
import ai.timefold.solver.core.config.solver.EnvironmentMode;
import ai.timefold.solver.core.config.solver.SolverConfig;
import ai.timefold.solver.core.impl.heuristic.selector.common.decorator.SelectionFilter;
import ai.timefold.solver.core.impl.heuristic.selector.move.generic.list.ListChangeMove;
import ai.timefold.solver.core.impl.phase.custom.CustomPhaseCommand;
import ai.timefold.solver.core.impl.score.director.InnerScoreDirector;
import fabriqon.app.business.manufacturing.tasks.TimefoldTaskScheduler;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Implementation using SelectionFilters instead of MoveListFactories
 */
@Slf4j
public class TimefoldInitializer {

    /**
     * Custom phase command that properly initializes the solution using ScoreDirector
     */
    public static class InitializationPhase implements CustomPhaseCommand<TimefoldTaskScheduler.ManufacturingSchedule> {
        @Override
        public void changeWorkingSolution(ScoreDirector<TimefoldTaskScheduler.ManufacturingSchedule> scoreDirector) {
            TimefoldTaskScheduler.ManufacturingSchedule solution = scoreDirector.getWorkingSolution();
            var innerScoreDirector = (InnerScoreDirector<TimefoldTaskScheduler.ManufacturingSchedule, HardSoftScore>) scoreDirector;

            log.info("Starting custom initialization phase");

            Map<UUID, TimefoldTaskScheduler.Employee> employeeMap = new HashMap<>();
            for (TimefoldTaskScheduler.Employee employee : solution.getEmployees()) {
                employeeMap.put(employee.getId(), employee);
            }

            // First pass: assign employees to assignments
            solution.getEmployeeAssignments().forEach(ea -> {
                var operation = ea.getOperation();
                var employee = employeeMap.get(operation.getCandidateEmployeeIds().getFirst());
                log.info("Assigning {} to {}", operation.getName(), employee.getName());
                // Properly notify the ScoreDirector about the list variable change
                scoreDirector.beforeListVariableChanged(employee, "assignments", employee.getAssignments().size() - 1, employee.getAssignments().size());
                employee.getAssignments().add(ea);
                scoreDirector.afterListVariableChanged(employee, "assignments", employee.getAssignments().size() - 1, employee.getAssignments().size());
                scoreDirector.triggerVariableListeners();
            });

            log.info("Custom initialization complete");
        }
    }

    /**
     * Filter to ensure only valid employees are considered for assignments
     */
    public static class CandidateEmployeeFilter implements SelectionFilter<TimefoldTaskScheduler.ManufacturingSchedule, ListChangeMove<TimefoldTaskScheduler.ManufacturingSchedule>> {
        @Override
        public boolean accept(ScoreDirector<TimefoldTaskScheduler.ManufacturingSchedule> scoreDirector,
                              ListChangeMove<TimefoldTaskScheduler.ManufacturingSchedule> move) {

            // Extract the assignment and target employee from the move
            TimefoldTaskScheduler.EmployeeAssignment assignment = (TimefoldTaskScheduler.EmployeeAssignment) move.getMovedValue();
            TimefoldTaskScheduler.Employee targetEmployee = (TimefoldTaskScheduler.Employee) move.getDestinationEntity();

            // Check if the employee is a valid candidate for this operation
            if (assignment == null || targetEmployee == null) {
                return false;
            }

            // Check if this employee is a candidate for this operation
            return assignment.getOperation().getCandidateEmployeeIds().contains(targetEmployee.getId());
        }
    }

    /**
     * Filter to ensure sequence constraints are respected, taking into account the insertion index
     */
    public static class SequenceConstraintFilter implements SelectionFilter<TimefoldTaskScheduler.ManufacturingSchedule, ListChangeMove<TimefoldTaskScheduler.ManufacturingSchedule>> {

        @Override
        public boolean accept(ScoreDirector<TimefoldTaskScheduler.ManufacturingSchedule> scoreDirector,
                              ListChangeMove<TimefoldTaskScheduler.ManufacturingSchedule> move) {

            // Extract the assignment and target employee from the move
            TimefoldTaskScheduler.EmployeeAssignment assignment = (TimefoldTaskScheduler.EmployeeAssignment) move.getMovedValue();
            TimefoldTaskScheduler.Employee targetEmployee = (TimefoldTaskScheduler.Employee) move.getDestinationEntity();
            int destinationIndex = move.getDestinationIndex();

            // Return false if either assignment or employee is null
            if (assignment == null || targetEmployee == null) {
                return false;
            }

            // Get the operation from the assignment
            TimefoldTaskScheduler.Operation operation = assignment.getOperation();

            // Get current assignments for this employee
            List<TimefoldTaskScheduler.EmployeeAssignment> currentAssignments = targetEmployee.getAssignments();

            // Create a new list that represents what the assignments would look like after the move
            List<TimefoldTaskScheduler.EmployeeAssignment> simulatedAssignments = new ArrayList<>(currentAssignments);

            // Handle the case where the destination index is out of bounds (append to the end)
            if (destinationIndex >= simulatedAssignments.size()) {
                simulatedAssignments.add(assignment);
            } else {
                simulatedAssignments.add(destinationIndex, assignment);
            }

            // Check basic sequence constraints and cross-workflow deadlocks
            return checkBasicSequenceConstraints(simulatedAssignments, operation)
//                    && !wouldCreateDeadlock(scoreDirector.getWorkingSolution(), targetEmployee, assignment, destinationIndex)
                    ;
        }

        // Check basic sequence constraints
        private boolean checkBasicSequenceConstraints(
                List<TimefoldTaskScheduler.EmployeeAssignment> simulatedAssignments,
                TimefoldTaskScheduler.Operation operation) {

            UUID orderId = operation.getManufacturingOrderId();
            int sequence = operation.getTaskSequence();

            // Group assignments by manufacturing order and collect their sequence numbers
            Map<UUID, List<Integer>> orderSequences = simulatedAssignments.stream()
                    .collect(Collectors.groupingBy(
                            assignment -> assignment.getOperation().getManufacturingOrderId(),
                            Collectors.mapping(
                                    assignment -> assignment.getOperation().getTaskSequence(),
                                    Collectors.toList()
                            )
                    ));

            // If no assignments for this order, no constraints to check
            if (!orderSequences.containsKey(orderId)) {
                return true;
            }

            List<Integer> existingSequences = orderSequences.get(orderId);

            // Check if there are operations with higher sequence numbers
            boolean hasLaterOperations = existingSequences.stream()
                    .anyMatch(existingSeq -> existingSeq > sequence);
            if (hasLaterOperations) {
                return false;
            }

            // Check for non-parallelizable operations with the same sequence
            boolean hasSameSequence = existingSequences.contains(sequence);
            if (hasSameSequence) {
                boolean hasNonParallelizableOpWithSameSequence = simulatedAssignments.stream()
                        .filter(a -> a.getOperation().getManufacturingOrderId().equals(orderId))
                        .filter(a -> a.getOperation().getTaskSequence() == sequence)
                        .anyMatch(a -> !a.getOperation().isParallelizable());

                if (hasNonParallelizableOpWithSameSequence || !operation.isParallelizable()) {
                    return false;
                }
            }

            // Check if we're missing predecessor operations
            if (!operation.isParallelizable()) {
                boolean isMissingPredecessor = IntStream.range(1, sequence)
                        .anyMatch(i -> !existingSequences.contains(i));
                if (isMissingPredecessor) {
                    return false;
                }
            }

            log.info("assigning: [{}] with assignments [{}]", operation.getName(), simulatedAssignments);
            return true;
        }

        // Helper record to identify an operation
        private record OperationKey(UUID orderId, int sequence) {}

        // Check for potential deadlocks
        private boolean wouldCreateDeadlock(
                TimefoldTaskScheduler.ManufacturingSchedule schedule,
                TimefoldTaskScheduler.Employee employee,
                TimefoldTaskScheduler.EmployeeAssignment newAssignment,
                int destinationIndex) {

            // Build dependency graph with the new assignment
            Map<OperationKey, Set<OperationKey>> dependencies = buildDependencyGraph(
                    schedule,
                    employee,
                    new OperationKey(
                            newAssignment.getOperation().getManufacturingOrderId(),
                            newAssignment.getOperation().getTaskSequence()
                    ),
                    destinationIndex
            );

            // Check for cycles using functional DFS approach
            return hasCycle(dependencies);
        }

        // Build a dependency graph including the new operation
        private Map<OperationKey, Set<OperationKey>> buildDependencyGraph(
                TimefoldTaskScheduler.ManufacturingSchedule schedule,
                TimefoldTaskScheduler.Employee employee,
                OperationKey newOpKey,
                int destinationIndex) {

            // Initialize with workflow dependencies
            Map<OperationKey, Set<OperationKey>> dependencies = initializeWorkflowDependencies(schedule);

            // Add employee-based dependencies
            addEmployeeDependencies(dependencies, schedule.getEmployees());

            // Add dependencies for the new operation with respect to the insertion index
            addNewOperationDependencies(dependencies, employee, newOpKey, destinationIndex);

            return dependencies;
        }

        // Initialize the dependency graph with workflow-based dependencies
        private Map<OperationKey, Set<OperationKey>> initializeWorkflowDependencies(
                TimefoldTaskScheduler.ManufacturingSchedule schedule) {

            return schedule.getOperations().stream()
                    .flatMap(op -> {
                        OperationKey key = new OperationKey(op.getManufacturingOrderId(), op.getTaskSequence());

                        // Create entry mapping from each operation to its next operations
                        return op.getNextOperations().stream()
                                .map(nextOp -> Map.entry(
                                        key,
                                        new OperationKey(nextOp.getManufacturingOrderId(), nextOp.getTaskSequence())
                                ));
                    })
                    .collect(Collectors.groupingBy(
                            Map.Entry::getKey,
                            Collectors.mapping(
                                    Map.Entry::getValue,
                                    Collectors.toSet()
                            )
                    ));
        }

        // Add dependencies based on employee assignment order
        private void addEmployeeDependencies(
                Map<OperationKey, Set<OperationKey>> dependencies,
                List<TimefoldTaskScheduler.Employee> employees) {

            employees.forEach(emp -> {
                List<TimefoldTaskScheduler.EmployeeAssignment> assignments = emp.getAssignments();

                // For each assignment, create dependencies to all later assignments
                IntStream.range(0, assignments.size() - 1).forEach(i -> {
                    OperationKey sourceKey = new OperationKey(
                            assignments.get(i).getOperation().getManufacturingOrderId(),
                            assignments.get(i).getOperation().getTaskSequence()
                    );

                    // Ensure source key exists in map
                    dependencies.computeIfAbsent(sourceKey, k -> new HashSet<>());

                    // Add dependencies to all later assignments
                    IntStream.range(i + 1, assignments.size()).forEach(j -> {
                        OperationKey targetKey = new OperationKey(
                                assignments.get(j).getOperation().getManufacturingOrderId(),
                                assignments.get(j).getOperation().getTaskSequence()
                        );

                        dependencies.get(sourceKey).add(targetKey);
                        dependencies.computeIfAbsent(targetKey, k -> new HashSet<>());
                    });
                });
            });
        }

        // Add dependencies for the new operation, considering the insertion index
        private void addNewOperationDependencies(
                Map<OperationKey, Set<OperationKey>> dependencies,
                TimefoldTaskScheduler.Employee employee,
                OperationKey newOpKey,
                int destinationIndex) {

            // Ensure new operation key exists in map
            dependencies.computeIfAbsent(newOpKey, k -> new HashSet<>());

            List<TimefoldTaskScheduler.EmployeeAssignment> assignments = employee.getAssignments();
            int assignmentsSize = assignments.size();

            // Determine the actual index for insertion
            int actualIndex = Math.min(destinationIndex, assignmentsSize);

            // Create dependencies from operations before the insertion index to the new operation
            IntStream.range(0, actualIndex).forEach(i -> {
                OperationKey existingKey = new OperationKey(
                        assignments.get(i).getOperation().getManufacturingOrderId(),
                        assignments.get(i).getOperation().getTaskSequence()
                );
                dependencies.computeIfAbsent(existingKey, k -> new HashSet<>()).add(newOpKey);
            });

            // Create dependencies from the new operation to operations after the insertion index
            IntStream.range(actualIndex, assignmentsSize).forEach(i -> {
                OperationKey existingKey = new OperationKey(
                        assignments.get(i).getOperation().getManufacturingOrderId(),
                        assignments.get(i).getOperation().getTaskSequence()
                );
                dependencies.get(newOpKey).add(existingKey);
                dependencies.computeIfAbsent(existingKey, k -> new HashSet<>());
            });
        }

        // Check for cycles in the dependency graph using functional DFS
        private boolean hasCycle(Map<OperationKey, Set<OperationKey>> graph) {
            Set<OperationKey> visited = new HashSet<>();
            Set<OperationKey> recursionStack = new HashSet<>();

            return graph.keySet().stream()
                    .anyMatch(node -> !visited.contains(node) &&
                            detectCycleDFS(graph, node, visited, recursionStack));
        }

        // Functional DFS for cycle detection
        private boolean detectCycleDFS(
                Map<OperationKey, Set<OperationKey>> graph,
                OperationKey current,
                Set<OperationKey> visited,
                Set<OperationKey> recursionStack) {

            // If already in recursion stack, we found a cycle
            if (recursionStack.contains(current)) {
                return true;
            }

            // If already visited in a different path, no cycle here
            if (visited.contains(current)) {
                return false;
            }

            // Mark as visited and add to recursion stack
            visited.add(current);
            recursionStack.add(current);

            // Check neighbors for cycles
            boolean hasCycle = graph.getOrDefault(current, Set.of()).stream()
                    .anyMatch(neighbor -> detectCycleDFS(graph, neighbor, visited, recursionStack));

            // Remove from recursion stack after processing
            recursionStack.remove(current);

            return hasCycle;
        }
    }

    /**
     * Configure solver using SelectionFilters instead of MoveListFactories
     */
    public static SolverConfig getSolverConfig(int solutionDuration) {
        // Create the solver config
        SolverConfig solverConfig = new SolverConfig()
                .withEnvironmentMode(EnvironmentMode.TRACKED_FULL_ASSERT)
                .withConstraintProviderClass(TimefoldTaskScheduler.ManufacturingConstraintProvider.class)
                .withSolutionClass(TimefoldTaskScheduler.ManufacturingSchedule.class)
                .withEntityClasses(TimefoldTaskScheduler.Employee.class,
                        TimefoldTaskScheduler.EmployeeAssignment.class,
                        TimefoldTaskScheduler.Operation.class)
                .withTerminationSpentLimit(Duration.ofSeconds(solutionDuration));

        // Create phase config list
        List<PhaseConfig> phaseConfigList = new ArrayList<>();

        // 1. Custom phase for proper initialization
        CustomPhaseConfig customPhaseConfig = new CustomPhaseConfig();
        customPhaseConfig.setCustomPhaseCommandClassList(List.of(InitializationPhase.class));
//        phaseConfigList.add(customPhaseConfig);

        // 2. Local search phase with selection filters
        LocalSearchPhaseConfig localSearchPhaseConfig = new LocalSearchPhaseConfig();

        // Create a list change move selector config for the LS phase
        ListChangeMoveSelectorConfig lsListChangeMoveSelectorConfig = new ListChangeMoveSelectorConfig();

        // Configure the destination entity selector - Employee entities
        DestinationSelectorConfig lsDestinationSelectorConfig = new DestinationSelectorConfig();
        lsDestinationSelectorConfig.setEntitySelectorConfig(new EntitySelectorConfig(TimefoldTaskScheduler.Employee.class));
        lsListChangeMoveSelectorConfig.setDestinationSelectorConfig(lsDestinationSelectorConfig);

        // Configure the value selector - EmployeeAssignment values
        ValueSelectorConfig lsValueMoveSelectorConfig = new ValueSelectorConfig();
        lsValueMoveSelectorConfig.setVariableName("assignments");
        lsListChangeMoveSelectorConfig.setValueSelectorConfig(lsValueMoveSelectorConfig);

        // Add selection filters to ensure only valid moves are considered
        lsListChangeMoveSelectorConfig.withFilterClass(CandidateEmployeeFilter.class).withFilterClass(SequenceConstraintFilter.class);

        // Set the move selector on the local search phase
        localSearchPhaseConfig.setMoveSelectorConfig(lsListChangeMoveSelectorConfig);

        // Add the local search phase to the phase config list
        phaseConfigList.add(localSearchPhaseConfig);

        // Set the phase config list on the solver config
        solverConfig.setPhaseConfigList(phaseConfigList);

        return solverConfig;
    }
}