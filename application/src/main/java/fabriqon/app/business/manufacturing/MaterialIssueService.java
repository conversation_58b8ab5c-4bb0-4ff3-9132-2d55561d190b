package fabriqon.app.business.manufacturing;

import fabriqon.app.business.exceptions.BusinessException;
import fabriqon.app.business.goods.RequiredMaterial;
import fabriqon.app.business.inventory.InventoryService;
import fabriqon.app.business.sequence.Sequences;
import fabriqon.app.business.services.ServicingOrder;
import fabriqon.app.common.model.Money;
import fabriqon.misc.Json;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.*;
import static java.lang.String.join;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

@Component
@Transactional
public class MaterialIssueService {

    private final DSLContext db;
    private final Sequences sequences;
    private final InventoryService inventoryService;

    @Autowired
    public MaterialIssueService(DSLContext db, Sequences sequences, InventoryService inventoryService) {
        this.db = db;
        this.sequences = sequences;
        this.inventoryService = inventoryService;
    }

    public MaterialIssueNote issueForManufacturing(UUID ownerId,
                                                   UUID manufacturingOrderId,
                                                   LocalDate date,
                                                   String inventoryManager,
                                                   String worker,
                                                   List<UsedMaterial> usedMaterials,
                                                   Money manufacturingCosts) {
        var defaultInventoryUnitId = inventoryService.defaultInventoryUnitId(ownerId);
        checkMaterialAvailability(ownerId, usedMaterials, defaultInventoryUnitId);
        var order = manufacturingOrder(manufacturingOrderId);
        var materials = materialsForManufacturingOrder(ownerId, usedMaterials, defaultInventoryUnitId, order.manufacturingOperations().stream().flatMap(op -> op.materials().stream()).toList());
        var note = createMaterialIssueNote(ownerId, manufacturingOrderId, null, date, inventoryManager, worker, materials);
        updateCosts(ownerId, manufacturingOrderId, null, order, materials, manufacturingCosts);
        usedMaterials.forEach(material -> inventoryService.removeForManufacturingOrder(ownerId, material.id(), defaultInventoryUnitId, material.quantity(), manufacturingOrderId));
        return note;
    }

    /**
     * Issue materials for a servicing order
     */
    public MaterialIssueNote issueForServicing(UUID ownerId,
                                               UUID servicingOrderId,
                                               LocalDate date,
                                               String inventoryManager,
                                               String worker,
                                               List<UsedMaterial> usedMaterials,
                                               Money manufacturingCosts) {
        var defaultInventoryUnitId = inventoryService.defaultInventoryUnitId(ownerId);
        checkMaterialAvailability(ownerId, usedMaterials, defaultInventoryUnitId);
        var order = servicingOrder(servicingOrderId);
        var materials = materialsForServicingOrder(ownerId, usedMaterials, defaultInventoryUnitId);
        var note = createMaterialIssueNote(ownerId, null, servicingOrderId, date, inventoryManager, worker, materials);
        updateCosts(ownerId, null, servicingOrderId, order, materials, manufacturingCosts);
        usedMaterials.forEach(material -> inventoryService.removeForServicingOrder(ownerId, material.id(), defaultInventoryUnitId, material.quantity(), servicingOrderId));
        return note;
    }

    private void checkMaterialAvailability(UUID ownerId, List<UsedMaterial> usedMaterials, UUID inventoryUnitId) {
        var notAvailableItems = usedMaterials.stream()
                .filter(material -> inventoryService.getCurrentStock(ownerId, material.id(), inventoryUnitId).compareTo(material.quantity()) < 0)
                .map(UsedMaterial::id)
                .toList();

        if (isNotEmpty(notAvailableItems)) {
            throw new BusinessException("Can't remove more materials than are available on stock",
                    "not_enough_stock_to_remove",
                    join(", ", db.select(MATERIAL_GOOD.NAME)
                            .from(MATERIAL_GOOD)
                            .where(MATERIAL_GOOD.ID.in(notAvailableItems))
                            .fetchInto(String.class)));
        }
    }

    private MaterialIssueNote createMaterialIssueNote(UUID ownerId,
                                                      UUID manufacturingOrderId,
                                                      UUID servicingOrderId,
                                                      LocalDate date,
                                                      String inventoryManager,
                                                      String worker,
                                                      List<MaterialIssueNote.Material> materials) {
        var id = UUID.randomUUID();
        var number = sequences.nextSequenceForMaterialIssueNote(ownerId);
        var details = new MaterialIssueNote.Details(inventoryManager, worker);

        db.insertInto(MATERIAL_ISSUE_NOTE)
                .set(MATERIAL_ISSUE_NOTE.ID, id)
                .set(MATERIAL_ISSUE_NOTE.OWNER_ID, ownerId)
                .set(MATERIAL_ISSUE_NOTE.MANUFACTURING_ORDER_ID, manufacturingOrderId)
                .set(MATERIAL_ISSUE_NOTE.SERVICING_ORDER_ID, servicingOrderId)
                .set(MATERIAL_ISSUE_NOTE.NUMBER, number)
                .set(MATERIAL_ISSUE_NOTE.DATE, date.atStartOfDay())
                .set(MATERIAL_ISSUE_NOTE.DETAILS, JSONB.jsonb(Json.write(details)))
                .set(MATERIAL_ISSUE_NOTE.MATERIALS, JSONB.jsonb(Json.write(materials)))
                .execute();

        return new MaterialIssueNote(id, Instant.now(), Instant.now(), false, ownerId,
                manufacturingOrderId, servicingOrderId, number, date, details, materials);
    }

    private void updateCosts(UUID ownerId,
                             UUID manufacturingOrderId,
                             UUID servicingOrderId,
                             Object order,
                             List<MaterialIssueNote.Material> materials,
                             Money manufacturingCosts) {
        // Calculate total material cost
        long totalMaterialCost = materials.stream().mapToLong(m -> m.totalCost().amount()).sum();
        long totalCost = totalMaterialCost + manufacturingCosts.amount();

        if (order instanceof ManufacturingOrder manufacturingOrder && manufacturingOrder.productId() != null) {
            //we update the inventory entry with the right amount for this MO;
            // this assumes that we have 1 one entry per MO (as the current implementation);
            // the query below should fail when that is not the case and we'll need to adjust the approach
            //FIXME it is hacky to update the inventory directly from this service. we should add the items to the
            // inventory with the right price but we would need to change the current flow to do that
            var quantity = db.select(INVENTORY.QUANTITY)
                    .from(INVENTORY)
                    .where(INVENTORY.OWNER_ID.eq(ownerId), INVENTORY.MANUFACTURING_ORDER_ID.eq(manufacturingOrderId), INVENTORY.QUANTITY.greaterThan(BigDecimal.ZERO))
                    .fetchSingleInto(BigDecimal.class);
            db.update(INVENTORY)
                    .set(INVENTORY.RECEPTION_PRICE_AMOUNT,
                            BigDecimal.valueOf(totalCost)
                                    .divide(quantity, RoundingMode.HALF_EVEN).longValue())
                    .where(INVENTORY.OWNER_ID.eq(ownerId), INVENTORY.MANUFACTURING_ORDER_ID.eq(manufacturingOrderId))
                    .execute();
        } else {
            var serviceId = order instanceof ManufacturingOrder manufacturingOrder ? manufacturingOrder.serviceId() : ((ServicingOrder) order).serviceId();
            var quantity = db.select(EXECUTED_SERVICES.QUANTITY)
                    .from(EXECUTED_SERVICES)
                    .where(EXECUTED_SERVICES.OWNER_ID.eq(ownerId), EXECUTED_SERVICES.SERVICING_ORDER_ID.eq(servicingOrderId), EXECUTED_SERVICES.SERVICE_ID.eq(serviceId))
                    .fetchSingleInto(BigDecimal.class);
            db.update(EXECUTED_SERVICES)
                    .set(EXECUTED_SERVICES.COST_AMOUNT, BigDecimal.valueOf(totalCost).divide(quantity, RoundingMode.HALF_EVEN).longValue())
                    .where(EXECUTED_SERVICES.OWNER_ID.eq(ownerId), EXECUTED_SERVICES.SERVICING_ORDER_ID.eq(servicingOrderId), EXECUTED_SERVICES.SERVICE_ID.eq(serviceId))
                    .execute();
        }
    }

    private List<MaterialIssueNote.Material> materialsForManufacturingOrder(UUID ownerId, List<UsedMaterial> materials,
                                                                            UUID defaultInventoryUnitId,
                                                                            List<RequiredMaterial> requiredMaterials) {
        return materials.stream().map(material ->
                        new MaterialIssueNote.Material(material.id(), defaultInventoryUnitId, material.quantity(),
                                inventoryService.getCostForQuantity(ownerId, material.id(), defaultInventoryUnitId, material.quantity()),
                                material.quantity()
                                        .multiply(requiredMaterials.stream()
                                                .filter(m -> m.materialIds().contains(material.id()))
                                                .map(m -> m.wastePercentage() != null ? m.wastePercentage() : BigDecimal.ZERO)
                                                .findFirst().orElse(BigDecimal.ZERO)
                                        )))
                .toList();
    }

    private List<MaterialIssueNote.Material> materialsForServicingOrder(UUID ownerId, List<UsedMaterial> materials,
                                                                        UUID defaultInventoryUnitId) {
        return materials.stream().map(material ->
                        new MaterialIssueNote.Material(material.id(), defaultInventoryUnitId, material.quantity(),
                                inventoryService.getCostForQuantity(ownerId, material.id(), defaultInventoryUnitId, material.quantity()),
                                BigDecimal.ZERO))
                .toList();
    }

    private ManufacturingOrder manufacturingOrder(UUID orderId) {
        return db.selectFrom(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.ID.eq(orderId))
                .fetchSingleInto(ManufacturingOrder.class);
    }

    private ServicingOrder servicingOrder(UUID orderId) {
        return db.selectFrom(SERVICING_ORDER)
                .where(SERVICING_ORDER.ID.eq(orderId))
                .fetchSingleInto(ServicingOrder.class);
    }
}
