package fabriqon.app.business.manufacturing;

import fabriqon.app.business.goods.RequiredMaterial;
import fabriqon.app.common.model.Money;

import java.util.List;
import java.util.UUID;

import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

public record ManufacturingOperation(
        UUID operationTemplateId,
        List<UUID> candidateWorkstationIds,
        List<UUID> candidateEmployeeIds,
        String name,
        Integer durationInMinutes,
        Boolean parallelizable,
        Money costPerHour,
        Integer numberOfAssignees,
        List<UUID> manuallyAssignedEmployees,
        List<UUID> manuallyAssignedWorkstations,
        List<RequiredMaterial> materials
) {

    public ManufacturingOperation numberOfAssignees(int numberOfAssignees) {
        return new ManufacturingOperation(operationTemplateId, candidateWorkstationIds, candidateEmployeeIds, name,
                durationInMinutes, parallelizable, costPerHour, numberOfAssignees, manuallyAssignedEmployees, manuallyAssignedWorkstations, materials);
    }

    public ManufacturingOperation manuallyAssignedEmployees(List<UUID> manuallyAssignedEmployees) {
        return new ManufacturingOperation(operationTemplateId, candidateWorkstationIds, candidateEmployeeIds, name,
                durationInMinutes, parallelizable, costPerHour,
                isNotEmpty(manuallyAssignedEmployees) ? (Integer) manuallyAssignedEmployees.size() : numberOfAssignees,
                manuallyAssignedEmployees, manuallyAssignedWorkstations, materials);
    }

    public ManufacturingOperation manuallyAssignedWorkstations(List<UUID> manuallyAssignedWorkstations) {
        return new ManufacturingOperation(operationTemplateId, candidateWorkstationIds, candidateEmployeeIds, name,
                durationInMinutes, parallelizable, costPerHour, numberOfAssignees, manuallyAssignedEmployees, manuallyAssignedWorkstations, materials);
    }

    public ManufacturingOperation setDuration(int durationInMinutes) {
        return new ManufacturingOperation(operationTemplateId, candidateWorkstationIds, candidateEmployeeIds, name, durationInMinutes, parallelizable, costPerHour, numberOfAssignees, manuallyAssignedEmployees, manuallyAssignedWorkstations, materials);
    }

    public ManufacturingOperation setMaterials(List<RequiredMaterial> materials) {
        return new ManufacturingOperation(operationTemplateId, candidateWorkstationIds, candidateEmployeeIds, name, durationInMinutes, parallelizable, costPerHour, numberOfAssignees, manuallyAssignedEmployees, manuallyAssignedWorkstations, materials);
    }
}
