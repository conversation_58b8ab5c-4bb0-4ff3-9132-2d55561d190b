package fabriqon.app.business.manufacturing.events;

import fabriqon.app.business.employees.EmployeeTimeoffAdded;
import fabriqon.app.business.manufacturing.tasks.ManufacturingTaskService;
import fabriqon.events.EventConsumer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class EmployeeTimeoffAddedConsumer extends EventConsumer<EmployeeTimeoffAdded> {

    private final ManufacturingTaskService manufacturingTaskService;

    @Autowired
    public EmployeeTimeoffAddedConsumer(ManufacturingTaskService manufacturingTaskService) {
        this.manufacturingTaskService = manufacturingTaskService;
    }

    @Override
    public void process(EmployeeTimeoffAdded event) {
        manufacturingTaskService.employeeTimeoffAdded(event.accountId, event.data.employeeId(), event.data.start(), event.data.end());
    }

}
