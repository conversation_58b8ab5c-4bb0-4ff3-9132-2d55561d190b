package fabriqon.app.business.manufacturing.tasks;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.exceptions.BusinessException;
import fabriqon.app.business.manufacturing.ManufacturingOperation;
import fabriqon.misc.Tuple;
import org.jooq.DSLContext;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static fabriqon.app.business.manufacturing.ManufacturingOrder.UNRANKED_ORDER_VALUE;
import static fabriqon.jooq.classes.Tables.*;
import static fabriqon.jooq.classes.Tables.EMPLOYEE_TIMEOFF;
import static org.apache.commons.lang3.BooleanUtils.isTrue;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isBlank;

class TaskScheduler {

    private final DSLContext db;
    private final TimeUtil timeUtil;

    public TaskScheduler(DSLContext db, Account.Settings accountSettings) {
        this.db = db;
        this.timeUtil = new TimeUtil(accountSettings);
    }

    /**
     * Schedule the task based on the operation by finding a workstation that is available (if there is one required);
     * once the workstation is identified we find an employee that is available
     */
    Tuple.Tuple4<UUID, List<UUID>, LocalDateTime, LocalDateTime> schedule(ManufacturingOperation operation,
                                                                          LocalDateTime earliestStartTime,
                                                                          int totalDurationMinutes,
                                                                          List<UUID> previousAssignees) {
        var workstationSchedule = workstationSchedule(operation, earliestStartTime);
        var noOfEmployees = operation.numberOfAssignees() != null ? operation.numberOfAssignees() : 1;
        var employeesWithPreferences = employeesWithPreferences(operation.name(), assignableEmployeeIds(operation));
        if (assignableWorkstationIds(operation).isEmpty()) {//no workstation is required. we just find an employee
            return employees(employeesWithPreferences, timeUtil.getStartTime(earliestStartTime),
                    LocalDateTime.MAX, previousAssignees, employeeSchedule(assignableEmployeeIds(operation), timeUtil.getStartTime(earliestStartTime)), totalDurationMinutes, noOfEmployees, operation.name())
                    .map(e -> Tuple.of((UUID) null, e.a(), e.b(), e.c()))
                    .orElseThrow(() -> new RuntimeException("can't schedule task for employee"));
        }

        var candidateWorkstations = new ArrayList<>(assignableWorkstationIds(operation));
        candidateWorkstations.removeAll(workstationSchedule.stream().map(Tuple.Tuple3::a).toList());
        if (!candidateWorkstations.isEmpty()) {//there is a free workstation. we just find an employee
            return employees(employeesWithPreferences, timeUtil.getStartTime(earliestStartTime),
                    LocalDateTime.MAX, previousAssignees, employeeSchedule(assignableEmployeeIds(operation), timeUtil.getStartTime(earliestStartTime)), totalDurationMinutes, noOfEmployees, operation.name())
                    .map(e -> Tuple.of(candidateWorkstations.get(0), e.a(), e.b(), e.c()))
                    .orElseThrow(() -> new RuntimeException("can't schedule task for employee"));
        }

        var intervals = timeUtil.intervalsByResource(assignableWorkstationIds(operation), earliestStartTime, workstationSchedule);
        for (Tuple.Tuple3<UUID, LocalDateTime, LocalDateTime> current : intervals) {
            var startTime = timeUtil.getStartTime(current.b());
            var endTime = timeUtil.getEndTime(startTime, totalDurationMinutes);
            if (endTime.isBefore(current.c())) {
                var employee = employees(employeesWithPreferences, startTime, current.c(), previousAssignees,
                        employeeSchedule(assignableEmployeeIds(operation), startTime), totalDurationMinutes, noOfEmployees, operation.name());
                if (employee.isPresent()) {
                    return employee.map(e -> Tuple.of(current.a(), e.a(), e.b(), e.c())).get();
                }
            }
        }

        var workstation = workstationSchedule.stream()
                .collect(Collectors.toMap(Tuple.Tuple3::a, Function.identity(),
                        BinaryOperator.maxBy(Comparator.comparing(Tuple.Tuple3::c))))
                .entrySet()
                .stream()
                .min(Comparator.comparing(e -> e.getValue().c()))
                .map(Map.Entry::getValue)
                .orElseThrow();
        return employees(employeesWithPreferences, workstation.c(), LocalDateTime.MAX, previousAssignees,
                employeeSchedule(assignableEmployeeIds(operation), workstation.c()), totalDurationMinutes, noOfEmployees, operation.name())
                .map(e -> Tuple.of(workstation.a(), e.a(), e.b(), e.c()))
                .orElseThrow(() -> new RuntimeException("can't schedule task for employee"));
    }

    private List<UUID> assignableWorkstationIds(ManufacturingOperation operation) {
        return isNotEmpty(operation.manuallyAssignedWorkstations()) ? operation.manuallyAssignedWorkstations() : operation.candidateWorkstationIds();
    }

    private List<UUID> assignableEmployeeIds(ManufacturingOperation operation) {
        return isNotEmpty(operation.manuallyAssignedEmployees()) ? operation.manuallyAssignedEmployees() : operation.candidateEmployeeIds();
    }

    private ArrayList<Tuple.Tuple3<UUID, LocalDateTime, LocalDateTime>> workstationSchedule(ManufacturingOperation operation, LocalDateTime from) {
        return new ArrayList<>(db.select(
                        MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_WORKSTATION_ID,
                        MANUFACTURING_TASK.START_TIME,
                        MANUFACTURING_TASK.END_TIME)
                .from(MANUFACTURINGTASK_WORKSTATION)
                .join(MANUFACTURING_TASK).on(MANUFACTURING_TASK.ID.eq(MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_TASK_ID))
                .where(MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_WORKSTATION_ID.in(assignableWorkstationIds(operation)),
                        MANUFACTURING_TASK.END_TIME.gt(from))
                .orderBy(MANUFACTURING_TASK.START_TIME)
                .fetchInto(Tuple.Tuple3.class)
                .stream().map(e -> Tuple.of((UUID) e.a(), (LocalDateTime) e.b(), (LocalDateTime) e.c())).toList());
    }

    private List<Tuple.Tuple3<UUID, LocalDateTime, LocalDateTime>> employeeSchedule(List<UUID> candidateEmployees, LocalDateTime from) {
        var taskSchedule = new ArrayList<>(db.select(MANUFACTURINGTASK_EMPLOYEE.USER_ID,
                        MANUFACTURING_TASK.START_TIME,
                        MANUFACTURING_TASK.END_TIME)
                .from(MANUFACTURINGTASK_EMPLOYEE)
                .join(MANUFACTURING_TASK).on(MANUFACTURING_TASK.ID.eq(MANUFACTURINGTASK_EMPLOYEE.MANUFACTURING_TASK_ID))
                .where(
                        MANUFACTURINGTASK_EMPLOYEE.USER_ID.in(candidateEmployees),
                        MANUFACTURING_TASK.END_TIME.gt(from)
                )
                .orderBy(MANUFACTURING_TASK.START_TIME)
                .fetchInto(Tuple.Tuple3.class)
                .stream().map(e -> Tuple.of((UUID) e.a(), (LocalDateTime) e.b(), (LocalDateTime) e.c())).toList());
        var timeoffSchedule = new ArrayList<>(db.select(EMPLOYEE_TIMEOFF.USER_ID, EMPLOYEE_TIMEOFF.START_TIME, EMPLOYEE_TIMEOFF.END_TIME)
                .from(EMPLOYEE_TIMEOFF)
                .where(EMPLOYEE_TIMEOFF.USER_ID.in(candidateEmployees), EMPLOYEE_TIMEOFF.END_TIME.gt(from))
                .orderBy(EMPLOYEE_TIMEOFF.START_TIME)
                .fetchInto(Tuple.Tuple3.class)
                .stream().map(e -> Tuple.of((UUID) e.a(), (LocalDateTime) e.b(), (LocalDateTime) e.c())).toList()
        );
        return Stream.concat(taskSchedule.stream(), timeoffSchedule.stream())
                .sorted(Comparator.comparing(Tuple.Tuple3::b))
                .toList();
    }

    //tries to assign the task to an employee
    private Optional<Tuple.Tuple3<List<UUID>, LocalDateTime, LocalDateTime>> employees(Map<UUID, Boolean> candidateEmployees,
                                                                                       LocalDateTime startTime,
                                                                                       LocalDateTime endTime,
                                                                                       List<UUID> previousAssignees,
                                                                                       List<Tuple.Tuple3<UUID, LocalDateTime, LocalDateTime>> employeeSchedule,
                                                                                       int totalDurationMinutes,
                                                                                       int noOfEmployees,
                                                                                       String operationName
    ) {
        //now we search for the employee that is available and preferably choose the one that has a preference or has handled the previous task
        var employeeIds = candidateEmployees.keySet().stream().toList();
        List<Tuple.Tuple3<UUID, LocalDateTime, LocalDateTime>> intervals = timeUtil.intervalsByResource(employeeIds, startTime, employeeSchedule);
        //add the employees that don't have anything scheduled
        var employeesWithNothingScheduled = new ArrayList<>(employeeIds);
        employeesWithNothingScheduled.removeAll(intervals.stream().map(Tuple.Tuple3::a).collect(Collectors.toSet()));
        employeesWithNothingScheduled.forEach(e -> intervals.add(Tuple.of(e, startTime, endTime)));
        //we sort to make sure that the preferential ones gets their first chance and then the previous assignee gets its chance second
        intervals.sort((o1, o2) -> {
            if (o1.b().isEqual(o2.b())) {
                if (isTrue(candidateEmployees.get(o1.a()))) {
                    return -1;
                } else if (isTrue(candidateEmployees.get(o2.a()))) {
                    return 1;
                }
                if (isNotEmpty(previousAssignees) && (previousAssignees.contains(o1.a()) || previousAssignees.contains(o2.a()))) {
                    if (previousAssignees.contains(o1.a()) && hasOtherPreferentialOperation(o1.a(), operationName)) {
                        return 1;
                    }
                    if (previousAssignees.contains(o2.a()) && hasOtherPreferentialOperation(o2.a(), operationName)) {
                        return -1;
                    }
                    return previousAssignees.contains(o1.a()) ? -1 : previousAssignees.contains(o2.a()) ? 1 : 0;
                }
                if (hasOtherPreferentialOperation(o1.a(), operationName)) {
                    return 1;
                }
                if (hasOtherPreferentialOperation(o2.a(), operationName)) {
                    return -1;
                }
                return 0;
            } else {
                return o1.b().compareTo(o2.b());
            }
        });
        if (noOfEmployees == 1) {
            for (Tuple.Tuple3<UUID, LocalDateTime, LocalDateTime> i : intervals) {
                var start = timeUtil.getStartTime(i.b().isAfter(startTime) ? i.b() : startTime);
                var end = timeUtil.getEndTime(start, totalDurationMinutes);
                if ((end.isBefore(endTime) || end.isEqual(endTime)) && (end.isBefore(i.c()) || end.isEqual(i.c()))) {
                    return Optional.of(Tuple.of(List.of(i.a()), start, end));
                }
            }
            return Optional.empty();
        } else {
            return employees(startTime, endTime, previousAssignees, totalDurationMinutes, noOfEmployees, intervals);
        }
    }

    private Optional<Tuple.Tuple3<List<UUID>, LocalDateTime, LocalDateTime>> employees(LocalDateTime startTime,
                                                                                       LocalDateTime endTime,
                                                                                       List<UUID> previousAssignees,
                                                                                       int totalDurationMinutes,
                                                                                       int noOfEmployees,
                                                                                       List<Tuple.Tuple3<UUID, LocalDateTime, LocalDateTime>> intervals) {
        record Point(LocalDateTime time, boolean start, Set<UUID> employees, Set<UUID> openEmployees) {
        }
        var points = intervals.stream()
                .flatMap(i -> Stream.of(
                        new Point(i.b(), true, Set.of(i.a()), new HashSet<>()),
                        new Point(i.c(), false, Set.of(i.a()), new HashSet<>())
                ))
                //we merge duplicate points
                .collect(Collectors.toMap(p -> Tuple.of(p.time, p.start), p -> p, (p1, p2) ->
                        new Point(p1.time, p1.start, Stream.concat(p1.employees.stream(), p2.employees.stream()).collect(Collectors.toSet()), new HashSet<>())))
                .entrySet().stream()
                .map(e -> new Point(e.getKey().a(), e.getKey().b(), e.getValue().employees(), e.getValue().openEmployees()))
                .sorted(Comparator.comparing(Point::time))
                .toList();
        var currentlyOpen = new HashSet<UUID>();
        points.forEach(p -> {
            if (p.start) {
                currentlyOpen.addAll(p.employees);
            } else {
                currentlyOpen.removeAll(p.employees);
            }
            p.openEmployees.addAll(currentlyOpen);
        });
        for (int i = 0; i < points.size(); i++) {
            var current = points.get(i);
            if (current.start && current.openEmployees.size() >= noOfEmployees) {
                var currentCandidates = new HashSet<>(current.openEmployees);
                for (Point point : points.subList(i + 1, points.size()).stream().filter(p -> !p.start).toList()) {
                    if (currentCandidates.size() - intersection(currentCandidates, point.employees).size() < noOfEmployees) {
                        var start = timeUtil.getStartTime(current.time.isAfter(startTime) ? current.time : startTime);
                        var end = timeUtil.getEndTime(start, totalDurationMinutes);
                        if (end.isBefore(endTime) && end.isBefore(point.time)) {
                            var previousAssigneeCandidates = isNotEmpty(previousAssignees) ? intersection(currentCandidates, new HashSet<>(previousAssignees)) : Set.<UUID>of();
                            if (previousAssigneeCandidates.size() >= noOfEmployees) {
                                return Optional.of(Tuple.of(new ArrayList<>(previousAssigneeCandidates).subList(0, noOfEmployees), start, end));
                            } else {
                                var assignees = Stream.concat(previousAssigneeCandidates.stream(),
                                                currentCandidates.stream().filter(c -> !previousAssigneeCandidates.contains(c)).limit(noOfEmployees - previousAssigneeCandidates.size()))
                                        .toList();
                                return Optional.of(Tuple.of(assignees, start, end));
                            }
                        }
                        break;
                    } else {
                        currentCandidates.removeAll(point.employees);
                    }
                }
            }
        }
        return Optional.empty();
    }

    private Map<UUID, Boolean> employeesWithPreferences(String name, List<UUID> candidateEmployeeIds) {
        if (isBlank(name)) {
            return candidateEmployeeIds.stream().collect(Collectors.toMap(v -> v, v -> false));
        }
        var employeesWithPreferences = db.select(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.USER_ID, EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.PREFERENTIAL)
                .from(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE)
                .join(MANUFACTURING_OPERATION_TEMPLATE).on(MANUFACTURING_OPERATION_TEMPLATE.ID.eq(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_OPERATION_TEMPLATE_ID))
                .where(MANUFACTURING_OPERATION_TEMPLATE.NAME.eq(name),
                        EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.USER_ID.in(candidateEmployeeIds))
                .fetch()
                .intoMap(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.USER_ID, EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.PREFERENTIAL);
        return candidateEmployeeIds.stream().collect(Collectors.toMap(e -> e, e -> employeesWithPreferences.getOrDefault(e, false)));
    }


    private boolean hasOtherPreferentialOperation(UUID employeeId, String operationName) {
        if (isBlank(operationName)) {
            return false;
        }
        return db.fetchCount(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE, EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.USER_ID.eq(employeeId)
                        .and(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.PREFERENTIAL.isTrue())
                        .and(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_OPERATION_TEMPLATE_ID.ne(
                                db.select(MANUFACTURING_OPERATION_TEMPLATE.ID).from(MANUFACTURING_OPERATION_TEMPLATE)
                                        .where(MANUFACTURING_OPERATION_TEMPLATE.NAME.eq(operationName))))) > 0;
    }

    private Set<UUID> intersection(Set<UUID> l1, Set<UUID> l2) {
        return l1.stream().filter(l2::contains).collect(Collectors.toSet());
    }

}
