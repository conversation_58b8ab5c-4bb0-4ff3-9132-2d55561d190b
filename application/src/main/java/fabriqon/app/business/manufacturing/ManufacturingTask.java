package fabriqon.app.business.manufacturing;

import fabriqon.misc.Json;

import java.beans.ConstructorProperties;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.UUID;

public record ManufacturingTask(
        UUID id,
        Instant createTime,
        Instant updateTime,
        boolean deleted,

        UUID ownerId,
        UUID manufacturingOrderId,
        Status status,
        StatusReason statusReason,
        LocalDateTime startTime,
        LocalDateTime estimatedStartTime,
        LocalDateTime endTime,
        LocalDateTime estimatedEndTime,
        int durationInMinutes,
        Details details,
        Long ranking
) {

    @ConstructorProperties({"id", "create_time", "update_time", "deleted", "owner_id", "manufacturing_order_id",
            "status", "status_reason", "start_time", "estimated_start_time", "end_time", "estimated_end_time",
            "duration_in_minutes", "details", "ranking"})
    public ManufacturingTask(UUID id, Instant createTime, Instant updateTime, boolean deleted, UUID ownerId,
                             UUID manufacturingOrderId, Status status, StatusReason statusReason,
                             LocalDateTime startTime, LocalDateTime estimatedStartTime,
                             LocalDateTime endTime, LocalDateTime estimatedEndTime,
                             int durationInMinutes, String details, Long ranking) {
        this(id, createTime, updateTime, deleted, ownerId,
                manufacturingOrderId, status, statusReason, startTime, estimatedStartTime, endTime, estimatedEndTime,
                durationInMinutes, Json.readNullSafe(details, Details.class), ranking
        );
    }

    public enum Status {TODO, IN_PROGRESS, DONE, STOPPED}
    public enum StatusReason {PAUSED, MISSING_MATERIAL, BROKEN_EQUIPMENT, EQUIPMENT_UNAVAILABLE}

    public record Details(
            String number,
            String name,
            boolean parallelizable
    ) {
    }

}
