package fabriqon.app.business.manufacturing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import fabriqon.Notification;
import fabriqon.ObjectStorage;
import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.accounts.AccountService;
import fabriqon.app.business.customers.Note;
import fabriqon.app.business.employees.Employee;
import fabriqon.app.business.exceptions.BusinessException;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.goods.RequiredMaterial;
import fabriqon.app.business.goods.materials.metals.MetalCalculatorService;
import fabriqon.app.business.inventory.InventoryService;
import fabriqon.app.business.manufacturing.tasks.ManufacturingTaskService;
import fabriqon.app.business.notifications.UserTaggingProcessor;
import fabriqon.app.business.purchases.PurchaseWishlistItem;
import fabriqon.app.business.purchases.PurchasesService;
import fabriqon.app.business.purchases.Source;
import fabriqon.app.business.sequence.Sequences;
import fabriqon.app.common.model.Money;
import fabriqon.jooq.classes.tables.records.ReservedInventoryRecord;
import fabriqon.misc.Json;
import fabriqon.misc.Tuple;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.Record2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static fabriqon.app.business.manufacturing.ManufacturingOrder.Status.MANUFACTURING;
import static fabriqon.app.business.manufacturing.ManufacturingOrder.Status.SUBMITTED;
import static fabriqon.app.business.manufacturing.ManufacturingOrder.UNRANKED_ORDER_VALUE;
import static fabriqon.jooq.JooqJsonbFunctions.stringField;
import static fabriqon.jooq.classes.Tables.*;
import static org.apache.commons.lang3.ObjectUtils.isEmpty;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.jooq.impl.DSL.*;

@Slf4j
@Component
@Transactional
public class ManufacturingService {

    private final AccountService accountService;
    private final Sequences sequences;
    private final DSLContext db;
    private final InventoryService inventory;
    private final ManufacturingTaskService manufacturingTaskService;
    private final ManufacturingOperationService manufacturingOperationService;
    private final MaterialIssueService materialIssueService;
    private final ObjectStorage objectStorage;
    private final MetalCalculatorService metalCalculatorService;
    private final UserTaggingProcessor userTaggingProcessor;
    private final PurchasesService purchasesService;

    private static final Map<String, Object> keyLocks = new ConcurrentHashMap<>();

    @Autowired
    public ManufacturingService(AccountService accountService, Sequences sequences, DSLContext db, InventoryService inventory,
                                ManufacturingTaskService manufacturingTaskService,
                                ManufacturingOperationService manufacturingOperationService,
                                MaterialIssueService materialIssueService, ObjectStorage objectStorage,
                                MetalCalculatorService metalCalculatorService, UserTaggingProcessor userTaggingProcessor,
                                PurchasesService purchasesService) {
        this.sequences = sequences;
        this.db = db;
        this.inventory = inventory;
        this.manufacturingTaskService = manufacturingTaskService;
        this.manufacturingOperationService = manufacturingOperationService;
        this.materialIssueService = materialIssueService;
        this.objectStorage = objectStorage;
        this.accountService = accountService;
        this.metalCalculatorService = metalCalculatorService;
        this.userTaggingProcessor = userTaggingProcessor;
        this.purchasesService = purchasesService;
    }

    public ManufacturingOrder orderManufacturing(UUID ownerId, UUID salesOrderId,
                                                 LocalDateTime productionDeadline,
                                                 UUID productId, UUID serviceId, BigDecimal quantity,
                                                 boolean customProduct, String notes,
                                                 List<Tuple.Tuple2<UUID, BigDecimal>> customizations,
                                                 List<Tuple.Tuple2<String, ByteArrayInputStream>> files) {
        return orderManufacturing(ownerId, null, salesOrderId, productionDeadline, productId, serviceId, quantity, customProduct, notes, customizations, files);
    }

    private ManufacturingOrder orderManufacturing(UUID ownerId, ManufacturingOrder parent, UUID salesOrderId,
                                                  LocalDateTime productionDeadline,
                                                  UUID productId, UUID serviceId, BigDecimal quantity,
                                                  boolean customProduct, String notes,
                                                  List<Tuple.Tuple2<UUID, BigDecimal>> customizations,
                                                  List<Tuple.Tuple2<String, ByteArrayInputStream>> files) {
        var orderId = UUID.randomUUID();
        var manufacturedGood = manufacturedGood(productId);
        var ranking = db.select(max(MANUFACTURING_ORDER.RANKING))
                .from(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.OWNER_ID.eq(ownerId))
                .fetchOptionalInto(Integer.class)
                .orElse(UNRANKED_ORDER_VALUE);
        var orderNumber = parent != null ? parent.number() + " / " + fetchNextChildOrderNumber(parent.id())
                : salesOrderId != null ? getNumberBySalesOrder(salesOrderId)
                : sequences.nextSequenceForManufacturingOrder(ownerId);
        var status = (customProduct) ? ManufacturingOrder.Status.CUSTOMIZATION_NEEDED : SUBMITTED;
        List<ManufacturingOperation> manufacturingOperations = manufacturedGood != null ? manufacturingOperationService.enhance(manufacturedGood.manufacturingOperations())
                .stream()
                //we need to clean the template id from the operation so we don't mix properties from the template with the customization
                .map(operation -> {
                            var materials = calculateRequiredMaterials(operation.materials(), customizations);
                            return customProduct
                                    ? new ManufacturingOperation(null,
                                    operation.candidateWorkstationIds(),
                                    operation.candidateEmployeeIds(),
                                    operation.name(),
                                    operation.durationInMinutes(),
                                    operation.parallelizable(),
                                    operation.costPerHour(),
                                    operation.numberOfAssignees(),
                                    List.of(),
                                    List.of(),
                                    materials)
                                    : operation.operationTemplateId() != null
                                    ? new ManufacturingOperation(operation.operationTemplateId(),
                                    null,
                                    null,
                                    null,
                                    operation.durationInMinutes(),
                                    null,
                                    null,
                                    operation.numberOfAssignees(),
                                    null,
                                    null,
                                    materials)
                                    : operation.setMaterials(materials);
                        }
                )
                .toList()
                : List.of();
        db.insertInto(MANUFACTURING_ORDER)
                .set(MANUFACTURING_ORDER.ID, orderId)
                .set(MANUFACTURING_ORDER.OWNER_ID, ownerId)
                .set(MANUFACTURING_ORDER.PARENT_ID, parent != null ? parent.id() : null)
                .set(MANUFACTURING_ORDER.SALES_ORDER_ID, salesOrderId)
                .set(MANUFACTURING_ORDER.NUMBER, orderNumber)
                .set(MANUFACTURING_ORDER.PRODUCTION_DEADLINE, productionDeadline)
                .set(MANUFACTURING_ORDER.STATUS, status.name())
                .set(MANUFACTURING_ORDER.PRODUCT_ID, productId)
                .set(MANUFACTURING_ORDER.SERVICE_ID, serviceId)
                .set(MANUFACTURING_ORDER.QUANTITY, quantity)
                .set(MANUFACTURING_ORDER.RANKING, ++ranking)
                .set(MANUFACTURING_ORDER.NOTES, notes)
                .set(MANUFACTURING_ORDER.CUSTOM_PRODUCT, customProduct)
                .set(MANUFACTURING_ORDER.MANUFACTURING_OPERATIONS, JSONB.jsonb(Json.write(manufacturingOperations)))
                .execute();
        var order = new ManufacturingOrder(
                orderId, null, null, false,
                ownerId, parent != null ? parent.id() : null, salesOrderId, orderNumber, productionDeadline,
                null, SUBMITTED,
                productId, null, quantity, List.of(),
                ranking, notes, customProduct, null, null, null
        );

        manufacturingOperations.stream().flatMap(op -> op.materials().stream())
                .flatMap(material -> reserveStock(ownerId, orderId, quantity, material.materialIds().getFirst(), material.quantity()).entrySet().stream())
                .forEach(unreserved -> createChildOrderIfNeeded(order, unreserved, files));
        //update ranking after if child MOs were created
        db.select(max(MANUFACTURING_ORDER.RANKING))
                .from(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.PARENT_ID.eq(orderId))
                .fetchOptionalInto(Integer.class)
                .ifPresent(v -> db.update(MANUFACTURING_ORDER).set(MANUFACTURING_ORDER.RANKING, ++v).where(MANUFACTURING_ORDER.ID.eq(orderId)).execute());
        if (status.equals(SUBMITTED) && serviceId == null) {
            manufacturingTaskService.createTasks(ownerId, orderId, orderNumber, quantity, manufacturingOperations);
        }
        files.forEach(f -> attach(ownerId, orderId, f.a(), f.b().readAllBytes()));

        return order;
    }

    private int fetchNextChildOrderNumber(UUID parentId) {
        return db.select(MANUFACTURING_ORDER.NUMBER)
                .from(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.PARENT_ID.eq(parentId))
                .fetchInto(String.class)
                .stream()
                .mapToInt(number -> {
                    String numPart = number.substring(number.lastIndexOf("/") + 1).trim();
                    return Integer.parseInt(numPart);
                })
                .max()
                .orElse(0) + 1;
    }

    public ManufacturingOrder updateManufacturingOrder(UUID ownerId, UUID orderId,
                                                       UUID assignedTo, LocalDateTime productionDeadline,
                                                       BigDecimal quantity,
                                                       List<ManufacturingOperation> manufacturingOperations,
                                                       String notes) {

        var ownershipCriteria = MANUFACTURING_ORDER.ID.eq(orderId).and(MANUFACTURING_ORDER.OWNER_ID.eq(ownerId));
        var order = order(ownerId, orderId);

        var status = db.select(MANUFACTURING_ORDER.STATUS).from(MANUFACTURING_ORDER).where(ownershipCriteria)
                .fetchSingleInto(ManufacturingOrder.Status.class);
        if ((order.productId() != null ? List.of(MANUFACTURING, ManufacturingOrder.Status.CONSUMPTION_RECORDED) : List.of(ManufacturingOrder.Status.CONSUMPTION_RECORDED))
                .contains(status)) {
            throw new BusinessException("Order cannot be modified in current status: " + status, "incorrect_status", status);
        }

        var updateStatement = db.update(MANUFACTURING_ORDER)
                .set(MANUFACTURING_ORDER.OWNER_ID, ownerId);
        if (assignedTo != null) {
            updateStatement = updateStatement.set(MANUFACTURING_ORDER.ASSIGNED_USER_ID, assignedTo);
        }
        if (productionDeadline != null) {
            updateStatement = updateStatement.set(MANUFACTURING_ORDER.PRODUCTION_DEADLINE, productionDeadline);
        }
        if (quantity != null) {
            updateStatement = updateStatement.set(MANUFACTURING_ORDER.QUANTITY, quantity);
        }
        List<ManufacturingOperation> updatedManufacturingOperations = null;
        if (manufacturingOperations != null) {
            if (manufacturingOperations.stream().anyMatch(Objects::isNull)) {
                throw new BusinessException("operation is null for update", "operation_is_null");
            }
            var orderQuantity = order.quantity();
            updatedManufacturingOperations = manufacturingOperations.stream()
                    .map(op -> op.setMaterials(op.materials().stream()
                            //we need to convert dimensions here
                            .map(m -> {
                                if (m.dimensions() != null) {
                                    return m.setQuantity(metalCalculatorService.calculateWeight(m.materialIds().getFirst(), m.dimensions()));
                                } else if (m.totalDimensions() != null) {
                                    return m.setQuantity(metalCalculatorService.calculateWeight(m.materialIds().getFirst(), m.totalDimensions()).divide(orderQuantity, 3, RoundingMode.HALF_UP));
                                } else {
                                    return m;
                                }
                            })
                            .toList()))
                    .toList();
            updateStatement = updateStatement.set(MANUFACTURING_ORDER.MANUFACTURING_OPERATIONS, JSONB.valueOf(Json.write(updatedManufacturingOperations)));
        }
        if (notes != null) {
            updateStatement = updateStatement.set(MANUFACTURING_ORDER.NOTES, notes);
        }

        updateStatement
                .where(MANUFACTURING_ORDER.ID.eq(orderId), MANUFACTURING_ORDER.OWNER_ID.eq(ownerId))
                .execute();

        if ((manufacturingOperations != null || quantity != null) && order.serviceId() == null) {
            if (manufacturingOperations != null) {
                order = order.setManufacturingOperations(updatedManufacturingOperations);
            }
            if (quantity != null) {
                order = order.setQuantity(quantity);
            }
            reReserveMaterials(order);
        }
        return db.selectFrom(MANUFACTURING_ORDER)
                .where(ownershipCriteria)
                .fetchSingleInto(ManufacturingOrder.class);
    }

    public void deleteOrder(UUID ownerId, UUID orderId, boolean reDistributeMaterials) {
        //first we drop all reserved inventory for the manufacturing orders
        inventory.clearReservedForManufacturingOrder(ownerId, orderId, true);
        //then we delete all the tasks that were created for the order
        db.deleteFrom(MANUFACTURING_TASK)
                .where(MANUFACTURING_TASK.OWNER_ID.eq(ownerId), MANUFACTURING_TASK.MANUFACTURING_ORDER_ID.eq(orderId))
                .execute();
        db.select(MANUFACTURINGORDER_FILE.FILE_ID)
                .from(MANUFACTURINGORDER_FILE)
                .where(MANUFACTURINGORDER_FILE.OWNER_ID.eq(ownerId), MANUFACTURINGORDER_FILE.MANUFACTURING_ORDER_ID.eq(orderId))
                .forEach(r -> {
                    db.deleteFrom(MANUFACTURINGORDER_FILE)
                            .where(MANUFACTURINGORDER_FILE.OWNER_ID.eq(ownerId),
                                    MANUFACTURINGORDER_FILE.MANUFACTURING_ORDER_ID.eq(orderId),
                                    MANUFACTURINGORDER_FILE.FILE_ID.eq(r.value1()))
                            .execute();
                    objectStorage.delete(ownerId, r.value1());
                });
        //delete all child orders
        db.select(MANUFACTURING_ORDER.ID, MANUFACTURING_ORDER.STATUS).from(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.OWNER_ID.eq(ownerId), MANUFACTURING_ORDER.PARENT_ID.eq(orderId))
                .fetch()
                .forEach(childOrder -> {
                    if (List.of(SUBMITTED.name(), MANUFACTURING.name()).contains(childOrder.value2())) {
                        deleteOrder(ownerId, childOrder.value1(), false);
                    } else {
                        //we remove the parent reference as it will get deleted
                        db.update(MANUFACTURING_ORDER)
                                .set(MANUFACTURING_ORDER.PARENT_ID, (UUID) null)
                                .where(MANUFACTURING_ORDER.ID.eq(childOrder.value1()))
                                .execute();
                    }
                });
        db.deleteFrom(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.OWNER_ID.eq(ownerId), MANUFACTURING_ORDER.ID.eq(orderId))
                .execute();
        inventory.clearReservedForManufacturingOrder(ownerId, orderId, true);
        if (reDistributeMaterials) {
            reDistributeMaterials(ownerId);
        }
    }

    public void manufacturing(UUID ownerId, UUID manufacturingOrderId) {
        var order = order(ownerId, manufacturingOrderId);
        if (order.serviceId() == null) {
            throw new BusinessException("order is not a service. cannot change status", "not_a_service");
        }
        int updated = db.update(MANUFACTURING_ORDER)
                .set(MANUFACTURING_ORDER.STATUS, MANUFACTURING.name())
                .set(MANUFACTURING_ORDER.RANKING, ManufacturingOrder.UNRANKED_ORDER_VALUE)//so it falls off the radar
                .where(MANUFACTURING_ORDER.OWNER_ID.eq(ownerId), MANUFACTURING_ORDER.ID.eq(manufacturingOrderId),
                        MANUFACTURING_ORDER.STATUS.in(SUBMITTED.name()))
                .execute();
        if (updated != 1) {
            throw new BusinessException("order is not in the correct state", "invalid_order_state");
        }
    }

    public Optional<MaterialIssueNote> recordConsumption(UUID ownerId,
                                                         UUID manufacturingOrderId,
                                                         LocalDate date,
                                                         String inventoryManager,
                                                         String worker,
                                                         List<UsedMaterial> usedMaterials) {
        var order = order(ownerId, manufacturingOrderId);
        db.update(MANUFACTURING_ORDER)
                .set(MANUFACTURING_ORDER.STATUS, ManufacturingOrder.Status.CONSUMPTION_RECORDED.name())
                .set(MANUFACTURING_ORDER.MANUFACTURING_COSTS, JSONB.jsonb(Json.write(new ManufacturingOrder.ManufacturingCosts(
                        employeeRates(ownerId, manufacturingOrderId),
                        workstationRates(ownerId, manufacturingOrderId),
                        operationCostPerHourRates(order),
                        manufacturingOverheadPerEmployeeHour(ownerId)
                ))))
                .where(MANUFACTURING_ORDER.OWNER_ID.eq(ownerId), MANUFACTURING_ORDER.ID.eq(manufacturingOrderId))
                .execute();
        if (isEmpty(usedMaterials)) {//for orders that don't use any materials just manpower/workstations
            return Optional.empty();
        }
        inventory.clearReservedForManufacturingOrder(ownerId, manufacturingOrderId, true);
        var materialIssueNote = materialIssueService.issueForManufacturing(ownerId,
                manufacturingOrderId,
                date,
                inventoryManager,
                worker,
                usedMaterials,
                employeeAndWorkstationCosts(ownerId, manufacturingOrderId).add(manufacturingOverheadCosts(ownerId, manufacturingOrderId)));
        //we re-distribute any unused materials
        var orderIds = db.select(MANUFACTURING_ORDER.ID)
                .from(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.OWNER_ID.eq(ownerId),
                        MANUFACTURING_ORDER.RANKING.gt(UNRANKED_ORDER_VALUE),
                        MANUFACTURING_ORDER.STATUS.eq(SUBMITTED.name())
                )
                .orderBy(MANUFACTURING_ORDER.RANKING.asc())
                .fetchInto(UUID.class);
        db.deleteFrom(RESERVED_INVENTORY)
                .where(RESERVED_INVENTORY.OWNER_ID.eq(ownerId), RESERVED_INVENTORY.MANUFACTURING_ORDER_ID.in(orderIds))
                .execute();
        orderIds.forEach(orderId -> {
            var o = order(ownerId, orderId);
            requiredMaterials(o).forEach(
                    material -> reserveStock(ownerId, o.id(), o.quantity(), material.materialIds().get(0), material.quantity())
            );
        });
        return Optional.of(materialIssueNote);
    }

    public ManufacturingOrder accounted(UUID ownerId, UUID orderId) {
        var order = order(ownerId, orderId);
        if (order.status() != ManufacturingOrder.Status.CONSUMPTION_RECORDED) {
            throw new BusinessException("Order is in invalid state.", "invalid_order_state");
        }
        var updated = db.update(MANUFACTURING_ORDER)
                .set(MANUFACTURING_ORDER.STATUS, ManufacturingOrder.Status.ACCOUNTED.name())
                .where(MANUFACTURING_ORDER.ID.eq(orderId), MANUFACTURING_ORDER.OWNER_ID.eq(ownerId))
                .execute();
        if (updated != 1) {
            throw new BusinessException("Order is in invalid state.", "invalid_order_state");
        }
        return order.status(ManufacturingOrder.Status.ACCOUNTED);
    }

    /**
     * Method used to change the ranking/priority of the manufacturing orders. This can only be performed on orders
     * that are still 'active' (i.e. not done).
     * During reordering the inventory is reserved again by the specified ranking
     * Tasks are also rescheduled based on the ranking.
     */
    public void applyRanking(UUID ownerId, List<UUID> orderIds) {
        if (db.fetchCount(MANUFACTURING_ORDER,
                MANUFACTURING_ORDER.STATUS.notIn(SUBMITTED.name(), ManufacturingOrder.Status.CUSTOMIZATION_NEEDED.name()),
                MANUFACTURING_ORDER.ID.in(orderIds)) > 0) {
            throw new BusinessException("You can only reorder ranking for orders that are 'SUBMITTED'", "invalid_order_statuses_for_ranking");
        }
        var ordersToReplan = filterChangedRanking(ownerId, orderIds);
        rePlan(ownerId, ordersToReplan.a(), ordersToReplan.b(), true, true);
    }

    /**
     * Mark the order as SUBMITTED, reserve inventory and schedule the tasks
     */
    public ManufacturingOrder customizationFinished(UUID ownerId, UUID orderId) {
        var order = order(ownerId, orderId);
        if (order.status() != ManufacturingOrder.Status.CUSTOMIZATION_NEEDED) {
            throw new BusinessException("Order is in invalid state.", "invalid_order_state");
        }
        db.update(MANUFACTURING_ORDER)
                .set(MANUFACTURING_ORDER.STATUS, SUBMITTED.name())
                .where(MANUFACTURING_ORDER.ID.eq(orderId), MANUFACTURING_ORDER.OWNER_ID.eq(ownerId))
                .execute();
        rePlan(ownerId, db.select(MANUFACTURING_ORDER.ID)
                        .from(MANUFACTURING_ORDER)
                        .where(MANUFACTURING_ORDER.OWNER_ID.eq(ownerId),
                                MANUFACTURING_ORDER.RANKING.greaterOrEqual(order.ranking()),
                                MANUFACTURING_ORDER.STATUS.eq(SUBMITTED.name()),
                                MANUFACTURING_ORDER.DELETED.isFalse(),
                                MANUFACTURING_ORDER.PRODUCT_ID.isNotNull()// services don't have tasks so we ignore them
                        )
                        .fetchInto(UUID.class),
                order.ranking(), false, true);
        return order;
    }

    public void assignTo(UUID ownerId, UUID manufacturingOrderId, UUID employeeId) {
        int updated = db.update(MANUFACTURING_ORDER)
                .set(MANUFACTURING_ORDER.ASSIGNED_USER_ID, employeeId)
                .where(MANUFACTURING_ORDER.OWNER_ID.eq(ownerId), MANUFACTURING_ORDER.ID.eq(manufacturingOrderId))
                .execute();
        if (updated != 1) {
            throw new BusinessException("order is not in the correct state", "invalid_order_state");
        }
    }

    /**
     * @return the materials needed for manufacturing one unit of this product
     */
    public List<RequiredMaterial> requiredMaterials(ManufacturingOrder order) {
        return Optional.ofNullable(order.manufacturingOperations())
                .map(ops -> ops.stream().flatMap(op -> op.materials().stream()).toList())
                //mostly leaving this in, so we don't crash on existing data; after the custom product impl this should never be the case hence the warn log
                .orElseGet(() -> {
                    log.warn("loading required materials from product definition");
                    return db.selectFrom(MATERIAL_GOOD)
                            .where(MATERIAL_GOOD.ID.eq(order.productId()))
                            .fetchSingleInto(MaterialGood.class)
                            .details.manufacturingOperations().stream().flatMap(op -> op.materials().stream())
                            .map(m -> new RequiredMaterial(m.configurableWithOptions() ? List.of(m.materialIds().getFirst()) : m.materialIds(), m.quantity(),
                                    m.optional(), m.configurableWithOptions(), m.replaceableWithOptions(), m.wastePercentage(), m.dimensions(), m.totalDimensions()))
                            .toList();
                });
    }

    public Money employeeAndWorkstationCosts(UUID ownerId, UUID orderId) {
        var order = order(ownerId, orderId);
        var currency = accountService.defaultCurrency(ownerId);
        if (order.status() == ManufacturingOrder.Status.CUSTOMIZATION_NEEDED) {//in this case there are no tasks created so we need to calculate
            return new Money(manufacturingOperationService.enhance(order.manufacturingOperations()).stream()
                    .mapToLong(operation -> {
                        var value = BigDecimal.ZERO;
                        BigDecimal durationByHour = BigDecimal.valueOf(operation.durationInMinutes()).setScale(4, RoundingMode.HALF_EVEN)
                                .divide(BigDecimal.valueOf(60), RoundingMode.HALF_EVEN);
                        if (operation.costPerHour() != null) {
                            value = value.add(operation.costPerHour().multiply(durationByHour).toBigDecimal());
                        } else {
                            if (isNotEmpty((operation.candidateEmployeeIds()))) {
                                value = value.add(
                                        //todo: average the employee rate
                                        employeeHourlyRate(operation.candidateEmployeeIds().getFirst()).multiply(durationByHour)
                                );
                            }
                            if (isNotEmpty((operation.candidateWorkstationIds()))) {
                                value = value.add(
                                        //todo: average the workstation rate
                                        workstationHourlyRate(operation.candidateWorkstationIds().getFirst()).multiply(durationByHour)
                                );
                            }
                        }
                        return value.longValue();
                    })
                    .sum(),
                    currency);
        } else {
            boolean orderIsDoneWithManufacturingCosts = order.status() == ManufacturingOrder.Status.CONSUMPTION_RECORDED && order.manufacturingCosts() != null;
            var employeeRates = orderIsDoneWithManufacturingCosts ? order.manufacturingCosts().employeeHourlyRates() : employeeRates(ownerId, orderId);
            var workstationRates = orderIsDoneWithManufacturingCosts ? order.manufacturingCosts().workstationHourlyRates() : workstationRates(ownerId, orderId);
            var operationRates = orderIsDoneWithManufacturingCosts && order.manufacturingCosts().operationCostPerHourRates() != null ? order.manufacturingCosts().operationCostPerHourRates() : operationCostPerHourRates(order);
            return new Money(db.selectFrom(MANUFACTURING_TASK)
                    .where(MANUFACTURING_TASK.OWNER_ID.eq(ownerId), MANUFACTURING_TASK.MANUFACTURING_ORDER_ID.eq(orderId))
                    .fetchInto(ManufacturingTask.class)
                    .stream()
                    .mapToLong(task -> {
                        var durationInHours = durationInHours(order, task);
                        Money laborCosts;
                        if (operationRates.containsKey(task.details().name())) {
                            laborCosts = operationRates.get(task.details().name()).multiply(durationInHours);
                        } else {
                            var employeeIds = db.select(MANUFACTURINGTASK_EMPLOYEE.USER_ID)
                                    .from(MANUFACTURINGTASK_EMPLOYEE)
                                    .where(MANUFACTURINGTASK_EMPLOYEE.MANUFACTURING_TASK_ID.eq(task.id()))
                                    .fetchInto(UUID.class);
                            laborCosts = employeeIds.stream()
                                    .map(employeeRates::get)
                                    .reduce(Money::add)
                                    .map(m -> m.divide(employeeIds.size()))
                                    .orElse(new Money(0, currency))
                                    .multiply(durationInHours);
                        }
                        var workstationCosts = db.select(MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_WORKSTATION_ID)
                                .from(MANUFACTURINGTASK_WORKSTATION)
                                .where(MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_TASK_ID.eq(task.id()))
                                .fetchOptionalInto(UUID.class)
                                .map(workstation -> workstationRates.get(workstation).multiply(durationInHours))
                                .orElse(new Money(0, currency));
                        return laborCosts.add(workstationCosts).amount();
                    })
                    .sum(),
                    currency);
        }
    }

    public Money manufacturingOverheadCosts(UUID ownerId, UUID orderId) {
        var order = order(ownerId, orderId);
        var manufacturingOverheadPerEmployeeHour = order.status() == ManufacturingOrder.Status.CONSUMPTION_RECORDED && order.manufacturingCosts() != null
                ? order.manufacturingCosts().manufacturingOverheadPerEmployeeHour()
                : manufacturingOverheadPerEmployeeHour(ownerId);
        var currency = manufacturingOverheadPerEmployeeHour.currency();
        if (order.status() == ManufacturingOrder.Status.CUSTOMIZATION_NEEDED) {
            return new Money(manufacturingOperationService.enhance(order.manufacturingOperations()).stream()
                    .mapToLong(operation -> {
                        BigDecimal durationInHours = BigDecimal.valueOf(operation.durationInMinutes()).setScale(4, RoundingMode.HALF_EVEN)
                                .divide(BigDecimal.valueOf(60), RoundingMode.HALF_EVEN);
                        var overheadCosts = manufacturingOverheadPerEmployeeHour
                                .multiply(durationInHours);
                        return overheadCosts.amount();
                    }).sum(),
                    currency);
        } else {
            return new Money(db.selectFrom(MANUFACTURING_TASK)
                    .where(MANUFACTURING_TASK.OWNER_ID.eq(ownerId), MANUFACTURING_TASK.MANUFACTURING_ORDER_ID.eq(orderId))
                    .fetchInto(ManufacturingTask.class)
                    .stream()
                    .mapToLong(task -> {
                        var durationInHours = durationInHours(order, task);
                        var employeeIds = db.select(MANUFACTURINGTASK_EMPLOYEE.USER_ID)
                                .from(MANUFACTURINGTASK_EMPLOYEE)
                                .where(MANUFACTURINGTASK_EMPLOYEE.MANUFACTURING_TASK_ID.eq(task.id()))
                                .fetchInto(UUID.class);
                        var overheadCosts = manufacturingOverheadPerEmployeeHour
                                .multiply(employeeIds.size())
                                .multiply(durationInHours);
                        return overheadCosts.amount();
                    })
                    .sum(),
                    currency);
        }
    }

    private BigDecimal durationInHours(ManufacturingOrder order, ManufacturingTask task) {
        int standardDuration = manufacturingOperationService.enhance(order.manufacturingOperations()).stream()
                .filter(op -> op.name().equalsIgnoreCase(task.details().name()))
                .findFirst().map(ManufacturingOperation::durationInMinutes)
                .orElse(task.durationInMinutes());
        return BigDecimal.valueOf(standardDuration)
                .setScale(4, RoundingMode.HALF_EVEN)
                .multiply(order.quantity())
                .divide(BigDecimal.valueOf(60), RoundingMode.HALF_EVEN);
    }

    public ManufacturingOrder attach(UUID ownerId, UUID orderId, String name, byte[] bytes) {
        var order = order(ownerId, orderId);//we do this here to validate access
        var fileId = objectStorage.store(ownerId, name, bytes);
        db.insertInto(MANUFACTURINGORDER_FILE)
                .set(MANUFACTURINGORDER_FILE.OWNER_ID, ownerId)
                .set(MANUFACTURINGORDER_FILE.MANUFACTURING_ORDER_ID, orderId)
                .set(MANUFACTURINGORDER_FILE.FILE_ID, fileId)
                .execute();
        return order;
    }

    public ManufacturingOrder deleteFile(UUID ownerId, UUID orderId, UUID fileId) {
        var order = order(ownerId, orderId);//we do this here to validate access
        db.deleteFrom(MANUFACTURINGORDER_FILE)
                .where(MANUFACTURINGORDER_FILE.OWNER_ID.eq(ownerId), MANUFACTURINGORDER_FILE.MANUFACTURING_ORDER_ID.eq(orderId), MANUFACTURINGORDER_FILE.FILE_ID.eq(fileId))
                .execute();
        objectStorage.delete(ownerId, fileId);
        return order;
    }

    public void addToWishlist(UUID ownerId, UUID orderId, UUID materialId, BigDecimal quantity, UUID supplierId) {
        ManufacturingOrder order = order(ownerId, orderId);
        if (requiredMaterials(order).stream()
                .noneMatch(material -> material.materialIds().contains(materialId))) {
            throw new BusinessException("material is not required for this manufacturing order", "material_not_required");
        }
        purchasesService.addToWishlist(ownerId,
                List.of(new PurchaseWishlistItem(null, null, null, false, null,
                        materialId, quantity, null, supplierId,
                        List.of(new Source(Source.Section.MANUFACTURING_ORDER,
                                order.parentId() != null ? getRootParentOrderId(ownerId, order.parentId()) : orderId)))));
    }

    public Note addNote(UUID ownerId, UUID orderId, UUID userId, String note) {
        var id = UUID.randomUUID();
        db.insertInto(NOTE)
                .set(NOTE.ID, id)
                .set(NOTE.OWNER_ID, ownerId)
                .set(NOTE.ADDED_BY_ID, userId)
                .set(NOTE.NOTE_, note)
                .execute();
        db.insertInto(MANUFACTURING_ORDER_NOTE)
                .set(MANUFACTURING_ORDER_NOTE.NOTE_ID, id)
                .set(MANUFACTURING_ORDER_NOTE.MANUFACTURING_ORDER_ID, orderId)
                .execute();
        userTaggingProcessor.notify(ownerId, Notification.Section.MANUFACTURING, orderId, userId, id, note, number(orderId));
        return new Note(id, Instant.now(), Instant.now(), false, ownerId, userId, note);
    }

    private List<RequiredMaterial> calculateRequiredMaterials(List<RequiredMaterial> materials,
                                                              List<Tuple.Tuple2<UUID, BigDecimal>> customizations) {
        var customizationList = new LinkedList<>(customizations);
        return materials.stream()
                .filter(m -> !m.optional() || customizationList.stream().anyMatch(c -> m.materialIds().contains(c.a())))
                .map(m -> {
                            if (m.configurableWithOptions()) {
                                var customization = customizationList.stream().filter(c -> m.materialIds().contains(c.a())).findAny();
                                if (customization.isPresent()) {
                                    customizationList.remove(customization.get());
                                    return new RequiredMaterial(List.of(customization.get().a()), customization.get().b(),
                                            m.optional(), true, m.replaceableWithOptions(),
                                            m.wastePercentage(), m.dimensions(), null);
                                } else {//we go with the default
                                    return new RequiredMaterial(List.of(m.materialIds().getFirst()), m.quantity(), m.optional(), true, m.replaceableWithOptions(), m.wastePercentage(), m.dimensions(), null);
                                }
                            } else {
                                return new RequiredMaterial(m.materialIds(), m.quantity(), m.optional(), false, m.replaceableWithOptions(), m.wastePercentage(), m.dimensions(), null);
                            }
                        }
                )
                .toList();
    }

    public void reDistributeMaterials(UUID ownerId) {
        var orderIds = db.select(MANUFACTURING_ORDER.ID)
                .from(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.OWNER_ID.eq(ownerId),
                        MANUFACTURING_ORDER.STATUS.in(ManufacturingOrder.Status.SUBMITTED.name(), MANUFACTURING.name()),
                        MANUFACTURING_ORDER.DELETED.isFalse())
                .orderBy(MANUFACTURING_ORDER.RANKING.asc())
                .fetchInto(UUID.class);
        rePlan(ownerId, orderIds, UNRANKED_ORDER_VALUE, false, false);
    }

    private void rePlan(UUID ownerId, List<UUID> orderIds, Integer startingRanking, boolean changeRanking, boolean recreateTasks) {
        synchronized (keyLocks.computeIfAbsent(ownerId.toString(), k -> new Object())) {
            log.info("replanning for [{}]", ownerId);
            rePlan0(ownerId, orderIds, startingRanking, changeRanking, recreateTasks);
            log.info("finished replanning for [{}]", ownerId);
        }
    }

    /**
     * Re-plan the orders by re-reserving inventory and re-scheduling tasks
     */
    private void rePlan0(UUID ownerId, List<UUID> orderIds, Integer startingRanking, boolean changeRanking, boolean recreateTasks) {
        //first we drop all reserved inventory for the manufacturing orders
        db.deleteFrom(RESERVED_INVENTORY)
                .where(RESERVED_INVENTORY.OWNER_ID.eq(ownerId),
                        RESERVED_INVENTORY.MANUFACTURING_ORDER_ID.in(orderIds),
                        RESERVED_INVENTORY.MANDATORY_FOR_ORDER.isFalse())
                .execute();
        //then we delete all the tasks that were created for the orders
        if (recreateTasks) {
            db.deleteFrom(MANUFACTURING_TASK)
                    .where(MANUFACTURING_TASK.OWNER_ID.eq(ownerId), MANUFACTURING_TASK.MANUFACTURING_ORDER_ID.in(orderIds), MANUFACTURING_TASK.STATUS.eq(ManufacturingTask.Status.TODO.name()))
                    .execute();
            //shift the current tasks in status TODO in place
            manufacturingTaskService.getTasks(ownerId, List.of(ManufacturingTask.Status.TODO), LocalDateTime.now(), LocalDateTime.now().plusDays(1));
        }
        var rankingCounter = new AtomicInteger(startingRanking);
        var rankedChildOrders = new ArrayList<>();
        IntStream.range(0, orderIds.size()).forEach(
                index -> {
                    var orderOptional = db.selectFrom(MANUFACTURING_ORDER)
                            .where(MANUFACTURING_ORDER.ID.eq(orderIds.get(index)), MANUFACTURING_ORDER.OWNER_ID.eq(ownerId))
                            .fetchOptionalInto(ManufacturingOrder.class);
                    if (orderOptional.isEmpty()) {
                        return;//this was a child order that got deleted
                    }
                    var order = orderOptional.get();
                    //we re-reserve the inventory for each in their order (which also takes care of child MOs
                    reReserveMaterials(order);
                    if (recreateTasks) {
                        //and recreate the tasks for the order
                        if (!order.status().equals(ManufacturingOrder.Status.CUSTOMIZATION_NEEDED)) {
                            manufacturingTaskService.createTasks(ownerId, order.id(), order.number(), order.quantity(),
                                    order.manufacturingOperations() != null
                                            ? order.manufacturingOperations()
                                            : manufacturedGood(order.productId()).manufacturingOperations());
                        }
                    }
                    if (changeRanking && !rankedChildOrders.contains(order.id())) {
                        var childOrders = db.select(MANUFACTURING_ORDER.ID, MANUFACTURING_ORDER.RANKING)
                                .from(MANUFACTURING_ORDER)
                                .where(MANUFACTURING_ORDER.OWNER_ID.eq(ownerId), MANUFACTURING_ORDER.PARENT_ID.eq(order.id()), MANUFACTURING_ORDER.RANKING.gt(UNRANKED_ORDER_VALUE))
                                .orderBy(MANUFACTURING_ORDER.NUMBER.asc())
                                .fetch().stream().collect(Collectors.toMap(r -> r.get(MANUFACTURING_ORDER.ID), r -> r.get(MANUFACTURING_ORDER.RANKING)));
                        childOrders.keySet().stream()
                                .filter(childOrderId -> !orderIds.subList(0, index).contains(childOrderId) && childOrders.get(childOrderId) >= startingRanking)
                                .forEach(childOrderId -> {
                                    rankedChildOrders.add(childOrderId);
                                    db.update(MANUFACTURING_ORDER)
                                            .set(MANUFACTURING_ORDER.RANKING, rankingCounter.incrementAndGet())
                                            .where(MANUFACTURING_ORDER.ID.eq(childOrderId))
                                            .execute();
                                });
                        db.update(MANUFACTURING_ORDER)
                                .set(MANUFACTURING_ORDER.RANKING, rankingCounter.incrementAndGet())
                                .where(MANUFACTURING_ORDER.ID.eq(order.id()))
                                .execute();
                    }
                }
        );
    }

    /**
     * returns a sublist starting with the first order that diverges from the original ranking.
     * this way we don't change anything for the first orders that didn't have a ranking change
     */
    private Tuple.Tuple2<List<UUID>, Integer> filterChangedRanking(UUID ownerId, List<UUID> orderIds) {
        var ordersRanked = db.select(MANUFACTURING_ORDER.ID, MANUFACTURING_ORDER.RANKING)
                .from(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.OWNER_ID.eq(ownerId), MANUFACTURING_ORDER.ID.in(orderIds))
                .orderBy(MANUFACTURING_ORDER.RANKING.asc())
                .fetch().map(r -> Tuple.of(r.value1(), r.value2()));
        for (int i = 0; i < orderIds.size(); i++) {
            if (!(ordersRanked.get(i).a().equals(orderIds.get(i)))) {
                return Tuple.of(orderIds.subList(i, orderIds.size()), ordersRanked.get(i).b());
            }
        }
        return Tuple.of(orderIds, 0);
    }

    private ManufacturedGood manufacturedGood(UUID goodId) {
        if (goodId == null) {
            return null;
        }
        return db.select(MATERIAL_GOOD.DETAILS)
                .from(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(goodId))
                .fetchSingleInto(ManufacturedGood.class);
    }

    private void reReserveMaterials(ManufacturingOrder order) {
        inventory.clearReservedForManufacturingOrder(order.ownerId(), order.id(), false);
        var childOrders = new ArrayList<>(getChildOrders(order));
        var requiredMaterials = requiredMaterials(order);
        requiredMaterials.forEach(material -> {
            var materialId = material.materialIds().getFirst();
            var childOrder = matchingInProgressChildOrder(order.quantity(), childOrders, materialId, material.quantity());
            if (childOrder.isPresent()) {
                childOrders.remove(childOrder.get());
                return;
            }
            reserveStock(order.ownerId(), order.id(), order.quantity(), materialId, material.quantity());
        });
        var notFinishedChildOrders = new ArrayList<>(getChildOrders(order).stream().filter(co -> List.of(SUBMITTED, MANUFACTURING).contains(co.status())).toList());
        var reservedMaterials = new ArrayList<>(db.selectFrom(RESERVED_INVENTORY).where(RESERVED_INVENTORY.MANUFACTURING_ORDER_ID.eq(order.id())).fetchInto(ReservedInventoryRecord.class));
        requiredMaterials.forEach(material -> {
            var materialId = material.materialIds().getFirst();
            BigDecimal requiredQuantity = material.quantity().multiply(order.quantity());
            var matchingInProgressChildOrder = matchingInProgressChildOrder(order.quantity(), notFinishedChildOrders, materialId, requiredQuantity);
            if (matchingInProgressChildOrder.isPresent()) {
                notFinishedChildOrders.remove(matchingInProgressChildOrder.get());
                return;
            }
            var matchingReservedMaterial = reservedMaterials.stream().filter(r -> r.getMaterialGoodId().equals(materialId) && r.getQuantity().compareTo(requiredQuantity) == 0).findFirst();
            if (matchingReservedMaterial.isPresent()) {
                reservedMaterials.remove(matchingReservedMaterial.get());
                return;
            }
            var reservedMaterial = reservedMaterials.stream().filter(r -> r.getMaterialGoodId().equals(materialId) && r.getQuantity().compareTo(requiredQuantity) < 0).findFirst();
            if (reservedMaterial.isPresent()) {
                reservedMaterials.remove(reservedMaterial.get());
                var matchingChildOrder = notFinishedChildOrders.stream().filter(co -> co.productId().equals(materialId)).findAny();
                if (matchingChildOrder.isPresent()) {
                    notFinishedChildOrders.remove(matchingChildOrder.get());
                    if (matchingChildOrder.get().quantity().add(reservedMaterial.get().getQuantity()).compareTo(requiredQuantity) != 0) {
                        db.update(MANUFACTURING_ORDER)
                                .set(MANUFACTURING_ORDER.QUANTITY, requiredQuantity.subtract(reservedMaterial.get().getQuantity()))
                                .where(MANUFACTURING_ORDER.ID.eq(matchingChildOrder.get().id()))
                                .execute();
                    }
                } else {
                    createChildOrderIfNeeded(order, new HashMap.SimpleEntry<>(materialId, requiredQuantity.subtract(reservedMaterial.get().getQuantity())), List.of());
                }
            } else {
                var matchingChildOrder = notFinishedChildOrders.stream().filter(co -> co.productId().equals(materialId)).findAny();
                if (matchingChildOrder.isPresent()) {
                    notFinishedChildOrders.remove(matchingChildOrder.get());
                    if (matchingChildOrder.get().quantity().compareTo(requiredQuantity) != 0) {
                        db.update(MANUFACTURING_ORDER)
                                .set(MANUFACTURING_ORDER.QUANTITY, requiredQuantity)
                                .where(MANUFACTURING_ORDER.ID.eq(matchingChildOrder.get().id()))
                                .execute();
                    }
                } else {
                    createChildOrderIfNeeded(order, new HashMap.SimpleEntry<>(materialId, requiredQuantity), List.of());
                }
            }
        });
        //clean up unnecessary child orders
        notFinishedChildOrders.forEach(co -> deleteOrder(co.ownerId(), co.id(), false));
    }

    private Optional<ManufacturingOrder> matchingInProgressChildOrder(BigDecimal orderQuantity, List<ManufacturingOrder> childOrders, UUID materialId, BigDecimal requiredQuantity) {
        return childOrders.stream().filter(co -> co.productId().equals(materialId)
                        && requiredQuantity.multiply(orderQuantity).compareTo(co.quantity()) == 0
                        && MANUFACTURING == co.status())
                .findFirst();
    }

    private List<ManufacturingOrder> getChildOrders(ManufacturingOrder parent) {
        return db.selectFrom(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.OWNER_ID.eq(parent.ownerId()), MANUFACTURING_ORDER.PARENT_ID.eq(parent.id()))
                .fetchInto(ManufacturingOrder.class);
    }

    private Map<UUID, BigDecimal> reserveStock(UUID ownerId, UUID orderId, BigDecimal orderQuantity, UUID materialId, BigDecimal requiredQuantity) {
        return inventory.reserveStockForManufacturingOrder(ownerId, orderId, List.of(new InventoryService.Stock(materialId, orderQuantity.multiply(requiredQuantity))),
                false, null);
    }

    private void createChildOrderIfNeeded(ManufacturingOrder parent, Map.Entry<UUID, BigDecimal> unreserved, List<Tuple.Tuple2<String, ByteArrayInputStream>> files) {
        var material = db.selectFrom(MATERIAL_GOOD).where(MATERIAL_GOOD.ID.eq(unreserved.getKey())).fetchSingleInto(MaterialGood.class);
        if (material.details.produced()) {
            orderManufacturing(parent.ownerId(), parent, null, parent.productionDeadline(), material.id, null, unreserved.getValue(), false, parent.notes(), List.of(), files);
        }
    }

    public ManufacturingOrder order(UUID owner, UUID manufacturingOrder) {
        return db.selectFrom(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.ID.eq(manufacturingOrder), MANUFACTURING_ORDER.OWNER_ID.eq(owner))
                .fetchSingleInto(ManufacturingOrder.class);
    }

    private String getNumberBySalesOrder(UUID salesOrderId) {
        var salesOrderNumber = db.select(SALES_ORDER.NUMBER)
                .from(SALES_ORDER)
                .where(SALES_ORDER.ID.eq(salesOrderId))
                .fetchSingleInto(String.class);
        var manufacturingOrdersForSalesOrderCount = db.select(count())
                .from(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.SALES_ORDER_ID.eq(salesOrderId))
                .fetchOptionalInto(Integer.class).orElse(0);

        return salesOrderNumber + " / " + ++manufacturingOrdersForSalesOrderCount;
    }

    private Money manufacturingOverheadPerEmployeeHour(UUID accountId) {
        var account = db.selectFrom(ACCOUNT)
                .where(ACCOUNT.ID.eq(accountId))
                .fetchSingleInto(Account.class);
        var overhead = account.settings().manufacturing().manufacturingOverheadPerEmployeeHour();
        return overhead != null ? overhead : new Money(0, account.settings().general().defaultCurrency());
    }

    //return all hourly rates for the employees that worked on this order
    private Map<UUID, Money> employeeRates(UUID ownerId, UUID orderId) {
        return db.select(USERS.ID, stringField(USERS.DETAILS, "hourlyRate"))
                .from(USERS)
                .where(USERS.OWNER_ID.eq(ownerId), USERS.ID.in(
                        select(MANUFACTURINGTASK_EMPLOYEE.USER_ID).from(MANUFACTURINGTASK_EMPLOYEE)
                                .where(MANUFACTURINGTASK_EMPLOYEE.MANUFACTURING_TASK_ID.in(
                                        select(MANUFACTURING_TASK.ID).from(MANUFACTURING_TASK).where(MANUFACTURING_TASK.MANUFACTURING_ORDER_ID.eq(orderId))
                                ))
                ))
                .fetchMap(Record2::value1,
                        r -> isBlank(r.value2()) ? new Money(0, Money.DEFAULT_CURRENCY) : Json.read(r.value2(), Money.class));
    }

    //return all hourly rates for the employees that worked on this order
    private Map<UUID, Money> workstationRates(UUID ownerId, UUID orderId) {
        return db.select(MANUFACTURING_WORKSTATION.ID, stringField(MANUFACTURING_WORKSTATION.DETAILS, "costPerHour"))
                .from(MANUFACTURING_WORKSTATION)
                .where(MANUFACTURING_WORKSTATION.OWNER_ID.eq(ownerId), MANUFACTURING_WORKSTATION.ID.in(
                        select(MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_WORKSTATION_ID).from(MANUFACTURINGTASK_WORKSTATION)
                                .where(MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_TASK_ID.in(
                                        select(MANUFACTURING_TASK.ID).from(MANUFACTURING_TASK).where(MANUFACTURING_TASK.MANUFACTURING_ORDER_ID.eq(orderId))
                                ))
                ))
                .fetchMap(Record2::value1,
                        r -> isBlank(r.value2()) ? new Money(0, Money.DEFAULT_CURRENCY) : Json.read(r.value2(), Money.class));
    }

    private Map<String, Money> operationCostPerHourRates(ManufacturingOrder order) {
        return manufacturingOperationService.enhance(order.manufacturingOperations()).stream()
                .filter(o -> o.costPerHour() != null)
                .collect(Collectors.toMap(ManufacturingOperation::name, ManufacturingOperation::costPerHour));
    }

    private BigDecimal employeeHourlyRate(UUID id) {
        var employee = db.selectFrom(USERS)
                .where(USERS.ID.eq(id))
                .fetchSingleInto(Employee.class);
        return new BigDecimal(employee.details().hourlyRate() != null
                ? employee.details().hourlyRate().amount()
                : 0
        );
    }

    private BigDecimal workstationHourlyRate(UUID id) {
        var workstation = db.selectFrom(MANUFACTURING_WORKSTATION)
                .where(MANUFACTURING_WORKSTATION.ID.eq(id))
                .fetchSingleInto(Workstation.class);
        return new BigDecimal(workstation.details().costPerHour() != null
                ? workstation.details().costPerHour().amount()
                : 0
        );
    }

    private String number(UUID orderId) {
        return db.select(MANUFACTURING_ORDER.NUMBER)
                .from(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.ID.eq(orderId))
                .fetchSingleInto(String.class);
    }

    private UUID getRootParentOrderId(UUID ownerId, UUID orderId) {
        ManufacturingOrder currentOrder = order(ownerId, orderId);
        if (currentOrder.parentId() == null) {
            return orderId;
        }
        return getRootParentOrderId(ownerId, currentOrder.parentId());
    }


    @JsonIgnoreProperties(ignoreUnknown = true)
    private record ManufacturedGood(
            List<ManufacturingOperation> manufacturingOperations
    ) {
    }
}
