package fabriqon.app.business.manufacturing;

import fabriqon.app.common.model.Money;
import fabriqon.misc.Json;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE;
import static fabriqon.jooq.classes.Tables.MANUFACTURING_WORKSTATION;
import static java.time.Instant.now;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

@Component
@Transactional
public class WorkstationService {

    private final DSLContext db;

    @Autowired
    public WorkstationService(final DSLContext db) {
        this.db = db;
    }

    public Workstation create(UUID ownerId, List<UUID> manufacturingOperationTemplates, String name, Workstation.Details details) {
        final var id = UUID.randomUUID();
        db.insertInto(MANUFACTURING_WORKSTATION)
                .set(MANUFACTURING_WORKSTATION.ID, id)
                .set(MANUFACTURING_WORKSTATION.OWNER_ID, ownerId)
                .set(MANUFACTURING_WORKSTATION.NAME, name)
                .set(MANUFACTURING_WORKSTATION.DETAILS, JSONB.valueOf(Json.write(details)))
                .execute();

        if (isNotEmpty(manufacturingOperationTemplates)) {
            associateWithManufacturingOperationTemplates(id, ownerId, manufacturingOperationTemplates);
        }

        return new Workstation(id, now(), now(), false, ownerId, name, details);
    }

    public Workstation update(UUID ownerId, UUID workstationId, List<UUID> manufacturingOperationTemplates, String name, Money costPerHour) {
        if (name != null || costPerHour != null) {
            var updateStep = db.update(MANUFACTURING_WORKSTATION).set(MANUFACTURING_WORKSTATION.DELETED, false);
            if (name != null) {
                    updateStep = updateStep.set(MANUFACTURING_WORKSTATION.NAME, name);
            }
            if (costPerHour != null) {
                updateStep = updateStep.set(MANUFACTURING_WORKSTATION.DETAILS, JSONB.valueOf(Json.write(new Workstation.Details(costPerHour))));
            }
            updateStep
                    .where(MANUFACTURING_WORKSTATION.ID.eq(workstationId), MANUFACTURING_WORKSTATION.OWNER_ID.eq(ownerId))
                    .execute();
        }


        if (manufacturingOperationTemplates != null) {
            associateWithManufacturingOperationTemplates(workstationId, ownerId, manufacturingOperationTemplates);
        }
        return db.selectFrom(MANUFACTURING_WORKSTATION)
                .where(MANUFACTURING_WORKSTATION.ID.eq(workstationId), MANUFACTURING_WORKSTATION.OWNER_ID.eq(ownerId))
                .fetchSingleInto(Workstation.class);
    }

    public void delete(UUID ownerId, UUID workstationId) {
        db.update(MANUFACTURING_WORKSTATION)
                .set(MANUFACTURING_WORKSTATION.DELETED, true)
                .where(MANUFACTURING_WORKSTATION.ID.eq(workstationId), MANUFACTURING_WORKSTATION.OWNER_ID.eq(ownerId))
                .execute();
    }

    private void associateWithManufacturingOperationTemplates(UUID workstationId, UUID ownerId, List<UUID> manufacturingOperationTemplates) {
        //first delete all the records for this workstation
        db.deleteFrom(MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE)
                .where(
                        MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE.OWNER_ID.eq(ownerId),
                        MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_WORKSTATION_ID.eq(workstationId)
                )
                .execute();
        db.batch(manufacturingOperationTemplates.stream()
                .map(templateId ->
                        db.insertInto(MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE)
                                .set(MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE.OWNER_ID, ownerId)
                                .set(MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_WORKSTATION_ID, workstationId)
                                .set(MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_OPERATION_TEMPLATE_ID, templateId)
                )
                .toList()
        ).execute();
    }
}
