package fabriqon.app.business.manufacturing;

import fabriqon.app.common.model.Money;
import fabriqon.misc.Json;
import fabriqon.misc.Tuple;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.*;
import static java.time.Instant.now;
import static org.apache.commons.lang3.ObjectUtils.isEmpty;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

@Component
@Transactional
public class ManufacturingOperationService {

    private final DSLContext db;

    @Autowired
    public ManufacturingOperationService(final DSLContext db) {
        this.db = db;
    }

    public ManufacturingOperationTemplate create(UUID ownerId,
                                                 List<UUID> workstationIds,
                                                 List<Tuple.Tuple2<UUID, Boolean>> employees,
                                                 String name,
                                                 ManufacturingOperationTemplate.Details details) {
        final var id = UUID.randomUUID();
        db.insertInto(MANUFACTURING_OPERATION_TEMPLATE)
                .set(MANUFACTURING_OPERATION_TEMPLATE.ID, id)
                .set(MANUFACTURING_OPERATION_TEMPLATE.OWNER_ID, ownerId)
                .set(MANUFACTURING_OPERATION_TEMPLATE.NAME, name)
                .set(MANUFACTURING_OPERATION_TEMPLATE.DETAILS, JSONB.valueOf(Json.write(details)))
                .execute();
        associateWithWorkstations(id, ownerId, workstationIds);
        associateWithEmployees(id, ownerId, employees);
        return new ManufacturingOperationTemplate(id, now(), now(), false, ownerId, name, details);
    }

    public ManufacturingOperationTemplate update(UUID ownerId, UUID operationId,
                                                 List<UUID> workstationIds,
                                                 List<Tuple.Tuple2<UUID, Boolean>> employeeIds,
                                                 String name,
                                                 Boolean parallelizable, Money costPerHour) {
        if (name != null) {
            var updateStep = db.update(MANUFACTURING_OPERATION_TEMPLATE)
                    .set(MANUFACTURING_OPERATION_TEMPLATE.DELETED, false)
                    .set(MANUFACTURING_OPERATION_TEMPLATE.NAME, name);
            if (parallelizable != null || costPerHour != null) {
                var details = db.select(MANUFACTURING_OPERATION_TEMPLATE.DETAILS)
                        .from(MANUFACTURING_OPERATION_TEMPLATE)
                        .where(MANUFACTURING_OPERATION_TEMPLATE.ID.eq(operationId), MANUFACTURING_OPERATION_TEMPLATE.OWNER_ID.eq(ownerId))
                        .fetchSingleInto(ManufacturingOperationTemplate.Details.class);
                details = new ManufacturingOperationTemplate.Details(parallelizable != null ? parallelizable : details.parallelizable(), costPerHour != null ? costPerHour : details.costPerHour());
                updateStep = updateStep.set(MANUFACTURING_OPERATION_TEMPLATE.DETAILS, JSONB.valueOf(Json.write(details)));
            }
            updateStep
                    .where(MANUFACTURING_OPERATION_TEMPLATE.ID.eq(operationId), MANUFACTURING_OPERATION_TEMPLATE.OWNER_ID.eq(ownerId))
                    .execute();
        }
        if (workstationIds != null) {
            associateWithWorkstations(operationId, ownerId, workstationIds);
        }
        if (employeeIds != null) {
            associateWithEmployees(operationId, ownerId, employeeIds);
        }
        return db.selectFrom(MANUFACTURING_OPERATION_TEMPLATE)
                .where(MANUFACTURING_OPERATION_TEMPLATE.ID.eq(operationId), MANUFACTURING_OPERATION_TEMPLATE.OWNER_ID.eq(ownerId))
                .fetchSingleInto(ManufacturingOperationTemplate.class);
    }

    public void delete(UUID ownerId, UUID operationId) {
        db.update(MANUFACTURING_OPERATION_TEMPLATE)
                .set(MANUFACTURING_OPERATION_TEMPLATE.DELETED, true)
                .where(MANUFACTURING_OPERATION_TEMPLATE.ID.eq(operationId), MANUFACTURING_OPERATION_TEMPLATE.OWNER_ID.eq(ownerId))
                .execute();
    }

    /**
     * Manufacturing operations can be customized in a product based on a template, defined from scratch so we need to
     * apply logic for where to get different data points from.
     * This method should be used whenever data about manufacturing operations configured for a product is required
     */
    public List<ManufacturingOperation> enhance(List<ManufacturingOperation> manufacturingOperations) {
        if (isEmpty(manufacturingOperations)) {
            return List.of();//for products that don't have manufacturing operations
        }
        return manufacturingOperations.stream()
                .map(operation -> {
                    if (operation.operationTemplateId() != null) {
                        //we load the operation from the DB and populate missing or priority parts of the data
                        var operationTemplate = loadOperationTemplate(operation.operationTemplateId());
                        return new ManufacturingOperation(
                                operation.operationTemplateId(),
                                candidateWorkstations(operation.operationTemplateId()),
                                candidateEmployees(operation.operationTemplateId()),
                                operation.name() != null ? operation.name() : operationTemplate.name(),
                                operation.durationInMinutes() != null ? operation.durationInMinutes() : 0,
                                operation.parallelizable() != null ? operation.parallelizable() : operationTemplate.details().parallelizable(),
                                operation.costPerHour() != null ? operation.costPerHour() : operationTemplate.details().costPerHour(),
                                operation.numberOfAssignees() != null ? operation.numberOfAssignees() : null,
                                isNotEmpty(operation.manuallyAssignedEmployees()) ? operation.manuallyAssignedEmployees() : List.of(),
                                isNotEmpty(operation.manuallyAssignedWorkstations()) ? operation.manuallyAssignedWorkstations() : List.of(),
                                isNotEmpty(operation.materials()) ? operation.materials() : List.of()
                        );
                    } else {
                        return operation;
                    }
                })
                .toList();
    }

    private List<UUID> candidateEmployees(UUID templateId) {
        return db.select(USERS.ID)
                .from(USERS)
                .join(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE).on(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.USER_ID.eq(USERS.ID))
                .where(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_OPERATION_TEMPLATE_ID.eq(templateId))
                .orderBy(USERS.NAME)
                .fetchInto(UUID.class);
    }

    private List<UUID> candidateWorkstations(UUID templateId) {
        return db.select(MANUFACTURING_WORKSTATION.ID)
                .from(MANUFACTURING_WORKSTATION)
                .join(MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE).on(MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_WORKSTATION_ID.eq(MANUFACTURING_WORKSTATION.ID))
                .where(MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_OPERATION_TEMPLATE_ID.eq(templateId))
                .orderBy(MANUFACTURING_WORKSTATION.NAME)
                .fetchInto(UUID.class);
    }

    private ManufacturingOperationTemplate loadOperationTemplate(UUID operationTemplateId) {
        return db.selectFrom(MANUFACTURING_OPERATION_TEMPLATE)
                .where(MANUFACTURING_OPERATION_TEMPLATE.ID.eq(operationTemplateId))
                .fetchSingleInto(ManufacturingOperationTemplate.class);
    }

    private void associateWithWorkstations(UUID templateId, UUID ownerId, List<UUID> workstationIds) {
        //first delete all the records for this operation
        db.deleteFrom(MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE)
                .where(
                        MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE.OWNER_ID.eq(ownerId),
                        MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_OPERATION_TEMPLATE_ID.eq(templateId)
                )
                .execute();
        db.batch(workstationIds.stream()
                .map(workstationId ->
                        db.insertInto(MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE)
                                .set(MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE.OWNER_ID, ownerId)
                                .set(MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_WORKSTATION_ID, workstationId)
                                .set(MANUFACTURINGWORKSTATION_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_OPERATION_TEMPLATE_ID, templateId)
                )
                .toList()
        ).execute();
    }

    private void associateWithEmployees(UUID templateId, UUID ownerId, List<Tuple.Tuple2<UUID, Boolean>> employees) {
        //first delete all the records for this operation
        db.deleteFrom(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE)
                .where(
                        EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.OWNER_ID.eq(ownerId),
                        EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_OPERATION_TEMPLATE_ID.eq(templateId)
                )
                .execute();

        db.batch(employees.stream()
                .map(employee ->
                        db.insertInto(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE)
                                .set(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.OWNER_ID, ownerId)
                                .set(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.USER_ID, employee.a())
                                .set(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_OPERATION_TEMPLATE_ID, templateId)
                                .set(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.PREFERENTIAL, employee.b())
                )
                .toList()
        ).execute();
    }

}
