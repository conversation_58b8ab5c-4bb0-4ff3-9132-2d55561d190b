package fabriqon.app.business.manufacturing.tasks;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.manufacturing.ManufacturingTask;
import fabriqon.misc.Tuple;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static fabriqon.jooq.classes.Tables.*;
import static org.apache.commons.lang3.BooleanUtils.isNotTrue;
import static org.apache.commons.lang3.BooleanUtils.isTrue;

@Slf4j
class TaskShifter {

    enum ShiftType { DEFAULT, IN_PROGRESS, DONE, EXTEND }

    private final DSLContext db;
    private final TransactionTemplate transactionTemplate;
    private final TimeUtil timeUtil;
    private static final Map<String, Object> keyLocks = new ConcurrentHashMap<>();

    TaskShifter(DSLContext db,
                TransactionTemplate transactionTemplate,
                Account.Settings accountSettings) {
        this.db = db;
        this.transactionTemplate = transactionTemplate;
        this.timeUtil = new TimeUtil(accountSettings);
    }

    void shift(UUID taskId, LocalDateTime startTime, LocalDateTime endTime,
                       boolean shiftForward, ShiftType shiftType) {
        var task = task(taskId);
        synchronized (keyLocks.computeIfAbsent(task.ownerId().toString(), k -> new Object())) {
            transactionTemplate.execute(new TransactionCallbackWithoutResult() {
                public void doInTransactionWithoutResult(TransactionStatus status) {
                    shift0(task, startTime, endTime, shiftForward, shiftType);
                }
            });
        }
    }

    private void shift0(ManufacturingTask task, LocalDateTime startTime, LocalDateTime endTime,
                        boolean shiftForward, ShiftType shiftType) {
        if (shiftType == ShiftType.DEFAULT
                && (task.startTime().equals(startTime) || (shiftForward ? task.startTime().isAfter(startTime) : task.startTime().isBefore(startTime)))) {
            return;
        }
        Tuple.Tuple2<LocalDateTime, LocalDateTime> adjustedStartEnd;
        if (shiftForward) {
            adjustedStartEnd = shiftTask(task, startTime, endTime, shiftType);
        } else {
            //we need to check how early backwards we can shift the task
            var earliestStartTime = shiftType == ShiftType.DEFAULT
                    ? nextAvailableStartTime(task.id(), startTime, task.durationInMinutes())
                    : startTime;
            adjustedStartEnd = shiftTask(task, earliestStartTime, endTime, shiftType);
        }
        getNextTasksInOrder(task)
                .forEach(t -> shift0(t, getStartTimeForOrder(shiftForward, adjustedStartEnd, t, task),
                        null, shiftForward, ShiftType.DEFAULT));
        getNextWorkstationTaskAfter(shiftForward, adjustedStartEnd.b(), task, workstation(task.id()))
                .filter(t -> !shiftForward || t.ranking() > task.ranking())
                .ifPresent(t -> shift0(t, getStartTimeForAssignees(shiftForward, adjustedStartEnd, t),
                        null, shiftForward, ShiftType.DEFAULT));
        employees(task.id())
                .forEach(e -> getNextEmployeeTaskAfter(shiftForward, adjustedStartEnd.b(), task, e)
                        .filter(t -> !shiftForward || t.ranking() > task.ranking())
                        .ifPresent(t -> shift0(t, getStartTimeForAssignees(shiftForward, adjustedStartEnd, t),
                                null, shiftForward, ShiftType.DEFAULT)));
    }

    private LocalDateTime getStartTimeForOrder(boolean shiftForward, Tuple.Tuple2<LocalDateTime, LocalDateTime> adjustedStartEnd,
                                               ManufacturingTask next, ManufacturingTask shifted) {
        var start = adjustedStartEnd.a() != null ? adjustedStartEnd.a() : shifted.startTime();//this might be null in certain conditions
        if (next.details().parallelizable()) {
            return shiftForward
                    ? getEndTimeFromPreviousNotParallelizableTask(shifted)
                    : nextAvailableStartTime(next.id(), start, next.durationInMinutes());
        }
        return shiftForward
                ? isTrue(shifted.details().parallelizable())
                ? getEndTimeFromPreviousParallelizableTasks(next)
                : adjustedStartEnd.b().isAfter(next.startTime()) ? adjustedStartEnd.b() : next.startTime()
                : nextAvailableStartTime(next.id(), adjustedStartEnd.b(), next.durationInMinutes());
    }

    private LocalDateTime getStartTimeForAssignees(boolean shiftForward,
                                                   Tuple.Tuple2<LocalDateTime, LocalDateTime> adjustedStartEnd,
                                                   ManufacturingTask next) {
        return shiftForward ? adjustedStartEnd.b() : getEarliestTimeToShiftBackwards(next, adjustedStartEnd.b());
    }

    private Tuple.Tuple2<LocalDateTime, LocalDateTime> shiftTask(ManufacturingTask task,
                                                                 LocalDateTime startTime,
                                                                 LocalDateTime endTime,
                                                                 ShiftType shiftType
    ) {
        var adjustedStartTime = shiftType == ShiftType.DEFAULT || shiftType == ShiftType.IN_PROGRESS
                ? timeUtil.getStartTime(startTime) : null;
        var adjustedEndTime = shiftType == ShiftType.DONE || shiftType == ShiftType.EXTEND
                ? endTime : timeUtil.getEndTime(adjustedStartTime, task.durationInMinutes());
        log.info("shifting [{}] for order [{}] with start [{}] and adjusted [{}] and end [{}] and adjusted [{}]",
                task.id(), task.manufacturingOrderId(), task.startTime(), adjustedStartTime, task.endTime(), adjustedEndTime);
        switch (shiftType) {
            case IN_PROGRESS -> db.update(MANUFACTURING_TASK)
                    .set(MANUFACTURING_TASK.STATUS, ManufacturingTask.Status.IN_PROGRESS.name())
                    .set(MANUFACTURING_TASK.STATUS_REASON, (String) null)
                    .set(MANUFACTURING_TASK.START_TIME, startTime)
                    .set(MANUFACTURING_TASK.END_TIME, adjustedEndTime)
                    .set(MANUFACTURING_TASK.ESTIMATED_END_TIME, adjustedEndTime)
                    .where(MANUFACTURING_TASK.ID.eq(task.id()))
                    .execute();
            case DONE -> {
                startTime = task.startTime().isBefore(adjustedEndTime) ? task.startTime() : adjustedEndTime;
                var update = db.update(MANUFACTURING_TASK)
                        .set(MANUFACTURING_TASK.STATUS, ManufacturingTask.Status.DONE.name())
                        .set(MANUFACTURING_TASK.START_TIME, startTime)
                        .set(MANUFACTURING_TASK.END_TIME, adjustedEndTime);
                if (task.status() == ManufacturingTask.Status.TODO) {//we set the start time as well in this case
                    update = update
                            .set(MANUFACTURING_TASK.END_TIME, adjustedEndTime);
                }
                update
                        .where(MANUFACTURING_TASK.ID.eq(task.id()))
                        .execute();
            }
            case EXTEND -> db.update(MANUFACTURING_TASK)
                    .set(MANUFACTURING_TASK.END_TIME, adjustedEndTime)
                    .set(MANUFACTURING_TASK.ESTIMATED_END_TIME, adjustedEndTime)
                    .where(MANUFACTURING_TASK.ID.eq(task.id()))
                    .execute();
            default -> db.update(MANUFACTURING_TASK)
                    .set(MANUFACTURING_TASK.START_TIME, adjustedStartTime)
                    .set(MANUFACTURING_TASK.ESTIMATED_START_TIME, adjustedStartTime)
                    .set(MANUFACTURING_TASK.END_TIME, adjustedEndTime)
                    .set(MANUFACTURING_TASK.ESTIMATED_END_TIME, adjustedEndTime)
                    .where(MANUFACTURING_TASK.ID.eq(task.id()))
                    .execute();
        }
        return Tuple.of(adjustedStartTime, adjustedEndTime);
    }

    private List<ManufacturingTask> getNextTasksInOrder(ManufacturingTask task) {
        var tasks = getTasksForOrder(task.manufacturingOrderId());
        var remaining = tasks.subList(tasks.stream().map(ManufacturingTask::id).toList().indexOf(task.id()) + 1, tasks.size());
        if (remaining.isEmpty()) {
            return List.of();
        }
        if (task.details().parallelizable()) {
            if (remaining.get(0).details().parallelizable()) {
                return List.of();
            } else {
                return List.of(remaining.get(0));
            }
        } else {
            if (!remaining.get(0).details().parallelizable()) {
                return List.of(remaining.get(0));
            } else {
                OptionalInt indexOpt = IntStream.range(0, remaining.size())
                        .filter(i -> !remaining.get(i).details().parallelizable())
                        .findFirst();
                return indexOpt.isPresent() ? remaining.subList(0, indexOpt.getAsInt()) : remaining;
            }
        }
    }

    private LocalDateTime getEndTimeFromPreviousNotParallelizableTask(ManufacturingTask task) {
        var tasks = getTasksForOrder(task.manufacturingOrderId());
        ManufacturingTask notParallelizableTask = null;
        for (ManufacturingTask current : tasks) {
            if (isNotTrue(current.details().parallelizable())) {
                notParallelizableTask = current;
            }
            if (current.id().equals(task.id())) {
                break;
            }
        }
        return notParallelizableTask != null
                ? notParallelizableTask.endTime()
                //this is the case when the order starts with parallel tasks; in this case we return the current time
                : timeUtil.localDateTimeForAccount();
    }

    private LocalDateTime getEndTimeFromPreviousParallelizableTasks(ManufacturingTask task) {
        var tasks = getTasksForOrder(task.manufacturingOrderId());
        Collections.reverse(tasks);
        var subList = tasks.subList(tasks.stream()
                        .map(ManufacturingTask::id)
                        .toList()
                        .indexOf(task.id()) + 1,
                tasks.size());
        var parallelizableTasks = new ArrayList<ManufacturingTask>();
        for (ManufacturingTask current : subList) {
            if (isNotTrue(current.details().parallelizable())) {
                break;
            }
            parallelizableTasks.add(current);
        }
        return parallelizableTasks
                .stream()
                .max(Comparator.comparing(ManufacturingTask::endTime))
                .map(ManufacturingTask::endTime)
                .orElseThrow();
    }

    private LocalDateTime nextAvailableStartTime(UUID taskId,
                                                 LocalDateTime earliestStartTime,
                                                 int totalDurationMinutes) {
        var earliestWorkstationStartTime = nextAvailableStartTime(
                getWorkstationTasksAfter(workstation(taskId), earliestStartTime),
                taskId,
                earliestStartTime,
                totalDurationMinutes);
        //and now we consider the employees
        return nextAvailableEmployeeStartTime(
                getEmployeeTasksAfter(employees(taskId), earliestWorkstationStartTime),
                taskId,
                earliestWorkstationStartTime,
                totalDurationMinutes);
    }

    //get the earliest start time where this task would fit
    private LocalDateTime nextAvailableStartTime(List<ManufacturingTask> manufacturingTasks,
                                                 UUID taskId,
                                                 LocalDateTime earliestStartTime,
                                                 int totalDurationMinutes) {
        if (manufacturingTasks.isEmpty()) {
            return earliestStartTime;
        }
        var intervals = intervalsByTasks(taskId, earliestStartTime, manufacturingTasks);

        for (int i = 0; i < intervals.size(); i++) {
            var current = intervals.get(i);
            var startTime = timeUtil.getStartTime(current.b());
            var endTime = timeUtil.getEndTime(startTime, totalDurationMinutes);
            if (endTime.isBefore(current.c())
                    || (i + 1 < intervals.size() && (intervals.get(i + 1) == null || intervals.get(i + 1).a().equals(taskId)))) {
                return startTime;
            }
        }
        //should never reach this point
        throw new RuntimeException("can't find a suitable start time");
    }

    private LocalDateTime nextAvailableEmployeeStartTime(List<ManufacturingTask> manufacturingTasks,
                                                         UUID taskId,
                                                         LocalDateTime earliestStartTime,
                                                         int totalDurationMinutes) {
        var employees = employees(taskId);
        if (employees.size() == 1) {
            return nextAvailableStartTime(manufacturingTasks, taskId, earliestStartTime, totalDurationMinutes);
        }
        return nextAvailableStartTimeForEmployees(employees.size(), earliestStartTime, totalDurationMinutes,
                timeUtil.intervalsByResource(employees, earliestStartTime,
                        manufacturingTasks.stream()
                                //we eliminate the task as we want to leave that spot open
                                .filter(t -> !t.id().equals(taskId))
                                .flatMap(t -> employees(t.id()).stream()
                                        .filter(employees::contains)
                                        .map(e -> Tuple.of(e, t.startTime(), t.endTime())))
                                .toList())
        );
    }

    private LocalDateTime nextAvailableStartTimeForEmployees(int noOfEmployees,
                                                             LocalDateTime startTime,
                                                             int totalDurationMinutes,
                                                             List<Tuple.Tuple3<UUID, LocalDateTime, LocalDateTime>> intervals) {
        record Point(LocalDateTime time, boolean start, Set<UUID> employees, Set<UUID> openEmployees) {}
        var points = intervals.stream()
                .flatMap(i -> Stream.of(
                        new Point(i.b(), true, Set.of(i.a()), new HashSet<>()),
                        new Point(i.c(), false, Set.of(i.a()), new HashSet<>())
                ))
                //we merge duplicate points
                .collect(Collectors.toMap(p -> Tuple.of(p.time, p.start), p -> p, (p1, p2) ->
                        new Point(p1.time, p1.start, Stream.concat(p1.employees.stream(), p2.employees.stream()).collect(Collectors.toSet()), new HashSet<>())))
                .entrySet().stream()
                .map(e -> new Point(e.getKey().a(), e.getKey().b(), e.getValue().employees(), e.getValue().openEmployees()))
                .sorted(Comparator.comparing(Point::time))
                .toList();
        var currentlyOpen = new HashSet<UUID>();
        points.forEach(p -> {
            if (p.start) {
                currentlyOpen.addAll(p.employees);
            } else {
                currentlyOpen.removeAll(p.employees);
            }
            p.openEmployees.addAll(currentlyOpen);
        });
        for (int i = 0; i < points.size(); i++) {
            var current = points.get(i);
            if (current.start && current.openEmployees.size() >= noOfEmployees) {
                var currentCandidates = new HashSet<>(current.openEmployees);
                for (Point point : points.subList(i + 1, points.size()).stream().filter(p -> !p.start).toList()) {
                    if (currentCandidates.size() - intersection(currentCandidates, point.employees).size() < noOfEmployees) {
                        var start = timeUtil.getStartTime(current.time.isAfter(startTime) ? current.time : startTime);
                        var end = timeUtil.getEndTime(start, totalDurationMinutes);
                        if (end.isBefore(point.time) || end.equals(point.time)) {
                            return start;
                        }
                        break;
                    } else {
                        currentCandidates.removeAll(point.employees);
                    }
                }
            }
        }
        //should never reach this point
        throw new RuntimeException("can't find a suitable start time");
    }

    private Set<UUID> intersection(Set<UUID> l1, Set<UUID> l2) {
        return l1.stream().filter(l2::contains).collect(Collectors.toSet());
    }

    private List<Tuple.Tuple3<UUID, LocalDateTime, LocalDateTime>> intervalsByTasks(UUID taskId, LocalDateTime startTime, List<ManufacturingTask> tasks) {
        var intervals = new ArrayList<Tuple.Tuple3<UUID, LocalDateTime, LocalDateTime>>();
        if (startTime.isBefore(tasks.get(0).startTime())) {
            intervals.add(Tuple.of(null, startTime, tasks.get(0).startTime()));
        }
        for (int i = 0; i < tasks.size() ; i++) {
            if (tasks.get(i).id().equals(taskId)) {
                //special room for the task as it can span it's already scheduled slot
                intervals.add(Tuple.of(
                        tasks.get(i).id(),
                        tasks.get(i).startTime(),
                        i + 1 < tasks.size()
                                ? tasks.get(i + 1).endTime()
                                : LocalDateTime.MAX));
            } else {
                intervals.add(Tuple.of(
                        tasks.get(i).id(),
                        tasks.get(i).endTime(),
                        i + 1 < tasks.size()
                                ? tasks.get(i + 1).startTime()
                                : LocalDateTime.MAX));
            }
        }
        intervals.sort(Comparator.comparing(Tuple.Tuple3::b));
        return intervals;
    }

    private Optional<ManufacturingTask> getNextEmployeeTaskAfter(boolean shiftForward, LocalDateTime startTime, ManufacturingTask task, UUID employeeId) {
        var filter = MANUFACTURING_TASK.ID.ne(task.id())
                .and(MANUFACTURINGTASK_EMPLOYEE.USER_ID.eq(employeeId))
                .and(MANUFACTURING_TASK.STATUS.eq(ManufacturingTask.Status.TODO.name()));
        filter = shiftForward
                ? filter.and(MANUFACTURING_TASK.RANKING.greaterOrEqual(task.ranking()))
                : filter.and(MANUFACTURING_TASK.START_TIME.greaterOrEqual(startTime));
        return db.select()
                .from(MANUFACTURING_TASK)
                .join(MANUFACTURINGTASK_EMPLOYEE).on(MANUFACTURING_TASK.ID.eq(MANUFACTURINGTASK_EMPLOYEE.MANUFACTURING_TASK_ID))
                .where(filter)
                .orderBy(MANUFACTURING_TASK.START_TIME)
                .limit(1)
                .fetchOptionalInto(ManufacturingTask.class);
    }

    private Optional<ManufacturingTask> getNextWorkstationTaskAfter(boolean shiftForward, LocalDateTime startTime, ManufacturingTask task, UUID workstationId) {
        var filter = MANUFACTURING_TASK.ID.ne(task.id())
                .and(MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_WORKSTATION_ID.eq(workstationId))
                .and(MANUFACTURING_TASK.STATUS.eq(ManufacturingTask.Status.TODO.name()));
        filter = shiftForward
                ? filter.and(MANUFACTURING_TASK.RANKING.greaterOrEqual(task.ranking()))
                : filter.and(MANUFACTURING_TASK.START_TIME.greaterOrEqual(startTime));
        return db.select()
                .from(MANUFACTURING_TASK)
                .join(MANUFACTURINGTASK_WORKSTATION).on(MANUFACTURING_TASK.ID.eq(MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_TASK_ID))
                .where(filter)
                .orderBy(MANUFACTURING_TASK.START_TIME)
                .limit(1)
                .fetchOptionalInto(ManufacturingTask.class);
    }

    private List<ManufacturingTask> getTasksForOrder(UUID orderId) {
        return db.selectFrom(MANUFACTURING_TASK)
                .where(MANUFACTURING_TASK.MANUFACTURING_ORDER_ID.eq(orderId))
                .orderBy(MANUFACTURING_TASK.CREATE_TIME)
                .fetchInto(ManufacturingTask.class);
    }

    private UUID workstation(UUID taskId) {
        return db.select(MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_WORKSTATION_ID).from(MANUFACTURINGTASK_WORKSTATION)
                .where(MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_TASK_ID.eq(taskId))
                .fetchOneInto(UUID.class);
    }

    private List<ManufacturingTask> getWorkstationTasksAfter(UUID workstation, LocalDateTime startTime) {
        return db.select()
                .from(MANUFACTURING_TASK)
                .join(MANUFACTURINGTASK_WORKSTATION).on(MANUFACTURING_TASK.ID.eq(MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_TASK_ID))
                .where(MANUFACTURING_TASK.END_TIME.greaterOrEqual(startTime), MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_WORKSTATION_ID.eq(workstation))
                .orderBy(MANUFACTURING_TASK.START_TIME)
                .fetchInto(ManufacturingTask.class);
    }

    private List<ManufacturingTask> getEmployeeTasksAfter(List<UUID> employees, LocalDateTime startTime) {
        return db.select()
                .from(MANUFACTURING_TASK)
                .join(MANUFACTURINGTASK_EMPLOYEE).on(MANUFACTURING_TASK.ID.eq(MANUFACTURINGTASK_EMPLOYEE.MANUFACTURING_TASK_ID))
                .where(MANUFACTURING_TASK.END_TIME.greaterOrEqual(startTime), MANUFACTURINGTASK_EMPLOYEE.USER_ID.in(employees))
                .orderBy(MANUFACTURING_TASK.START_TIME)
                .fetchInto(ManufacturingTask.class);
    }


    private LocalDateTime getEarliestTimeToShiftBackwards(ManufacturingTask task, LocalDateTime endTime) {
        var taskBefore = db.select()
                .from(MANUFACTURING_TASK)
                .where(MANUFACTURING_TASK.END_TIME.lessOrEqual(task.startTime()), MANUFACTURING_TASK.MANUFACTURING_ORDER_ID.eq(task.manufacturingOrderId()))
                .orderBy(MANUFACTURING_TASK.END_TIME.desc())
                .limit(1)
                .fetchOptionalInto(ManufacturingTask.class);
        return taskBefore
                .filter(t -> !t.endTime().isBefore(endTime))
                .map(ManufacturingTask::endTime)
                .orElse(endTime);
    }


    List<UUID> employees(UUID taskId) {
        return db.select(MANUFACTURINGTASK_EMPLOYEE.USER_ID).from(MANUFACTURINGTASK_EMPLOYEE)
                .where(MANUFACTURINGTASK_EMPLOYEE.MANUFACTURING_TASK_ID.eq(taskId))
                .fetchInto(UUID.class);
    }

    ManufacturingTask task(UUID id) {
        return db.selectFrom(MANUFACTURING_TASK)
                .where(MANUFACTURING_TASK.ID.eq(id))
                .fetchSingleInto(ManufacturingTask.class);
    }
    
}
