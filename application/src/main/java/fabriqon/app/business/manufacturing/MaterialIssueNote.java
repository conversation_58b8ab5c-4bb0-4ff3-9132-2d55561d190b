package fabriqon.app.business.manufacturing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.type.TypeReference;
import fabriqon.app.common.model.Money;
import fabriqon.misc.Json;

import java.beans.ConstructorProperties;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

public record MaterialIssueNote(
        UUID id,
        Instant createTime,
        Instant updateTime,
        boolean deleted,

        UUID ownerId,
        UUID manufacturingOrderId,
        UUID servicingOrderId,
        String number,
        LocalDate date,
        Details details,
        List<Material> materials
) {

    @ConstructorProperties({"id", "create_time", "update_time", "deleted", "owner_id", "manufacturing_order_id", "servicing_order_id",
            "number", "date", "details", "materials"})
    public MaterialIssueNote(UUID id, Instant createTime, Instant updateTime, boolean deleted, UUID ownerId,
                             UUID manufacturingOrderId, UUID servicingOrderId, String number, LocalDate date,
                             String details, String materials) {
        this(id, createTime, updateTime, deleted, ownerId,
                manufacturingOrderId, servicingOrderId, number, date,
                Json.readNullSafe(details, Details.class),
                Json.readNullSafe(materials, new TypeReference<List<Material>>() {})
        );
    }

    public record Details(
            String inventoryManager,
            String worker
    ) {}

    @JsonIgnoreProperties(ignoreUnknown = true)
    public record Material(
            UUID id,
            UUID inventoryUnitId,
            BigDecimal quantity,
            Money totalCost,
            BigDecimal wastedQuantity
    ) {
    }
}
