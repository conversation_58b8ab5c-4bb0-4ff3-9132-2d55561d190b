package fabriqon.app.business.manufacturing.tasks;

import fabriqon.Notification;
import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.exceptions.BusinessException;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.manufacturing.ManufacturingOperation;
import fabriqon.app.business.manufacturing.ManufacturingOperationService;
import fabriqon.app.business.manufacturing.ManufacturingOrder;
import fabriqon.app.business.manufacturing.ManufacturingTask;
import fabriqon.app.business.manufacturing.events.TaskCompleted;
import fabriqon.app.business.notifications.NotificationService;
import fabriqon.events.Events;
import fabriqon.misc.Json;
import fabriqon.misc.Tuple;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.jooq.Table;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.stream.IntStream;

import static fabriqon.app.business.manufacturing.ManufacturingOrder.UNRANKED_ORDER_VALUE;
import static fabriqon.jooq.classes.Tables.*;
import static org.apache.commons.lang3.BooleanUtils.isNotTrue;
import static org.apache.commons.lang3.BooleanUtils.isTrue;
import static org.apache.commons.lang3.ObjectUtils.isEmpty;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.jooq.impl.DSL.and;
import static org.jooq.impl.DSL.select;

@Slf4j
@Component
public class ManufacturingTaskService {
    private final DSLContext db;
    private final TransactionTemplate transactionTemplate;
    private final ManufacturingOperationService manufacturingOperationService;
    private final Events events;
    private final NotificationService notificationService;

    @Autowired
    public ManufacturingTaskService(PlatformTransactionManager transactionManager,
                                    DSLContext db,
                                    ManufacturingOperationService manufacturingOperationService,
                                    Events events,
                                    NotificationService notificationService) {
        this.transactionTemplate = new TransactionTemplate(transactionManager);
        this.db = db;
        this.manufacturingOperationService = manufacturingOperationService;
        this.events = events;
        this.notificationService = notificationService;
    }

    /**
     * Create tasks for the manufacturing order based on the manufacturing operations.
     */
    @Transactional
    public void createTasks(UUID ownerId, UUID orderId,
                            String orderNumber,
                            BigDecimal quantity,
                            List<ManufacturingOperation> manufacturingOperations) {
        if (isEmpty(manufacturingOperations)) {
            log.warn("Manufacturing operations list is empty for [{}]", orderId);
            throw new BusinessException("Cannot create tasks. Empty operations.", "no_manufacturing_operations_defined");
        }
        var enhancedManufacturingOperations = manufacturingOperationService.enhance(manufacturingOperations);
        if (enhancedManufacturingOperations.stream().anyMatch(o -> isEmpty(o.candidateEmployeeIds()))) {
            throw new BusinessException("Cannot create tasks. No candidate employees.", "no_candidate_employees_defined");
        }
        var accountSettings = accountSettings(ownerId);
        var counter = new AtomicLong(0);
        var timeUtil = new TimeUtil(accountSettings);
        var earliestStartTime = new AtomicReference<>(timeUtil.localDateTimeForAccount());
        var earliestStartTimeAssignees = new AtomicReference<List<UUID>>();
        var latestTime = new AtomicReference<>(earliestStartTime.get());
        var latestAssignees = new AtomicReference<List<UUID>>();
        var scheduler = new TaskScheduler(db, accountSettings);
        enhancedManufacturingOperations.forEach(operation -> {
            var taskNumber = orderNumber + " / " + counter.incrementAndGet();
            //see if there is a task already created
            var task = db.selectFrom(MANUFACTURING_TASK)
                    .where(MANUFACTURING_TASK.OWNER_ID.eq(ownerId), MANUFACTURING_TASK.MANUFACTURING_ORDER_ID.eq(orderId))
                    .fetchInto(ManufacturingTask.class)
                    .stream()
                    .filter(t -> t.details().number().equals(taskNumber))
                    .findAny()
                    .orElseGet(() -> {
                        if (isNotTrue(operation.parallelizable()) && latestTime.get().isAfter(earliestStartTime.get())) {
                            earliestStartTime.set(latestTime.get());
                            earliestStartTimeAssignees.set(latestAssignees.get());
                        }
                        var totalDurationInMinutes = totalDuration(operation.durationInMinutes(), quantity,
                                operation.numberOfAssignees() != null ? operation.numberOfAssignees() : 1);
                        var schedule = scheduler.schedule(operation, earliestStartTime.get(), totalDurationInMinutes, earliestStartTimeAssignees.get());
                        return createTask(ownerId, orderId, schedule.c(), schedule.d(), totalDurationInMinutes,
                                taskNumber, operation.name(), isTrue(operation.parallelizable()),
                                schedule.a(), schedule.b());
                    });
            latestAssignees.set(latestTime.get().isAfter(task.endTime()) ? latestAssignees.get() : employees(task.id()));
            latestTime.set(latestTime.get().isAfter(task.endTime()) ? latestTime.get() : task.endTime());
            if (isNotTrue(operation.parallelizable())) {
                earliestStartTime.set(task.endTime());
                earliestStartTimeAssignees.set(employees(task.id()));
            }
        });
    }

    public List<ManufacturingTask> getTasks(UUID ownerId, List<ManufacturingTask.Status> statuses, LocalDateTime start, LocalDateTime end) {
        var accountSettings = accountSettings(ownerId);
        var localDateTime = new TimeUtil(accountSettings).localDateTimeForAccount();
        //we construct this filter for ownership and to get all tasks that might need to be shifted
        var tasks = db.selectFrom(MANUFACTURING_TASK)
                .where(MANUFACTURING_TASK.OWNER_ID.eq(ownerId)
                        .and(MANUFACTURING_TASK.DELETED.isFalse())
                        .and(MANUFACTURING_TASK.STATUS.notEqual(ManufacturingTask.Status.DONE.name()))
                        .and(MANUFACTURING_TASK.START_TIME.lt(localDateTime)))
                .orderBy(MANUFACTURING_TASK.START_TIME)
                .fetchInto(ManufacturingTask.class);
        log.info("before processing results");
        tasks.stream()
                .filter(task -> task.status() == ManufacturingTask.Status.TODO)
                .filter(task -> task.startTime().isBefore(localDateTime
                        //add some delay here so we can test
                        .minusSeconds(30)))
                .forEach(task -> new TaskShifter(db, transactionTemplate, accountSettings).shift(task.id(), localDateTime, null, true, TaskShifter.ShiftType.DEFAULT));
        tasks.stream()
                .filter(task -> task.status() != ManufacturingTask.Status.TODO)
                .filter(task -> task.endTime().isBefore(localDateTime))
                .forEach(task -> new TaskShifter(db, transactionTemplate, accountSettings).shift(task.id(), null, localDateTime, true, TaskShifter.ShiftType.EXTEND));
        log.info("after processing results");
        //get fresh data with applied filters
        var filter = MANUFACTURING_TASK.OWNER_ID.eq(ownerId).and(MANUFACTURING_TASK.DELETED.isFalse());
        if (isNotEmpty(statuses)) {
            filter = filter.and(MANUFACTURING_TASK.STATUS.in(statuses));
        }
        if (start != null && end != null) {
            filter = filter.and(
                    and(MANUFACTURING_TASK.START_TIME.gt(start).and(MANUFACTURING_TASK.START_TIME.lt(end)))
                            .or(MANUFACTURING_TASK.END_TIME.gt(start).and(MANUFACTURING_TASK.END_TIME.lt(end)))
                            .or(MANUFACTURING_TASK.START_TIME.lt(start).and(MANUFACTURING_TASK.END_TIME.gt(end)))
            );
        }
        return db.selectFrom(MANUFACTURING_TASK)
                .where(filter)
                .orderBy(MANUFACTURING_TASK.START_TIME)
                .fetchInto(ManufacturingTask.class);
    }

    @Transactional
    public void updateEmployees(UUID ownerId, UUID taskId, List<UUID> employees) {
        updateAssignments(ownerId, taskId, employees,
                this::updateEmployeeAssignments,
                MANUFACTURINGTASK_EMPLOYEE,
                ManufacturingOperation::manuallyAssignedEmployees);
    }

    @Transactional
    public void updateWorkstations(UUID ownerId, UUID taskId, List<UUID> workstations) {
        updateAssignments(ownerId, taskId, workstations,
                this::updateWorkstationAssignments,
                MANUFACTURINGTASK_WORKSTATION,
                ManufacturingOperation::manuallyAssignedWorkstations);
    }

    private <T> void updateAssignments(
            UUID ownerId,
            UUID taskId,
            List<UUID> assignments,
            BiConsumer<UUID, List<UUID>> assignmentUpdater,
            Table<?> assignmentTable,
            BiFunction<ManufacturingOperation, List<UUID>, ManufacturingOperation> operationUpdater) {

        var task = task(taskId);
        if (task.status() == ManufacturingTask.Status.DONE) {
            throw new BusinessException("can't change assignments for a task that is DONE", "invalid_task_status", "DONE");
        }
        var order = order(task.manufacturingOrderId());
        var manufacturingOperations = manufacturingOperations(order)
                .stream()
                .map(operation -> operation.name().equals(task.details().name())
                        ? operationUpdater.apply(operation, assignments)
                        : operation)
                .toList();

        db.update(MANUFACTURING_ORDER)
                .set(MANUFACTURING_ORDER.MANUFACTURING_OPERATIONS, JSONB.valueOf(Json.write(manufacturingOperations)))
                .where(MANUFACTURING_ORDER.ID.eq(order.id()))
                .execute();

        var ordersThatNeedReplanning = fetchOrdersThatNeedReplanning(ownerId, order);

        clearTodoTasks(ownerId, ordersThatNeedReplanning);

        if (task.status() == ManufacturingTask.Status.IN_PROGRESS || task.status() == ManufacturingTask.Status.STOPPED) {
            clearAssignments(ownerId, taskId, assignmentTable);
            assignmentUpdater.accept(taskId, assignments);
        }

        replanTasks(ownerId, ordersThatNeedReplanning);
    }

    private List<UUID> fetchOrdersThatNeedReplanning(UUID ownerId, ManufacturingOrder order) {
        return db.select(MANUFACTURING_ORDER.ID)
                .from(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.OWNER_ID.eq(ownerId),
                        MANUFACTURING_ORDER.DELETED.isFalse(),
                        MANUFACTURING_ORDER.RANKING.greaterOrEqual(order.ranking()),
                        MANUFACTURING_ORDER.STATUS.in(
                                ManufacturingOrder.Status.SUBMITTED.name(),
                                ManufacturingOrder.Status.MANUFACTURING.name()))
                .orderBy(MANUFACTURING_ORDER.RANKING)
                .fetchInto(UUID.class);
    }

    private void clearTodoTasks(UUID ownerId, List<UUID> ordersThatNeedReplanning) {
        db.deleteFrom(MANUFACTURING_TASK)
                .where(MANUFACTURING_TASK.OWNER_ID.eq(ownerId),
                        MANUFACTURING_TASK.MANUFACTURING_ORDER_ID.in(ordersThatNeedReplanning),
                        MANUFACTURING_TASK.STATUS.eq(ManufacturingTask.Status.TODO.name()))
                .execute();
    }

    private void clearAssignments(UUID ownerId, UUID taskId, Table<?> assignmentTable) {
        db.deleteFrom(assignmentTable)
                .where(assignmentTable.field("MANUFACTURING_TASK_ID", UUID.class).eq(taskId),
                        assignmentTable.field("OWNER_ID", UUID.class).eq(ownerId))
                .execute();
    }

    private void updateEmployeeAssignments(UUID taskId, List<UUID> employees) {
        db.batch(employees.stream()
                .map(employeeId ->
                        db.insertInto(MANUFACTURINGTASK_EMPLOYEE)
                                .set(MANUFACTURINGTASK_EMPLOYEE.OWNER_ID, taskId)
                                .set(MANUFACTURINGTASK_EMPLOYEE.USER_ID, employeeId)
                                .set(MANUFACTURINGTASK_EMPLOYEE.MANUFACTURING_TASK_ID, taskId)
                )
                .toList()
        ).execute();
    }

    private void updateWorkstationAssignments(UUID taskId, List<UUID> workstations) {
        db.batch(workstations.stream()
                .map(workstationId ->
                        db.insertInto(MANUFACTURINGTASK_WORKSTATION)
                                .set(MANUFACTURINGTASK_WORKSTATION.OWNER_ID, taskId)
                                .set(MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_WORKSTATION_ID, workstationId)
                                .set(MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_TASK_ID, taskId)
                )
                .toList()
        ).execute();
    }

    private void replanTasks(UUID ownerId, List<UUID> ordersThatNeedReplanning) {
        ordersThatNeedReplanning.stream()
                .map(this::order)
                .forEach(o -> createTasks(ownerId, o.id(), o.number(), o.quantity(), manufacturingOperations(o)));
    }

    @Transactional
    public void inProgress(UUID ownerId, UUID id) {
        var accountSettings = accountSettings(ownerId);
        var task = task(id);
        var currentTime = new TimeUtil(accountSettings).localDateTimeForAccount();
        db.update(MANUFACTURING_ORDER)
                .set(MANUFACTURING_ORDER.STATUS, ManufacturingOrder.Status.MANUFACTURING.name())
                .set(MANUFACTURING_ORDER.RANKING, UNRANKED_ORDER_VALUE)
                .where(
                        MANUFACTURING_ORDER.ID.eq(select(MANUFACTURING_TASK.MANUFACTURING_ORDER_ID)
                                .from(MANUFACTURING_TASK).where(MANUFACTURING_TASK.ID.eq(id))),
                        MANUFACTURING_ORDER.OWNER_ID.eq(ownerId)
                )
                .execute();
        new TaskShifter(db, transactionTemplate, accountSettings)
                .shift(task.id(), currentTime, null, currentTime.isAfter(task.startTime()), TaskShifter.ShiftType.IN_PROGRESS);
    }

    @Transactional
    public void done(UUID ownerId, UUID id) {
        var accountSettings = accountSettings(ownerId);
        var task = task(id);
        var currentTime = new TimeUtil(accountSettings).localDateTimeForAccount();
        var order = order(task.manufacturingOrderId());
        if (order.ranking() > UNRANKED_ORDER_VALUE) {
            db.update(MANUFACTURING_ORDER)
                    .set(MANUFACTURING_ORDER.STATUS, ManufacturingOrder.Status.MANUFACTURING.name())
                    .set(MANUFACTURING_ORDER.RANKING, UNRANKED_ORDER_VALUE)
                    .where(
                            MANUFACTURING_ORDER.ID.eq(select(MANUFACTURING_TASK.MANUFACTURING_ORDER_ID)
                                    .from(MANUFACTURING_TASK).where(MANUFACTURING_TASK.ID.eq(id))),
                            MANUFACTURING_ORDER.OWNER_ID.eq(ownerId)
                    )
                    .execute();
        }
        //we shift the next tasks in place
        new TaskShifter(db, transactionTemplate, accountSettings)
                .shift(task.id(), null, currentTime, currentTime.isAfter(task.endTime()), TaskShifter.ShiftType.DONE);
        events.publish(
                new TaskCompleted(new TaskCompleted.Data(
                        ownerId, order.id(), order.quantity()
                ))
        );
    }

    @Transactional
    public void stopped(UUID ownerId, UUID id, ManufacturingTask.StatusReason reason, String triggeredBy) {
        db.update(MANUFACTURING_TASK)
                .set(MANUFACTURING_TASK.STATUS, ManufacturingTask.Status.STOPPED.name())
                .set(MANUFACTURING_TASK.STATUS_REASON, reason.name())
                .where(MANUFACTURING_TASK.ID.eq(id), MANUFACTURING_TASK.OWNER_ID.eq(ownerId))
                .execute();
        if (reason != ManufacturingTask.StatusReason.PAUSED) {//trigger a notification
            var task = task(id);
            var order = order(task.manufacturingOrderId());
            var workstation = db.select(MANUFACTURING_WORKSTATION.ID, MANUFACTURING_WORKSTATION.NAME)
                    .from(MANUFACTURING_WORKSTATION)
                    .join(MANUFACTURINGTASK_WORKSTATION).on(MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_WORKSTATION_ID.eq(MANUFACTURING_WORKSTATION.ID))
                    .where(MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_TASK_ID.eq(task.id()))
                    .fetchOptionalInto(Tuple.Tuple2.class);
            var details = new HashMap<String, Object>();
            details.put("orderId", order.id());
            details.put("orderNumber", order.number());
            details.put("statusReason", reason.name());
            workstation.ifPresent(w -> {
                details.put("workstationId", w.a().toString());
                details.put("workstationName", w.b().toString());
            });
            notificationService.create(
                    ownerId,
                    Notification.Type.ISSUE,
                    Notification.Section.MANUFACTURING_TASK,
                    id,
                    triggeredBy,
                    details);
        }
    }

    @Transactional
    public void changeNumberOfAssignees(UUID ownerId, UUID taskId, int numberOfAssignees) {
        var task = task(taskId);
        if (task.status() != ManufacturingTask.Status.TODO) {
            throw new BusinessException("can't change assignees for a task that is not in TODO", "invalid_task_status", "TODO");
        }
        var order = order(task.manufacturingOrderId());
        var manufacturingOperations = manufacturingOperations(order)
                .stream()
                .map(operation -> operation.name().equals(task.details().name())
                        ? operation.numberOfAssignees(numberOfAssignees)
                        : operation)
                .toList();
        db.update(MANUFACTURING_ORDER)
                .set(MANUFACTURING_ORDER.MANUFACTURING_OPERATIONS, JSONB.valueOf(Json.write(manufacturingOperations)))
                .where(MANUFACTURING_ORDER.ID.eq(order.id()))
                .execute();
        var ordersThatNeedReplanning = fetchOrdersThatNeedReplanning(ownerId, order);
        clearTodoTasks(ownerId, ordersThatNeedReplanning);
        replanTasks(ownerId, ordersThatNeedReplanning);
    }

    @Transactional
    public void employeeTimeoffAdded(UUID ownerId, UUID employeeId, LocalDateTime start, LocalDateTime end) {
        var assignedTasks = db.select()
                .from(MANUFACTURING_TASK)
                .join(MANUFACTURINGTASK_EMPLOYEE).on(MANUFACTURINGTASK_EMPLOYEE.MANUFACTURING_TASK_ID.eq(MANUFACTURING_TASK.ID))
                .where(MANUFACTURINGTASK_EMPLOYEE.USER_ID.eq(employeeId),
                        and(MANUFACTURING_TASK.START_TIME.gt(start).and(MANUFACTURING_TASK.START_TIME.lt(end)))
                                .or(MANUFACTURING_TASK.END_TIME.gt(start).and(MANUFACTURING_TASK.END_TIME.lt(end)))
                                .or(MANUFACTURING_TASK.START_TIME.lt(start).and(MANUFACTURING_TASK.END_TIME.gt(end))))
                .fetchInto(ManufacturingTask.class);
        var orders = assignedTasks.stream().map(t -> order(t.manufacturingOrderId())).toList();
        var ordersThatNeedReplanning = db.select(MANUFACTURING_ORDER.ID)
                .from(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.OWNER_ID.eq(ownerId), MANUFACTURING_ORDER.DELETED.isFalse(),
                        MANUFACTURING_ORDER.RANKING.greaterOrEqual(orders.stream().map(ManufacturingOrder::ranking).min(Integer::compareTo).orElse(Integer.MAX_VALUE)))
                .orderBy(MANUFACTURING_ORDER.RANKING)
                .fetchInto(UUID.class);
        db.deleteFrom(MANUFACTURING_TASK)
                .where(MANUFACTURING_TASK.OWNER_ID.eq(ownerId),
                        MANUFACTURING_TASK.ID.in(assignedTasks.stream().map(ManufacturingTask::id).toList()),
                        MANUFACTURING_TASK.STATUS.ne(ManufacturingTask.Status.DONE.name()))
                .execute();
        db.deleteFrom(MANUFACTURING_TASK)
                .where(MANUFACTURING_TASK.OWNER_ID.eq(ownerId),
                        MANUFACTURING_TASK.MANUFACTURING_ORDER_ID.in(ordersThatNeedReplanning),
                        MANUFACTURING_TASK.STATUS.eq(ManufacturingTask.Status.TODO.name()))
                .execute();
        ordersThatNeedReplanning.stream()
                .map(this::order)
                .forEach(o -> createTasks(ownerId, o.id(), o.number(), o.quantity(), manufacturingOperations(o)));
    }


    private ManufacturingOrder order(UUID id) {
        return db.selectFrom(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.ID.eq(id))
                .fetchSingleInto(ManufacturingOrder.class);
    }

    private List<ManufacturingOperation> manufacturingOperations(ManufacturingOrder order) {
        return manufacturingOperationService.enhance(order.manufacturingOperations() != null
                ? order.manufacturingOperations()
                : db.select(MATERIAL_GOOD.DETAILS)
                .from(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(order.productId()))
                .fetchSingleInto(MaterialGood.Details.class)
                .manufacturingOperations());
    }

    private ManufacturingTask createTask(UUID ownerId, UUID orderId,
                                         LocalDateTime start, LocalDateTime end, int durationInMinutes,
                                         String number, String name, boolean parallelizable,
                                         UUID workstationId, List<UUID> employeeIds) {
        var taskId = UUID.randomUUID();
        var now = Instant.now().atZone(ZoneId.systemDefault()).toLocalDateTime();
        var details = new ManufacturingTask.Details(number, name, parallelizable);
        db.insertInto(MANUFACTURING_TASK)
                .set(MANUFACTURING_TASK.ID, taskId)
                //added create/update time as current_timestamp provides the transaction start time
                .set(MANUFACTURING_TASK.CREATE_TIME, now)
                .set(MANUFACTURING_TASK.UPDATE_TIME, now)
                .set(MANUFACTURING_TASK.OWNER_ID, ownerId)
                .set(MANUFACTURING_TASK.MANUFACTURING_ORDER_ID, orderId)
                .set(MANUFACTURING_TASK.STATUS, ManufacturingTask.Status.TODO.name())
                .set(MANUFACTURING_TASK.START_TIME, start)
                .set(MANUFACTURING_TASK.ESTIMATED_START_TIME, start)
                .set(MANUFACTURING_TASK.END_TIME, end)
                .set(MANUFACTURING_TASK.ESTIMATED_END_TIME, end)
                .set(MANUFACTURING_TASK.DURATION_IN_MINUTES, durationInMinutes)
                .set(MANUFACTURING_TASK.DETAILS, JSONB.jsonb(Json.write(details)))
                .set(MANUFACTURING_TASK.RANKING, start.toEpochSecond(ZoneOffset.UTC))
                .execute();
        if (workstationId != null) {
            db.insertInto(MANUFACTURINGTASK_WORKSTATION)
                    .set(MANUFACTURINGTASK_WORKSTATION.OWNER_ID, ownerId)
                    .set(MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_TASK_ID, taskId)
                    .set(MANUFACTURINGTASK_WORKSTATION.MANUFACTURING_WORKSTATION_ID, workstationId)
                    .execute();
        }
        db.batch(employeeIds.stream()
                        .map(employeeId -> db.insertInto(MANUFACTURINGTASK_EMPLOYEE)
                                .set(MANUFACTURINGTASK_EMPLOYEE.OWNER_ID, ownerId)
                                .set(MANUFACTURINGTASK_EMPLOYEE.MANUFACTURING_TASK_ID, taskId)
                                .set(MANUFACTURINGTASK_EMPLOYEE.USER_ID, employeeId))
                        .toList()
                )
                .execute();
        return new ManufacturingTask(taskId, null, null, false,
                ownerId, orderId, ManufacturingTask.Status.TODO, null,
                start, start, end, end, durationInMinutes, details, start.toEpochSecond(ZoneOffset.UTC));
    }

    private Account.Settings accountSettings(UUID accountId) {
        return db.selectFrom(ACCOUNT)
                .where(ACCOUNT.ID.eq(accountId))
                .fetchSingleInto(Account.class)
                .settings();
    }

    static int totalDuration(int duration, BigDecimal quantity, int noOfEmployees) {
        var idealPerEmployee = quantity.multiply(BigDecimal.valueOf(duration)).divide(new BigDecimal(noOfEmployees), RoundingMode.HALF_UP);
        var parallelizationCost = IntStream.range(1, noOfEmployees)
                .mapToObj(i -> idealPerEmployee.multiply(
                        new BigDecimal(noOfEmployees < 3 ? "0.2" : noOfEmployees < 6 ? "0.15" : "0.1")))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return idealPerEmployee.add(parallelizationCost).intValue();
    }

    List<UUID> employees(UUID taskId) {
        return db.select(MANUFACTURINGTASK_EMPLOYEE.USER_ID).from(MANUFACTURINGTASK_EMPLOYEE)
                .where(MANUFACTURINGTASK_EMPLOYEE.MANUFACTURING_TASK_ID.eq(taskId))
                .fetchInto(UUID.class);
    }

    ManufacturingTask task(UUID id) {
        return db.selectFrom(MANUFACTURING_TASK)
                .where(MANUFACTURING_TASK.ID.eq(id))
                .fetchSingleInto(ManufacturingTask.class);
    }

}
