package fabriqon.app.business.manufacturing.tasks;

import fabriqon.app.business.accounts.Account;
import fabriqon.misc.Tuple;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoField;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;
import java.util.function.BinaryOperator;
import java.util.stream.Collectors;

public class TimeUtil {

    private final Account.Settings accountSettings;

    TimeUtil(Account.Settings accountSettings) {
        this.accountSettings = accountSettings;
    }

    LocalDateTime getStartTime(LocalDateTime candidateDateTime) {
        if (!accountSettings.manufacturing().workingDays().contains(candidateDateTime.get(ChronoField.DAY_OF_WEEK))
                || candidateDateTime.toLocalTime().isAfter(accountSettings.manufacturing().workDayEndTime())) {
            return getStartTime(startOfNextWorkDay(candidateDateTime));
        } else if (candidateDateTime.toLocalTime().isBefore(accountSettings.manufacturing().workDayStartTime())) {
            return candidateDateTime.withHour(accountSettings.manufacturing().workDayStartTime().getHour())
                    .withMinute(accountSettings.manufacturing().workDayStartTime().getMinute());
        }
        return candidateDateTime.withSecond(0).withNano(0);
    }

    LocalDateTime getEndTime(LocalDateTime candidateDateTime, int totalDurationInMinutes) {
        if (!accountSettings.manufacturing().workingDays().contains(candidateDateTime.get(ChronoField.DAY_OF_WEEK))
                || candidateDateTime.toLocalTime().isAfter(accountSettings.manufacturing().workDayEndTime())) {
            return getEndTime(startOfNextWorkDay(candidateDateTime), totalDurationInMinutes);
        } else {
            var availableMinutesToday = ChronoUnit.MINUTES.between(candidateDateTime.toLocalTime(), accountSettings.manufacturing().workDayEndTime());
            if (availableMinutesToday > totalDurationInMinutes) {
                return candidateDateTime.plusMinutes(totalDurationInMinutes);
            } else {
                return getEndTime(startOfNextWorkDay(candidateDateTime),
                        totalDurationInMinutes - (int) availableMinutesToday);
            }
        }
    }

    LocalDateTime startOfNextWorkDay(LocalDateTime starting) {
        var candidate = starting.plusDays(1)
                .withHour(accountSettings.manufacturing().workDayStartTime().getHour())
                .withMinute(accountSettings.manufacturing().workDayStartTime().getMinute());
        if (!accountSettings.manufacturing().workingDays().contains(candidate.get(ChronoField.DAY_OF_WEEK))) {
            return startOfNextWorkDay(candidate);
        }
        return candidate;
    }

    LocalDateTime localDateTimeForAccount() {
        return Instant.now().atZone(accountSettings.general().defaultTimeZone().toZoneId()).toLocalDateTime();
    }

    List<Tuple.Tuple3<UUID, LocalDateTime, LocalDateTime>> intervalsByResource(List<UUID> resources,
                                                                               LocalDateTime startTime,
                                                                               List<Tuple.Tuple3<UUID, LocalDateTime, LocalDateTime>> schedule) {
        var intervals = new ArrayList<>(schedule.stream()
                .collect(Collectors.toMap(Tuple.Tuple3::a, Tuple.Tuple3::b,
                        BinaryOperator.minBy(LocalDateTime::compareTo)))
                .entrySet()
                .stream()
                .filter(e -> startTime.isBefore(e.getValue()))
                .map(e -> Tuple.of(e.getKey(), startTime, e.getValue()))
                .toList());
        for (int i = 0; i < schedule.size() - 1; i++) {
            intervals.add(Tuple.of(
                    schedule.get(i).a(),
                    schedule.get(i).c(),
                    i + 1 < schedule.size()
                            ? nextStartForResource(schedule.subList(i + 1, schedule.size()), schedule.get(i).a())
                            : LocalDateTime.MAX));
        }
        intervals.addAll(schedule.stream()
                .collect(Collectors.toMap(Tuple.Tuple3::a, Tuple.Tuple3::c,
                        BinaryOperator.maxBy(LocalDateTime::compareTo)))
                .entrySet()
                .stream()
                .map(e -> Tuple.of(e.getKey(), e.getValue(), LocalDateTime.MAX))
                .toList()
        );
        var currentlyMappedResources = intervals.stream().map(Tuple.Tuple3::a).collect(Collectors.toSet());
        intervals.addAll(
                resources.stream()
                        .filter(resource -> !currentlyMappedResources.contains(resource))
                        .map(resource -> Tuple.of(resource, startTime, LocalDateTime.MAX))
                        .toList()
        );
        intervals.sort(Comparator.comparing(Tuple.Tuple3::b));
        return intervals.stream().filter(i -> i.b().isBefore(i.c())).collect(Collectors.toCollection(ArrayList::new));
    }

    private LocalDateTime nextStartForResource(List<Tuple.Tuple3<UUID, LocalDateTime, LocalDateTime>> schedule,
                                               UUID resourceId) {
        return schedule.stream()
                .filter(s -> s.a().equals(resourceId))
                .findFirst()
                .map(Tuple.Tuple3::b)
                .orElse(LocalDateTime.MAX);
    }
}
