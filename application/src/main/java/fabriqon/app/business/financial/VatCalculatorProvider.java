package fabriqon.app.business.financial;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.common.model.Address;
import fabriqon.app.common.model.Company;
import fabriqon.app.common.model.Money;
import org.jooq.DSLContext;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Locale;
import java.util.UUID;

import static fabriqon.app.business.financial.CountryUtil.getCountry;
import static fabriqon.jooq.classes.Tables.*;

@Component
public class VatCalculatorProvider {

    private final DSLContext db;

    public VatCalculatorProvider(DSLContext db) {
        this.db = db;
    }

    public Calculator with(UUID accountId, UUID buyerId) {
        var buyer = db.selectFrom(COMPANY)
                .where(COMPANY.ID.eq(buyerId))
                .fetchSingleInto(Company.class);
        var buyerAddress = buyerAddress(buyer);
        return with(accountId, buyerAddress);
    }

    public Calculator with(Account seller, Company buyer) {
        return with(seller, buyerAddress(buyer));
    }

    public Calculator with(UUID accountId, Address buyerAddress) {
        var account = db.selectFrom(ACCOUNT)
                .where(ACCOUNT.ID.eq(accountId))
                .fetchSingleInto(Account.class);
        return with(account, buyerAddress);
    }

    /**
     * If either seller or buyer country information is not present then we default to Romania
     */
    public Calculator with(Account account, Address buyerAddress) {
        var sellerCountry = account.information().address() != null && account.information().address().country() != null
                ? getCountry(account.information().address().country()) : getCountry("RO");
        var buyerCountry = buyerAddress != null && buyerAddress.country() != null
                ? getCountry(buyerAddress.country()) : getCountry("RO");
        return new Calculator(db, sellerCountry, buyerCountry);
    }

    private Address buyerAddress(Company buyer) {
        return buyer.details().addresses() != null
                ? buyer.details().addresses().stream().filter(a -> a.types().contains(Address.Type.BILLING)).findFirst().orElse(null)
                : null;
    }

    public static class Calculator {

        private final DSLContext db;
        private final Locale sellerCountry;
        private final Locale buyerCountry;

        public Calculator(DSLContext db, Locale sellerCountry, Locale buyerCountry) {
            this.db = db;
            this.sellerCountry = sellerCountry;
            this.buyerCountry = buyerCountry;
        }

        public long calculate(BigDecimal amount, BigDecimal vatRate) {
            return calculate(amount.longValue(), vatRate);
        }

        public long calculate(long amount, BigDecimal vatRate) {
            return getVatRate(vatRate).multiply(BigDecimal.valueOf(amount)).longValue();
        }

        public long calculate(Money amount, BigDecimal vatRate) {
            return getVatRate(vatRate).multiply(BigDecimal.valueOf(amount.amount())).longValue();
        }

        /**
         * If the buyer and seller are from the same country then we calculate VAT; otherwise we calculate 0 as it will
         * be handled outside of our system
         */
        public BigDecimal getVatRate(BigDecimal vatRate) {
            return isVatApplicable() ? vatRate : BigDecimal.ZERO;
        }

        public boolean isVatApplicable() {
            return buyerCountry.getCountry().equals(sellerCountry.getCountry());
        }

        public BigDecimal itemVatRate(UUID productId) {
            //TODO solve service as well
            if (productId != null) {
                return db.select(MATERIAL_GOOD.DETAILS)
                        .from(MATERIAL_GOOD)
                        .where(MATERIAL_GOOD.ID.eq(productId))
                        .fetchSingleInto(MaterialGood.Details.class)
                        .vatRate();
            } else {//the service case
                return new BigDecimal("0.19");
            }
        }

    }

}
