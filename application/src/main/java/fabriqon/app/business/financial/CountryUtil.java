package fabriqon.app.business.financial;

import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;

import java.util.Arrays;
import java.util.Locale;

public class CountryUtil {

    private static final BiMap<String, String> COUNTRIES = HashBiMap.create();
    private static final BiMap<String, String> INVERSE_COUNTRIES = HashBiMap.create();

    static {
        for (String iso : Locale.getISOCountries()) {
            Locale l = new Locale("", iso);
            COUNTRIES.put(iso, l.getDisplayCountry());
        }
        INVERSE_COUNTRIES.putAll(COUNTRIES.inverse());
    }

    public static Locale getCountry(String country) {
        var c = COUNTRIES.containsKey(country) ? country : INVERSE_COUNTRIES.get(country);
        return c != null ? new Locale("", c) : new Locale("", "RO");
    }

}
