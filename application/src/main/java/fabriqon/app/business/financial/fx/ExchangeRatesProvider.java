package fabriqon.app.business.financial.fx;

import fabriqon.app.common.model.Money;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Currency;
import java.util.List;
import java.util.Map;

public interface ExchangeRatesProvider {

    Map<Currency, BigDecimal> getExchangeRates(List<Currency> currencies, LocalDate date);

    BigDecimal getExchangeRate(Currency fromCurrency, Currency toCurrency);

    BigDecimal getExchangeRate(Currency fromCurrency, Currency toCurrency, LocalDate date);

    Money convert(Money money, Currency toCurrency, LocalDate date);

}
