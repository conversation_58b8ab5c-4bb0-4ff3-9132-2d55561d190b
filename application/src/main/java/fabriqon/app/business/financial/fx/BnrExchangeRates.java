package fabriqon.app.business.financial.fx;

import fabriqon.app.common.model.Money;
import fabriqon.misc.Tuple;
import jakarta.xml.bind.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.codec.xml.Jaxb2XmlDecoder;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static fabriqon.app.business.financial.SystemCurrencies.RON;
import static fabriqon.app.business.financial.SystemCurrencies.SYSTEM_CURRENCIES;

@Component
public class BnrExchangeRates implements ExchangeRatesProvider {

    private final WebClient.Builder clientBuilder;

    private final Map<Tuple.Tuple2<Currency, LocalDate>, BigDecimal> exchangeRateCache = new HashMap<>();

    @Autowired
    public BnrExchangeRates(WebClient.Builder clientBuilder) {
        this.clientBuilder = clientBuilder;
    }

    @Override
    public Map<Currency, BigDecimal> getExchangeRates(List<Currency> currencies, LocalDate date) {
        ObjectUtils.requireNonEmpty(currencies, "Currencies cannot be empty");
        return currencies.stream()
                .collect(Collectors.toMap(
                                currency -> currency,
                                currency -> {
                                    var rate = exchangeRateCache.get(Tuple.of(currency,  date));
                                    if (rate == null) {
                                        getRates(date);
                                        rate = exchangeRateCache.get(Tuple.of(currency, date));
                                        if (rate == null) {//get the last one we have
                                            rate = exchangeRateCache.get(exchangeRateCache.keySet().stream()
                                                    .filter(r -> r.a().equals(currency))
                                                    .max(Comparator.comparing(Tuple.Tuple2::b))
                                                    .orElseThrow()
                                            );
                                        }
                                    }
                                    return rate;
                                }
                        )
                );
    }

    @Override
    public BigDecimal getExchangeRate(Currency fromCurrency, Currency toCurrency) {
        return getExchangeRate(fromCurrency, toCurrency, LocalDate.now());
    }

    @Override
    public BigDecimal getExchangeRate(Currency fromCurrency, Currency toCurrency, LocalDate date) {
        Objects.requireNonNull(fromCurrency, "From currency cannot be null");
        Objects.requireNonNull(toCurrency, "To currency cannot be null");
        if (fromCurrency != RON && toCurrency != RON) {
            throw new RuntimeException("We can only convert to/from RON");
        }
        if (fromCurrency == toCurrency) {
            return BigDecimal.ONE.setScale(4, RoundingMode.HALF_EVEN);
        }
        LocalDate effectiveDate = date;
        // Adjust to yesterday's rate if requested for today before 13:10 Bucharest time
        if (date.isEqual(LocalDate.now()) &&
                LocalDateTime.now().isBefore(LocalDate.now(ZoneId.of("Europe/Bucharest")).atTime(13, 10))) {
            effectiveDate = date.minusDays(1);
        }
        Currency currency = (fromCurrency == RON) ? toCurrency : fromCurrency;
        BigDecimal rate = exchangeRateCache.get(Tuple.of(currency, effectiveDate));
        if (rate == null) {
            getRates(effectiveDate);
            rate = exchangeRateCache.get(Tuple.of(currency, effectiveDate));
            while (rate == null) {
                effectiveDate = effectiveDate.minusDays(1);
                rate = exchangeRateCache.get(Tuple.of(currency, effectiveDate));
            }
        }
        // Return the rate based on the direction of the conversion
        return (fromCurrency == RON) ? rate : BigDecimal.ONE.setScale(4, RoundingMode.HALF_EVEN).divide(rate, RoundingMode.HALF_EVEN);
    }

    @Override
    public Money convert(Money money, Currency toCurrency, LocalDate date) {
        return new Money(money.divide(getExchangeRate(money.currency(), toCurrency, date)).amount(), toCurrency);
    }


    private void getRates(LocalDate date) {
        var bnrRates = clientBuilder
                .exchangeStrategies(ExchangeStrategies.builder()
                        .codecs(configurer -> configurer.defaultCodecs().jaxb2Decoder(new Jaxb2XmlDecoder()))
                        .build())
                .baseUrl("https://www.bnr.ro/files/xml/years/nbrfxrates" + date.getYear() + ".xml")
                .build()
                .get().accept(MediaType.APPLICATION_XML)
                .retrieve().toEntity(BnrExchangeRate.class).block();

        if (bnrRates == null || bnrRates.getBody() == null || !bnrRates.getStatusCode().is2xxSuccessful()) {
            throw new RuntimeException("cannot get BNR exchange rates");
        }
        exchangeRateCache.clear();
        bnrRates.getBody().body.cubes
                .forEach(cube -> cube.rates.stream()
                        .filter(rate -> SYSTEM_CURRENCIES.contains(Currency.getInstance(rate.currency)))
                        .forEach(rate -> exchangeRateCache.put(Tuple.of(Currency.getInstance(rate.currency), LocalDate.parse(cube.date)),
                                rate.multiplier != null
                                        ? rate.value.divide(BigDecimal.valueOf(rate.multiplier), RoundingMode.HALF_EVEN)
                                        : rate.value)));
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @XmlRootElement(name = "DataSet", namespace = "http://www.bnr.ro/xsd")
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class BnrExchangeRate {
        @XmlElement(name = "Header", namespace = "http://www.bnr.ro/xsd")
        private Header header;
        @XmlElement(name = "Body", namespace = "http://www.bnr.ro/xsd")
        private Body body;

        @Setter
        @Getter
        @NoArgsConstructor
        @AllArgsConstructor
        @XmlAccessorType(XmlAccessType.FIELD)
        public static class Header {

            @XmlElement(name = "Publisher", namespace = "http://www.bnr.ro/xsd")
            private String publisher;

            @XmlElement(name = "PublishingDate", namespace = "http://www.bnr.ro/xsd")
            private String publishingDate;

            @XmlElement(name = "MessageType", namespace = "http://www.bnr.ro/xsd")
            private String messageType;
        }

        @Setter
        @Getter
        @NoArgsConstructor
        @AllArgsConstructor
        @XmlAccessorType(XmlAccessType.FIELD)
        public static class Body {

            @XmlElement(name = "Subject", namespace = "http://www.bnr.ro/xsd")
            private String subject;

            @XmlElement(name = "OrigCurrency", namespace = "http://www.bnr.ro/xsd")
            private String origCurrency;

            @XmlElement(name = "Cube", namespace = "http://www.bnr.ro/xsd")
            private List<Cube> cubes;
        }

        @Setter
        @Getter
        @NoArgsConstructor
        @AllArgsConstructor
        @XmlAccessorType(XmlAccessType.FIELD)
        public static class Cube {

            @XmlAttribute(name = "date")
            private String date;

            @XmlElement(name = "Rate", namespace = "http://www.bnr.ro/xsd")
            private List<Rate> rates;
        }

        @Setter
        @Getter
        @NoArgsConstructor
        @AllArgsConstructor
        @XmlAccessorType(XmlAccessType.FIELD)
        public static class Rate {

            @XmlAttribute(name = "currency")
            private String currency;
            @XmlAttribute(name = "multiplier")
            private Integer multiplier;
            @XmlValue
            private BigDecimal value;
        }
    }

}
