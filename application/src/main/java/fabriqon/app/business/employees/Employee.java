package fabriqon.app.business.employees;

import fabriqon.app.common.model.Money;

import java.beans.ConstructorProperties;
import java.time.Instant;
import java.util.UUID;

public record Employee(
        UUID id,
        Instant createTime,
        Instant updateTime,
        boolean deleted,

        UUID ownerId,
        String name,
        Details details
) {

    @ConstructorProperties({"id", "create_time", "update_time", "deleted", "owner_id", "name", "details", "hidden", "text_search"})
    public Employee(UUID id, Instant createTime, Instant updateTime, boolean deleted, UUID ownerId, String name, Details details,
                    //we declare this so jooq can deserialize it but it's not needed in the code
                    boolean hidden, String textSearch) {
        this(id, createTime, updateTime, deleted, ownerId, name, details);
    }

    public record Details(
            String position,
            Money monthlyGrossSalary,
            Money hourlyRate
    ) {
        public Details setPosition(String position) {
            return new Details(position, monthlyGrossSalary, hourlyRate);
        }

        public Details setMonthlyGrossSalary(Money monthlyGrossSalary) {
            return new Details(position, monthlyGrossSalary, hourlyRate);
        }

        public Details setHourlyRate(Money hourlyRate) {
            return new Details(position, monthlyGrossSalary, hourlyRate);
        }
    }
}
