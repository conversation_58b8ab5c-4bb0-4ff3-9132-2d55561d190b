package fabriqon.app.business.employees;

import fabriqon.events.UserTriggeredEvent;

import java.time.LocalDateTime;
import java.util.UUID;

public class EmployeeTimeoffAdded extends UserTriggeredEvent {

    public final Data data;

    public EmployeeTimeoffAdded(UUID accountId, UUID userId, Data data) {
        super(accountId, userId);
        this.data = data;
    }

    public record Data(UUID employeeId, LocalDateTime start, LocalDateTime end) {}

}
