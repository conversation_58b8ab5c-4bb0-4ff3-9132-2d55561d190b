package fabriqon.app.business.employees;

import fabriqon.app.common.model.Money;
import fabriqon.misc.Json;
import fabriqon.misc.Tuple;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.USERS;
import static fabriqon.jooq.classes.Tables.EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE;
import static java.time.Instant.now;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Component
@Transactional
public class EmployeeService {

    private final DSLContext db;

    @Autowired
    public EmployeeService(final DSLContext db) {
        this.db = db;
    }

    public Employee create(UUID ownerId, List<Tuple.Tuple2<UUID, Boolean>> manufacturingOperationTemplates, String name, Employee.Details details) {
        final var id = UUID.randomUUID();
        details = details.setHourlyRate(hourlyRate(details.monthlyGrossSalary()));
        db.insertInto(USERS)
                .set(USERS.ID, id)
                .set(USERS.OWNER_ID, ownerId)
                .set(USERS.NAME, name)
                .set(USERS.DETAILS, JSONB.valueOf(Json.write(details)))
                .execute();

        if (isNotEmpty(manufacturingOperationTemplates)) {
            associateWithManufacturingOperationTemplates(id, ownerId, manufacturingOperationTemplates);
        }

        return new Employee(id, now(), now(), false, ownerId, name, details);
    }

    public Employee update(UUID ownerId, UUID employeeId, List<Tuple.Tuple2<UUID, Boolean>> manufacturingOperationTemplates,
                           String name, Employee.Details details) {
        var updateStatement = db.update(USERS).set(USERS.OWNER_ID, ownerId);
        if (isNotBlank(name)) {
            updateStatement = updateStatement.set(USERS.NAME, name);
        }
        var employeeDetails = load(ownerId, employeeId).details();
        var updateDetails = false;
        if (isNotBlank(details.position())) {
            employeeDetails = employeeDetails.setPosition(details.position());
            updateDetails = true;
        }
        if (details.monthlyGrossSalary() != null) {
            employeeDetails = employeeDetails.setMonthlyGrossSalary(details.monthlyGrossSalary());
            employeeDetails = employeeDetails.setHourlyRate(hourlyRate(details.monthlyGrossSalary()));
            updateDetails = true;
        }
        if (updateDetails) {
            updateStatement = updateStatement.set(USERS.DETAILS, JSONB.jsonb(Json.write(employeeDetails)));
        }
        updateStatement
                .where(USERS.ID.eq(employeeId), USERS.OWNER_ID.eq(ownerId))
                .execute();
        if (manufacturingOperationTemplates != null) {
            associateWithManufacturingOperationTemplates(employeeId, ownerId, manufacturingOperationTemplates);
        }
        return db.selectFrom(USERS)
                .where(USERS.ID.eq(employeeId), USERS.OWNER_ID.eq(ownerId))
                .fetchSingleInto(Employee.class);
    }

    public void delete(UUID ownerId, UUID employeeId) {
        db.update(USERS)
                .set(USERS.DELETED, true)
                .where(USERS.ID.eq(employeeId), USERS.OWNER_ID.eq(ownerId))
                .execute();
    }

    private Employee load(UUID ownerId, UUID employeeId) {
        return db.selectFrom(USERS)
                .where(USERS.ID.eq(employeeId), USERS.OWNER_ID.eq(ownerId))
                .fetchSingleInto(Employee.class);
    }

    private void associateWithManufacturingOperationTemplates(UUID employeeId, UUID ownerId, List<Tuple.Tuple2<UUID, Boolean>> manufacturingOperationTemplates) {
        //first delete all the records for this employee
        db.deleteFrom(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE)
                .where(
                        EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.OWNER_ID.eq(ownerId),
                        EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.USER_ID.eq(employeeId)
                )
                .execute();
        db.batch(manufacturingOperationTemplates.stream()
                .map(operation ->
                        db.insertInto(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE)
                                .set(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.OWNER_ID, ownerId)
                                .set(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.USER_ID, employeeId)
                                .set(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.MANUFACTURING_OPERATION_TEMPLATE_ID, operation.a())
                                .set(EMPLOYEE_MANUFACTURINGOPERATIONTEMPLATE.PREFERENTIAL, operation.b())
                )
                .toList()
        ).execute();
    }

    private Money hourlyRate(Money monthlyGrossSalary) {
        return new Money(monthlyGrossSalary.amount() * 12 / 52 / 40, monthlyGrossSalary.currency());
    }

}
