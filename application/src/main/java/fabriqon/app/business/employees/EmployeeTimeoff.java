package fabriqon.app.business.employees;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.UUID;

public record EmployeeTimeoff (
        UUID id,
        Instant createTime,
        Instant updateTime,
        boolean deleted,

        UUID ownerId,
        UUID employeeId,
        LocalDateTime startTime,
        LocalDateTime endTime,
        Type type,
        UUID servicingOrderId
) {
    public enum Type { UNDEFINED, PTO, BUSINESS }
}
