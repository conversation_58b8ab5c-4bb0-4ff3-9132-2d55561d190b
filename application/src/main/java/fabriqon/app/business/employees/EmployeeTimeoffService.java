package fabriqon.app.business.employees;

import fabriqon.events.Events;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.EMPLOYEE_TIMEOFF;

@Component
@Transactional
public class EmployeeTimeoffService {

    private final DSLContext db;
    private final Events events;

    @Autowired
    public EmployeeTimeoffService(DSLContext db, Events events) {
        this.db = db;
        this.events = events;
    }

    public EmployeeTimeoff add(UUID ownerId, UUID currentUserId, UUID employeeId, LocalDateTime startTime, LocalDateTime endTime, EmployeeTimeoff.Type type, UUID servicingOrderId) {
        var id = UUID.randomUUID();
        db.insertInto(EMPLOYEE_TIMEOFF)
                .set(EMPLOYEE_TIMEOFF.ID, id)
                .set(EMPLOYEE_TIMEOFF.OWNER_ID, ownerId)
                .set(EMPLOYEE_TIMEOFF.USER_ID, employeeId)
                .set(EMPLOYEE_TIMEOFF.START_TIME, startTime)
                .set(EMPLOYEE_TIMEOFF.END_TIME, endTime)
                .set(EMPLOYEE_TIMEOFF.TYPE, type != null ? type.name() : null)
                .set(EMPLOYEE_TIMEOFF.SERVICING_ORDER_ID, servicingOrderId)
                .execute();
        events.publish(new EmployeeTimeoffAdded(ownerId, currentUserId, new EmployeeTimeoffAdded.Data(employeeId, startTime, endTime)));
        return new EmployeeTimeoff(id, Instant.now(), Instant.now(), false, ownerId, employeeId, startTime, endTime, null, servicingOrderId);
    }

    public void delete(UUID ownerId, UUID employeeId, UUID timeoffId) {
        db.deleteFrom(EMPLOYEE_TIMEOFF)
                .where(EMPLOYEE_TIMEOFF.OWNER_ID.eq(ownerId), EMPLOYEE_TIMEOFF.USER_ID.eq(employeeId), EMPLOYEE_TIMEOFF.ID.eq(timeoffId))
                .execute();
    }

}
