package fabriqon.app.business.purchases;

import com.fasterxml.jackson.core.type.TypeReference;
import fabriqon.app.common.model.Money;
import fabriqon.misc.Json;

import java.beans.ConstructorProperties;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Currency;
import java.util.List;
import java.util.UUID;

public record PurchaseWishlistItem(
        UUID id,
        Instant createTime,
        Instant updateTime,
        boolean deleted,

        UUID ownerId,
        UUID materialGoodId,
        BigDecimal quantity,
        Money expectedPrice,
        UUID supplierId,
        List<Source> sources
) {

    @ConstructorProperties({"id", "create_time", "update_time", "deleted", "owner_id", "material_good_id",
            "quantity", "expected_price_amount", "expected_price_currency", "supplier_id", "sources"})
    public PurchaseWishlistItem(UUID id, Instant createTime, Instant updateTime, boolean deleted,
                                UUID ownerId, UUID materialGoodId, BigDecimal quantity,
                                Long expectedPriceAmount, Currency expectedPriceCurrency,
                                UUID supplierId, String sources) {
        this(id, createTime, updateTime, deleted,
                ownerId, materialGoodId, quantity,
                expectedPriceAmount != null ? new Money(expectedPriceAmount, expectedPriceCurrency) : null,
                supplierId,
                Json.readNullSafe(sources, new TypeReference<List<Source>>() {})
        );
    }
}
