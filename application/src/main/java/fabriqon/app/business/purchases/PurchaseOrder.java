package fabriqon.app.business.purchases;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.type.TypeReference;
import fabriqon.app.common.model.Money;
import fabriqon.misc.Json;

import java.beans.ConstructorProperties;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

public record PurchaseOrder(
        UUID id,
        Instant createTime,
        Instant updateTime,
        boolean deleted,

        UUID ownerId,
        UUID supplierId,
        Status status,
        String number,
        LocalDate expectedBy,
        LocalDateTime deliveredAt,
        List<Item> items,
        RenderingDetails renderingDetails,
        UUID managedBy
) {

    @ConstructorProperties({"id", "create_time", "update_time", "deleted", "owner_id", "supplier_id", "status", "number", "expected_by", "delivered_at", "items", "rendering_details", "managed_by"})
    public PurchaseOrder(UUID id, Instant createTime, Instant updateTime, boolean deleted, UUID ownerId, UUID supplierId,
                         PurchaseOrder.Status status, String number, LocalDate expectedBy, LocalDateTime deliveredAt, String items, String renderingDetails, UUID managedBy) {
        this(id, createTime, updateTime, deleted, ownerId, supplierId, status, number, expectedBy, deliveredAt,
                Json.read(items, new TypeReference<List<PurchaseOrder.Item>>() {}), Json.readNullSafe(renderingDetails, RenderingDetails.class),
                managedBy);
    }

    public enum Status { SUBMITTED, SENT_FOR_QUOTE, SENT, DELIVERED, CANCELED}

    @JsonIgnoreProperties(ignoreUnknown = true)
    public record Item(
            UUID originalWishlistId,
            UUID materialGoodId,
            BigDecimal quantity,
            Money price,
            List<Source> sources
    ) {
    }

    public record RenderingDetails(
            String title,
            String language,
            List<HideableColumn> columnsToHide
    ) {
        public enum HideableColumn { PRICE, QUANTITY }
    }
}
