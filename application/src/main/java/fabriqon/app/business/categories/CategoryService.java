package fabriqon.app.business.categories;

import fabriqon.app.business.goods.Category;
import fabriqon.misc.Json;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

import static fabriqon.jooq.classes.Tables.CATEGORY;
import static java.time.Instant.now;

@Component
@Transactional
public class CategoryService {

    private final DSLContext db;

    @Autowired
    public CategoryService(final DSLContext db) {
        this.db = db;
    }

    public Category create(UUID ownerId, String name) {
        final var id = UUID.randomUUID();
        db.insertInto(CATEGORY)
                .set(CATEGORY.ID, id)
                .set(CATEGORY.OWNER_ID, ownerId)
                .set(CATEGORY.DETAILS, JSONB.valueOf(Json.write(new Category.Details(name))))
                .execute();

        return new Category(id, now(), now(), false, ownerId,
                new Category.Details(name));
    }

    public void update(UUID categoryId, UUID ownerId, String name) {
        db.update(CATEGORY)
                .set(CATEGORY.DETAILS, JSONB.valueOf(Json.write(new Category.Details(name))))
                .where(CATEGORY.ID.eq(categoryId), CATEGORY.OWNER_ID.eq(ownerId))
                .execute();
    }

    public void delete(UUID categoryId, UUID ownerId) {
        db.update(CATEGORY)
                .set(CATEGORY.DELETED, true)
                .where(CATEGORY.ID.eq(categoryId), CATEGORY.OWNER_ID.eq(ownerId))
                .execute();
    }

}
