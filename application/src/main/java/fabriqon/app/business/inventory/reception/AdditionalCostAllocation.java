package fabriqon.app.business.inventory.reception;

import fabriqon.app.common.model.Money;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

import static java.math.BigDecimal.valueOf;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

class AdditionalCostAllocation {

    private final List<AdditionalCost> additionalCosts;
    private final Map<AdditionalCost.AllocationStrategy, AdditionalCostAllocationStrategy> allocationStrategies = Map.of(
            AdditionalCost.AllocationStrategy.BY_VALUE, new AdditionalCostAllocationStrategy.ByValue(),
            AdditionalCost.AllocationStrategy.BY_QUANTITY, new AdditionalCostAllocationStrategy.ByQuantity(),
            AdditionalCost.AllocationStrategy.CUSTOM, new AdditionalCostAllocationStrategy.Custom()
    );

    AdditionalCostAllocation(List<AdditionalCost> additionalCosts) {
        this.additionalCosts = isNotEmpty(additionalCosts) ? additionalCosts : List.of();
    }

    interface AdditionalCostAllocationStrategy {
        Money allocate(List<ReceivedGood> receivedGoods, ReceivedGood receivedGood, Money multiply);

        class ByValue implements AdditionalCostAllocationStrategy {
            @Override
            public Money allocate(List<ReceivedGood> receivedGoods, ReceivedGood receivedGood, Money cost) {
                var totalCost = receivedGoods.stream()
                        .map(this::cost)
                        .reduce(new Money(0, Money.DEFAULT_CURRENCY), Money::add);
                var percentOfTotal = valueOf(cost(receivedGood).amount())
                        .divide(valueOf(totalCost.amount()), 4, RoundingMode.HALF_UP);
                return cost.multiply(percentOfTotal);
            }

            Money cost(ReceivedGood good) {
                return good.price().multiply(good.receivedQuantity());
            }
        }

        class ByQuantity implements AdditionalCostAllocationStrategy {
            @Override
            public Money allocate(List<ReceivedGood> receivedGoods, ReceivedGood receivedGood, Money cost) {
                var totalQuantity = receivedGoods.stream()
                        .map(ReceivedGood::receivedQuantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                var percentOfTotal = receivedGood.receivedQuantity()
                        .divide(totalQuantity, 4, RoundingMode.HALF_UP);
                return cost.multiply(percentOfTotal);
            }
        }

        class Custom implements AdditionalCostAllocationStrategy {
            @Override
            public Money allocate(List<ReceivedGood> receivedGoods, ReceivedGood receivedGood, Money multiply) {
                return receivedGood.additionalCostCustomValue();
            }
        }
    }

    Money allocateFor(List<ReceivedGood> receivedGoods, ReceivedGood receivedGood) {
        return additionalCosts.stream()
                .map(cost -> allocationStrategies.get(cost.allocationStrategy())
                        .allocate(receivedGoods, receivedGood, cost.price().multiply(cost.quantity())))
                .reduce(Money::add).orElse(new Money(0, Money.DEFAULT_CURRENCY));
    }

}
