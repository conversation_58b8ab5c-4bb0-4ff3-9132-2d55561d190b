package fabriqon.app.business.inventory.accounting;

import fabriqon.app.business.accounts.Account;
import org.jooq.DSLContext;
import org.springframework.stereotype.Component;

@Component
public class AccountingStrategyProvider {

    private final DSLContext db;

    public AccountingStrategyProvider(DSLContext db) {
        this.db = db;
    }

    public AccountingStrategy strategy(Account.Settings.General.InventoryAccountingSettings.Method method) {
        switch (method) {
            case WEIGHTED_AVERAGE:
                return new WeightedAverageAccountingStrategy(db);
            case FIFO:
            default:
                return new FifoAccountingStrategy(db);
        }
    }
}
