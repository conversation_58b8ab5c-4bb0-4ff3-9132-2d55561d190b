package fabriqon.app.business.inventory.reception;

import fabriqon.ObjectStorage;
import fabriqon.app.business.inventory.InventoryEntry;
import fabriqon.app.business.inventory.InventoryService;
import fabriqon.app.business.sequence.Sequences;
import fabriqon.misc.Json;
import fabriqon.misc.Tuple;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Currency;
import java.util.List;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.RECEPTIONRECEIPT_FILE;
import static fabriqon.jooq.classes.Tables.RECEPTION_RECEIPT;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Component
public class ReceptionService {

    private final DSLContext db;
    private final Sequences sequences;
    private final InventoryService inventoryService;
    private final ObjectStorage objectStorage;

    @Autowired
    public ReceptionService(DSLContext db, Sequences sequences, InventoryService inventoryService, ObjectStorage objectStorage) {
        this.db = db;
        this.sequences = sequences;
        this.inventoryService = inventoryService;
        this.objectStorage = objectStorage;
    }

    public ReceptionReceipt receive(UUID ownerId,
                                    UUID supplierId,
                                    UUID purchaseOrderId,
                                    String documentNumber,
                                    LocalDate receptionDate,
                                    SupportingDocument supportingDocument,
                                    Currency currency,
                                    String notes,
                                    String receivedBy,
                                    String transportedBy,
                                    String transportedWith,
                                    List<ReceivedGood> goods,
                                    List<AdditionalCost> additionalCosts,
                                    List<Tuple.Tuple2<String, ByteArrayInputStream>> files
    ) {
        var id = UUID.randomUUID();
        var number = isNotBlank(documentNumber) ? documentNumber : sequences.nextSequenceForReceptionReceipt(ownerId);
        var details = new ReceptionReceipt.Details(currency, notes, receivedBy, transportedBy, transportedWith, additionalCosts);
        var additionalCostAllocator = new AdditionalCostAllocation(additionalCosts);
        var updatedGoods = goods.stream()
                .map(good -> good.setCalculatedAdditionalCost(additionalCostAllocator.allocateFor(goods, good)))
                .toList();
        db.insertInto(RECEPTION_RECEIPT)
                .set(RECEPTION_RECEIPT.ID, id)
                .set(RECEPTION_RECEIPT.OWNER_ID, ownerId)
                .set(RECEPTION_RECEIPT.SUPPLIER_ID, supplierId)
                .set(RECEPTION_RECEIPT.PURCHASE_ORDER_ID, purchaseOrderId)
                .set(RECEPTION_RECEIPT.NUMBER, number)
                .set(RECEPTION_RECEIPT.RECEPTION_DATE, receptionDate.atStartOfDay())
                .set(RECEPTION_RECEIPT.SUPPORTING_DOCUMENT, JSONB.jsonb(Json.write(supportingDocument)))
                .set(RECEPTION_RECEIPT.DETAILS, JSONB.jsonb(Json.write(details)))
                .set(RECEPTION_RECEIPT.GOODS, JSONB.jsonb(Json.write(updatedGoods)))
                .execute();

        inventoryService.fill(
                updatedGoods.stream()
                        .map(good -> new InventoryEntry(null, null, null, false,
                                ownerId,
                                good.materialGoodId(),
                                null,
                                supplierId,
                                null,
                                null,
                                null,
                                null,
                                id,
                                good.inventoryUnitId(),
                                receptionDate.atStartOfDay(),
                                null,
                                good.price().amount()
                                        + (good.calculatedAdditionalCost() != null ? good.calculatedAdditionalCost().divide(good.receivedQuantity()).amount() : 0),
                                good.price().currency(),
                                null, null,
                                good.receivedQuantity()
                        ))
                        .collect(toList())
        );

        files.forEach(f -> attach(ownerId, id, f.a(), f.b().readAllBytes()));

        return new ReceptionReceipt(id, Instant.now(), Instant.now(), false, ownerId,
                supplierId, purchaseOrderId, number, receptionDate, supportingDocument, details, updatedGoods);
    }

    public void attach(UUID ownerId,  UUID receiptId, String fileName, byte[] fileContent) {
        var fileId = objectStorage.store(ownerId, fileName, fileContent);
        db.insertInto(RECEPTIONRECEIPT_FILE)
                .set(RECEPTIONRECEIPT_FILE.OWNER_ID, ownerId)
                .set(RECEPTIONRECEIPT_FILE.RECEPTION_RECEIPT_ID, receiptId)
                .set(RECEPTIONRECEIPT_FILE.FILE_ID, fileId)
                .execute();
    }

}
