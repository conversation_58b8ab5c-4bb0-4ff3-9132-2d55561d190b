package fabriqon.app.business.inventory;

import fabriqon.events.Event;

import java.util.UUID;

public class InventoryChangedEvent extends Event {

    public final Data data;

    public InventoryChangedEvent(Data data) {
        this.data = data;
    }

    public record Data(
            UUID ownerId,
            UUID inventoryEntry,
            ManufacturingContext manufacturingContext
    ) {
    }

}
