package fabriqon.app.business.inventory.reception;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import fabriqon.app.common.model.Money;

import java.math.BigDecimal;
import java.util.UUID;

@JsonIgnoreProperties(ignoreUnknown = true)
public record ReceivedGood(
        UUID materialGoodId,
        BigDecimal orderedQuantity,
        BigDecimal receivedQuantity,
        Money price,
        UUID inventoryUnitId,
        Money additionalCostCustomValue,
        Money calculatedAdditionalCost
) {
    public ReceivedGood setCalculatedAdditionalCost(Money calculatedAdditionalCost) {
        return new ReceivedGood(materialGoodId, orderedQuantity, receivedQuantity, price, inventoryUnitId,
                additionalCostCustomValue, calculatedAdditionalCost);
    }
}
