package fabriqon.app.business.inventory.accounting;

import fabriqon.app.business.inventory.InventoryEntry;
import fabriqon.app.common.model.Money;
import org.jooq.DSLContext;

import java.math.BigDecimal;
import java.util.Deque;
import java.util.LinkedList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static fabriqon.jooq.classes.Tables.INVENTORY;
import static java.math.BigDecimal.ZERO;

public abstract class BaseAccountingStrategy implements AccountingStrategy {

    private final DSLContext db;

    public BaseAccountingStrategy(DSLContext db) {
        this.db = db;
    }

    @Override
    public Money calculateAverageCostPerItem(UUID ownerId, UUID materialGoodId, UUID inventoryUnitId) {
        var stock = calculateStock(ownerId, materialGoodId, inventoryUnitId);
        var stockValue = calculateStockValue(ownerId, materialGoodId, inventoryUnitId);
        if (stock.longValue() == 0) {
            return stockValue;
        }
        return stockValue.divide(stock);
    }

    @Override
    public Money calculateStockValue(UUID ownerId, UUID materialGoodId, UUID inventoryUnitId) {
        return Money.sum(calculateAvailable(ownerId, materialGoodId, inventoryUnitId)
                .stream().map(item -> item.receptionPrice().multiply(item.quantity())).toList());
    }

    public BigDecimal calculateStock(UUID ownerId, UUID materialGoodId, UUID inventoryUnitId) {
        return calculateStock(calculateAvailable(ownerId, materialGoodId, inventoryUnitId));
    }

    protected List<InventoryEntry> inventoryEntries(UUID ownerId, UUID materialGoodId, UUID inventoryUnitId) {
        var filter = INVENTORY.OWNER_ID.eq(ownerId).and(INVENTORY.MATERIAL_GOOD_ID.eq(materialGoodId));
        if (inventoryUnitId != null) {
            filter = filter.and(INVENTORY.UNIT_ID.eq(inventoryUnitId));
        }
        return db.selectFrom(INVENTORY)
                .where(filter)
                .orderBy(INVENTORY.CREATE_TIME.asc())
                .fetchInto(InventoryEntry.class);
    }

    //calculate the stock; return the entries that can be used to remove goods from
    protected Deque<InventoryEntry> calculateStock(Deque<InventoryEntry> fills, Deque<InventoryEntry> removals, BigDecimal toRemove) {
        if (!fills.isEmpty()) {
            var fill = fills.remove();
            var remainingToRemove = fill.quantity().add(toRemove);
            if (remainingToRemove.compareTo(ZERO) <= 0) {
                //we need to get the next fill item and retain what needs to be subtracted
                return calculateStock(fills, removals, remainingToRemove);
            } else {
                fills.addFirst(fill.setQuantity(remainingToRemove));
                if (!removals.isEmpty()) {
                    toRemove = removals.remove().quantity();
                    return calculateStock(fills, removals, toRemove);
                } else {
                    return fills;
                }
            }
        } else {
            return new LinkedList<>();
        }
    }

    protected BigDecimal calculateStock(List<InventoryEntry> available) {
        return available.stream().map(InventoryEntry::quantity).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    protected long calculateValue(List<InventoryEntry> entries) {
        return entries.stream()
                .map(e -> BigDecimal.valueOf(e.receptionPrice().amount()).multiply(e.quantity()).longValue())
                .mapToLong(Long::longValue).sum();
    }

    /**
     * returns entries that represent the available stock
     */
    protected List<InventoryEntry> calculateAvailable(UUID ownerId, UUID materialGoodId, UUID inventoryUnitId) {
        var entries = inventoryEntries(ownerId, materialGoodId, inventoryUnitId);
        if (entries.isEmpty()) {
            return List.of();
        }
        var fills = entries.stream()
                .filter(entry -> entry.quantity().compareTo(ZERO) > 0)
                .collect(Collectors.toCollection(LinkedList::new));
        var removals = entries.stream()
                .filter(entry -> entry.quantity().compareTo(ZERO) < 0)
                .collect(Collectors.toCollection(LinkedList::new));
        return new LinkedList<>(calculateStock(fills, removals, ZERO));
    }

    /**
     * returns entries that entered the inventory (with a positive qty)
     */
    private List<InventoryEntry> calculateEntries(UUID ownerId, UUID materialGoodId, UUID inventoryUnitId) {
        var entries = inventoryEntries(ownerId, materialGoodId, inventoryUnitId);
        if (entries.isEmpty()) {
            return List.of();
        }
        return entries.stream()
                .filter(entry -> entry.quantity().compareTo(ZERO) > 0)
                .collect(Collectors.toCollection(LinkedList::new));
    }

}
