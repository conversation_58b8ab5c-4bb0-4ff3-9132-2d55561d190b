package fabriqon.app.business.inventory;

import fabriqon.app.common.model.Money;

import java.beans.ConstructorProperties;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Currency;
import java.util.UUID;

public record InventoryEntry(
        UUID id,
        Instant createTime,
        Instant updateTime,
        boolean deleted,

        UUID ownerId,
        UUID materialGoodId,
        UUID locationId,
        UUID supplierId,
        UUID inventoryAdjustmentOrderId,
        UUID salesOrderId,
        UUID manufacturingOrderId,
        UUID servicingOrderId,
        UUID receptionReceiptId,
        UUID unitId,
        LocalDateTime purchaseDate,
        LocalDateTime expiryDate,
        Money receptionPrice,
        Money exitPrice,
        BigDecimal quantity
) {

    //FIXME we don't have another way for mapping from JOOQ to a java.lang.Record so we introduce the annotation and
    // define the constructor + we don't yet know how to solve complex types like Money
    @ConstructorProperties({"id", "create_time", "update_time", "deleted", "owner_id", "material_good_id", "location_id",
            "vendor_id", "inventory_adjustment_order_id", "sales_order_id", "manufacturing_order_id", "servicing_order_id", "reception_receipt_id",
            "unit_id","purchase_date", "expiry_date", "reception_price_amount", "reception_price_currency",
            "exit_price_amount", "exit_price_currency", "quantity"})
    public InventoryEntry(UUID id,
                          Instant createTime,
                          Instant updateTime,
                          boolean deleted,

                          UUID ownerId,
                          UUID materialGoodId,
                          UUID locationId,
                          UUID supplierId,
                          UUID inventoryAdjustmentOrderId,
                          UUID salesOrderId,
                          UUID manufacturingOrderId,
                          UUID servicingOrderId,
                          UUID receptionReceiptId,
                          UUID unitId,
                          LocalDateTime purchaseDate,
                          LocalDateTime expiryDate,
                          Long receptionPriceAmount,
                          Currency receptionPriceCurrency,
                          Long exitPriceAmount,
                          Currency exitPriceCurrency,
                          BigDecimal quantity) {
        this(id, createTime, updateTime, deleted, ownerId, materialGoodId, locationId, supplierId,
                inventoryAdjustmentOrderId, salesOrderId, manufacturingOrderId, servicingOrderId, receptionReceiptId, unitId, purchaseDate, expiryDate,
                receptionPriceAmount != null ? new Money(receptionPriceAmount, receptionPriceCurrency) : null,
                exitPriceAmount != null ? new Money(exitPriceAmount, exitPriceCurrency) : null,
                quantity);
    }

    public InventoryEntry setId(final UUID id) {
        return new InventoryEntry(
                id, createTime, updateTime, deleted,
                ownerId,
                materialGoodId,
                locationId,
                supplierId,
                inventoryAdjustmentOrderId,
                salesOrderId,
                manufacturingOrderId,
                servicingOrderId,
                receptionReceiptId,
                unitId,
                purchaseDate,
                expiryDate,
                receptionPrice,
                exitPrice,
                quantity
        );
    }

    public InventoryEntry setQuantity(final BigDecimal quantity) {
        return new InventoryEntry(
                id, createTime, updateTime, deleted,
                ownerId,
                materialGoodId,
                locationId,
                supplierId,
                inventoryAdjustmentOrderId,
                salesOrderId,
                manufacturingOrderId,
                servicingOrderId,
                receptionReceiptId,
                unitId,
                purchaseDate,
                expiryDate,
                receptionPrice,
                exitPrice,
                quantity
        );
    }

    public InventoryEntry setSalesOrderId(UUID salesOrderId) {
        return new InventoryEntry(
                id, createTime, updateTime, deleted,
                ownerId,
                materialGoodId,
                locationId,
                supplierId,
                inventoryAdjustmentOrderId,
                salesOrderId,
                manufacturingOrderId,
                servicingOrderId,
                receptionReceiptId,
                unitId,
                purchaseDate,
                expiryDate,
                receptionPrice,
                exitPrice,
                quantity
        );
    }

    public InventoryEntry setManufacturingOrderId(UUID manufacturingOrderId) {
        return new InventoryEntry(
                id, createTime, updateTime, deleted,
                ownerId,
                materialGoodId,
                locationId,
                supplierId,
                inventoryAdjustmentOrderId,
                salesOrderId,
                manufacturingOrderId,
                servicingOrderId,
                receptionReceiptId,
                unitId,
                purchaseDate,
                expiryDate,
                receptionPrice,
                exitPrice,
                quantity
        );
    }

    public InventoryEntry setServicingOrderId(UUID servicingOrderId) {
        return new InventoryEntry(
                id, createTime, updateTime, deleted,
                ownerId,
                materialGoodId,
                locationId,
                supplierId,
                inventoryAdjustmentOrderId,
                salesOrderId,
                manufacturingOrderId,
                servicingOrderId,
                receptionReceiptId,
                unitId,
                purchaseDate,
                expiryDate,
                receptionPrice,
                exitPrice,
                quantity
        );
    }

    public InventoryEntry setInventoryAdjustmentOrderId(UUID inventoryAdjustmentOrderId) {
        return new InventoryEntry(
                id, createTime, updateTime, deleted,
                ownerId,
                materialGoodId,
                locationId,
                supplierId,
                inventoryAdjustmentOrderId,
                salesOrderId,
                manufacturingOrderId,
                servicingOrderId,
                receptionReceiptId,
                unitId,
                purchaseDate,
                expiryDate,
                receptionPrice,
                exitPrice,
                quantity
        );
    }

    public InventoryEntry setExitPrice(Money exitPrice) {
        return new InventoryEntry(
                id, createTime, updateTime, deleted,
                ownerId,
                materialGoodId,
                locationId,
                supplierId,
                inventoryAdjustmentOrderId,
                salesOrderId,
                manufacturingOrderId,
                servicingOrderId,
                receptionReceiptId,
                unitId,
                purchaseDate,
                expiryDate,
                receptionPrice,
                exitPrice,
                quantity
        );
    }
}
