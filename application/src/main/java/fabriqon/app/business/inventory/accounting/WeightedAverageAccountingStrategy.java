package fabriqon.app.business.inventory.accounting;

import fabriqon.app.business.exceptions.BusinessException;
import fabriqon.app.business.inventory.InventoryEntry;
import fabriqon.app.common.model.Money;
import org.jooq.DSLContext;

import java.math.BigDecimal;
import java.util.*;

//TODO the calculations need to be verified again for this accounting strategy; average cost is probably not calculated correctly
public class WeightedAverageAccountingStrategy extends BaseAccountingStrategy {

    public WeightedAverageAccountingStrategy(DSLContext db) {
        super(db);
    }

    @Override
    public List<InventoryEntry> remove(UUID ownerId, UUID materialGoodId, BigDecimal quantity, UUID inventoryUnitId) {
        Objects.requireNonNull(inventoryUnitId, "inventory unit cannot be null");
        var currentlyAvailable = calculateStock(ownerId, materialGoodId, inventoryUnitId);
        if (currentlyAvailable.compareTo(quantity) < 0) {
            throw new RuntimeException("not enough stock");
        }
        return List.of(new InventoryEntry(null, null, null, false,
                ownerId, materialGoodId, inventoryUnitId,
                null,
                null,
                null,
                null,
                null,
                null,
                inventoryUnitId,
                null,
                null,
                calculateAverageCostPerItem(ownerId, materialGoodId, inventoryUnitId),
                null,
                quantity.multiply(BigDecimal.valueOf(-1))));
    }

    @Override
    public Money calculateCostForQuantity(UUID ownerId, UUID materialGoodId, UUID inventoryUnitId, BigDecimal quantity) {
        return calculateAverageCostPerItem(ownerId, materialGoodId, inventoryUnitId).multiply(quantity);
    }

}
