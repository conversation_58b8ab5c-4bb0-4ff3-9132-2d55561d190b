package fabriqon.app.business.inventory;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import fabriqon.app.common.model.Money;
import fabriqon.misc.Json;
import jakarta.validation.constraints.NotNull;

import java.beans.ConstructorProperties;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

public record InventoryAdjustmentOrder(
        UUID id,
        Instant createTime,
        Instant updateTime,
        boolean deleted,

        UUID ownerId,
        String number,
        Type type,
        Details details
) {

    @ConstructorProperties({"id", "create_time", "update_time", "deleted", "owner_id", "number", "type", "details"})
    public InventoryAdjustmentOrder(UUID id,
                                    Instant createTime,
                                    Instant updateTime,
                                    boolean deleted,

                                    UUID ownerId,
                                    String number,
                                    Type type,
                                    String details
    ) {
        this(id, createTime, updateTime, deleted, ownerId, number, type, Json.read(details, Details.class));
    }

    public InventoryAdjustmentOrder setId(UUID id) {
        return new InventoryAdjustmentOrder(
                id,
                this.createTime,
                this.updateTime,
                this.deleted,
                this.ownerId,
                this.number,
                this.type,
                this.details
        );
    }

    public InventoryAdjustmentOrder setNumber(String number) {
        return new InventoryAdjustmentOrder(
                this.id,
                this.createTime,
                this.updateTime,
                this.deleted,
                this.ownerId,
                number,
                this.type,
                this.details
        );
    }

    public record Details(
            String reason,
            //the next two are used with Type.UNIT_CHANGE where we move items from one inventory unit to another
            String deliveredBy,
            String receivedBy,
            List<InventoryEntry> inventoryEntries
    ) {
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public record InventoryEntry(
            @NotNull
            UUID materialGoodId,
            UUID locationId, UUID supplierId,
            //used with Type.UNIT_CHANGE where we move items from one inventory unit to another
            UUID fromUnit,
            @NotNull
            UUID unitId,
            LocalDateTime purchaseDate, LocalDateTime expiryDate,
            @NotNull
            Money price,
            @NotNull
            BigDecimal quantity,
            BigDecimal originalQuantity, BigDecimal originalFromQuantity
    ) {
        public InventoryEntry setOriginalQuantity(BigDecimal originalQuantity) {
            return new InventoryEntry(materialGoodId, locationId, supplierId, fromUnit, unitId, purchaseDate, expiryDate, price, quantity, originalQuantity, originalFromQuantity);
        }

        public InventoryEntry setOriginalFromQuantity(BigDecimal originalFromQuantity) {
            return new InventoryEntry(materialGoodId, locationId, supplierId, fromUnit, unitId, purchaseDate, expiryDate, price, quantity, originalQuantity, originalFromQuantity);
        }
    }

    public enum Type { ADJUSTMENT, TYPE_CHANGE }
}
