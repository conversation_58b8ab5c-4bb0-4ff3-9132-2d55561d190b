package fabriqon.app.business.inventory;

import fabriqon.events.EventConsumer;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.ZoneId;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.INVENTORY;

@Component
public class InventoryChangedConsumer extends EventConsumer<InventoryChangedEvent> {

    final DSLContext db;
    final InventoryCurrentStockUpdater updater;

    @Autowired
    public InventoryChangedConsumer(DSLContext db, InventoryCurrentStockUpdater updater) {
        this.db = db;
        this.updater = updater;
    }

    @Override
    public void process(InventoryChangedEvent event) {
        var entry = inventoryEntry(event.data.ownerId(), event.data.inventoryEntry());
        updater.updateCurrentStock(entry.ownerId(), entry.materialGoodId(), entry.unitId(), entry.updateTime().atZone(ZoneId.systemDefault()).toLocalDateTime());
    }

    private InventoryEntry inventoryEntry(UUID ownerId, UUID entryId) {
        return db.selectFrom(INVENTORY).where(INVENTORY.OWNER_ID.eq(ownerId), INVENTORY.ID.eq(entryId)).fetchSingleInto(InventoryEntry.class);
    }
}
