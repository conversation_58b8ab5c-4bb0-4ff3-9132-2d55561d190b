package fabriqon.app.business.inventory;

import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.Record4;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static fabriqon.jooq.classes.Tables.INVENTORY;
import static fabriqon.jooq.classes.Tables.INVENTORY_CURRENT_STOCK;
import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.max;

@Component
@Slf4j
public class InventoryCurrentStockUpdater {

    final DSLContext db;
    final InventoryService inventoryService;

    @Autowired
    public InventoryCurrentStockUpdater(DSLContext db, InventoryService inventoryService) {
        this.db = db;
        this.inventoryService = inventoryService;
    }

    @Scheduled(fixedDelay = 1, timeUnit = TimeUnit.MINUTES)
    public void updateEntries() {

        var latestUpdateTime = db.select(max(INVENTORY_CURRENT_STOCK.UPDATE_TIME))
                .from(INVENTORY_CURRENT_STOCK)
                .fetchOptionalInto(LocalDateTime.class)
                .orElse(LocalDateTime.of(2020, 1, 1, 0, 0));

        db.select(INVENTORY.MATERIAL_GOOD_ID, INVENTORY.OWNER_ID, INVENTORY.UNIT_ID, max(INVENTORY.CREATE_TIME).as("max_create_time"))
                .from(INVENTORY)
                .where(INVENTORY.CREATE_TIME.gt(latestUpdateTime.atZone(ZoneId.systemDefault()).toLocalDateTime()))
                .groupBy(INVENTORY.MATERIAL_GOOD_ID, INVENTORY.UNIT_ID, INVENTORY.OWNER_ID)
                .orderBy(field("max_create_time"))
                .fetch().forEach(entry -> updateCurrentStock(entry.value2(), entry.value1(), entry.value3(), entry.value4()));
    }

    void updateCurrentStock(UUID ownerId, UUID goodId, UUID unitId, LocalDateTime time) {
        var cost = inventoryService.getAverageCost(ownerId, goodId);
        log.info("updating inventory current stock for good [{}] and unit [{}]", goodId, unitId);
        db.insertInto(INVENTORY_CURRENT_STOCK)
                .set(INVENTORY_CURRENT_STOCK.UPDATE_TIME, time)
                .set(INVENTORY_CURRENT_STOCK.MATERIAL_GOOD_ID, goodId)
                .set(INVENTORY_CURRENT_STOCK.UNIT_ID, unitId)
                .set(INVENTORY_CURRENT_STOCK.QUANTITY, inventoryService.getCurrentStock(ownerId, goodId, unitId))
                .set(INVENTORY_CURRENT_STOCK.CURRENCY, cost.currency().toString())
                .set(INVENTORY_CURRENT_STOCK.COST, cost.amount())
                .set(INVENTORY_CURRENT_STOCK.TOTAL_VALUE, inventoryService.getCurrentStockValue(ownerId, goodId, unitId).amount())
                .onConflict(INVENTORY_CURRENT_STOCK.MATERIAL_GOOD_ID, INVENTORY_CURRENT_STOCK.UNIT_ID)
                .doUpdate()
                .set(INVENTORY_CURRENT_STOCK.QUANTITY, inventoryService.getCurrentStock(ownerId, goodId, unitId))
                .set(INVENTORY_CURRENT_STOCK.COST, cost.amount())
                .set(INVENTORY_CURRENT_STOCK.TOTAL_VALUE, inventoryService.getCurrentStockValue(ownerId, goodId, unitId).amount())
                .set(INVENTORY_CURRENT_STOCK.UPDATE_TIME, time)
                .execute();
    }

}
