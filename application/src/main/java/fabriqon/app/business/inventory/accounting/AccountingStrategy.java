package fabriqon.app.business.inventory.accounting;

import fabriqon.app.business.inventory.InventoryEntry;
import fabriqon.app.common.model.Money;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

public interface AccountingStrategy {

    /**
     * returns the entries that represent removal of the specified quantity for the material good
     */
    List<InventoryEntry> remove(UUID ownerId, UUID materialGoodId, BigDecimal quantity, UUID inventoryUnitId);
    BigDecimal calculateStock(UUID ownerId, UUID materialGoodId, UUID inventoryUnitId);

    Money calculateStockValue(UUID ownerId, UUID materialGoodId, UUID inventoryUnitId);
    Money calculateAverageCostPerItem(UUID ownerId, UUID materialGoodId, UUID inventoryUnitId);
    Money calculateCostForQuantity(UUID ownerId, UUID materialGoodId, UUID inventoryUnitId, BigDecimal quantity);

}
