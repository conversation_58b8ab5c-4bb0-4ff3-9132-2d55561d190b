package fabriqon.app.business.inventory;

import fabriqon.app.business.exceptions.BusinessException;
import fabriqon.app.business.sequence.Sequences;
import fabriqon.misc.Json;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.INVENTORY_ADJUSTMENT_ORDER;

@Component
@Transactional
public class InventoryAdjustmentService {

    private final PlatformTransactionManager platformTransactionManager;
    private final DSLContext db;
    private final Sequences sequences;
    private final InventoryService inventoryService;


    @Autowired
    public InventoryAdjustmentService(PlatformTransactionManager platformTransactionManager,
                                      DSLContext db,
                                      Sequences sequences,
                                      InventoryService inventoryService) {
        this.platformTransactionManager = platformTransactionManager;
        this.db = db;
        this.sequences = sequences;
        this.inventoryService = inventoryService;
    }

    public InventoryAdjustmentOrder adjustInventory(UUID ownerId, String reason, List<InventoryAdjustmentOrder.InventoryEntry> inventoryEntries) {
        var id = UUID.randomUUID();
        var number = sequences.nextSequenceForInventoryAdjustmentOrder(ownerId);
        var details = new InventoryAdjustmentOrder.Details(reason, null, null,
                inventoryEntries.stream()
                        .map(e ->e.setOriginalQuantity(inventoryService.getCurrentStock(ownerId, e.materialGoodId(), e.unitId())))
                        .toList());

        persist(id, ownerId, number, InventoryAdjustmentOrder.Type.ADJUSTMENT, details);
        registerEntries(ownerId, id, inventoryEntries);

        return new InventoryAdjustmentOrder(id, Instant.now(), Instant.now(), false, ownerId, number,
                InventoryAdjustmentOrder.Type.ADJUSTMENT, details);
    }

    public InventoryAdjustmentOrder moveToUnit(UUID ownerId, String reason,
                                               String deliveredBy, String receivedBy,
                                               List<InventoryAdjustmentOrder.InventoryEntry> inventoryEntries) {
        if (inventoryEntries.stream().anyMatch(entry -> entry.quantity().compareTo(inventoryService.getCurrentStock(ownerId, entry.materialGoodId(), entry.fromUnit())) > 0)) {
            throw new BusinessException("Not enough stock to move.", "not_enough_stock_to_move");
        }

        var id = UUID.randomUUID();
        var number = sequences.nextSequenceForInventoryAdjustmentOrder(ownerId);
        var details = new InventoryAdjustmentOrder.Details(reason, deliveredBy, receivedBy, inventoryEntries.stream()
                .map(e -> {
                    var entry = e.setOriginalQuantity(inventoryService.getCurrentStock(ownerId, e.materialGoodId(), e.unitId()));
                    if (e.fromUnit() != null) {
                        entry = entry.setOriginalFromQuantity(inventoryService.getCurrentStock(ownerId, e.materialGoodId(), e.fromUnit()));
                    }
                    return entry;
                })
                .toList());
        persist(id, ownerId, number, InventoryAdjustmentOrder.Type.TYPE_CHANGE, details);
        inventoryEntries.forEach(entry -> inventoryService.removeForAdjustmentOrder(ownerId, entry.materialGoodId(), entry.fromUnit(), entry.quantity(), id));
        registerEntries(ownerId, id, inventoryEntries);

        return new InventoryAdjustmentOrder(id, Instant.now(), Instant.now(), false, ownerId, number,
                InventoryAdjustmentOrder.Type.TYPE_CHANGE, details);
    }

    private void persist(UUID id, UUID ownerId, String number, InventoryAdjustmentOrder.Type type, InventoryAdjustmentOrder.Details details) {
        db.insertInto(INVENTORY_ADJUSTMENT_ORDER)
                .set(INVENTORY_ADJUSTMENT_ORDER.ID, id)
                .set(INVENTORY_ADJUSTMENT_ORDER.OWNER_ID, ownerId)
                .set(INVENTORY_ADJUSTMENT_ORDER.NUMBER, number)
                .set(INVENTORY_ADJUSTMENT_ORDER.TYPE, type.name())
                .set(INVENTORY_ADJUSTMENT_ORDER.DETAILS, JSONB.valueOf(Json.write(details)))
                .execute();
    }

    private void registerEntries(UUID ownerId, UUID adjustmentOrderId, List<InventoryAdjustmentOrder.InventoryEntry> inventoryEntries) {
        var transactionTemplate = new TransactionTemplate(platformTransactionManager);
        transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        inventoryEntries.forEach(entry ->
                        inventoryService.registerEntry(new InventoryEntry(
                                null, null, null, false,
                                ownerId,
                                entry.materialGoodId(),
                                entry.locationId(),
                                entry.supplierId(),
                                adjustmentOrderId,
                                null,
                                null,
                                null,
                                null,
                                entry.unitId(),
                                entry.purchaseDate(),
                                entry.expiryDate(),
                                entry.price(),
                                null,
                                entry.quantity()
                        ))
        );
    }
}
