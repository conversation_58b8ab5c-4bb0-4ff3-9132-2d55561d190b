package fabriqon.app.business.inventory.accounting;

import fabriqon.app.business.inventory.InventoryEntry;
import fabriqon.app.common.model.Money;
import org.jooq.DSLContext;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ZERO;

public class FifoAccountingStrategy extends BaseAccountingStrategy {

    public FifoAccountingStrategy(DSLContext db) {
        super(db);
    }

    @Override
    public List<InventoryEntry> remove(UUID ownerId, UUID materialGoodId, BigDecimal quantity, UUID inventoryUnitId) {
        Objects.requireNonNull(inventoryUnitId, "inventory type cannot be null");
        var entries = inventoryEntries(ownerId, materialGoodId, inventoryUnitId);
        var fills = entries.stream()
                .filter(entry -> entry.quantity().compareTo(ZERO) > 0)
                .collect(Collectors.toCollection(LinkedList::new));
        var removals = entries.stream()
                .filter(entry -> entry.quantity().compareTo(ZERO) < 0)
                .collect(Collectors.toCollection(LinkedList::new));

        var stock = calculateStock(fills, removals, !removals.isEmpty() ? removals.remove().quantity() : ZERO);
        var quantityToRemove = quantity.subtract(ZERO);
        List<InventoryEntry> inventoryEntriesForRemoval = new LinkedList<>();
        while (quantityToRemove.compareTo(ZERO) > 0 && !stock.isEmpty()) {
            var currentEntry = stock.remove();
            var toSubtract = quantityToRemove.min(currentEntry.quantity());
            inventoryEntriesForRemoval.add(new InventoryEntry(null, null, null, false,
                    ownerId, materialGoodId, inventoryUnitId,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    currentEntry.unitId(),
                    currentEntry.purchaseDate(),
                    currentEntry.expiryDate(),
                    currentEntry.receptionPrice(),
                    null,
                    toSubtract.multiply(BigDecimal.valueOf(-1))));
            quantityToRemove = quantityToRemove.subtract(toSubtract);
        }
        return inventoryEntriesForRemoval;
    }


    @Override
    public Money calculateCostForQuantity(UUID ownerId, UUID materialGoodId, UUID inventoryUnitId, BigDecimal quantity) {
        var removals = remove(ownerId, materialGoodId, quantity, inventoryUnitId);
        if (!removals.isEmpty()) {
            return new Money(removals.stream().mapToLong(e -> e.receptionPrice().multiply(e.quantity().multiply(BigDecimal.valueOf(-1))).amount()).sum(), removals.get(0).receptionPrice().currency());
        }
        return new Money(0, Money.DEFAULT_CURRENCY);
    }

}
