package fabriqon.app.business.inventory.reception;

import fabriqon.app.common.model.Money;

import java.math.BigDecimal;

public record AdditionalCost(
        String description,
        String code,
        Type type,
        BigDecimal quantity,
        Money price,
        BigDecimal vat,
        AllocationStrategy allocationStrategy
) {
    public enum Type {TRANSPORTATION}

    public enum AllocationStrategy {BY_VALUE, BY_QUANTITY, CUSTOM}
}
