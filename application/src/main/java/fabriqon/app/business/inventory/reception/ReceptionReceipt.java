package fabriqon.app.business.inventory.reception;

import com.fasterxml.jackson.core.type.TypeReference;
import fabriqon.misc.Json;

import java.beans.ConstructorProperties;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Currency;
import java.util.List;
import java.util.UUID;


public record ReceptionReceipt(
        UUID id,
        Instant createTime,
        Instant updateTime,
        boolean deleted,

        UUID ownerId,
        UUID supplierId,
        UUID purchaseOrderId,
        String number,
        LocalDate receptionDate,
        SupportingDocument supportingDocument,
        Details details,
        List<ReceivedGood> goods
) {

    public record Details(
            Currency currency,
            String notes,
            String receivedBy,
            String transportedBy,
            String transportedWith,
            List<AdditionalCost> additionalCosts
    ) {
    }


    @ConstructorProperties({"id", "create_time", "update_time", "deleted", "owner_id", "supplier_id", "purchase_order_id",
            "number", "reception_date", "supporting_document", "details", "goods"})
    public ReceptionReceipt(UUID id,
                            Instant createTime,
                            Instant updateTime,
                            boolean deleted,
                            UUID ownerId,
                            UUID supplierId,
                            UUID purchaseOrderId,
                            String number,
                            LocalDate receptionDate,
                            String supportingDocument,
                            String details,
                            String goods
    ) {
        this(id, createTime, updateTime, deleted, ownerId, supplierId, purchaseOrderId, number, receptionDate,
                Json.read(supportingDocument, SupportingDocument.class),
                Json.read(details, Details.class),
                Json.read(goods, new TypeReference<List<ReceivedGood>>() {})
        );
    }
}
