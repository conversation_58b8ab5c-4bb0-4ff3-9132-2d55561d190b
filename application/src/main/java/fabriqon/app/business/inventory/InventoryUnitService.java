package fabriqon.app.business.inventory;

import fabriqon.app.business.exceptions.BusinessException;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.INVENTORY;
import static fabriqon.jooq.classes.Tables.INVENTORY_UNIT;
import static java.time.Instant.now;
import static org.jooq.impl.DSL.sum;

@Component
@Transactional
public class InventoryUnitService {

    private final DSLContext db;

    @Autowired
    public InventoryUnitService(final DSLContext db) {
        this.db = db;
    }

    public InventoryUnit create(UUID ownerId, String name) {
        final var id = UUID.randomUUID();
        db.insertInto(INVENTORY_UNIT)
                .set(INVENTORY_UNIT.ID, id)
                .set(INVENTORY_UNIT.OWNER_ID, ownerId)
                .set(INVENTORY_UNIT.NAME, name)
                .execute();

        return new InventoryUnit(id, now(), now(), false, ownerId, name);
    }

    public void update(UUID inventoryUnitId, UUID ownerId, String name) {
        db.update(INVENTORY_UNIT)
                .set(INVENTORY_UNIT.NAME, name)
                .where(INVENTORY_UNIT.ID.eq(inventoryUnitId), INVENTORY_UNIT.OWNER_ID.eq(ownerId))
                .execute();
    }

    public void delete(UUID inventoryUnitId, UUID ownerId) {
        if (db.select(INVENTORY.MATERIAL_GOOD_ID, sum(INVENTORY.QUANTITY))
                .from(INVENTORY)
                .where(INVENTORY.OWNER_ID.eq(ownerId), INVENTORY.UNIT_ID.eq(inventoryUnitId))
                .groupBy(INVENTORY.MATERIAL_GOOD_ID)
                .fetch()
                .stream()
                .anyMatch(r -> r.value2().compareTo(BigDecimal.ZERO) > 0)) {
            throw new BusinessException("can't delete inventory unit if there is inventory on it", "invalid_state_inventory_not_empty");
        }

        db.update(INVENTORY_UNIT)
                .set(INVENTORY_UNIT.DELETED, true)
                .where(INVENTORY_UNIT.ID.eq(inventoryUnitId), INVENTORY_UNIT.OWNER_ID.eq(ownerId))
                .execute();
    }

}
