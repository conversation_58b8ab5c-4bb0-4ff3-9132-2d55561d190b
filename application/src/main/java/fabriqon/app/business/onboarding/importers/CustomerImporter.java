package fabriqon.app.business.onboarding.importers;

import fabriqon.app.business.customers.CustomerService;
import fabriqon.app.common.model.Address;
import fabriqon.app.common.model.BankAccount;
import fabriqon.app.common.model.Company;
import fabriqon.app.common.model.ContactPerson;
import fabriqon.misc.CsvUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Slf4j
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class CustomerImporter extends EntityImporter {

    private final CustomerService customerService;

    @Autowired
    public CustomerImporter(CustomerService customerService) {
        this.customerService = customerService;
    }


    @Override
    protected String entityName() {
        return "customer";
    }

    @Override
    protected void importEntities(UUID ownerId, byte[] csv) {
        var records = new CsvUtils().parse(new String(csv, StandardCharsets.ISO_8859_1), StandardCharsets.ISO_8859_1);
        log.info("got [{}] records", records.size());
        records.forEach(record -> customerService.create(ownerId,
                isNotBlank(record.get(SagaHeaders.cod_fiscal.name())) ? Company.Type.LOCAL_LEGAL_ENTITY : Company.Type.INDIVIDUAL,
                record.get(SagaHeaders.denumire.name()),
                new Company.Details(
                        record.get(SagaHeaders.reg_com.name()),
                        record.get(SagaHeaders.cod_fiscal.name()),
                        List.of(new Address(
                                null,
                                record.get(SagaHeaders.tara.name()),
                                record.get(SagaHeaders.localitate.name()),
                                record.get(SagaHeaders.judet.name()),
                                record.get(SagaHeaders.adresa.name()),
                                null,
                                null,
                                Set.of(Address.Type.BILLING, Address.Type.SHIPPING)
                        )),
                        List.of(new ContactPerson(
                                null,
                                record.get(SagaHeaders.tel.name()),
                                record.get(SagaHeaders.email.name()),
                                null
                        )),
                        List.of(new BankAccount(
                                null,
                                record.get(SagaHeaders.cont_banca.name()),
                                record.get(SagaHeaders.banca.name()),
                                null
                        )),
                        null
                )
        ));
    }

    //TODO see if we'll use this sometime
    private void importCustomersDefault(UUID ownerId, String csv) {
        var records = new CsvUtils().parse(new String(csv.getBytes(), StandardCharsets.UTF_8));
        records.forEach(record -> customerService.create(ownerId,
                isNotBlank(record.get(Headers.tax_identification_number.name())) ? Company.Type.LOCAL_LEGAL_ENTITY : Company.Type.INDIVIDUAL,
                record.get(Headers.name.name()),
                new Company.Details(
                        record.get(Headers.tax_identification_number.name()),
                        record.get(Headers.tax_identification_number.name()),
                        List.of(new Address(
                                record.get(Headers.address_name.name()),
                                record.get(Headers.address_country.name()),
                                record.get(Headers.address_city.name()),
                                record.get(Headers.address_state.name()),
                                record.get(Headers.address_address1.name()),
                                record.get(Headers.address_address2.name()),
                                record.get(Headers.address_zip.name()),
                                Set.of(Address.Type.BILLING, Address.Type.SHIPPING)
                        )),
                        List.of(new ContactPerson(
                                record.get(Headers.contact_name.name()),
                                record.get(Headers.contact_phonenumber.name()),
                                record.get(Headers.contact_email.name()),
                                null
                        )),
                        List.of(),
                        null
                )
        ));
    }

    enum Headers {
        name,
        tax_identification_number,
        address_name,
        address_country,
        address_city,
        address_state,
        address_address1,
        address_address2,
        address_zip,
        contact_name,
        contact_phonenumber,
        contact_email
    }

    enum SagaHeaders {
        cod,
        denumire,
        cod_fiscal,
        analitic,
        tara,
        judet,
        localitate,
        adresa,
        cont_banca,
        banca,
        tel,
        email,
        grupa,
        reg_com,
        den_agent
    }

}
