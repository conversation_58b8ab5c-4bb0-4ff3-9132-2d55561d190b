package fabriqon.app.business.onboarding.importers;

import fabriqon.app.business.suppliers.SupplierService;
import fabriqon.app.common.model.Address;
import fabriqon.app.common.model.BankAccount;
import fabriqon.app.common.model.Company;
import fabriqon.app.common.model.ContactPerson;
import fabriqon.misc.CsvUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Slf4j
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class SupplierImporter extends EntityImporter {

    private final SupplierService supplierService;

    @Autowired
    public SupplierImporter(SupplierService supplierService) {
        this.supplierService = supplierService;
    }

    @Override
    protected String entityName() {
        return "supplier";
    }

    @Override
    protected void importEntities(UUID ownerId, byte[] csv) {
        var records = new CsvUtils().parse(new String(csv, StandardCharsets.UTF_8), StandardCharsets.UTF_8);
        log.info("got [{}] records ", records.size());
        records.forEach(record -> supplierService.create(ownerId,
                Company.Type.LOCAL_LEGAL_ENTITY,
                record.get(Headers.name.name()),
                new Company.Details(
                        record.get(Headers.identification_number.name()),
                        record.get(Headers.tax_identification_number.name()),
                        List.of(new Address(
                                record.get(Headers.address_name.name()),
                                record.get(Headers.address_country.name()),
                                record.get(Headers.address_city.name()),
                                record.get(Headers.address_state.name()),
                                record.get(Headers.address_address1.name()),
                                record.get(Headers.address_address2.name()),
                                record.get(Headers.address_zip.name()),
                                Set.of(Address.Type.BILLING)
                        )),
                        List.of(new ContactPerson(
                                record.get(Headers.contact_name.name()),
                                record.get(Headers.contact_phonenumber.name()),
                                record.get(Headers.contact_email.name()),
                                null
                        )),
                        List.of(new BankAccount(
                                record.get(Headers.bankaccount_name.name()),
                                record.get(Headers.bankaccount_number.name()),
                                record.get(Headers.bankaccount_bank.name()),
                                null
                        )),
                        null
                )
        ));
    }

    enum Headers {
        name,
        identification_number,
        tax_identification_number,
        address_name,
        address_country,
        address_city,
        address_state,
        address_address1,
        address_address2,
        address_zip,
        contact_name,
        contact_phonenumber,
        contact_email,
        bankaccount_name,
        bankaccount_number,
        bankaccount_bank,
    }

}
