package fabriqon.app.business.onboarding;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Scanner;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

@Slf4j
@Component
public class OnboardingService {

    private final List<Importer> importers;

    @Autowired
    public OnboardingService(@Lazy List<Importer> importers) {
        this.importers = importers;
    }

    public void importData(UUID ownerId, ZipInputStream zipInputStream) {
        try {
            var files = new HashMap<String, byte[]>();
            for (ZipEntry entry; (entry = zipInputStream.getNextEntry()) != null; ) {
                log.info("reading [{}]", entry.getName());
                files.put(entry.getName(), zipInputStream.readAllBytes());
            }
            importers.forEach(importer -> importer.importData(ownerId, files));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
