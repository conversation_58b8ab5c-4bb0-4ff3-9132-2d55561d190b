package fabriqon.app.business.onboarding.importers;

import fabriqon.app.business.onboarding.Importer;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.UUID;

@Slf4j
public abstract class EntityImporter implements Importer {

    @Override
    public void importData(UUID ownerId, Map<String, byte[]> files) {
        files.entrySet().stream()
                .filter(f -> f.getKey().startsWith(entityName()))
                .findAny()
                .map(Map.Entry::getValue)
                .ifPresent(csv -> importEntities(ownerId, csv));
    }

    protected abstract String entityName();
    protected abstract void importEntities(UUID ownerId, byte[] csv);

}
