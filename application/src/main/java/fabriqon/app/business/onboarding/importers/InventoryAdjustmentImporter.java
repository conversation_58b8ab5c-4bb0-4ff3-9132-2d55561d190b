package fabriqon.app.business.onboarding.importers;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.inventory.InventoryAdjustmentOrder;
import fabriqon.app.business.inventory.InventoryAdjustmentService;
import fabriqon.app.common.model.Money;
import fabriqon.misc.CsvUtils;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Currency;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.*;
import static org.apache.logging.log4j.util.Strings.isBlank;
import static org.apache.logging.log4j.util.Strings.isNotBlank;

@Slf4j
@Component
@Order(5)
public class InventoryAdjustmentImporter extends EntityImporter {

    private final InventoryAdjustmentService inventoryAdjustmentService;
    private final DSLContext db;

    public InventoryAdjustmentImporter(InventoryAdjustmentService inventoryAdjustmentService, DSLContext db) {
        this.inventoryAdjustmentService = inventoryAdjustmentService;
        this.db = db;
    }

    @Override
    protected String entityName() {
        return "inventory";
    }

    @Override
    protected void importEntities(UUID ownerId, byte[] csv) {
        var records = new CsvUtils().parse(new String(csv, StandardCharsets.UTF_8), StandardCharsets.UTF_8);
        log.info("got [{}] records ", records.size());
        var accountCurrency = accountCurrency(ownerId);
        inventoryAdjustmentService.adjustInventory(
                ownerId, "Ajustare initiala",
                records.stream().map(record -> new InventoryAdjustmentOrder.InventoryEntry(
                        materialGoodId(ownerId, record.get(Headers.name.name())),
                        null,
//                                            supplierId(ownerId, record.get(Headers.supplier.name())),
                        null,
                        null,
                        inventoryUnitId(ownerId, record.get(Headers.inventory_unit.name())),
                        isNotBlank(record.get(Headers.purchase_date.name())) ? LocalDate.parse(record.get(Headers.purchase_date.name()), DateTimeFormatter.ofPattern("dd.MM.yyyy")).atStartOfDay() : null,
//                                            isNotBlank(record.get(Headers.expiration_date.name())) ? LocalDate.parse(record.get(Headers.expiration_date.name())).atStartOfDay().toInstant(ZoneOffset.UTC) : null,
                        null,
                        isNotBlank(record.get(Headers.price.name())) ? new Money(new BigDecimal(record.get(Headers.price.name()).replaceAll(",", "")).multiply(BigDecimal.valueOf(100)).longValue(), accountCurrency) : new Money(0, accountCurrency),
                        new BigDecimal(record.get(Headers.quantity.name()).replaceAll(",", "")),
                        null, null
                )).toList()
        );
    }

    private UUID supplierId(UUID ownerId, String supplier) {
        if (isBlank(supplier)) {
            return null;
        }
        return db.select(COMPANY.ID)
                .from(COMPANY)
                .where(COMPANY.OWNER_ID.eq(ownerId), COMPANY.COMPANY_NAME.eq(supplier))
                .fetchSingleInto(UUID.class);
    }

    private UUID materialGoodId(UUID ownerId, String materialGoodName) {
        return db.select(MATERIAL_GOOD.ID)
                .from(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.OWNER_ID.eq(ownerId), MATERIAL_GOOD.NAME.eq(materialGoodName))
                .fetchSingleInto(UUID.class);
    }


    private UUID inventoryUnitId(UUID ownerId, String unit) {
        if (isBlank(unit)) {
            throw new RuntimeException("inventory unit cannot be null");
        }
        try {
            return db.select(INVENTORY_UNIT.ID)
                    .from(INVENTORY_UNIT)
                    .where(INVENTORY_UNIT.OWNER_ID.eq(ownerId), INVENTORY_UNIT.NAME.eq(unit))
                    .fetchSingleInto(UUID.class);
        } catch (RuntimeException e) {
            System.out.println(unit);
           throw e;
        }
    }

    private Currency accountCurrency(UUID ownerId) {
        return db.selectFrom(ACCOUNT)
                .where(ACCOUNT.ID.eq(ownerId))
                .fetchSingleInto(Account.class)
                .settings().general().defaultCurrency();
    }

    enum Headers {
        name,
        supplier,
        inventory_unit,
        purchase_date,
        expiration_date,
        price,
        quantity
    }
}
