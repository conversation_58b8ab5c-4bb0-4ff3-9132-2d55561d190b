package fabriqon.app.business.onboarding.importers;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.employees.Employee;
import fabriqon.app.business.employees.EmployeeService;
import fabriqon.app.common.model.Money;
import fabriqon.misc.CsvUtils;
import fabriqon.misc.Tuple;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Currency;
import java.util.List;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.ACCOUNT;
import static fabriqon.jooq.classes.Tables.MANUFACTURING_OPERATION_TEMPLATE;
import static org.apache.commons.lang3.StringUtils.*;

@Slf4j
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class EmployeeImporter extends EntityImporter {
    
    private final EmployeeService employeeService;
    private final DSLContext db;

    public EmployeeImporter(EmployeeService employeeService, DSLContext db) {
        this.employeeService = employeeService;
        this.db = db;
    }

    @Override
    protected String entityName() {
        return "employee";
    }

    @Override
    protected void importEntities(UUID ownerId, byte[] csv) {
        var records = new CsvUtils().parse(new String(csv, StandardCharsets.UTF_8), StandardCharsets.UTF_8);
        log.info("got [{}] records ", records.size());
        records.forEach(record -> employeeService.create(ownerId,
                manufacturingOperationTemplates(ownerId, record.get(Headers.manufacturing_operations.name()))
                        .stream().map(o -> Tuple.of(o, false)).toList(),
                record.get(Headers.name.name()),
                new Employee.Details(
                        record.get(Headers.position.name()),
                        new Money(isNotBlank(record.get(Headers.monthly_gross_salary.name())) ? Long.parseLong(record.get(Headers.monthly_gross_salary.name())) : 0, accountCurrency(ownerId)),
                        null
                )

        ));
    }

    private List<UUID> manufacturingOperationTemplates(UUID ownerId, String operationNames) {
        if (isBlank(operationNames)) {
            return List.of();
        }
        return db.select(MANUFACTURING_OPERATION_TEMPLATE.ID)
                .from(MANUFACTURING_OPERATION_TEMPLATE)
                .where(MANUFACTURING_OPERATION_TEMPLATE.OWNER_ID.eq(ownerId),
                        MANUFACTURING_OPERATION_TEMPLATE.NAME.in(operationNames.split(";")))
                .fetchInto(UUID.class);
    }

    private Currency accountCurrency(UUID ownerId) {
        return db.selectFrom(ACCOUNT)
                .where(ACCOUNT.ID.eq(ownerId))
                .fetchSingleInto(Account.class)
                .settings().general().defaultCurrency();
    }

    enum Headers {
        name,
        position,
        monthly_gross_salary,
        manufacturing_operations
    }
}
