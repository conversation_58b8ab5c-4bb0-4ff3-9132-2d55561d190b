package fabriqon.app.business.onboarding.importers;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.goods.Category;
import fabriqon.app.business.goods.GoodsService;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.common.model.Money;
import fabriqon.misc.CsvUtils;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Currency;
import java.util.List;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.*;
import static org.apache.commons.lang3.StringUtils.isBlank;

@Slf4j
@Component
@Order(1)
public class MaterialGoodImporter extends EntityImporter {

    private final GoodsService goodsService;
    private final DSLContext db;

    public MaterialGoodImporter(GoodsService goodsService, DSLContext db) {
        this.goodsService = goodsService;
        this.db = db;
    }

    @Override
    protected String entityName() {
        return "material";
    }

    @Override
    protected void importEntities(UUID ownerId, byte[] csv) {
        var records = new CsvUtils().parse(new String(csv, StandardCharsets.UTF_8), StandardCharsets.UTF_8);
        log.info("got [{}] records ", records.size());
        var categories = db.selectFrom(CATEGORY).where(CATEGORY.OWNER_ID.eq(ownerId)).fetchInto(Category.class);
        var accountCurrency = accountCurrency(ownerId);
        records.forEach(record -> {
            var name = record.get(Headers.name.name());
            if (db.fetchCount(MATERIAL_GOOD, MATERIAL_GOOD.OWNER_ID.eq(ownerId).and(MATERIAL_GOOD.NAME.eq(name))) > 0) {
                return;
            }
            goodsService.create(ownerId, new MaterialGood(null, null, null, false,
                            ownerId,
                            name,
                            record.get(Headers.code.name()),
                            record.isMapped(Headers.external_code.name()) ? record.get(Headers.external_code.name()) : null,
                            categoryId(categories, record.get(Headers.category.name())),
                            null,
                            null,
                            new Money(0, accountCurrency),
                            new MaterialGood.Details(
//                                new BigDecimal(record.get(Headers.critical_on_hand.name())),
                                    BigDecimal.ZERO,
                                    MeasurementUnit.valueOf(record.get(Headers.measurement_unit.name())),
                                    List.of(),
                                    List.of(),
                                    List.of(),
                                    false, null, new BigDecimal("0.19"), null, null, null
                            )
                    )
            );
        });
    }

    private UUID supplierId(UUID ownerId, String supplier) {
        if (isBlank(supplier)) {
            return null;
        }
        return db.select(COMPANY.ID)
                .from(COMPANY)
                .where(COMPANY.OWNER_ID.eq(ownerId), COMPANY.COMPANY_NAME.eq(supplier))
                .fetchSingleInto(UUID.class);
    }

    private UUID categoryId(List<Category> categories, String category) {
        if (isBlank(category)) {
            return null;
        }
        return categories.stream().filter(c -> category.equals(c.details().name())).findFirst().map(Category::id).orElse(null);
    }

    private Currency accountCurrency(UUID ownerId) {
        return db.selectFrom(ACCOUNT)
                .where(ACCOUNT.ID.eq(ownerId))
                .fetchSingleInto(Account.class)
                .settings().general().defaultCurrency();
    }

    enum Headers {
        name,
        code,
        external_code,
        category,
        critical_on_hand,
        measurement_unit,
    }
}
