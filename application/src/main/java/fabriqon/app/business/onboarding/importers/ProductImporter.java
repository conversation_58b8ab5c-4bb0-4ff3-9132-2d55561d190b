package fabriqon.app.business.onboarding.importers;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.goods.Category;
import fabriqon.app.business.goods.GoodsService;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.goods.RequiredMaterial;
import fabriqon.app.business.onboarding.Importer;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.common.model.Money;
import fabriqon.misc.CsvUtils;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Currency;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.*;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Slf4j
@Component
@Order(2)
public class ProductImporter implements Importer {

    private final GoodsService productsService;
    private final DSLContext db;

    public ProductImporter(GoodsService productsService, DSLContext db) {
        this.productsService = productsService;
        this.db = db;
    }


    @Override
    public void importData(UUID ownerId, Map<String, byte[]> files) {
        files.entrySet().stream()
                .filter(f -> f.getKey().startsWith("products"))
                .findAny()
                .map(Map.Entry::getValue)
                .ifPresent(csv -> importProducts(ownerId, csv, files));
    }


    protected void importProducts(UUID ownerId, byte[] csv, Map<String, byte[]> files) {
        var records = new CsvUtils().parse(new String(csv, StandardCharsets.UTF_8), StandardCharsets.UTF_8);
        log.info("got [{}] records ", records.size());
        var categories = db.selectFrom(CATEGORY).where(CATEGORY.OWNER_ID.eq(ownerId)).fetchInto(Category.class);
        var accountCurrency = accountCurrency(ownerId);
        records.forEach(record -> {
            var code = record.get(Headers.code.name());
            var produced = Boolean.valueOf(record.get(Headers.produced.name()));
            productsService.create(ownerId, new MaterialGood(null, null, null, false,
                            ownerId,
                            record.get(Headers.name.name()),
                            code,
                            record.isMapped(Headers.external_code.name()) ? record.get(Headers.external_code.name()) : null,
                            categoryId(categories, record.get(Headers.category.name())),
                            null,
                            record.get(Headers.description.name()),
                            new Money(Long.parseLong(record.get(Headers.sell_price.name())), accountCurrency),
                            new MaterialGood.Details(
                                    isNotBlank(record.get(Headers.critical_on_hand.name())) ? new BigDecimal(record.get(Headers.critical_on_hand.name())) : null,
                                    MeasurementUnit.valueOf(record.get(Headers.measurement_unit.name())),
//                                    produced ? materials(ownerId, code, files) : List.of(),
                                    List.of(),
                                    List.of(),
                                    List.of(),
                                    produced,
                                    null,
                                    null,
                                    BigDecimal.ONE,
                                    null,
                                    null
                            )
                    )
            );
        });
    }

    private List<RequiredMaterial> materials(UUID ownerId, String code, Map<String, byte[]> files) {
        return files.entrySet().stream()
                .filter(f -> f.getKey().equals("product_" + code + "_materials.csv"))
                .findAny()
                .map(Map.Entry::getValue)
                .map(csv -> new CsvUtils().parse(new String(csv, StandardCharsets.UTF_8), StandardCharsets.UTF_8)
                        .stream()
                        .map(r -> new RequiredMaterial(List.of(materialId(ownerId, r.get(MaterialHeaders.material.name()))),
                                new BigDecimal(r.get(MaterialHeaders.quantity.name())), false, false, false, BigDecimal.ZERO, null, null))
                        .toList()
                )
                .orElse(List.of());
    }

    private UUID materialId(UUID ownerId, String material) {
        return db.select(MATERIAL_GOOD.ID)
                .from(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.OWNER_ID.eq(ownerId), MATERIAL_GOOD.NAME.eq(material))
                .fetchSingleInto(UUID.class);
    }

    private UUID supplierId(UUID ownerId, String supplier) {
        if (isBlank(supplier)) {
            return null;
        }
        return db.select(COMPANY.ID)
                .from(COMPANY)
                .where(COMPANY.OWNER_ID.eq(ownerId), COMPANY.COMPANY_NAME.eq(supplier))
                .fetchSingleInto(UUID.class);
    }

    private UUID categoryId(List<Category> categories, String category) {
        if (isBlank(category)) {
            return null;
        }
        return categories.stream().filter(c -> category.equals(c.details().name())).findFirst().map(Category::id).orElse(null);
    }

    private Currency accountCurrency(UUID ownerId) {
        return db.selectFrom(ACCOUNT)
                .where(ACCOUNT.ID.eq(ownerId))
                .fetchSingleInto(Account.class)
                .settings().general().defaultCurrency();
    }



    enum Headers {
        name,
        code,
        external_code,
        category,
        sell_price,
        description,
        critical_on_hand,
        measurement_unit,
        produced,
    }

    enum MaterialHeaders {
        material,
        quantity
    }
}
