package fabriqon.app.business.goods.materials.metals;

import fabriqon.app.business.goods.Dimensions;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.common.model.MeasurementUnit;
import org.jooq.DSLContext;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static fabriqon.jooq.classes.Tables.MATERIAL_GOOD;

@Service
public class MetalCalculatorService {

    private static final BigDecimal MM_TO_M = new BigDecimal("0.001");
    private static final BigDecimal PI = new BigDecimal(Math.PI, new MathContext(10));
    private static final int CALCULATION_SCALE = 10;

    private final DSLContext db;
    private final MetalMaterialService metalMaterialService;

    public MetalCalculatorService(DSLContext db, MetalMaterialService metalMaterialService) {
        this.db = db;
        this.metalMaterialService = metalMaterialService;
    }

    public BigDecimal calculateWeight(UUID materialId, Dimensions requiredDimensions) {
        var dimensions = new LinkedList<DimensionValue>();
        if (requiredDimensions.length() != null) {
            dimensions.add(new DimensionValue(MetalMaterialService.DimensionKey.LENGTH, requiredDimensions.length()));
        }
        if (requiredDimensions.width() != null) {
            dimensions.add(new DimensionValue(MetalMaterialService.DimensionKey.WIDTH, requiredDimensions.width()));
        }
        return calculateWeight(materialId, dimensions);
    }

    public BigDecimal calculateWeight(UUID materialId, List<DimensionValue> requiredDimensions) {
        var material = db.select(MATERIAL_GOOD.DETAILS).from(MATERIAL_GOOD).where(MATERIAL_GOOD.ID.eq(materialId)).fetchSingleInto(MaterialGood.Details.class);
        if (!material.measurementUnit().equals(MeasurementUnit.KG)) {
            throw new IllegalArgumentException("Measurement unit not supported: " + material.measurementUnit());
        }
        var materialDetails = material.material();
        Objects.requireNonNull(materialDetails, "Material must not be null");
        return calculateWeight(metalMaterialService.getMaterialByKey(materialDetails.key(), "en"),
                metalMaterialService.getShapeByKey(materialDetails.shape()),
                Stream.concat(materialDetails.dimensions().stream(), requiredDimensions.stream()).toList());
    }

    /**
     * Calculate volume in cubic meters based on shape and dimensions
     * @param shape The metal shape
     * @param dimensions The dimensions (all in mm)
     * @return The volume in cubic meters
     */
    public BigDecimal calculateVolume(MetalMaterialService.Shape shape, List<DimensionValue> dimensions) {
        Map<MetalMaterialService.DimensionKey, BigDecimal> dimMap = dimensions.stream()
                .collect(Collectors.toMap(DimensionValue::key, DimensionValue::value));

        // Get length in mm, default to 1000mm (1m) if not provided
        BigDecimal lengthInMm = dimMap.getOrDefault(
                MetalMaterialService.DimensionKey.LENGTH,
                new BigDecimal("1000")
        );

        // Convert length to meters
        BigDecimal lengthInMeters = lengthInMm.multiply(MM_TO_M);

        if (lengthInMeters.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Length must be positive");
        }

        // Validate all required dimensions are present (except length which is optional)
        shape.dimensions().stream()
                .filter(dim -> dim.key() != MetalMaterialService.DimensionKey.LENGTH)
                .forEach(dim -> {
                    if (!dimMap.containsKey(dim.key())) {
                        throw new IllegalArgumentException("Missing required dimension: " + dim.key());
                    }
                });

        return switch (shape.type()) {
            case PLATE, PLATE_BLACK, PLATE_GALVANIZED, STRIP_ROLL -> {
                BigDecimal thickness = dimMap.get(MetalMaterialService.DimensionKey.THICKNESS).multiply(MM_TO_M);
                BigDecimal width = dimMap.getOrDefault(MetalMaterialService.DimensionKey.WIDTH, BigDecimal.ONE).multiply(MM_TO_M);
                yield thickness.multiply(width).multiply(lengthInMeters);
            }
            case ROUND_BAR -> {
                BigDecimal radius = dimMap.get(MetalMaterialService.DimensionKey.DIAMETER)
                        .multiply(MM_TO_M)
                        .divide(new BigDecimal("2"), CALCULATION_SCALE, RoundingMode.HALF_UP);
                BigDecimal area = PI.multiply(radius.pow(2));
                yield area.multiply(lengthInMeters);
            }
            case SQUARE_BAR -> {
                BigDecimal side = dimMap.get(MetalMaterialService.DimensionKey.SIDE).multiply(MM_TO_M);
                yield side.pow(2).multiply(lengthInMeters);
            }
            case L_PROFILE -> {
                BigDecimal legA = dimMap.get(MetalMaterialService.DimensionKey.LEG_A).multiply(MM_TO_M);
                BigDecimal legB = dimMap.get(MetalMaterialService.DimensionKey.LEG_B).multiply(MM_TO_M);
                BigDecimal thickness = dimMap.get(MetalMaterialService.DimensionKey.THICKNESS).multiply(MM_TO_M);
                // L-profile area = (legA * thickness) + (legB - thickness) * thickness
                BigDecimal area = legA.multiply(thickness).add(
                        legB.subtract(thickness).multiply(thickness)
                );
                yield area.multiply(lengthInMeters);
            }
            case T_PROFILE -> {
                BigDecimal width = dimMap.get(MetalMaterialService.DimensionKey.WIDTH).multiply(MM_TO_M);
                BigDecimal height = dimMap.get(MetalMaterialService.DimensionKey.HEIGHT).multiply(MM_TO_M);
                BigDecimal thickness = dimMap.get(MetalMaterialService.DimensionKey.THICKNESS).multiply(MM_TO_M);
                // T-profile area = (width * thickness) + (height - thickness) * thickness
                BigDecimal area = width.multiply(thickness).add(
                        (height.subtract(thickness)).multiply(thickness)
                );
                yield area.multiply(lengthInMeters);
            }
            case U_PROFILE -> {
                BigDecimal width = dimMap.get(MetalMaterialService.DimensionKey.WIDTH).multiply(MM_TO_M);
                BigDecimal height = dimMap.get(MetalMaterialService.DimensionKey.HEIGHT).multiply(MM_TO_M);
                BigDecimal thickness = dimMap.get(MetalMaterialService.DimensionKey.THICKNESS).multiply(MM_TO_M);
                // U-profile area = (width * thickness) + 2 * (height - thickness) * thickness
                BigDecimal area = width.multiply(thickness).add(
                        new BigDecimal("2").multiply(height.subtract(thickness)).multiply(thickness)
                );
                yield area.multiply(lengthInMeters);
            }
            case RECTANGULAR_TUBE -> {
                BigDecimal width = dimMap.get(MetalMaterialService.DimensionKey.WIDTH).multiply(MM_TO_M);
                BigDecimal height = dimMap.get(MetalMaterialService.DimensionKey.HEIGHT).multiply(MM_TO_M);
                BigDecimal thickness = dimMap.get(MetalMaterialService.DimensionKey.THICKNESS).multiply(MM_TO_M);
                // Rectangular tube area = width * height - (width - 2*thickness) * (height - 2*thickness)
                BigDecimal outerArea = width.multiply(height);
                BigDecimal innerArea = width.subtract(thickness.multiply(new BigDecimal("2")))
                        .multiply(height.subtract(thickness.multiply(new BigDecimal("2"))));
                BigDecimal area = outerArea.subtract(innerArea);
                yield area.multiply(lengthInMeters);
            }
            case SQUARE_TUBE -> {
                BigDecimal side = dimMap.get(MetalMaterialService.DimensionKey.SIDE).multiply(MM_TO_M);
                BigDecimal thickness = dimMap.get(MetalMaterialService.DimensionKey.THICKNESS).multiply(MM_TO_M);
                // Square tube area = side^2 - (side - 2*thickness)^2
                BigDecimal outerArea = side.pow(2);
                BigDecimal innerArea = side.subtract(thickness.multiply(new BigDecimal("2"))).pow(2);
                BigDecimal area = outerArea.subtract(innerArea);
                yield area.multiply(lengthInMeters);
            }
            case ROUND_TUBE -> {
                BigDecimal outerDiameter = dimMap.get(MetalMaterialService.DimensionKey.OUTER_DIAMETER).multiply(MM_TO_M);
                BigDecimal thickness = dimMap.get(MetalMaterialService.DimensionKey.THICKNESS).multiply(MM_TO_M);

                BigDecimal outerRadius = outerDiameter.divide(new BigDecimal("2"), CALCULATION_SCALE, RoundingMode.HALF_UP);
                BigDecimal innerRadius = outerRadius.subtract(thickness);

                // Round tube area = π * (outer_radius^2 - inner_radius^2)
                BigDecimal area = PI.multiply(
                        outerRadius.pow(2).subtract(innerRadius.pow(2))
                );
                yield area.multiply(lengthInMeters);
            }
            default -> {
                throw new IllegalArgumentException("Unsupported shape type: " + shape.type());
            }
        };
    }

    /**
     * Calculate weight in kg based on material, shape and dimensions
     * @param material The metal material
     * @param shape The metal shape
     * @param dimensions The dimensions (all in mm)
     * @return The weight in kg
     */
    public BigDecimal calculateWeight(MetalMaterialService.Material material, MetalMaterialService.Shape shape,
                                      List<DimensionValue> dimensions) {
        // Volume (m³) * Density (kg/m³) = Weight (kg)
        return calculateVolume(shape, dimensions)
                .multiply(material.density())
                .setScale(4, RoundingMode.HALF_UP);
    }

    /**
     * Calculate weight in kg for a standard length of 1 meter
     * @param material The metal material
     * @param shape The metal shape
     * @param dimensions The cross-section dimensions (in mm, excluding length)
     * @return The weight in kg for a 1 meter length
     */
    public BigDecimal calculateWeightPerMeter(MetalMaterialService.Material material, MetalMaterialService.Shape shape,
                                              List<DimensionValue> dimensions) {
        // Create a new list of dimensions with a 1000mm (1m) length
        List<DimensionValue> dimensionsWithStandardLength = dimensions.stream()
                .filter(dim -> dim.key() != MetalMaterialService.DimensionKey.LENGTH)
                .collect(Collectors.toList());

        dimensionsWithStandardLength.add(
                new DimensionValue(MetalMaterialService.DimensionKey.LENGTH, new BigDecimal("1000"))
        );

        return calculateWeight(material, shape, dimensionsWithStandardLength);
    }

    /**
     * Calculate length in mm based on weight, material, shape and dimensions
     * @param weight The weight in kg
     * @param material The metal material
     * @param shape The metal shape
     * @param dimensions The cross-section dimensions (in mm, excluding length)
     * @return The length in mm
     */
    public BigDecimal calculateLength(BigDecimal weight, MetalMaterialService.Material material,
                                      MetalMaterialService.Shape shape, List<DimensionValue> dimensions) {
        if (weight == null || weight.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Weight must be positive");
        }

        // Filter out any length dimension if present
        List<DimensionValue> dimensionsWithoutLength = dimensions.stream()
                .filter(dim -> dim.key() != MetalMaterialService.DimensionKey.LENGTH)
                .collect(Collectors.toList());

        // Add a 1 meter (1000mm) length to calculate volume per meter
        dimensionsWithoutLength.add(
                new DimensionValue(MetalMaterialService.DimensionKey.LENGTH, new BigDecimal("1000"))
        );

        // Calculate volume for 1m length
        BigDecimal volumePerMeter = calculateVolume(shape, dimensionsWithoutLength);
        if (volumePerMeter.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("Invalid dimensions resulting in zero volume");
        }

        // Length in meters = Weight / (Density * Volume per meter)
        BigDecimal lengthInMeters = weight
                .divide(material.density().multiply(volumePerMeter).divide(new BigDecimal("1000"), CALCULATION_SCALE, RoundingMode.HALF_UP),
                        CALCULATION_SCALE, RoundingMode.HALF_UP);

        // Convert back to mm
        return lengthInMeters
                .multiply(new BigDecimal("1000"))
                .setScale(1, RoundingMode.HALF_UP);
    }
}