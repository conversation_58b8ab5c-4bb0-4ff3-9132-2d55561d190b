package fabriqon.app.business.goods;

import com.google.common.collect.Sets;
import fabriqon.ObjectStorage;
import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.employees.Employee;
import fabriqon.app.business.exceptions.BusinessException;
import fabriqon.app.business.goods.materials.metals.MetalCalculatorService;
import fabriqon.app.business.manufacturing.ManufacturingOperationService;
import fabriqon.app.business.manufacturing.Workstation;
import fabriqon.app.common.model.Money;
import fabriqon.misc.Json;
import fabriqon.misc.Tuple;
import org.apache.commons.lang3.RandomStringUtils;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

import static fabriqon.jooq.classes.Tables.*;
import static org.apache.commons.lang3.BooleanUtils.isFalse;
import static org.apache.commons.lang3.BooleanUtils.isTrue;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

@Component
@Transactional
public class GoodsService {

    private final DSLContext db;
    private final ManufacturingOperationService manufacturingOperationService;
    private final MaterialCosts materialCosts;
    private final ObjectStorage objectStorage;
    private final MetalCalculatorService metalCalculatorService;

    @Autowired
    public GoodsService(DSLContext db, ManufacturingOperationService manufacturingOperationService, MaterialCosts materialCosts,
                        ObjectStorage objectStorage, MetalCalculatorService metalCalculatorService) {
        this.db = db;
        this.manufacturingOperationService = manufacturingOperationService;
        this.materialCosts = materialCosts;
        this.objectStorage = objectStorage;
        this.metalCalculatorService = metalCalculatorService;
    }

    public MaterialGood create(UUID ownerId, MaterialGood materialGood) {
        var saved = materialGood
                .setOwnerId(ownerId)
                .setId(UUID.randomUUID())
                .setCode(materialGood.code != null
                        ? materialGood.code
                        : RandomStringUtils.random(10, true, true).toUpperCase())
                .setDetails(materialGood.details
                        .setVatRate(materialGood.details.vatRate() != null ? materialGood.details.vatRate() : getAccountVatRate(ownerId))
                        .setManufacturingOperations(materialGood.details.manufacturingOperations().stream().map(op -> op.setMaterials(processRequiredMaterials(op.materials()))).toList())
                );


        db.insertInto(MATERIAL_GOOD)
                .set(MATERIAL_GOOD.ID, saved.id)
                .set(MATERIAL_GOOD.OWNER_ID, ownerId)
                .set(MATERIAL_GOOD.NAME, saved.name)
                .set(MATERIAL_GOOD.CODE, saved.code)
                .set(MATERIAL_GOOD.EXTERNAL_CODE, saved.externalCode)
                .set(MATERIAL_GOOD.CATEGORY_ID, saved.categoryId)
                .set(MATERIAL_GOOD.PARENT_ID, saved.parentId)
                .set(MATERIAL_GOOD.DESCRIPTION, saved.description)
                .set(MATERIAL_GOOD.SELL_PRICE_AMOUNT, saved.sellPrice != null ? saved.sellPrice.amount() : 0)
                .set(MATERIAL_GOOD.SELL_PRICE_CURRENCY, saved.sellPrice != null ? saved.sellPrice.currency() : Money.DEFAULT_CURRENCY)
                .set(MATERIAL_GOOD.DETAILS, JSONB.jsonb(Json.write(saved.details())))
                .execute();

        return saved;
    }

    public void update(UUID goodId, UUID ownerId, MaterialGood updated) {
        var good = load(ownerId, goodId);
        var updateStatement = db.update(MATERIAL_GOOD).set(MATERIAL_GOOD.OWNER_ID, ownerId);
        if (updated.name != null) {
            updateStatement = updateStatement.set(MATERIAL_GOOD.NAME, updated.name);
        }
        if (updated.code != null) {
            updateStatement = updateStatement.set(MATERIAL_GOOD.CODE, updated.code);
        }
        if (updated.externalCode != null) {
            updateStatement = updateStatement.set(MATERIAL_GOOD.EXTERNAL_CODE, updated.externalCode);
        }
        if (updated.categoryId != null) {
            updateStatement = updateStatement.set(MATERIAL_GOOD.CATEGORY_ID, updated.categoryId);
        }
        if (updated.description != null) {
            updateStatement = updateStatement.set(MATERIAL_GOOD.DESCRIPTION, updated.description);
        }
        if (updated.sellPrice != null) {
            updateStatement = updateStatement
                    .set(MATERIAL_GOOD.SELL_PRICE_AMOUNT, updated.sellPrice.amount())
                    .set(MATERIAL_GOOD.SELL_PRICE_CURRENCY, updated.sellPrice.currency());
        }

        if (updated.details != null) {
            var shouldUpdateDetails = false;
            var details = good.details;
            if (isFalse(good.details.produced()) && isTrue(updated.details.produced()) && details.unitOfProduction() == null) {
                details = new MaterialGood.Details(updated.details.criticalOnHand(), details.measurementUnit(),
                        details.manufacturingOperations(),
                        details.variantOptions(),
                        details.appliedVariantOptions(),
                        details.produced(),
                        details.markup(),
                        details.vatRate(),
                        BigDecimal.ONE,
                        details.dimensions(),
                        details.material());
                shouldUpdateDetails = true;
            }
            var unitOfProduction = updated.details.unitOfProduction() != null ? updated.details.unitOfProduction() : details.unitOfProduction();
            if (updated.details.criticalOnHand() != null) {
                details = new MaterialGood.Details(updated.details.criticalOnHand(), details.measurementUnit(),
                        details.manufacturingOperations(),
                        details.variantOptions(),
                        details.appliedVariantOptions(),
                        details.produced(),
                        details.markup(),
                        details.vatRate(),
                        details.unitOfProduction(),
                        details.dimensions(),
                        details.material());
                shouldUpdateDetails = true;
            }
            if (updated.details.measurementUnit() != null) {
                details = new MaterialGood.Details(details.criticalOnHand(), updated.details.measurementUnit(),
                        details.manufacturingOperations(),
                        details.variantOptions(),
                        details.appliedVariantOptions(),
                        details.produced(),
                        details.markup(),
                        details.vatRate(),
                        details.unitOfProduction(),
                        details.dimensions(),
                        details.material());
                shouldUpdateDetails = true;
            }
            if (updated.details.manufacturingOperations() != null) {
                details = new MaterialGood.Details(
                        details.criticalOnHand(), details.measurementUnit(),
                        updated.details.manufacturingOperations().stream()
                                .map(mo -> mo
                                        .setDuration(new BigDecimal(mo.durationInMinutes()).divide(unitOfProduction, RoundingMode.HALF_EVEN).intValue())
                                        .setMaterials(mo.materials().stream().map(m -> m.setQuantity(m.quantity().setScale(4, RoundingMode.HALF_EVEN).divide(unitOfProduction, RoundingMode.HALF_EVEN))).toList())
                                )
                                .toList(),
                        details.variantOptions(),
                        details.appliedVariantOptions(),
                        details.produced(),
                        details.markup(),
                        details.vatRate(),
                        details.unitOfProduction(),
                        details.dimensions(),
                        details.material()
                );
                shouldUpdateDetails = true;
            }
            if (updated.details.variantOptions() != null) {
                details = new MaterialGood.Details(
                        details.criticalOnHand(), details.measurementUnit(),
                        details.manufacturingOperations(),
                        updated.details.variantOptions(),
                        details.appliedVariantOptions(),
                        details.produced(),
                        details.markup(),
                        details.vatRate(),
                        details.unitOfProduction(),
                        details.dimensions(),
                        details.material()
                );
                shouldUpdateDetails = true;
            }
            if (updated.details.appliedVariantOptions() != null) {
                details = new MaterialGood.Details(
                        details.criticalOnHand(), details.measurementUnit(),
                        details.manufacturingOperations(),
                        details.variantOptions(),
                        updated.details.appliedVariantOptions(),
                        details.produced(),
                        details.markup(),
                        details.vatRate(),
                        details.unitOfProduction(),
                        details.dimensions(),
                        details.material()
                );
                shouldUpdateDetails = true;
            }
            details = new MaterialGood.Details(
                    details.criticalOnHand(), details.measurementUnit(),
                    details.manufacturingOperations().stream().map(mo -> mo.setMaterials(processRequiredMaterials(mo.materials()))).toList(),
                    details.variantOptions(),
                    details.appliedVariantOptions(),
                    updated.details.produced(),
                    details.markup(),
                    details.vatRate(),
                    details.unitOfProduction(),
                    details.dimensions(),
                    details.material()
            );
            if (updated.details.markup() != null) {
                details = new MaterialGood.Details(
                        details.criticalOnHand(), details.measurementUnit(),
                        details.manufacturingOperations(),
                        details.variantOptions(),
                        details.appliedVariantOptions(),
                        details.produced(),
                        updated.details().markup(),
                        details.vatRate(),
                        details.unitOfProduction(),
                        details.dimensions(),
                        details.material()
                );
                shouldUpdateDetails = true;
            }
            if (updated.details.vatRate() != null) {
                details = new MaterialGood.Details(
                        details.criticalOnHand(), details.measurementUnit(),
                        details.manufacturingOperations(),
                        details.variantOptions(),
                        details.appliedVariantOptions(),
                        details.produced(),
                        details.markup(),
                        updated.details().vatRate(),
                        details.unitOfProduction(),
                        details.dimensions(),
                        details.material()
                );
                shouldUpdateDetails = true;
            }
            if (updated.details.unitOfProduction() != null) {
                details = new MaterialGood.Details(
                        details.criticalOnHand(), details.measurementUnit(),
                        details.manufacturingOperations(),
                        details.variantOptions(),
                        details.appliedVariantOptions(),
                        details.produced(),
                        details.markup(),
                        details.vatRate(),
                        updated.details.unitOfProduction(),
                        details.dimensions(),
                        details.material()
                );
                shouldUpdateDetails = true;
            }
            if (updated.details.dimensions() != null) {
                details = new MaterialGood.Details(
                        details.criticalOnHand(), details.measurementUnit(),
                        details.manufacturingOperations(),
                        details.variantOptions(),
                        details.appliedVariantOptions(),
                        details.produced(),
                        details.markup(),
                        details.vatRate(),
                        details.unitOfProduction(),
                        updated.details.dimensions(),
                        details.material()
                );
                shouldUpdateDetails = true;
            }
            if (updated.details.material() != null) {
                details = new MaterialGood.Details(
                        details.criticalOnHand(), details.measurementUnit(),
                        details.manufacturingOperations(),
                        details.variantOptions(),
                        details.appliedVariantOptions(),
                        details.produced(),
                        details.markup(),
                        details.vatRate(),
                        details.unitOfProduction(),
                        details.dimensions(),
                        updated.details.material()
                );
                shouldUpdateDetails = true;
            }
            if (shouldUpdateDetails) {
                updateStatement = updateStatement.set(MATERIAL_GOOD.DETAILS, JSONB.jsonb(Json.write(details)));
            }
        }

        updateStatement.where(MATERIAL_GOOD.OWNER_ID.eq(ownerId), MATERIAL_GOOD.ID.eq(goodId)).execute();
    }

    public void delete(UUID ownerId, UUID productId) {
        db.update(MATERIAL_GOOD)
                .set(MATERIAL_GOOD.DELETED, true)
                .where(
                        MATERIAL_GOOD.ID.eq(productId),
                        MATERIAL_GOOD.OWNER_ID.eq(ownerId)
                )
                .execute();
    }

    public void updateVariants(UUID ownerId, UUID productId, List<MaterialGood.VariantOption> variantOptions) {
        var parentProduct = load(ownerId, productId);
        if (parentProduct.parentId != null) {
            throw new BusinessException("Cannot create variants for a variant", "not_parent_product");
        }
        var definedVariants = db.selectFrom(MATERIAL_GOOD).where(MATERIAL_GOOD.OWNER_ID.eq(ownerId), MATERIAL_GOOD.PARENT_ID.eq(productId), MATERIAL_GOOD.DELETED.isFalse()).fetchInto(MaterialGood.class);
        var processedVariants = new ArrayList<UUID>();
        var counter = new AtomicInteger(definedVariants.stream().map(v -> v.code.substring(parentProduct.code.length())).map(Integer::parseInt).max(Integer::compareTo).orElse(0));
        Sets.cartesianProduct(variantOptions.stream().map(MaterialGood.VariantOption::values).toList())
                .forEach(option -> {
                    var variantName = parentProduct.name + " " + String.join(" - ", option);
                    var existingVariant = definedVariants.stream().filter(v -> v.name.equals(variantName)).findAny();
                    if (existingVariant.isPresent()) {
                        processedVariants.add(existingVariant.map(v -> v.id).orElseThrow());
                        return;
                    }
                    create(ownerId, new MaterialGood(null, null, null, false,
                        ownerId,
                        variantName,
                        parentProduct.code + counter.incrementAndGet(),
                        parentProduct.externalCode,
                        parentProduct.categoryId,
                        parentProduct.id,
                        parentProduct.description,
                        parentProduct.sellPrice,
                        new MaterialGood.Details(
                                parentProduct.details.criticalOnHand(),
                                parentProduct.details.measurementUnit(),
                                parentProduct.details.manufacturingOperations(),
                                null,
                                appliedVariantOptions(option, variantOptions),
                                parentProduct.details.produced(),
                                parentProduct.details.markup(),
                                parentProduct.details.vatRate(),
                                parentProduct.details.unitOfProduction(),
                                parentProduct.details.dimensions(),
                                parentProduct.details.material()
                        )
                ));});
        definedVariants.stream()
                .filter(v -> !processedVariants.contains(v.id))
                .filter(v -> db.fetchCount(INVENTORY, INVENTORY.OWNER_ID.eq(ownerId), INVENTORY.MATERIAL_GOOD_ID.eq(v.id))  != 0)
                .forEach(v -> delete(ownerId, v.id));
        db.update(MATERIAL_GOOD)
                .set(MATERIAL_GOOD.DETAILS, JSONB.jsonb(Json.write(parentProduct.details.setVariantOptions(variantOptions))))
                .where(MATERIAL_GOOD.OWNER_ID.eq(ownerId), MATERIAL_GOOD.ID.eq(productId))
                .execute();
    }

    private List<MaterialGood.AppliedVariantOption> appliedVariantOptions(List<String> options, List<MaterialGood.VariantOption> variantOptions) {
        return options.stream()
                .map(value -> variantOptions.stream()
                        .filter(option -> option.values().contains(value))
                        .map(option -> new MaterialGood.AppliedVariantOption(option.name(), value))
                        .findFirst())
                .map(value -> value.orElse(MaterialGood.AppliedVariantOption.NONE))
                .toList();
    }

    protected MaterialGood load(UUID ownerId, UUID productId) {
        return db.selectFrom(MATERIAL_GOOD)
                .where(MATERIAL_GOOD.ID.eq(productId), MATERIAL_GOOD.OWNER_ID.eq(ownerId))
                .fetchSingleInto(MaterialGood.class);
    }

    /**
     * It returns an estimated cost of the materials to be used during manufacturing
     */
    public Money estimatedMaterialCosts(UUID ownerId, UUID productId) {
        var product = load(ownerId, productId);
        return product.details.manufacturingOperations().stream().flatMap(op -> op.materials().stream())
                .map(material -> Tuple.of(material.materialIds().getFirst(), material.quantity()))
                .map(material -> new Money(materialCosts.materialCost(ownerId, material.a()).multiply(material.b()).amount(),
                        product.sellPrice.currency()))
                .reduce(Money::add)
                .orElse(new Money(0, Money.DEFAULT_CURRENCY));
    }


    public Money laborCosts(UUID ownerId, UUID productId) {
        var product = load(ownerId, productId);
        var manufacturingOverheadPerEmployeeHour = manufacturingOverheadPerEmployeeHour(ownerId);
        return new Money(manufacturingOperationService.enhance(product.details.manufacturingOperations())
                .stream()
                .map(operation -> {
                    var value = BigDecimal.ZERO;
                    BigDecimal durationByHour = BigDecimal.valueOf(operation.durationInMinutes()).setScale(4, RoundingMode.HALF_EVEN)
                            .divide(BigDecimal.valueOf(60), RoundingMode.HALF_EVEN);
                    if (operation.costPerHour() != null) {
                        value = value.add(operation.costPerHour().multiply(durationByHour).toBigDecimal());
                    } else {
                        if (isNotEmpty((operation.candidateEmployeeIds()))) {
                            value = value.add(
                                    //todo: average the employee rate
                                    employeeHourlyRate(operation.candidateEmployeeIds().get(0)).multiply(durationByHour)
                            );
                            value = value.add(BigDecimal.valueOf(manufacturingOverheadPerEmployeeHour.multiply(durationByHour).amount()));
                        }
                    }
                    if (isNotEmpty((operation.candidateWorkstationIds()))) {
                        value = value.add(
                                //todo: average the workstation rate
                                workstationHourlyRate(operation.candidateWorkstationIds().get(0)).multiply(durationByHour)
                        );
                    }
                    return value;
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .longValue(),
                product.sellPrice.currency());
    }

    public Money productionOverheadCosts(UUID ownerId, UUID productId) {
        var product = load(ownerId, productId);
        var manufacturingOverheadPerEmployeeHour = manufacturingOverheadPerEmployeeHour(ownerId);
        return new Money(manufacturingOperationService.enhance(product.details.manufacturingOperations())
                .stream()
                .map(operation -> {
                    var value = BigDecimal.ZERO;
                    if (isNotEmpty((operation.candidateEmployeeIds()))) {
                        value = value.add(BigDecimal.valueOf(manufacturingOverheadPerEmployeeHour
                                .multiply(BigDecimal.valueOf(operation.durationInMinutes()).setScale(4, RoundingMode.HALF_EVEN)
                                        .divide(BigDecimal.valueOf(60), RoundingMode.HALF_EVEN))
                                .amount()));
                    }
                    return value;
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .longValue(),
                product.sellPrice.currency());
    }

    public Money administrativeOverheadCosts(UUID ownerId, UUID productId) {
        var account = db.selectFrom(ACCOUNT)
                .where(ACCOUNT.ID.eq(ownerId))
                .fetchSingleInto(Account.class);
        var overhead = account.settings().manufacturing().administrativeOverheadPerEmployeeHour();
        if (overhead == null) {
            return new Money(0, account.settings().general().defaultCurrency());
        }

        var product = load(ownerId, productId);
        return new Money(manufacturingOperationService.enhance(product.details.manufacturingOperations())
                .stream()
                .map(operation -> BigDecimal.valueOf(overhead
                        .multiply(BigDecimal.valueOf(operation.durationInMinutes()).setScale(4, RoundingMode.HALF_EVEN)
                                .divide(BigDecimal.valueOf(60), RoundingMode.HALF_EVEN))
                        .amount())
                )
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .longValue(),
                product.sellPrice.currency());
    }

    public MaterialGood clone(UUID ownerId, UUID productId) {
        var product = load(ownerId, productId);
        return create(ownerId, new MaterialGood(null, null, null, false,
                ownerId,
                "Copy of " + product.name,
                product.code.startsWith("Copy of") ? product.code : "Copy of " + product.code,
                product.externalCode,
                product.categoryId,
                null,
                product.description,
                product.sellPrice,
                product.details
        ));
    }

    public MaterialGood attach(UUID ownerId, UUID productId, String name, byte[] bytes) {
        var product = load(ownerId, productId);//we do this here to validate access
        var fileId = objectStorage.store(ownerId, name, bytes);
        db.insertInto(PRODUCT_FILE)
                .set(PRODUCT_FILE.OWNER_ID, ownerId)
                .set(PRODUCT_FILE.PRODUCT_ID, productId)
                .set(PRODUCT_FILE.FILE_ID, fileId)
                .execute();
        return product;
    }

    public MaterialGood deleteFile(UUID ownerId, UUID productId, UUID fileId) {
        var product = load(ownerId, productId);//we do this here to validate access
        db.deleteFrom(PRODUCT_FILE)
                .where(PRODUCT_FILE.OWNER_ID.eq(ownerId), PRODUCT_FILE.PRODUCT_ID.eq(productId), PRODUCT_FILE.FILE_ID.eq(fileId))
                .execute();
        objectStorage.delete(ownerId, fileId);
        return product;
    }

    private BigDecimal employeeHourlyRate(UUID id) {
        var employee = db.selectFrom(USERS).where(USERS.ID.eq(id)).fetchSingleInto(Employee.class);
        return new BigDecimal(employee.details().hourlyRate() != null
                ? employee.details().hourlyRate().amount()
                : 0
        );
    }

    private BigDecimal workstationHourlyRate(UUID id) {
        var workstation = db.selectFrom(MANUFACTURING_WORKSTATION)
                .where(MANUFACTURING_WORKSTATION.ID.eq(id))
                .fetchSingleInto(Workstation.class);
        return new BigDecimal(workstation.details().costPerHour() != null
                ? workstation.details().costPerHour().amount()
                : 0
        );
    }

    private Money manufacturingOverheadPerEmployeeHour(UUID accountId) {
        var overhead = db.selectFrom(ACCOUNT)
                .where(ACCOUNT.ID.eq(accountId))
                .fetchSingleInto(Account.class)
                .settings().manufacturing().manufacturingOverheadPerEmployeeHour();
        return overhead != null ? overhead : new Money(0, Money.DEFAULT_CURRENCY);
    }

    private BigDecimal getAccountVatRate(UUID ownerId) {
        return db.selectFrom(ACCOUNT).where(ACCOUNT.ID.eq(ownerId)).fetchSingleInto(Account.class).settings().defaultVAT();
    }

    private List<RequiredMaterial> processRequiredMaterials(List<RequiredMaterial> materials) {
        return materials.stream()
                .map(m -> m.setQuantity(m.dimensions() != null
                        ? metalCalculatorService.calculateWeight(m.materialIds().getFirst(), m.dimensions())
                        : m.quantity()))
                .toList();
    }

}
