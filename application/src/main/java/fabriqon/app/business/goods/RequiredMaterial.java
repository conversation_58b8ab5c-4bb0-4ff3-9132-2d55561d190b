package fabriqon.app.business.goods;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

public record RequiredMaterial(List<UUID> materialIds,
                               BigDecimal quantity,
                               boolean optional,
                               boolean configurableWithOptions,
                               boolean replaceableWithOptions,
                               BigDecimal wastePercentage,
                               Dimensions dimensions,
                               Dimensions totalDimensions) {

    public RequiredMaterial setQuantity(BigDecimal quantity) {
        return new RequiredMaterial(materialIds, quantity, optional, configurableWithOptions, replaceableWithOptions, wastePercentage, dimensions, totalDimensions);
    }

}
