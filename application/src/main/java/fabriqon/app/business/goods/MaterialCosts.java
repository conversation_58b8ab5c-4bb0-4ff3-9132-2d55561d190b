package fabriqon.app.business.goods;

import fabriqon.app.business.inventory.InventoryService;
import fabriqon.app.common.model.Money;
import fabriqon.jooq.JooqJsonbFunctions;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.UUID;

import static fabriqon.jooq.classes.Tables.INVENTORY;
import static fabriqon.jooq.classes.Tables.MATERIAL_GOOD;

@Component
public class MaterialCosts {

    final DSLContext db;
    final InventoryService inventoryService;
    private final GoodsService goodsService;

    @Autowired
    public MaterialCosts(DSLContext db, InventoryService inventoryService, @Lazy GoodsService goodsService) {
        this.db = db;
        this.inventoryService = inventoryService;
        this.goodsService = goodsService;
    }

    /**
     * Return the average cost. In case there's no more on inventory we return the last price the material was on the inventory.
     * In case the material was never on inventory and if it produced we return the production cost
     */
    public Money materialCost(UUID ownerId, UUID materialId) {
        var averageCost = inventoryService.getAverageCost(ownerId, materialId);
        if (averageCost.amount() == 0) {
            //try to see if the item was on inventory and if it was then return the last price
            return db.select(INVENTORY.RECEPTION_PRICE_AMOUNT, INVENTORY.RECEPTION_PRICE_CURRENCY)
                    .from(INVENTORY)
                    .where(INVENTORY.OWNER_ID.eq(ownerId), INVENTORY.MATERIAL_GOOD_ID.eq(materialId))
                    .orderBy(INVENTORY.CREATE_TIME)
                    .limit(1)
                    .fetchOptionalInto(Money.class)
                    .orElseGet(() -> {
                        if (db.fetchCount(MATERIAL_GOOD, MATERIAL_GOOD.OWNER_ID.eq(ownerId),
                                MATERIAL_GOOD.ID.eq(materialId),
                                JooqJsonbFunctions.booleanField(MATERIAL_GOOD.DETAILS, "produced").isTrue()
                        ) > 0) {
                            return goodsService.estimatedMaterialCosts(ownerId, materialId)
                                    .add(goodsService.laborCosts(ownerId, materialId)
                                            .add(goodsService.productionOverheadCosts(ownerId, materialId))
                                    );
                        }
                        return new Money(0, Money.DEFAULT_CURRENCY);
                    });
        }
        return averageCost;
    }

}
