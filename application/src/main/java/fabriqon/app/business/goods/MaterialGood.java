package fabriqon.app.business.goods;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import fabriqon.app.business.goods.materials.metals.DimensionValue;
import fabriqon.app.business.goods.materials.metals.MetalMaterialService;
import fabriqon.app.business.manufacturing.ManufacturingOperation;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.common.model.Money;
import fabriqon.misc.Json;

import java.beans.ConstructorProperties;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Currency;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static java.util.Optional.ofNullable;

public class MaterialGood {

    public final UUID id;
    public final Instant createTime;
    public final Instant updateTime;
    public final boolean deleted;

    public final UUID ownerId;
    public final String name;
    public final String code;
    public final String externalCode;
    public final UUID categoryId;
    public final UUID parentId;
    public final String description;
    public final Money sellPrice;
    public Details details;

    public MaterialGood(UUID id, Instant createTime, Instant updateTime, boolean deleted, UUID ownerId, String name,
                        String code, String externalCode, UUID categoryId, UUID parentId, String description, Money sellPrice,
                        Details details) {
        this.id = id;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.deleted = deleted;
        this.ownerId = ownerId;
        this.name = name;
        this.code = code;
        this.externalCode = externalCode;
        this.categoryId = categoryId;
        this.parentId = parentId;
        this.description = description;
        this.sellPrice = sellPrice;
        this.details = details;
    }

    @ConstructorProperties({"id", "create_time", "update_time", "deleted", "owner_id", "name", "code", "external_code", "category_id",
            "parent_id", "description", "sell_price_amount", "sell_price_currency", "details"})
    public MaterialGood(UUID id, Instant createTime, Instant updateTime, boolean deleted,
                        UUID ownerId,
                        String name,
                        String code,
                        String externalCode,
                        UUID categoryId,
                        UUID parentId,
                        String description,
                        Long sellPriceAmount, Currency sellPriceCurrency,
                        String details) {
        this.id = id;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.deleted = deleted;
        this.ownerId = ownerId;
        this.name = name;
        this.code = code;
        this.externalCode = externalCode;
        this.categoryId = categoryId;
        this.parentId = parentId;
        this.description = description;
        this.sellPrice = sellPriceAmount != null ? new Money(sellPriceAmount, sellPriceCurrency) : null;
        this.details = Json.read(ofNullable(details).orElse("{}"), Details.class);
    }

    public MaterialGood setId(final UUID id) {
        return new MaterialGood(id, createTime, updateTime, deleted, ownerId, name, code, externalCode, categoryId, parentId, description,
                sellPrice, details);
    }

    public MaterialGood setOwnerId(final UUID ownerId) {
        return new MaterialGood(id, createTime, updateTime, deleted, ownerId, name, code, externalCode, categoryId, parentId, description,
                sellPrice, details);
    }

    public MaterialGood setCode(final String code) {
        return new MaterialGood(id, createTime, updateTime, deleted, ownerId, name, code, externalCode, categoryId, parentId, description,
                sellPrice, details);
    }

    public MaterialGood setDetails(final Details details) {
        return new MaterialGood(id, createTime, updateTime, deleted, ownerId, name, code, externalCode, categoryId, parentId, description,
                sellPrice, details);
    }

    public Details details() {
        return details;
    }

    //TODO clean this deserialization hack up
    @JsonIgnoreProperties(ignoreUnknown = true)
    public record Details(
            BigDecimal criticalOnHand,
            MeasurementUnit measurementUnit,
            List<ManufacturingOperation> manufacturingOperations,
            List<VariantOption> variantOptions,
            List<AppliedVariantOption> appliedVariantOptions,
            boolean produced,
            BigDecimal markup,
            BigDecimal vatRate,
            //materials & durations of production are stored for 1 unit of production but the bom and durations for
            // the operations are defined for the number below by users (for example 100 of units)
            BigDecimal unitOfProduction,
            Dimensions dimensions,
            Material material
    ) {

        public Details setVatRate(final BigDecimal vatRate) {
            return new Details(criticalOnHand, measurementUnit, manufacturingOperations, variantOptions, appliedVariantOptions, produced, markup, vatRate, unitOfProduction, dimensions, material);
        }

        public Details setVariantOptions(final List<VariantOption> variantOptions) {
            return new Details(criticalOnHand, measurementUnit, manufacturingOperations, variantOptions, appliedVariantOptions, produced, markup, vatRate, unitOfProduction, dimensions, material);
        }

        public Details setManufacturingOperations(List<ManufacturingOperation> manufacturingOperations) {
            return new Details(criticalOnHand, measurementUnit, manufacturingOperations, variantOptions, appliedVariantOptions, produced, markup, vatRate, unitOfProduction, dimensions, material);
        }
    }

    public record VariantOption(
            String name,
            Set<String> values
    ) {
    }

    public record AppliedVariantOption(
            String name,
            String value
    ) {
        public static AppliedVariantOption NONE = new AppliedVariantOption("N/A", "N/A");
    }

    public record Material(String key, MetalMaterialService.ShapeType shape, List<DimensionValue> dimensions) {}
}
