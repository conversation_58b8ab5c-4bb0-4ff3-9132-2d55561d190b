package fabriqon.app.business.goods;

import fabriqon.app.common.model.MeasurementUnit;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;
import java.util.UUID;

import static fabriqon.jooq.classes.Tables.MATERIAL_GOOD;

@Component
public class DimensionToMeasurementUnitConverter {

    private static final int DECIMAL_SCALE = 6;

    private final DSLContext db;

    @Autowired
    public DimensionToMeasurementUnitConverter(DSLContext db) {
        this.db = db;
    }

    public BigDecimal convert(UUID materialId, Dimensions dimensions) {
        var materialDetails = db.select(MATERIAL_GOOD.DETAILS).from(MATERIAL_GOOD).where(MATERIAL_GOOD.ID.eq(materialId)).fetchSingleInto(MaterialGood.Details.class);
        Objects.requireNonNull(materialDetails.dimensions(), "Material dimensions must not be null");
        return convert(materialDetails.dimensions(), dimensions, materialDetails.measurementUnit());
    }

    public BigDecimal convert(Dimensions perUnitDimensions, Dimensions dimensions, MeasurementUnit measurementUnit) {
        if (perUnitDimensions == null || dimensions == null) {
            throw new IllegalArgumentException("Per unit dimensions and dimensions cannot be null");
        }
        BigDecimal dimensionalRatio = calculateDimensionalRatio(perUnitDimensions, dimensions);
        return convertToUnit(dimensionalRatio, perUnitDimensions, measurementUnit);
    }

    /**
     * Calculates the ratio between the requested dimensions and per unit dimensions
     */
    private BigDecimal calculateDimensionalRatio(Dimensions perUnitDimensions, Dimensions dimensions) {
        return createDimensionConverter(dimensions).convert(perUnitDimensions, dimensions);
    }

    /**
     * Converts the dimensional ratio to the target measurement unit
     */
    private BigDecimal convertToUnit(BigDecimal dimensionalRatio, Dimensions perUnitDimensions, MeasurementUnit measurementUnit) {
        return switch (measurementUnit) {
            case PIECE -> dimensionalRatio;
            case KG, G, TONNE -> {
                if (perUnitDimensions.weight() == null) {
                    throw new IllegalArgumentException("Per unit dimensions must include weight for mass conversions");
                }
                yield dimensionalRatio.multiply(perUnitDimensions.weight());
            }
            default -> throw new IllegalArgumentException("Unsupported measurement unit: " + measurementUnit);
        };
    }

    private DimensionConverter createDimensionConverter(Dimensions dimensions) {
        if (dimensions.length() != null && dimensions.width() != null && dimensions.height() != null) {
            return new VolumetricConverter();
        }
        if (dimensions.length() != null && dimensions.width() != null) {
            return new AreaConverter();
        }
        if (dimensions.length() != null) {
            return new LengthConverter();
        }
        if (dimensions.weight() != null) {
            return new MassConverter();
        }
        throw new IllegalArgumentException("Dimensions or weight must be specified");
    }


    interface DimensionConverter {
        BigDecimal convert(Dimensions perUnitDimensions, Dimensions dimensions);
    }

    static class VolumetricConverter implements DimensionConverter {
        @Override
        public BigDecimal convert(Dimensions perUnitDimensions, Dimensions dimensions) {
            validateDimensions(perUnitDimensions, dimensions);
            BigDecimal perUnitVolume = perUnitDimensions.length()
                    .multiply(perUnitDimensions.width())
                    .multiply(perUnitDimensions.height());
            BigDecimal requestedVolume = dimensions.length()
                    .multiply(dimensions.width())
                    .multiply(dimensions.height());
            return calculateRatio(requestedVolume, perUnitVolume, "volume");
        }

        private void validateDimensions(Dimensions perUnitDimensions, Dimensions dimensions) {
            if (perUnitDimensions.length() == null || perUnitDimensions.width() == null || perUnitDimensions.height() == null) {
                throw new IllegalArgumentException("Per unit dimensions must include length, width, and height");
            }
            if (dimensions.length() == null || dimensions.width() == null || dimensions.height() == null) {
                throw new IllegalArgumentException("Order dimensions must include length, width, and height");
            }
        }
    }

    static class AreaConverter implements DimensionConverter {
        @Override
        public BigDecimal convert(Dimensions perUnitDimensions, Dimensions dimensions) {
            validateDimensions(perUnitDimensions, dimensions);
            BigDecimal perUnitArea = perUnitDimensions.length().multiply(perUnitDimensions.width());
            BigDecimal requestedArea = dimensions.length().multiply(dimensions.width());
            return calculateRatio(requestedArea, perUnitArea, "area");
        }

        private void validateDimensions(Dimensions perUnitDimensions, Dimensions dimensions) {
            if (perUnitDimensions.length() == null || perUnitDimensions.width() == null) {
                throw new IllegalArgumentException("Per unit dimensions must include length and width");
            }
            if (dimensions.length() == null || dimensions.width() == null) {
                throw new IllegalArgumentException("Order dimensions must include length and width");
            }
        }
    }

    static class LengthConverter implements DimensionConverter {
        @Override
        public BigDecimal convert(Dimensions perUnitDimensions, Dimensions dimensions) {
            validateDimensions(perUnitDimensions, dimensions);
            return calculateRatio(dimensions.length(), perUnitDimensions.length(), "length");
        }

        private void validateDimensions(Dimensions perUnitDimensions, Dimensions dimensions) {
            if (perUnitDimensions.length() == null) {
                throw new IllegalArgumentException("Per unit dimensions must include length");
            }
            if (dimensions.length() == null) {
                throw new IllegalArgumentException("Order dimensions must include length");
            }
        }
    }

    static class MassConverter implements DimensionConverter {
        @Override
        public BigDecimal convert(Dimensions perUnitDimensions, Dimensions dimensions) {
            validateDimensions(perUnitDimensions, dimensions);
            return calculateRatio(dimensions.weight(), perUnitDimensions.weight(), "weight");
        }

        private void validateDimensions(Dimensions perUnitDimensions, Dimensions dimensions) {
            if (perUnitDimensions.weight() == null) {
                throw new IllegalArgumentException("Per unit dimensions must include weight");
            }
            if (dimensions.weight() == null) {
                throw new IllegalArgumentException("Order dimensions must include weight");
            }
        }
    }

    private static BigDecimal calculateRatio(BigDecimal numerator, BigDecimal denominator, String dimensionType) {
        if (denominator.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("Per unit " + dimensionType + " cannot be zero");
        }

        return numerator.divide(denominator, DECIMAL_SCALE, RoundingMode.HALF_UP);
    }
}