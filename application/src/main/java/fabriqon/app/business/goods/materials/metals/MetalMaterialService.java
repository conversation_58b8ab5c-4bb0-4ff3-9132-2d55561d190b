package fabriqon.app.business.goods.materials.metals;

import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

@Service
public class MetalMaterialService {

    final Map<String, ResourceBundle> loadedBundles = new HashMap<>();

    private final List<Material> materials;
    private final List<Shape> shapes;
    private final Map<ShapeType, Map<DimensionKey, List<BigDecimal>>> standardSizes;

    public MetalMaterialService() {
        this.materials = initializeMaterials();
        this.shapes = initializeShapes();
        this.standardSizes = initializeStandardSizes();
    }

    private List<Material> initializeMaterials() {
        // Define materials with keys
        return List.of(
                new Material("aluminum.pure", "Aluminiu - Pur (1050, 1100)", new BigDecimal("2715")),
                new Material("aluminum.alloy.6000", "Aluminiu - 6061, 6063, 6082 (Al-Mg-Si)", new BigDecimal("2700")),
                new Material("aluminum.alloy.7075", "Aluminiu - 7075 (Al-Zn)", new BigDecimal("2800")),
                new Material("titanium.pure", "Titan - Pur (Grade 1-4)", new BigDecimal("4500")),
                new Material("titanium.alloy.grade5", "Titan - Ti-6Al-4V (Grade 5)", new BigDecimal("4430")),
                new Material("steel.carbon.mild", "Oțel carbon moale - S235, S275, S355", new BigDecimal("7850")),
                new Material("steel.galvanized", "Oțel zincat - S235/S275/S355", new BigDecimal("7850")),
                new Material("steel.stainless.austenitic", "Oțel inoxidabil - 304, 316 (Austenitic)", new BigDecimal("7955")),
                new Material("steel.stainless.ferritic", "Oțel inoxidabil - 430 (Ferritic), 410 (Martensitic)", new BigDecimal("7725")),
                new Material("copper.pure", "Cupru - Pur (C10100, C11000)", new BigDecimal("8940")),
                new Material("copper.brass", "Cupru - Alamă (Cu-Zn, ex. C26000)", new BigDecimal("8625")),
                new Material("copper.bronze", "Cupru - Bronz (Cu-Sn, Cu-Al, Cu-Si)", new BigDecimal("8150"))
        );
    }

    private List<Shape> initializeShapes() {
        List<Shape> allShapes = new ArrayList<>();

        // Plate (Generic)
        allShapes.add(new Shape(
                ShapeType.PLATE,
                "Tablă",
                List.of(new Dimension(DimensionKey.THICKNESS)),
                false
        ));

        // Plate (Black) - Steel specific
        allShapes.add(new Shape(
                ShapeType.PLATE_BLACK,
                "Tablă Neagră",
                List.of(new Dimension(DimensionKey.THICKNESS)),
                true
        ));

        // Plate (Galvanized) - Steel specific
        allShapes.add(new Shape(
                ShapeType.PLATE_GALVANIZED,
                "Tablă Zincată",
                List.of(new Dimension(DimensionKey.THICKNESS)),
                true
        ));

        // Strip Roll
        allShapes.add(new Shape(
                ShapeType.STRIP_ROLL,
                "Bandă Rulou",
                List.of(new Dimension(DimensionKey.THICKNESS)),
                false
        ));

        // Round Bar
        allShapes.add(new Shape(
                ShapeType.ROUND_BAR,
                "Bară Rotundă",
                List.of(new Dimension(DimensionKey.DIAMETER)),
                false
        ));

        // Square Bar
        allShapes.add(new Shape(
                ShapeType.SQUARE_BAR,
                "Bară Pătrată",
                List.of(new Dimension(DimensionKey.SIDE)),
                false
        ));

        // L Profile
        allShapes.add(new Shape(
                ShapeType.L_PROFILE,
                "Profil L",
                List.of(
                        new Dimension(DimensionKey.LEG_A),
                        new Dimension(DimensionKey.LEG_B),
                        new Dimension(DimensionKey.THICKNESS)
                ),
                false
        ));

        // T Profile
        allShapes.add(new Shape(
                ShapeType.T_PROFILE,
                "Profil T",
                List.of(
                        new Dimension(DimensionKey.WIDTH),
                        new Dimension(DimensionKey.HEIGHT),
                        new Dimension(DimensionKey.THICKNESS)
                ),
                false
        ));

        // U Profile
        allShapes.add(new Shape(
                ShapeType.U_PROFILE,
                "Profil U",
                List.of(
                        new Dimension(DimensionKey.WIDTH),
                        new Dimension(DimensionKey.HEIGHT),
                        new Dimension(DimensionKey.THICKNESS)
                ),
                false
        ));

        // Rectangular Tube
        allShapes.add(new Shape(
                ShapeType.RECTANGULAR_TUBE,
                "Țeavă Rect.",
                List.of(
                        new Dimension(DimensionKey.WIDTH),
                        new Dimension(DimensionKey.HEIGHT),
                        new Dimension(DimensionKey.THICKNESS)
                ),
                false
        ));

        // Square Tube
        allShapes.add(new Shape(
                ShapeType.SQUARE_TUBE,
                "Țeavă Pătrată",
                List.of(
                        new Dimension(DimensionKey.SIDE),
                        new Dimension(DimensionKey.THICKNESS)
                ),
                false
        ));

        // Round Tube
        allShapes.add(new Shape(
                ShapeType.ROUND_TUBE,
                "Țeavă Rotundă",
                List.of(
                        new Dimension(DimensionKey.OUTER_DIAMETER),
                        new Dimension(DimensionKey.THICKNESS)
                ),
                false
        ));

        return allShapes;
    }

    private Map<ShapeType, Map<DimensionKey, List<BigDecimal>>> initializeStandardSizes() {
        Map<ShapeType, Map<DimensionKey, List<BigDecimal>>> sizes = new EnumMap<>(ShapeType.class);

        // Plate (Generic)
        Map<DimensionKey, List<BigDecimal>> plateSizes = new EnumMap<>(DimensionKey.class);
        plateSizes.put(DimensionKey.THICKNESS, numberListToBigDecimal(List.of(
                0.4, 0.45, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 1.2, 1.25, 1.5, 1.75,
                2.0, 2.25, 2.5, 2.75, 3.0, 3.5, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0,
                10.0, 12.0, 14.0, 15.0, 20.0, 22.0, 25.0, 28.0, 30.0, 35.0,
                40.0, 45.0, 50.0, 60.0, 70.0, 80.0, 90.0, 100.0
        )));
        sizes.put(ShapeType.PLATE, plateSizes);

        // Plate (Black) - Steel specific
        Map<DimensionKey, List<BigDecimal>> plateBlackSizes = new EnumMap<>(DimensionKey.class);
        plateBlackSizes.put(DimensionKey.THICKNESS, numberListToBigDecimal(List.of(
                1.5, 2.0, 2.5, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 12.0,
                14.0, 15.0, 20.0, 22.0, 25.0, 28.0, 30.0, 35.0, 40.0, 45.0,
                50.0, 60.0, 70.0, 80.0, 90.0, 100.0
        )));
        sizes.put(ShapeType.PLATE_BLACK, plateBlackSizes);

        // Plate (Galvanized) - Steel specific
        Map<DimensionKey, List<BigDecimal>> plateGalvanizedSizes = new EnumMap<>(DimensionKey.class);
        plateGalvanizedSizes.put(DimensionKey.THICKNESS, numberListToBigDecimal(List.of(
                0.4, 0.45, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 1.2, 1.25, 1.5, 1.75,
                2.0, 2.25, 2.5, 2.75, 3.0, 3.5, 4.0, 5.0
        )));
        sizes.put(ShapeType.PLATE_GALVANIZED, plateGalvanizedSizes);

        // Strip Roll
        Map<DimensionKey, List<BigDecimal>> stripRollSizes = new EnumMap<>(DimensionKey.class);
        stripRollSizes.put(DimensionKey.THICKNESS, numberListToBigDecimal(List.of(
                0.4, 0.45, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 1.2, 1.25, 1.5, 1.75,
                2.0, 2.25, 2.5, 2.75, 3.0
        )));
        sizes.put(ShapeType.STRIP_ROLL, stripRollSizes);

        // Round Bar
        Map<DimensionKey, List<BigDecimal>> roundBarSizes = new EnumMap<>(DimensionKey.class);
        roundBarSizes.put(DimensionKey.DIAMETER, numberListToBigDecimal(List.of(
                8.0, 9.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0,
                22.0, 24.0, 25.0, 26.0, 28.0, 30.0, 32.0, 35.0, 36.0, 38.0, 40.0, 42.0, 45.0,
                50.0, 55.0, 56.0, 60.0, 65.0, 70.0, 75.0, 80.0, 85.0, 90.0, 92.0, 95.0, 100.0,
                105.0, 110.0, 115.0, 120.0, 125.0, 130.0, 135.0, 140.0, 145.0, 150.0, 160.0,
                170.0, 180.0, 190.0, 200.0, 220.0, 250.0
        )));
        sizes.put(ShapeType.ROUND_BAR, roundBarSizes);

        // Square Bar
        Map<DimensionKey, List<BigDecimal>> squareBarSizes = new EnumMap<>(DimensionKey.class);
        squareBarSizes.put(DimensionKey.SIDE, numberListToBigDecimal(List.of(
                6.0, 8.0, 10.0, 12.0, 14.0, 15.0, 16.0, 18.0, 20.0, 22.0, 24.0, 25.0, 26.0,
                28.0, 30.0, 32.0, 35.0, 40.0, 42.0, 45.0, 50.0, 55.0, 60.0, 65.0, 70.0, 75.0,
                80.0, 85.0, 90.0, 95.0, 100.0, 110.0, 115.0, 120.0, 125.0, 130.0, 140.0, 150.0,
                160.0, 170.0, 180.0, 190.0, 200.0, 220.0, 250.0
        )));
        sizes.put(ShapeType.SQUARE_BAR, squareBarSizes);

        // L Profile
        Map<DimensionKey, List<BigDecimal>> lProfileSizes = new EnumMap<>(DimensionKey.class);
        lProfileSizes.put(DimensionKey.LEG_A, numberListToBigDecimal(List.of(
                20.0, 25.0, 30.0, 35.0, 40.0, 45.0, 50.0, 55.0, 60.0, 70.0, 80.0, 90.0,
                100.0, 120.0, 150.0, 200.0
        )));
        lProfileSizes.put(DimensionKey.LEG_B, numberListToBigDecimal(List.of(
                20.0, 25.0, 30.0, 35.0, 40.0, 45.0, 50.0, 55.0, 60.0, 70.0, 80.0, 90.0,
                100.0, 120.0, 150.0, 200.0
        )));
        lProfileSizes.put(DimensionKey.THICKNESS, numberListToBigDecimal(List.of(
                3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 10.0, 12.0, 15.0, 16.0, 18.0, 20.0, 24.0
        )));
        sizes.put(ShapeType.L_PROFILE, lProfileSizes);

        // T Profile
        Map<DimensionKey, List<BigDecimal>> tProfileSizes = new EnumMap<>(DimensionKey.class);
        tProfileSizes.put(DimensionKey.WIDTH, numberListToBigDecimal(List.of(
                20.0, 25.0, 30.0, 35.0, 40.0, 45.0, 50.0, 55.0, 60.0, 65.0, 70.0, 75.0,
                80.0, 90.0, 100.0
        )));
        tProfileSizes.put(DimensionKey.HEIGHT, numberListToBigDecimal(List.of(
                20.0, 25.0, 30.0, 35.0, 40.0, 45.0, 50.0, 55.0, 60.0, 65.0, 70.0, 75.0,
                80.0, 90.0, 100.0
        )));
        tProfileSizes.put(DimensionKey.THICKNESS, numberListToBigDecimal(List.of(
                3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 10.0, 12.0, 13.0, 15.0, 16.0, 17.0,
                18.0, 20.0, 24.0
        )));
        sizes.put(ShapeType.T_PROFILE, tProfileSizes);

        // U Profile
        Map<DimensionKey, List<BigDecimal>> uProfileSizes = new EnumMap<>(DimensionKey.class);
        uProfileSizes.put(DimensionKey.WIDTH, numberListToBigDecimal(List.of(
                50.0, 80.0, 100.0, 120.0, 160.0, 200.0, 240.0, 300.0
        )));
        uProfileSizes.put(DimensionKey.HEIGHT, numberListToBigDecimal(List.of(
                38.0, 45.0, 50.0, 55.0, 65.0, 75.0, 85.0, 100.0
        )));
        uProfileSizes.put(DimensionKey.THICKNESS, numberListToBigDecimal(List.of(
                3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 10.0, 12.0, 13.0, 15.0, 16.0, 17.0,
                18.0, 20.0, 24.0
        )));
        sizes.put(ShapeType.U_PROFILE, uProfileSizes);

        // Rectangular Tube
        Map<DimensionKey, List<BigDecimal>> rectTubeSizes = new EnumMap<>(DimensionKey.class);
        rectTubeSizes.put(DimensionKey.WIDTH, numberListToBigDecimal(List.of(
                20.0, 30.0, 40.0, 50.0, 60.0, 70.0, 80.0, 100.0, 120.0, 140.0, 150.0,
                160.0, 180.0, 200.0
        )));
        rectTubeSizes.put(DimensionKey.HEIGHT, numberListToBigDecimal(List.of(
                10.0, 20.0, 30.0, 40.0, 50.0, 60.0, 80.0, 100.0
        )));
        rectTubeSizes.put(DimensionKey.THICKNESS, numberListToBigDecimal(List.of(
                1.5, 2.0, 3.0, 4.0, 5.0, 6.0, 8.0, 10.0, 12.0
        )));
        sizes.put(ShapeType.RECTANGULAR_TUBE, rectTubeSizes);

        // Square Tube
        Map<DimensionKey, List<BigDecimal>> squareTubeSizes = new EnumMap<>(DimensionKey.class);
        squareTubeSizes.put(DimensionKey.SIDE, numberListToBigDecimal(List.of(
                15.0, 20.0, 25.0, 30.0, 40.0, 50.0, 60.0, 70.0, 80.0, 90.0, 100.0,
                110.0, 120.0, 130.0, 140.0, 150.0, 160.0, 180.0, 200.0, 220.0, 250.0,
                260.0, 300.0
        )));
        squareTubeSizes.put(DimensionKey.THICKNESS, numberListToBigDecimal(List.of(
                1.5, 2.0, 3.0, 4.0, 5.0, 6.0, 8.0, 10.0, 12.0
        )));
        sizes.put(ShapeType.SQUARE_TUBE, squareTubeSizes);

        // Round Tube
        Map<DimensionKey, List<BigDecimal>> roundTubeSizes = new EnumMap<>(DimensionKey.class);
        roundTubeSizes.put(DimensionKey.OUTER_DIAMETER, numberListToBigDecimal(List.of(
                17.1, 21.3, 26.9, 33.7, 42.4, 48.3, 60.3, 76.1, 88.9, 114.3
        )));
        roundTubeSizes.put(DimensionKey.THICKNESS, numberListToBigDecimal(List.of(
                1.5, 2.0, 2.3, 2.6, 2.9, 3.0, 3.2, 3.6, 4.0, 4.5, 5.0, 6.0, 6.3, 7.1,
                8.0, 10.0, 12.0
        )));
        sizes.put(ShapeType.ROUND_TUBE, roundTubeSizes);

        return sizes;
    }

    private List<BigDecimal> numberListToBigDecimal(List<Double> numbers) {
        return numbers.stream()
                .map(BigDecimal::valueOf)
                .toList();
    }

    public List<MaterialWithShapes> getMaterialsWithShapes(String language) {
        var bundle = loadedBundles.computeIfAbsent(language,
                l -> ResourceBundle.getBundle("materials", Locale.forLanguageTag(language)));

        List<Material> translatedMaterials = materials.stream()
                .map(material -> material.setName(bundle.getString("material." + material.key())))
                .toList();

        Map<String, String> dimensionTranslations = new HashMap<>();
        Map<String, String> shapeTranslations = new HashMap<>();

        try {
            shapes.forEach(shape -> {
                shapeTranslations.put(shape.type.key, bundle.getString("shape." + shape.type.key));
                shape.dimensions.forEach(dim ->
                        dimensionTranslations.putIfAbsent(dim.key.key, bundle.getString("dimension." + dim.key.key)));
            });
        } catch (MissingResourceException e) {
            // Fallback to default names if translations not available
        }

        return translatedMaterials.stream()
                .map(material -> {
                    boolean isSteel = material.key().startsWith("steel.");

                    List<ShapeWithDimensions> availableShapes = shapes.stream()
                            .filter(shape -> !shape.isRestrictedToSteels() || isSteel)
                            .map(shape -> {
                                String translatedName = shapeTranslations.getOrDefault(shape.type.key, shape.name());

                                List<DimensionWithValues> translatedDimensions = shape.dimensions.stream()
                                        .map(dim -> {
                                            String translatedDimName = dimensionTranslations.getOrDefault(dim.key.key, dim.name);

                                            // Get standard values for this dimension
                                            List<BigDecimal> standardValues = standardSizes
                                                    .getOrDefault(shape.type, Collections.emptyMap())
                                                    .getOrDefault(dim.key, Collections.emptyList());

                                            return new DimensionWithValues(
                                                    dim.key,
                                                    translatedDimName,
                                                    standardValues
                                            );
                                        })
                                        .toList();

                                return new ShapeWithDimensions(
                                        shape.type,
                                        translatedName,
                                        translatedDimensions,
                                        shape.isRestrictedToSteels()
                                );
                            })
                            .toList();

                    return new MaterialWithShapes(material, availableShapes);
                })
                .toList();
    }

    public Material getMaterialByKey(String key, String language) {
        var bundle = loadedBundles.computeIfAbsent(language,
                l -> ResourceBundle.getBundle("materials", Locale.forLanguageTag(language)));
        return materials.stream()
                .filter(m -> m.key.equals(key))
                .findAny()
                .map(m -> m.setName(bundle.getString("material." + m.key)))
                .orElseThrow();
    }

    public Shape getShapeByKey(ShapeType shape) {
        return shapes.stream()
                .filter(s -> s.type.equals(shape))
                .findAny()
                .orElseThrow();
    }

    public String getShapeName(ShapeType shape, String language) {
        var bundle = loadedBundles.computeIfAbsent(language,
                l -> ResourceBundle.getBundle("materials", Locale.forLanguageTag(language)));
        return bundle.getString("shape." + shape.key);
    }

    public String getDimensionName(DimensionKey key, String language) {
        var bundle = loadedBundles.computeIfAbsent(language,
                l -> ResourceBundle.getBundle("materials", Locale.forLanguageTag(language)));
        return bundle.getString("dimension." + key.key);
    }

    public enum DimensionKey {
        THICKNESS("thickness"),
        DIAMETER("diameter"),
        SIDE("side"),
        LEG_A("legA"),
        LEG_B("legB"),
        WIDTH("width"),
        HEIGHT("height"),
        OUTER_DIAMETER("od"),
        LENGTH("length");

        public final String key;

        DimensionKey(String key) {
            this.key = key;
        }

        public static DimensionKey fromKey(String key) {
            return Arrays.stream(values())
                    .filter(type -> type.key.equals(key))
                    .findFirst()
                    .orElseThrow(() -> new IllegalArgumentException("Unknown dimension key: " + key));
        }
    }

    public enum ShapeType {
        PLATE("plate"),
        PLATE_BLACK("plate-black"),
        PLATE_GALVANIZED("plate-galvanized"),
        STRIP_ROLL("strip-roll"),
        ROUND_BAR("round-bar"),
        SQUARE_BAR("square-bar"),
        L_PROFILE("l-profile"),
        T_PROFILE("t-profile"),
        U_PROFILE("u-profile"),
        RECTANGULAR_TUBE("rect-tube"),
        SQUARE_TUBE("square-tube"),
        ROUND_TUBE("round-tube");

        public final String key;

        ShapeType(String key) {
            this.key = key;
        }

        public String getKey() {
            return key;
        }

    }

    public record DimensionWithValues(
            DimensionKey key,
            String name,
            List<BigDecimal> standardValues
    ) {}

    public record ShapeWithDimensions(
            ShapeType type,
            String name,
            List<DimensionWithValues> dimensions,
            boolean isRestrictedToSteels
    ) {
        public String key() {
            return type.key;
        }
    }

    public record MaterialWithShapes(
            Material material,
            List<ShapeWithDimensions> availableShapes
    ) {}

    public record Dimension(DimensionKey key, String name) {
        public Dimension(DimensionKey key) {
            this(key, null);
        }
    }

    public record Shape(ShapeType type, String name, List<Dimension> dimensions, boolean isRestrictedToSteels) {
    }

    public record Material(String key, String name, BigDecimal density) {
        public Material setName(String translatedName) {
            return new Material(this.key, translatedName, this.density);
        }
    }
}