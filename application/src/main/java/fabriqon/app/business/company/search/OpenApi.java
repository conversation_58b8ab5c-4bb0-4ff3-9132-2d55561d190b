package fabriqon.app.business.company.search;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.type.TypeReference;
import fabriqon.app.common.model.Address;
import fabriqon.misc.Json;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class OpenApi {

    @Value("${openapi.key:DUMMY}")
    private String openapiKey;

    public List<CompanySearchResult> searchByName(String name) {
        var response = WebClient.create("https://api.openapi.ro/v1/companies/search")
                .post()
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .header("x-api-key", openapiKey)
                .bodyValue(Json.write(Map.of("q", name)))
                .retrieve()
                .toEntity(Map.class)
                .block();
        if (response != null && response.getStatusCode().is2xxSuccessful()) {
            return Json.read(Json.write(response.getBody().get("data")), new TypeReference<List<Company>>() {})
                    .stream()
                    .map(c -> new CompanySearchResult(c.denumire, c.cif, null, new Address(null, null,
                            null, c.judet, null, null, null, null)
                    ))
                    .toList();
        } else {
            log.error("Can't retrieve company info for [{}], error from VIES [{}] [{}]", name, response.getStatusCode(), response.getBody());
            throw new RuntimeException("Can't retrieve company info from VIES");
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    record Company(String cif, String denumire, String judet) { }

}
