package fabriqon.app.business.company.search;

import fabriqon.app.common.model.Address;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.StringUtil;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

import static fabriqon.jooq.JooqTextSearchFunctions.textSearch;
import static fabriqon.jooq.classes.Tables.RO_COMPANIES;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Slf4j
@Component
public class CompanySearchService {

    private final DSLContext db;
    private final VIES vies;
    private final OpenApi openApi;

    @Autowired
    public CompanySearchService(DSLContext db, VIES vies, OpenApi openApi) {
        this.db = db;
        this.vies = vies;
        this.openApi = openApi;
    }

    public List<CompanySearchResult> search(String query) {
        try {
            var res = db.selectFrom(RO_COMPANIES)
                    .where(textSearch(RO_COMPANIES.TEXT_SEARCH, query))
                    .limit(20)
                    .fetch()
                    .map(r -> new CompanySearchResult(r.getNume(), r.getCui(), r.getNrRegCom(),
                            new Address(null, (isNotBlank(r.getJudet()) && !r.getJudet().equals("0")) || isNotBlank(r.getCodPostal()) ? "RO" : null, r.getLocalitate(), r.getJudet(),
                                    r.getStrada() + " nr. " + r.getNumar()
                                            + (StringUtil.isNotBlank(r.getSector()) ? " sector " + r.getSector() : "")
                                            + (StringUtil.isNotBlank(r.getBloc()) ? " bloc " + r.getBloc() : "")
                                            + (StringUtil.isNotBlank(r.getScara()) ? " sc. " + r.getScara() : "")
                                            + (StringUtil.isNotBlank(r.getEtaj()) ? " et. " + r.getEtaj() : "")
                                            + (StringUtil.isNotBlank(r.getApartament()) ? " ap. " + r.getApartament() : ""),
                                    null,
                                    r.getCodPostal(),
                                    Set.of(Address.Type.BILLING)
                            )));
            if (!res.isEmpty()) {
                return res;
            }
            if (StringUtils.isNumeric(query)) {
                return vies.searchByVat(query).stream().toList();
            }
            if (query.startsWith("RO") && StringUtils.isNumeric(query.substring(2))) {
                return vies.searchByVat(query.substring(2)).stream().toList();
            } else {
                //search openapi and then get vies
                return openApi.searchByName(query)
                        .stream()
                        .map(company -> vies.searchByVat(company.taxIdentificationNumber())
                                .map(r -> new CompanySearchResult(company.name(), company.taxIdentificationNumber(), null,
                                        new Address(null, "RO", r.address().city(),
                                                //we get the county from openapi and we save it
                                                company.address().state(),
                                                r.address().address1(), r.address().address2(), r.address().zip(), r.address().types())))
                                .orElse(company)
                        )
                        .toList();
            }
        } catch (Exception e) {
            log.error("Couldn't search for company", e);
            throw new RuntimeException("Couldn't search for company.");
        }
    }

}
