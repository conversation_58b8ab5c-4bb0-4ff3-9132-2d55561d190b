package fabriqon.app.business.company.search;

import fabriqon.app.common.model.Address;

import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static java.util.Collections.emptySet;

public class AddressExtractor {

    private static final Pattern CITY_PATTERN = Pattern.compile("(ORŞ\\.|MUNICIPIUL)\\s*([\\S]*)");
    private static final Pattern ZIPCODE_PATTERN = Pattern.compile("(\\d{4,8})");

    public static Address extract(String country, String address) {
        return new Address(null, country, getCity(address), null, address, null, getZipCode(address), Set.of(Address.Type.BILLING));
    }

    private static String getCity(String address) {
        return value(CITY_PATTERN.matcher(address), 2);
    }

    private static String getZipCode(String address) {
        return value(ZIPCODE_PATTERN.matcher(address), 1);
    }

    private static String value(Matcher m, int g) {
        if (m.find()) {
            return m.group(g);
        }
        return null;
    }

}
