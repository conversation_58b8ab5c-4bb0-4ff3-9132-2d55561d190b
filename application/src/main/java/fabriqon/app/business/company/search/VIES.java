package fabriqon.app.business.company.search;

import fabriqon.misc.Json;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.Map;
import java.util.Optional;

import static org.apache.commons.lang3.BooleanUtils.isTrue;

@Slf4j
@Component
public class VIES {

    public Optional<CompanySearchResult> searchByVat(String vatNumber) {
        var response = WebClient.create("https://ec.europa.eu/taxation_customs/vies/rest-api/check-vat-number")
                .post()
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .bodyValue(Json.write(Map.of(
                        "countryCode", "RO",
                        "vatNumber", vatNumber
                        )))
                .retrieve()
                .toEntity(Map.class)
                .block();
        if (response != null && response.getStatusCode().is2xxSuccessful()) {
            if (isTrue((Boolean) response.getBody().get("valid"))) {
                return Optional.of(new CompanySearchResult(
                        (String) response.getBody().get("name"),
                        (String) response.getBody().get("vatNumber"),
                        null,
                        AddressExtractor.extract("RO", (String) response.getBody().get("address"))
                ));
            } else {
                log.debug("Can't retrieve company info for [{}], error from VIES [{}] [{}]", vatNumber, response.getStatusCode(), response.getBody());
                return Optional.empty();
            }
        } else {
            log.error("Can't retrieve company info for [{}], error from VIES [{}] [{}]", vatNumber, response.getStatusCode(), response.getBody());
            throw new RuntimeException("Can't retrieve company info from VIES");
        }
    }
}
