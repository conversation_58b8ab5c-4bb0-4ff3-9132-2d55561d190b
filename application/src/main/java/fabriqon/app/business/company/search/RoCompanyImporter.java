package fabriqon.app.business.company.search;

import fabriqon.misc.CsvUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVRecord;
import org.jooq.DSLContext;
import org.jooq.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.FileInputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import static fabriqon.jooq.JooqTextSearchFunctions.toTsVector;
import static fabriqon.jooq.classes.Tables.JOB_STATUS;
import static fabriqon.jooq.classes.Tables.RO_COMPANIES;
import static org.apache.poi.util.StringUtil.isNotBlank;

@Component
@Transactional
@Slf4j
public class RoCompanyImporter {

    private final DSLContext db;

    @Autowired
    public RoCompanyImporter(DSLContext db) {
        this.db = db;
    }

    @PostConstruct
    public void postConstruct() {
        if (alreadyRun()) {
            return;
        }
        Executors.newSingleThreadExecutor().execute(() -> {
            try {
                var file = new File("/data/ro_companies/date_identificare_platitori_2023_csv.zip");
                if (!file.exists()) {
                    log.info("import source [{}] not available. returning", file.getAbsolutePath());
                    return;
                }
                ZipInputStream zipInputStream = new ZipInputStream(new FileInputStream(file));
                for (ZipEntry entry; (entry = zipInputStream.getNextEntry()) != null; ) {
                    log.info("reading [{}]", entry.getName());
                    var batch = new LinkedList<Query>();
                    var processedCount = new CsvUtils().parseAndProcess(zipInputStream, StandardCharsets.UTF_8, "|",
                            record -> {
                                try {
                                    batch.add(db.insertInto(RO_COMPANIES)
                                                    .set(RO_COMPANIES.NUME, record.get(Headers.DENUMIRE.name()))
                                                    .set(RO_COMPANIES.CUI, record.get(Headers.COD_FISCAL.name()))
                                                    .set(RO_COMPANIES.NR_REG_COM, isNotBlank(record.get(Headers.JUDET_COMERT.name())) ? record.get(Headers.JUDET_COMERT.name()) + "/" + record.get(Headers.NR_COMERT.name()) + "/" + record.get(Headers.AN_COMERT.name()) : null)
                                                    .set(RO_COMPANIES.JUDET, record.get(Headers.JUDET.name()))
                                                    .set(RO_COMPANIES.LOCALITATE, record.get(Headers.LOCALITATE.name()))
                                                    .set(RO_COMPANIES.STRADA, record.get(Headers.STRADA.name()))
                                                    .set(RO_COMPANIES.NUMAR, record.get(Headers.NR.name()))
                                                    .set(RO_COMPANIES.SECTOR, record.get(Headers.SECTOR.name()))
                                                    .set(RO_COMPANIES.BLOC, record.get(Headers.BLOC.name()))
                                                    .set(RO_COMPANIES.SCARA, record.get(Headers.SCARA.name()))
                                                    .set(RO_COMPANIES.ETAJ, record.get(Headers.ETAJ.name()))
                                                    .set(RO_COMPANIES.APARTAMENT, record.get(Headers.AP.name()))
                                                    .set(RO_COMPANIES.COD_POSTAL, record.get(Headers.COD_POSTAL.name()))
                                                    .set(RO_COMPANIES.TEXT_SEARCH, toTsVector(textSearch(record))));
                                    if (batch.size() == 500) {
                                        db.batch(batch).execute();
                                        batch.clear();
                                    }
                                } catch (Exception e) {
                                    log.error("exception for record [{}]", record, e);
                                }
                            });
                    db.batch(batch).execute();
                    log.info("got [{}] records ", processedCount);
                }
                db.insertInto(JOB_STATUS)
                        .set(JOB_STATUS.NAME, this.getClass().getName())
                        .set(JOB_STATUS.STATUS, true)
                        .execute();
            } catch (Exception e) {
                log.error("exception", e);
            }
        });
    }

    private String textSearch(CSVRecord record) {
        return String.join(" ",
                        record.get(Headers.DENUMIRE.name()),
                        record.get(Headers.COD_FISCAL.name()),
                        isNotBlank(record.get(Headers.JUDET_COMERT.name())) ? record.get(Headers.JUDET_COMERT.name()) + "/" + record.get(Headers.NR_COMERT.name()) + "/" + record.get(Headers.AN_COMERT.name()) : "")
                .replaceAll("'", "");
    }

    private boolean alreadyRun() {
        return db.fetchCount(JOB_STATUS, JOB_STATUS.NAME.eq(this.getClass().getName()), JOB_STATUS.STATUS.isTrue()) > 0;
    }

    enum Headers {
        DENUMIRE, COD_FISCAL, JUDET_COMERT, NR_COMERT, AN_COMERT, LOCALITATE, JUDET, STRADA, NR, SECTOR, BLOC, SCARA, ETAJ, AP, DETALII_ADRESA, COD_POSTAL, TELEFON
    }

}
