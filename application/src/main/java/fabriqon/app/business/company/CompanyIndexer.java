package fabriqon.app.business.company;

import fabriqon.app.common.model.Company;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;

import static fabriqon.jooq.JooqTextSearchFunctions.toTsVector;
import static fabriqon.jooq.classes.Tables.COMPANY;
import static fabriqon.jooq.classes.Tables.JOB_STATUS;

@Component
@Transactional
public class CompanyIndexer {

    private final DSLContext db;
    private final CompanyService companyService;

    @Autowired
    public CompanyIndexer(DSLContext db, CompanyService companyService) {
        this.db = db;
        this.companyService = companyService;
    }

    @PostConstruct
    public void postConstruct() {
        if (alreadyRun()) {
            return;
        }
        db.selectFrom(COMPANY)
                .fetchInto(Company.class)
                .forEach(c -> db.update(COMPANY)
                        .set(COMPANY.TEXT_SEARCH, toTsVector(companyService.textSearchValues(c.name(), c.details())))
                        .where(COMPANY.ID.eq(c.id()))
                        .execute());
        db.insertInto(JOB_STATUS)
                .set(JOB_STATUS.NAME, this.getClass().getName())
                .set(JOB_STATUS.STATUS, true)
                .execute();
    }

    private boolean alreadyRun() {
        return db.fetchCount(JOB_STATUS, JOB_STATUS.NAME.eq(this.getClass().getName()), JOB_STATUS.STATUS.isTrue()) > 0;
    }

}
