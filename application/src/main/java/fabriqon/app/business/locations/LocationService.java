package fabriqon.app.business.locations;

import fabriqon.app.common.model.Location;
import fabriqon.misc.Json;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

import static fabriqon.jooq.classes.Tables.LOCATION;
import static java.time.Instant.now;

@Component
@Transactional
public class LocationService {

    private final DSLContext db;

    @Autowired
    public LocationService(final DSLContext db) {
        this.db = db;
    }

    public Location create(UUID ownerId, String name) {
        final var id = UUID.randomUUID();
        db.insertInto(LOCATION)
                .set(LOCATION.ID, id)
                .set(LOCATION.OWNER_ID, ownerId)
                .set(LOCATION.DETAILS, JSONB.valueOf(Json.write(new Location.Details(name))))
                .execute();

        return new Location(id, now(), now(), false, ownerId,
                new Location.Details(name));
    }

}
