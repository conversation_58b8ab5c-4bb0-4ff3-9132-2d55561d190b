package fabriqon.app;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(exclude = {org.springframework.boot.autoconfigure.session.SessionAutoConfiguration.class})
@ComponentScan(basePackages = {"fabriqon"})
@EnableScheduling
public class FabriqonApplication {

    public static void main(String[] args) {
        SpringApplication.run(FabriqonApplication.class, args);
    }

}
