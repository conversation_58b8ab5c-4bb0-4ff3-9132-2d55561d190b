package fabriqon.logback;

import ch.qos.logback.classic.pattern.ExtendedThrowableProxyConverter;
import ch.qos.logback.classic.pattern.MessageConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.classic.spi.IThrowableProxy;
import com.fasterxml.jackson.core.JsonGenerator;
import fabriqon.misc.SensitiveValues;
import net.logstash.logback.LogstashFormatter;
import net.logstash.logback.composite.AbstractCompositeJsonFormatter;
import net.logstash.logback.composite.JsonProvider;
import net.logstash.logback.composite.JsonWritingUtils;
import net.logstash.logback.composite.loggingevent.MessageJsonProvider;
import net.logstash.logback.composite.loggingevent.RawMessageJsonProvider;
import net.logstash.logback.composite.loggingevent.StackTraceJsonProvider;
import net.logstash.logback.encoder.LogstashEncoder;
import org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter;
import org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter;

import java.io.IOException;
import java.util.List;
import java.util.function.Function;
import java.util.regex.Pattern;

public final class SensitiveValuesMasking {

    private static final Pattern POSSIBLE_CARD_NUMBERS = Pattern.compile(
            "(?:^|[^\\d\\w])((?:(?:\\d{8,})|(?:\\d{4}-){2,4}|(?:\\d{4}\\s+){2,4})\\d{2,4})(?:[^\\d\\w]|$)",
            Pattern.CASE_INSENSITIVE
    );
    private static final Pattern POSSIBLE_TOKEN_VALUES = Pattern.compile(
            //there is a dot at the end of the 2nd capturing group for this case compared to the card pattern;
            // tokens are masked by using the SensitiveStringValues class but in the HTTP request we identify a token
            // property by the pattern 'token_value.token_property' so we try to match for 'number.'
            "(?:^|[^\\d\\w])((?:(?:\\d{8,})|(?:\\d{4}-){2,4}|(?:\\d{4}\\s+){2,4})\\d{2,4}\\.)(?:[^\\d\\w]|$)",
            Pattern.CASE_INSENSITIVE
    );
    // https://www.iana.org/assignments/http-authschemes/http-authschemes.xhtml
    private static final Pattern HTTP_AUTHENTICATION_SCHEMES = Pattern.compile(
            "(?:Basic|Bearer|Digest|HOBA|Mutual|Negotiate|SCRAM-SHA-1|SCRAM-SHA-256|vapid|OAuth|PA)\\s+([^\\s'\"]+)",
            Pattern.CASE_INSENSITIVE
    );

    private static final List<Function<String, String>> MASKS = List.of(
            message -> SensitiveValues.mask(message, POSSIBLE_CARD_NUMBERS, match -> SensitiveValues.mask(match, 4)),
            message -> SensitiveValues.mask(message, POSSIBLE_TOKEN_VALUES, match -> SensitiveValues.mask(match, 4)),
            message -> SensitiveValues.mask(message, HTTP_AUTHENTICATION_SCHEMES)
    );

    public static String mask(final String clear) {
        var masked = clear;
        for (final Function<String, String> mask : MASKS) {
            masked = mask.apply(masked);
        }
        return masked;
    }

    public static final class Messages extends MessageConverter {

        @Override
        public String convert(final ILoggingEvent event) {
            return mask(super.convert(event));
        }

    }

    public static final class Throwables extends WhitespaceThrowableProxyConverter {

        @Override
        protected String throwableProxyToString(IThrowableProxy tp) {
            return mask(super.throwableProxyToString(tp));
        }

    }

    public static final class ExtendedThrowables extends ExtendedWhitespaceThrowableProxyConverter {

        @Override
        protected String throwableProxyToString(IThrowableProxy tp) {
            return mask(super.throwableProxyToString(tp));
        }

    }

    public static final class Logstash extends LogstashEncoder {

        @Override
        protected AbstractCompositeJsonFormatter<ILoggingEvent> createFormatter() {
            final var formatter = new LogstashFormatter(this);
            final var providers = formatter.getProviders();
            for (final JsonProvider<ILoggingEvent> provider : List.copyOf(providers.getProviders())) {
                if (provider instanceof MessageJsonProvider) {
                    providers.removeProvider(provider);
                    providers.addMessage(new MessageJsonProvider() {
                        @Override
                        public void writeTo(JsonGenerator generator, ILoggingEvent event) throws IOException {
                            JsonWritingUtils.writeStringField(generator, getFieldName(), mask(event.getFormattedMessage()));
                        }
                    });
                } else if (provider instanceof RawMessageJsonProvider) {
                    providers.removeProvider(provider);
                    providers.addRawMessage(new RawMessageJsonProvider() {
                        @Override
                        public void writeTo(JsonGenerator generator, ILoggingEvent event) throws IOException {
                            JsonWritingUtils.writeStringField(generator, getFieldName(), mask(event.getMessage()));
                        }
                    });
                } else if (provider instanceof StackTraceJsonProvider) {
                    ((StackTraceJsonProvider) provider).setThrowableConverter(new ExtendedThrowableProxyConverter() {
                        @Override
                        protected String throwableProxyToString(IThrowableProxy tp) {
                            return mask(super.throwableProxyToString(tp));
                        }
                    });
                }
            }
            return formatter;
        }

    }

}
