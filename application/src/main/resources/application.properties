spring.servlet.multipart.max-file-size=30MB
spring.servlet.multipart.max-request-size=30MB
spring.codec.max-in-memory-size=30MB

spring.security.oauth2.resourceserver.jwt.issuer-uri=https://ezbizdev.eu.auth0.com/
spring.security.oauth2.resourceserver.jwt.jws-algorithms=RS256

security.auth0.audience=http://localhost:8080

spring.datasource.url=**************************************
spring.datasource.username=postgres
spring.datasource.password=password

spring.mail.host=localhost
spring.mail.username=
spring.mail.password=
spring.mail.port=1025

spring.liquibase.change-log=classpath:/db/changelog/db.changelog-master.xml

management.endpoints.web.exposure.include=health,metrics,info

#logging
logging.level.fabriqon=DEBUG
logging.level.ai.timefold.solver=INFO

fabriqon.fabrication.url=https://fabriqon-app-prod.web.app

fabriqon.html2pdf.url=http://localhost:9999/convert

auth0.management.url=https://ezbizdev.eu.auth0.com

#fabriqon.email.sender=mailgun
#fabriqon.email.sender.mailgun.privatekey=DUMMY

fabriqon.reporting.overview.url=https://lookerstudio.google.com/embed/reporting/0795ae40-1ddb-4f87-867f-c8a5d68434ad/page/OZRvD
fabriqon.reporting.production.url=https://lookerstudio.google.com/embed/reporting/0795ae40-1ddb-4f87-867f-c8a5d68434ad/page/OZRvD

efactura.service.url=https://api.anaf.ro/test/FCTEL/rest/