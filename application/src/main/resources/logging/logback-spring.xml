<configuration>

    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <springProperty name="encoding.json.enabled" source="logging.encoding.json.enabled" defaultValue="true"/>

    <conversionRule conversionWord="m" converterClass="fabriqon.logback.SensitiveValuesMasking$Messages" />
    <conversionRule conversionWord="msg" converterClass="fabriqon.logback.SensitiveValuesMasking$Messages" />
    <conversionRule conversionWord="message" converterClass="fabriqon.logback.SensitiveValuesMasking$Messages" />

    <conversionRule conversionWord="wex" converterClass="fabriqon.logback.SensitiveValuesMasking$Throwables" />
    <conversionRule conversionWord="wEx" converterClass="fabriqon.logback.SensitiveValuesMasking$ExtendedThrowables" />

    <if condition='Boolean.valueOf(property("encoding.json.enabled"))'>
        <then>
            <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
                <encoder class="fabriqon.misc.SensitiveValuesMasking$Logstash"/>
            </appender>
            <logger name="jsonLogger" additivity="false" level="DEBUG">
                <appender-ref ref="CONSOLE"/>
            </logger>
        </then>
        <else>
            <include resource="org/springframework/boot/logging/logback/console-appender.xml" />
        </else>
    </if>

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>

</configuration>
