package fabriqon.e2e;

import fabriqon.app.business.accounts.Account;
import fabriqon.app.business.financial.SystemCurrencies;
import fabriqon.app.common.model.Address;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.common.model.Money;
import fabriqon.misc.Json;
import org.jooq.JSONB;
import org.jooq.impl.DSL;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.time.LocalTime;
import java.util.*;

import static fabriqon.e2e.data.Utils.mediumRandomInt;
import static fabriqon.jooq.classes.Tables.ACCOUNT;
import static fabriqon.jooq.classes.Tables.INVENTORY_UNIT;

public class SetupUtils {

    public static final String DATASOURCE = "datasource";

    private static final ThreadLocal<Map<String, Object>> context = ThreadLocal.withInitial(HashMap::new);

    public static DataSource getDataSource() {
        return (DataSource) context.get().get(DATASOURCE);
    }

    public static void addContext(final String key, final Object value) {
        context.get().put(key, value);
    }

    public static void clearContext(final String key) {
        context.get().remove(key);
    }

    //----- test utilities -----//

    public static final String INVENTORY_HQ_UNIT = "HQ unit";
    public static final String INVENTORY_SUB_UNIT = "Sub unit";

    public static String randomAccount() throws SQLException {
        var accountId = UUID.randomUUID();
        var hqInventoryUnit = UUID.randomUUID();
        var subInventoryUnit = UUID.randomUUID();

        DSL.using(getDataSource().getConnection()).insertInto(ACCOUNT)
                .set(ACCOUNT.ID, accountId)
                .set(ACCOUNT.NAME, "MyAccount")
                .set(ACCOUNT.INFORMATION, JSONB.jsonb(Json.write(new Account.Information(200, true,
                        "J/12/2023/"+ mediumRandomInt(), true,
                        "RO"+ mediumRandomInt(), true,
                        new Address("default", "RO", "Cluj-Napoca", "Cluj", "Calea Turzii 150", null, "400000", Set.of(Address.Type.BILLING)), true,
                        "+40 123123", true, "<EMAIL>", true,
                        List.of(), List.of()))))
                .set(ACCOUNT.SETTINGS, JSONB.jsonb(Json.write(new Account.Settings(
                        new Account.Settings.General(
                                new Account.Settings.General.InventoryAccountingSettings(Account.Settings.General.InventoryAccountingSettings.Method.FIFO,
                                        new Account.Settings.General.InventoryAccountingSettings.UnitDesignations(hqInventoryUnit)),
                                14,
                                Locale.US.getLanguage(),
                                Currency.getInstance("RON"),
                                TimeZone.getTimeZone("Europe/Bucharest")
                        ),
                        List.of(MeasurementUnit.PIECE, MeasurementUnit.M, MeasurementUnit.CM),
                        new BigDecimal("0.19"),
                        new Account.Settings.Manufacturing(LocalTime.of(8, 0), LocalTime.of(17, 0),
                                List.of(1, 2, 3, 4, 5), new Money(0, SystemCurrencies.RON), new Money(0, SystemCurrencies.RON))
                ))))
                .set(ACCOUNT.LOGO, "")
                .execute();

        createInventoryUnit(hqInventoryUnit, accountId, INVENTORY_HQ_UNIT);
        createInventoryUnit(subInventoryUnit, accountId, INVENTORY_SUB_UNIT);

        return accountId.toString();
    }

    private static UUID createInventoryUnit(UUID id, UUID accountId, String name) throws SQLException {
        DSL.using(getDataSource().getConnection())
                .insertInto(INVENTORY_UNIT)
                .set(INVENTORY_UNIT.ID, id)
                .set(INVENTORY_UNIT.OWNER_ID, accountId)
                .set(INVENTORY_UNIT.NAME, name)
                .execute();
        return id;
    }

}
