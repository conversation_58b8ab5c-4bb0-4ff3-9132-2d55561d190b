package fabriqon.e2e.tests;

import fabriqon.e2e.IntegrationTest;
import fabriqon.e2e.dsl.Account;
import org.junit.Test;
import org.springframework.http.MediaType;

import static org.hamcrest.Matchers.is;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class AccountSettingsTests extends IntegrationTest {

    @Test
    public void readsAndUpdatesSettings() throws Exception {
        Account account = setup.account();

        mvc.perform(get("/account/settings")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.enabledMeasurementUnits.[0]", is("PIECE")))
                .andExpect(jsonPath("$.enabledMeasurementUnits.[1]", is("M")));

        mvc.perform(post("/account/settings/update")
                        .header("Authorization", "Bearer " + account.accountId)
                        .content("{\"general\": {\"inventoryAccountingSettings\": {\"method\": \"WEIGHTED_AVERAGE\"}, \"defaultDeliveryTimeForSalesOrders\": 14, \"defaultLeadTimeForPurchaseOrders\": 14, \"defaultLanguage\": \"en\", \"defaultCurrency\": \"RON\"}, \"enabledMeasurementUnits\": [\"PIECE\", \"M\", \"MM\"], \"taxRates\": {\"purchase_order\": 19, \"sales_order\": 19}}")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        mvc.perform(get("/account/settings")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.enabledMeasurementUnits.[0]", is("PIECE")))
                .andExpect(jsonPath("$.enabledMeasurementUnits.[1]", is("M")))
                .andExpect(jsonPath("$.enabledMeasurementUnits.[2]", is("MM")));
    }

}
