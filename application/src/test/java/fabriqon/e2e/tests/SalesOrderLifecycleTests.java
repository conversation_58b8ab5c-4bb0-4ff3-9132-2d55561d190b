package fabriqon.e2e.tests;

import fabriqon.app.business.goods.RequiredMaterial;
import fabriqon.app.business.sales.SalesOrder;
import fabriqon.e2e.IntegrationTest;
import fabriqon.e2e.data.TestValueConstants;
import fabriqon.e2e.dsl.Account;
import fabriqon.misc.Json;
import org.junit.Test;
import org.springframework.http.MediaType;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import static org.junit.Assert.assertEquals;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class SalesOrderLifecycleTests extends IntegrationTest {

    @Test
    public void orderTransitionsToPickingPackingOnCreationWhenAllItemsAvailable() throws Exception {
        Account account = setupAccountWithProduct(BigDecimal.valueOf(10)); // Sufficient stock

        // Create order with quantity that can be fully reserved
        account.sale().withQuantity(BigDecimal.valueOf(5)).withSellPrice(321).create();

        var order = account.sale().order(account.sale().salesOrderId);
        assertEquals(SalesOrder.Status.PICKING_PACKING, order.status());
        assertEquals(0, order.items().get(0).reserved().compareTo(BigDecimal.valueOf(5.0)));
    }

    @Test
    public void orderStaysInSubmittedWhenInsufficientStock() throws Exception {
        Account account = setupAccountWithProduct(BigDecimal.valueOf(3)); // Insufficient stock

        // Create order with quantity that cannot be fully reserved
        account.sale().withQuantity(BigDecimal.valueOf(5)).withSellPrice(321).create();

        var order = account.sale().order(account.sale().salesOrderId);
        assertEquals(SalesOrder.Status.SUBMITTED, order.status());
        assertEquals(0, order.items().get(0).reserved().compareTo(BigDecimal.valueOf(3.0))); // Only partial reservation
    }

    @Test
    public void orderTransitionsToPickingPackingWhenQuantityReducedInUpdate() throws Exception {
        Account account = setupAccountWithProduct(BigDecimal.valueOf(3)); // Limited stock

        // Create order with insufficient stock
        account.sale().withQuantity(BigDecimal.valueOf(5)).withSellPrice(321).create();
        String orderId = account.sale().salesOrderId;

        var order = account.sale().order(orderId);
        assertEquals(SalesOrder.Status.SUBMITTED, order.status());

        // Update order to reduce quantity to available stock
        mvc.perform(post("/sales/orders/" + orderId + "/update")
                        .content("{\"items\":[{\"productId\":\"" + account.good().goodId + "\",\"quantity\":3,\"price\":{\"amount\":321,\"currency\":\"RON\"}}]}")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        order = account.sale().order(orderId);
        assertEquals(SalesOrder.Status.PICKING_PACKING, order.status());
        assertEquals(0, order.items().get(0).reserved().compareTo(BigDecimal.valueOf(3.0)));
    }

    @Test
    public void orderTransitionsFromPickingPackingWhenQuantityIncreasedInUpdate() throws Exception {
        Account account = setupAccountWithProduct(BigDecimal.valueOf(5)); // Sufficient stock initially

        // Create order that goes to PICKING_PACKING
        account.sale().withQuantity(BigDecimal.valueOf(5)).withSellPrice(321).create();
        String orderId = account.sale().salesOrderId;

        var order = account.sale().order(orderId);
        assertEquals(SalesOrder.Status.PICKING_PACKING, order.status());

        // Update order to increase quantity beyond available stock
        mvc.perform(post("/sales/orders/" + orderId + "/update")
                        .content("{\"items\":[{\"productId\":\"" + account.good().goodId + "\",\"quantity\":10,\"price\":{\"amount\":321,\"currency\":\"RON\"}}]}")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        order = account.sale().order(orderId);
        assertEquals(SalesOrder.Status.SUBMITTED, order.status());
        assertEquals(0, order.items().get(0).reserved().compareTo(BigDecimal.valueOf(5.0))); // Only what's available
    }

    @Test
    public void orderTransitionsToPickingPackingAfterManufacturing() throws Exception {
        Account account = setupAccountWithProduct(BigDecimal.valueOf(2)); // Insufficient stock

        // Create order with insufficient stock
        account.sale().withQuantity(BigDecimal.valueOf(5)).withSellPrice(321).create();
        String orderId = account.sale().salesOrderId;

        var order = account.sale().order(orderId);
        assertEquals(SalesOrder.Status.SUBMITTED, order.status());

        // Order manufacturing for missing products
        account.sale().manufactureMissing(account.good().goodId, BigDecimal.valueOf(3));

        order = account.sale().order(orderId);
        assertEquals(SalesOrder.Status.PROCESSING, order.status());

        // Complete manufacturing
        account.manufacturing().withManufacturingOrderId(account.sale().manufacturingOrderId).completeTask();

        order = account.sale().order(orderId);
        assertEquals(SalesOrder.Status.PICKING_PACKING, order.status());
        assertEquals(0, order.items().get(0).reserved().compareTo(BigDecimal.valueOf(5.0)));
    }

    @Test
    public void orderTransitionsToPickingPackingWhenInventoryAdded() throws Exception {
        Account account = setupAccountWithProduct(BigDecimal.valueOf(2)); // Insufficient stock

        // Create order with insufficient stock
        account.sale().withQuantity(BigDecimal.valueOf(5)).withSellPrice(321).create();
        String orderId = account.sale().salesOrderId;

        var order = account.sale().order(orderId);
        assertEquals(SalesOrder.Status.SUBMITTED, order.status());

        // Add more inventory to make order fully reservable
        account.inventory()
                .withGood(account.good().goodId)
                .withQuantity(BigDecimal.valueOf(3))
                .fill();

        order = account.sale().order(orderId);
        assertEquals(SalesOrder.Status.PICKING_PACKING, order.status());
        assertEquals(0, order.items().get(0).reserved().compareTo(BigDecimal.valueOf(5.0)));
    }

    @Test
    public void orderTransitionsToPickingPackingDuringRankingChangeWithHigherPriority() throws Exception {
        Account account = setupAccountWithProduct(BigDecimal.valueOf(5)); // Limited stock

        // Create first order that gets all stock
        var firstOrderId = account.sale().withQuantity(BigDecimal.valueOf(5)).withSellPrice(321).create().salesOrderId;
        
        // Create second order with insufficient stock
        var secondOrderId = account.sale().withQuantity(BigDecimal.valueOf(3)).withSellPrice(321).create().salesOrderId;

        var firstOrder = account.sale().order(firstOrderId);
        var secondOrder = account.sale().order(secondOrderId);

        assertEquals(SalesOrder.Status.PICKING_PACKING, firstOrder.status());
        assertEquals(SalesOrder.Status.SUBMITTED, secondOrder.status());

        // Change ranking to prioritize second order
        mvc.perform(post("/sales/orders/ranking")
                        .header("Authorization", "Bearer " + account.accountId)
                        .content(Json.write(List.of(secondOrderId, firstOrderId)))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        firstOrder = account.sale().order(firstOrderId);
        secondOrder = account.sale().order(secondOrderId);

        // Second order should now be PICKING_PACKING with full reservation
        assertEquals(SalesOrder.Status.PICKING_PACKING, secondOrder.status());
        assertEquals(0, secondOrder.items().get(0).reserved().compareTo(BigDecimal.valueOf(3.0)));

        // First order should be SUBMITTED with partial reservation
        assertEquals(SalesOrder.Status.SUBMITTED, firstOrder.status());
        assertEquals(0, firstOrder.items().get(0).reserved().compareTo(BigDecimal.valueOf(2.0)));
    }

    @Test
    public void orderTransitionsFromPickingPackingDuringRankingChangeLosingPriority() throws Exception {
        Account account = setupAccountWithProduct(BigDecimal.valueOf(6));

        var firstOrderId = account.sale().withQuantity(BigDecimal.valueOf(3)).withSellPrice(321).create().salesOrderId;
        var secondOrderId = account.sale().withQuantity(BigDecimal.valueOf(5)).withSellPrice(321).create().salesOrderId;

        var firstOrder = account.sale().order(firstOrderId);
        var secondOrder = account.sale().order(secondOrderId);

        assertEquals(SalesOrder.Status.PICKING_PACKING, firstOrder.status());
        assertEquals(SalesOrder.Status.SUBMITTED, secondOrder.status());

        // prioritize second order
        mvc.perform(post("/sales/orders/ranking")
                        .header("Authorization", "Bearer " + account.accountId)
                        .content(Json.write(List.of(secondOrderId, firstOrderId)))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        firstOrder = account.sale().order(firstOrderId);
        secondOrder = account.sale().order(secondOrderId);

        assertEquals(SalesOrder.Status.PICKING_PACKING, secondOrder.status());
        assertEquals(0, secondOrder.items().get(0).reserved().compareTo(BigDecimal.valueOf(5.0)));

        assertEquals(SalesOrder.Status.SUBMITTED, firstOrder.status());
        assertEquals(0, firstOrder.items().get(0).reserved().compareTo(BigDecimal.valueOf(1.0)));
    }

    private Account setupAccountWithProduct(BigDecimal inventoryQuantity) throws Exception {
        Account account = setup
                .account()
                .category().withName(TestValueConstants.CATEGORIES.poll()).create().account()
                .supplier().create().account()
                .location().withName("HQ").create().account()
                .customer().create().account();

        // Create raw material
        var rawMaterialId = account.good()
                .withCategory(account.category().categoryId)
                .withDescription("raw material")
                .createRawMaterial().goodId;
        
        // Add raw materials to inventory
        account.inventory().withGood(rawMaterialId).withQuantity(BigDecimal.valueOf(100)).fill();

        // Create the product
        account.good()
                .withCategory(account.category().categoryId)
                .withDescription("test product")
                .withSellPrice(321)
                .withMarkup(BigDecimal.valueOf(1.5))
                .withMaterials(List.of(new RequiredMaterial(List.of(UUID.fromString(rawMaterialId)), BigDecimal.TEN, false, false, false, BigDecimal.ZERO, null, null)))
                .create();

        // Fill inventory for product with specified quantity
        account.inventory()
                .withGood(account.good().goodId)
                .withQuantity(inventoryQuantity)
                .fill();

        return account;
    }
}
