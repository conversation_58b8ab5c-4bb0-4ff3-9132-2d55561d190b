package fabriqon.e2e.tests;

import fabriqon.e2e.IntegrationTest;
import fabriqon.misc.Json;
import org.junit.Test;
import org.springframework.http.MediaType;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class OnboardingTests extends IntegrationTest {

    @Test
    public void onboard() throws Exception {
        var account = setup.account();
        account.manufacturingOperationTemplate()
                .withName("Taiere").create()
                .withName("Sudare").create();
        account.category()
                .withName("Lemn").create()
                .withName("Mobila").create();
        mvc.perform(multipart("/onboarding/import")
                        .file("file", zip())
                        .header("Authorization", "Bearer " + account.accountId))
                .andExpect(status().isOk());
    }

    private byte[] zip() throws IOException {
        var files = Files.list(Path.of("src/test/resources/onboarding"));
        var baos = new ByteArrayOutputStream();
        var zipOut = new ZipOutputStream(baos);
        files.forEach(f -> {
            try {
                zipOut.putNextEntry(new ZipEntry(f.getFileName().toString()));
                zipOut.write(Files.readAllBytes(f));
                zipOut.closeEntry();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });
        zipOut.close();
        return baos.toByteArray();
    }

}

