package fabriqon.e2e.tests;

import com.jayway.jsonpath.JsonPath;
import fabriqon.app.business.employees.EmployeeTimeoff;
import fabriqon.app.http.controllers.EmployeesController;
import fabriqon.e2e.IntegrationTest;
import fabriqon.e2e.dsl.Account;
import fabriqon.misc.Json;
import org.junit.Test;
import org.springframework.http.MediaType;

import java.time.LocalDateTime;

import static org.hamcrest.Matchers.hasSize;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class EmployeeTests extends IntegrationTest {

    @Test
    public void testTimeoff() throws Exception {
        Account account = setup.account();
        var employeeId = account.employee().create().employeeId;
        var result = account.dsl().mvc()
                .perform(post("/employees/" + employeeId + "/timeoff/add")
                        .content(Json.write(new EmployeesController.EmployeeTimeoffDefinition(LocalDateTime.now().withHour(0), LocalDateTime.now().plusDays(2), null, null)))
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("Authorization", "Bearer " + account.accountId))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andReturn();

        result = account.dsl().mvc()
                .perform(get("/employees/" + employeeId + "/details")
                        .content(Json.write(new EmployeesController.EmployeeTimeoffDefinition(LocalDateTime.now().withHour(0), LocalDateTime.now().plusDays(2), EmployeeTimeoff.Type.BUSINESS, null)))
                        .header("Authorization", "Bearer " + account.accountId))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.activeTimeoffs", hasSize(1)))
                .andReturn();
        var timeoffId = JsonPath.read(result.getResponse().getContentAsString(), "$.activeTimeoffs.[0].id");

        account.dsl().mvc()
                .perform(delete("/employees/" + employeeId + "/timeoff/" + timeoffId + "/delete")
                        .header("Authorization", "Bearer " + account.accountId))
                .andExpect(status().isOk());

    }
}
