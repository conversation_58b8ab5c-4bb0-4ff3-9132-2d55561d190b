package fabriqon.e2e.tests;

import fabriqon.e2e.IntegrationTest;
import fabriqon.e2e.dsl.Account;
import org.junit.Test;
import org.springframework.http.MediaType;

import static org.hamcrest.Matchers.is;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class ConfigurationTests extends IntegrationTest {

    @Test
    public void readsMeasurementUnits() throws Exception {
        Account account = setup.account();

        mvc.perform(get("/configurations/measurement-units")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.MM", is("mm")));
    }

    @Test
    public void readsCurrencies() throws Exception {
        Account account = setup.account();

        mvc.perform(get("/configurations/currencies")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("RON.defaultFractionDigits", is(2)));
    }

}
