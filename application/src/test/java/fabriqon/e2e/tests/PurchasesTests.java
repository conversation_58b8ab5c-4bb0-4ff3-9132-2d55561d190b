package fabriqon.e2e.tests;

import com.jayway.jsonpath.JsonPath;
import fabriqon.app.common.model.Money;
import fabriqon.e2e.IntegrationTest;
import fabriqon.e2e.data.TestValueConstants;
import fabriqon.e2e.dsl.Account;
import fabriqon.e2e.dsl.Purchases;
import org.junit.Test;
import org.springframework.http.MediaType;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Currency;
import java.util.List;

import static fabriqon.app.business.purchases.PurchaseOrder.Status.SENT_FOR_QUOTE;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.Matchers.closeTo;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class PurchasesTests extends IntegrationTest {

    @Test
    public void testPurchaseOrders() throws Exception {
        Account account = setup
                .account()
                .category().withName(TestValueConstants.CATEGORIES.poll()).create().account()
                .supplier().withName(TestValueConstants.SUPPLIERS.poll()).create().account()
                .location().withName("HQ").create().account()
                .customer().create().account();

        //create raw material
        account.good()
                .withName(TestValueConstants.MATERIALS.poll())
                .withCategory(account.category().categoryId)
                .withDescription("raw material")
                .create();
        var firstProductId = account.good().goodId;
        //create a product
        var productName = TestValueConstants.PRODUCTS.poll();
        account.good()
                .withName(productName)
                .withCategory(account.category().categoryId)
                .withDescription("something to write with")
                .create();
        var secondProductId = account.good().goodId;

        account.purchases()
                .addToWishlist(
                        account.supplier().supplierId,
                        firstProductId,
                        BigDecimal.valueOf(3)
                );

        var result = mvc.perform(get("/purchases/wishlist/suppliers/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andReturn();
        String wishlistItemId = JsonPath.read(result.getResponse().getContentAsString(), "[0].wishlistItems.[0].id");

        var items = List.of(
                new Purchases.ItemData(
                        wishlistItemId,
                        firstProductId,
                        BigDecimal.ONE,
                        new Money(1, Currency.getInstance("RON")),
                        LocalDate.now().plusDays(30)
                ),
                new Purchases.ItemData(
                        null,
                        secondProductId,
                        BigDecimal.TEN,
                        new Money(10, Currency.getInstance("RON")),
                        LocalDate.now().plusDays(30)
                )
        );
        account.purchases().create(account.supplier().supplierId, items);

        mvc.perform(get("/purchases/wishlist/suppliers/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isEmpty());

        result = mvc.perform(get("/purchases/orders/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].status", is(SENT_FOR_QUOTE.name())))
                .andReturn();
        String purchaseOrderId = JsonPath.read(result.getResponse().getContentAsString(), "[0].id");

        account.purchases().delivered(purchaseOrderId, items);

        mvc.perform(get("/purchases/orders/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].status", is("DELIVERED")))
                .andReturn();

        mvc.perform(get("/inventory/list?materialType=products&ids=" + secondProductId)
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("[0].name", is(productName)))
                .andExpect(jsonPath("[0].quantity", closeTo(10.0, 0.001)));

    }

}
