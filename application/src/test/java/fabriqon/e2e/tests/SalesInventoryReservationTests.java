package fabriqon.e2e.tests;

import fabriqon.app.business.goods.RequiredMaterial;
import fabriqon.app.business.sales.SalesOrder;
import fabriqon.e2e.IntegrationTest;
import fabriqon.e2e.data.TestValueConstants;
import fabriqon.e2e.dsl.Account;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class SalesInventoryReservationTests extends IntegrationTest {

    @Test
    public void testReservations() {
        Account account = setup
                .account()
                .category().withName(TestValueConstants.CATEGORIES.poll()).create().account()
                .supplier().withName(TestValueConstants.SUPPLIERS.poll()).create().account()
                .location().withName("HQ").create().account()
                .customer().create().account();

        //create the product
        var productName = TestValueConstants.PRODUCTS.poll();
        account.good()
                .withName(productName)
                .withCategory(account.category().categoryId)
                .withDescription("some description")
                .withSellPrice(100)
                .withMarkup(BigDecimal.valueOf(1.5))
//                .withMaterials(List.of(new ProductsController.Material(UUID.fromString(rawMaterialId), BigDecimal.TEN)))
                .create();


        //create the sale order
        var order1Id = account.sale().withQuantity(BigDecimal.valueOf(3)).create().salesOrderId;
        var order2Id = account.sale().withQuantity(BigDecimal.valueOf(5)).create().salesOrderId;

        var order1 = account.sale().order(order1Id);
        assertEquals(0, (int) order1.numberOfAvailableItems());
        var order2 = account.sale().order(order2Id);
        assertEquals(0, (int) order2.numberOfAvailableItems());

        //add 4 products to stock so the first order can be fulfilled but the 2nd is only partially available
        account.inventory()
                .withGood(account.good().goodId)
                .withQuantity(BigDecimal.valueOf(4))
                .fill();

        order1 = account.sale().order(order1Id);
        assertEquals(1, (int) order1.numberOfAvailableItems());
        order2 = account.sale().order(order2Id);
        assertEquals(0, (int) order2.numberOfAvailableItems());

        //add 5 products to stock so both orders are fulfilled
        account.inventory()
                .withGood(account.good().goodId)
                .withQuantity(BigDecimal.valueOf(5))
                .fill();

        order1 = account.sale().order(order1Id);
        assertEquals(1, (int) order1.numberOfAvailableItems());
        order2 = account.sale().order(order2Id);
        assertEquals(1, (int) order2.numberOfAvailableItems());
        assertEquals(0, order2.items().get(0).reserved().compareTo(BigDecimal.valueOf(5)));

        //move 2 products to a different inventory type
        account.inventory().moveToUnit(account.inventory().defaultInventoryUnit(), account.inventory().subInventoryUnit(),
                account.good().goodId, BigDecimal.valueOf(2));

        order1 = account.sale().order(order1Id);
        assertEquals(1, (int) order1.numberOfAvailableItems());
        order2 = account.sale().order(order2Id);
        assertEquals(0, (int) order2.numberOfAvailableItems());
        assertEquals(0, order2.items().get(0).reserved().compareTo(BigDecimal.valueOf(4)));

    }

    /**
     * CRITICAL TEST 1: Tests remainder redistribution when manufacturing excess for specific sales order
     * This tests the core logic in ProductsManufacturedConsumer where manufactured products
     * exceed the target sales order requirement and remainder gets redistributed to other orders.
     */
    @Test
    public void manufacturedProductsRedistributeRemainderToOtherSalesOrders() {
        Account account = setupAccountWithManufacturableProduct();

        // Create two sales orders - first needs 3, second needs 5
        var order1Id = account.sale().withQuantity(BigDecimal.valueOf(3)).create().salesOrderId;
        var order2Id = account.sale().withQuantity(BigDecimal.valueOf(5)).create().salesOrderId;

        // Verify initial state - no stock available
        var order1 = account.sale().order(order1Id);
        var order2 = account.sale().order(order2Id);
        assertEquals(SalesOrder.Status.SUBMITTED, order1.status());
        assertEquals(SalesOrder.Status.SUBMITTED, order2.status());
        assertEquals(0, order1.items().get(0).reserved().compareTo(BigDecimal.ZERO));
        assertEquals(0, order2.items().get(0).reserved().compareTo(BigDecimal.ZERO));

        // Manufacture 6 units specifically for order1 (which only needs 3)
        // This should allocate 3 to order1 and redistribute 3 to order2
        account.sale().withSalesOrderId(order1Id).manufactureMissing(account.good().goodId, BigDecimal.valueOf(6));
        account.manufacturing().withManufacturingOrderId(account.sale().manufacturingOrderId).completeTask();

        // Verify order1 gets exactly what it needs (3 units) and transitions to PICKING_PACKING
        order1 = account.sale().order(order1Id);
        assertEquals(SalesOrder.Status.PICKING_PACKING, order1.status());
        assertEquals(0, order1.items().get(0).reserved().compareTo(BigDecimal.valueOf(3)));

        // Verify order2 gets the remainder (3 units) but stays SUBMITTED since it needs 5 total
        order2 = account.sale().order(order2Id);
        assertEquals(SalesOrder.Status.SUBMITTED, order2.status());
        assertEquals(0, order2.items().get(0).reserved().compareTo(BigDecimal.valueOf(3)));
    }

    /**
     * CRITICAL TEST 2: Tests exact quantity match with existing reservations
     * This tests the logic where manufactured quantity exactly matches required quantity
     * and existing reservations need to be cleared and redistributed.
     */
    @Test
    public void exactQuantityMatchClearsAndRedistributesExistingReservations() {
        Account account = setupAccountWithManufacturableProduct();

        // Create three sales orders
        var order1Id = account.sale().withQuantity(BigDecimal.valueOf(4)).create().salesOrderId;
        var order2Id = account.sale().withQuantity(BigDecimal.valueOf(3)).create().salesOrderId;
        var order3Id = account.sale().withQuantity(BigDecimal.valueOf(2)).create().salesOrderId;

        // Add some initial stock that gets allocated by ranking (order1 gets priority)
        account.inventory().withGood(account.good().goodId).withQuantity(BigDecimal.valueOf(3)).fill();

        // Verify initial allocation by ranking
        var order1 = account.sale().order(order1Id);
        var order2 = account.sale().order(order2Id);
        var order3 = account.sale().order(order3Id);
        assertEquals(0, order1.items().get(0).reserved().compareTo(BigDecimal.valueOf(3))); // Gets 3 out of 4 needed
        assertEquals(0, order2.items().get(0).reserved().compareTo(BigDecimal.ZERO)); // Gets nothing
        assertEquals(0, order3.items().get(0).reserved().compareTo(BigDecimal.ZERO)); // Gets nothing

        // Now manufacture exactly 4 units for order1 (exact match)
        // This should clear order1's existing 3 units and give them the new 4 units
        // The freed 3 units should be redistributed to order2
        account.sale().withSalesOrderId(order1Id).manufactureMissing(account.good().goodId, BigDecimal.valueOf(4));
        account.manufacturing().withManufacturingOrderId(account.sale().manufacturingOrderId).completeTask();

        // Verify order1 now has exactly 4 units (from manufacturing) and is PICKING_PACKING
        order1 = account.sale().order(order1Id);
        assertEquals(SalesOrder.Status.PICKING_PACKING, order1.status());
        assertEquals(0, order1.items().get(0).reserved().compareTo(BigDecimal.valueOf(4)));

        // Verify order2 gets the redistributed 3 units and is PICKING_PACKING
        order2 = account.sale().order(order2Id);
        assertEquals(SalesOrder.Status.PICKING_PACKING, order2.status());
        assertEquals(0, order2.items().get(0).reserved().compareTo(BigDecimal.valueOf(3)));

        // Verify order3 still has nothing
        order3 = account.sale().order(order3Id);
        assertEquals(SalesOrder.Status.SUBMITTED, order3.status());
        assertEquals(0, order3.items().get(0).reserved().compareTo(BigDecimal.ZERO));
    }

    /**
     * CRITICAL TEST 3: Tests partial fulfillment with existing reservations
     * This tests the complex partial allocation logic when manufacturing additional
     * products for a sales order that already has some reservations.
     */
    @Test
    public void partialManufacturingWithExistingReservations() {
        Account account = setupAccountWithManufacturableProduct();

        // Create sales order needing 10 units
        var orderId = account.sale().withQuantity(BigDecimal.valueOf(10)).create().salesOrderId;

        // Add some initial stock (3 units) that gets allocated to the order
        account.inventory().withGood(account.good().goodId).withQuantity(BigDecimal.valueOf(3)).fill();

        // Verify initial allocation
        var order = account.sale().order(orderId);
        assertEquals(SalesOrder.Status.SUBMITTED, order.status());
        assertEquals(0, order.items().get(0).reserved().compareTo(BigDecimal.valueOf(3)));

        // Manufacture 4 more units for this specific order
        // Total will be 7 (3 existing + 4 new), still less than 10 needed
        account.sale().manufactureMissing(account.good().goodId, BigDecimal.valueOf(4));
        account.manufacturing().withManufacturingOrderId(account.sale().manufacturingOrderId).completeTask();

        // Verify order now has 7 units reserved but still PROCESSING (needs 10 total)
        order = account.sale().order(orderId);
        assertEquals(SalesOrder.Status.PROCESSING, order.status());
        assertEquals(0, order.items().get(0).reserved().compareTo(BigDecimal.valueOf(7)));

        // Manufacture 3 more units to complete the order
        account.manufacturing().orderManufacturing(account.good().goodId, BigDecimal.valueOf(3)).completeTask();

        // Verify order is now fully allocated and PICKING_PACKING
        order = account.sale().order(orderId);
        assertEquals(SalesOrder.Status.PICKING_PACKING, order.status());
        assertEquals(0, order.items().get(0).reserved().compareTo(BigDecimal.valueOf(10)));
    }

    /**
     * IMPORTANT TEST 4: Tests custom product flag preservation through manufacturing
     * This verifies that custom product information is maintained through the
     * manufacturing → allocation flow.
     */
    @Test
    public void customProductFlagPreservedThroughManufacturing() {
        var testData = setupAccountWithCustomizableProduct();

        // Create sales order with customization
        var orderId = testData.account.sale()
                .withProduct(testData.account.good().goodId)
                .withCustomizations(List.of(new SalesOrder.Item.Customization(UUID.fromString(testData.customizableMaterialId), BigDecimal.ONE)))
                .create()
                .salesOrderId;

        // Verify order has customization
        var order = testData.account.sale().order(orderId);
        assertEquals(1, order.items().get(0).customizations().size());
        assertTrue("Order should be marked as custom", order.items().get(0).customizations().size() > 0);

        // Manufacture the custom product
        testData.account.sale().manufactureMissing(testData.account.good().goodId, BigDecimal.valueOf(1));
        testData.account.manufacturing().withManufacturingOrderId(testData.account.sale().manufacturingOrderId).completeTask();

        // Verify order transitions to PICKING_PACKING and custom info is preserved
        order = testData.account.sale().order(orderId);
        assertEquals(SalesOrder.Status.PICKING_PACKING, order.status());
        assertEquals(0, order.items().get(0).reserved().compareTo(BigDecimal.valueOf(1)));
        assertEquals(1, order.items().get(0).customizations().size());
    }

    /**
     * IMPORTANT TEST 5: Tests manufacturing order reference tracking
     * This verifies that the fromOrder parameter is correctly tracked when
     * products are allocated from manufacturing.
     */
    @Test
    public void manufacturingOrderReferenceTrackedInReservations() {
        Account account = setupAccountWithManufacturableProduct();

        // Create sales order
        var orderId = account.sale().withQuantity(BigDecimal.valueOf(5)).create().salesOrderId;

        // Manufacture products for this order
        account.sale().manufactureMissing(account.good().goodId, BigDecimal.valueOf(5));
        var manufacturingOrderId = account.sale().manufacturingOrderId;
        account.manufacturing().withManufacturingOrderId(manufacturingOrderId).completeTask();

        // Verify order is fulfilled
        var order = account.sale().order(orderId);
        assertEquals(SalesOrder.Status.PICKING_PACKING, order.status());
        assertEquals(0, order.items().get(0).reserved().compareTo(BigDecimal.valueOf(5)));

        // Note: In a real implementation, we would verify the fromOrder reference
        // is stored in the reservation records, but this requires database inspection
        // which is beyond the scope of this E2E test. This test ensures the flow works.
    }

    /**
     * IMPORTANT TEST 6: Tests event consumer priority handling
     * This verifies that when products are manufactured, the sales consumer
     * gets priority over manufacturing consumer for stock allocation.
     */
    @Test
    public void eventConsumerPriorityHandlesManufacturedProducts() {
        Account account = setupAccountWithManufacturableProduct();

        var product1 = account.good().goodId;

        //create product that uses product1 as raw material
        var product2 = account.good()
                .withCategory(account.category().categoryId)
                .withMaterials(List.of(new RequiredMaterial(List.of(UUID.fromString(product1)), BigDecimal.ONE, false, false, false, BigDecimal.ZERO, null, null)))
                .create().goodId;

        // Create both sales order and manufacturing order for same product
        var salesOrderId = account.sale().withProduct(product1).withQuantity(BigDecimal.valueOf(3)).create().salesOrderId;
        var manufacturingOrderId = account.manufacturing().orderManufacturing(product2, BigDecimal.valueOf(2)).manufacturingOrderId;

        // Verify initial state - no stock available
        var salesOrder = account.sale().order(salesOrderId);
        var manufacturingOrder = account.manufacturing().order(manufacturingOrderId);
        assertEquals(SalesOrder.Status.SUBMITTED, salesOrder.status());
        assertEquals(0, salesOrder.items().get(0).reserved().compareTo(BigDecimal.ZERO));
        assertTrue("Manufacturing order should not have materials available", !manufacturingOrder.allMaterialsAvailable());

        // Add limited stock (2 units) - should go to sales order first due to priority
        account.inventory().withGood(product1).withQuantity(BigDecimal.valueOf(2)).fill();

        // Verify sales order gets priority
        salesOrder = account.sale().order(salesOrderId);
        manufacturingOrder = account.manufacturing().order(manufacturingOrderId);
        assertEquals(0, salesOrder.items().get(0).reserved().compareTo(BigDecimal.valueOf(2)));
        assertTrue("Manufacturing order should still not have materials", !manufacturingOrder.allMaterialsAvailable());

        // Add more stock - manufacturing order should now get materials
        account.inventory().withGood(product1).withQuantity(BigDecimal.valueOf(3)).fill();

        // Verify both orders now have what they need
        salesOrder = account.sale().order(salesOrderId);
        manufacturingOrder = account.manufacturing().order(manufacturingOrderId);
        assertEquals(SalesOrder.Status.PICKING_PACKING, salesOrder.status());
        assertEquals(0, salesOrder.items().get(0).reserved().compareTo(BigDecimal.valueOf(3)));
        assertTrue("Manufacturing order should now have materials available", manufacturingOrder.allMaterialsAvailable());
    }

    /**
     * CRITICAL TEST 7: Tests manufacturing more than required handles overflow correctly
     * This tests the edge case where manufacturing produces significantly more than needed
     * and verifies proper allocation and redistribution.
     */
    @Test
    public void manufacturingMoreThanRequiredHandlesOverflow() {
        Account account = setupAccountWithManufacturableProduct();

        // Create sales order needing only 2 units
        var orderId = account.sale().withQuantity(BigDecimal.valueOf(2)).create().salesOrderId;

        // Create second sales order needing 3 units for redistribution test
        var order2Id = account.sale().withQuantity(BigDecimal.valueOf(3)).create().salesOrderId;

        // Manufacture 10 units for the first order (massive overflow)
        account.sale().manufactureMissing(account.good().goodId, BigDecimal.valueOf(10));
        account.manufacturing().withManufacturingOrderId(account.sale().manufacturingOrderId).completeTask();

        // Verify first order gets exactly what it needs (2 units)
        var order1 = account.sale().order(orderId);
        assertEquals(SalesOrder.Status.PICKING_PACKING, order1.status());
        assertEquals(0, order1.items().get(0).reserved().compareTo(BigDecimal.valueOf(2)));

        // Verify second order gets some of the overflow (3 units)
        var order2 = account.sale().order(order2Id);
        assertEquals(SalesOrder.Status.PICKING_PACKING, order2.status());
        assertEquals(0, order2.items().get(0).reserved().compareTo(BigDecimal.valueOf(3)));

        // Verify remaining 5 units are available in general inventory
        // (This would be verified by creating another order and seeing it gets allocated)
        var order3Id = account.sale().withQuantity(BigDecimal.valueOf(4)).create().salesOrderId;
        var order3 = account.sale().order(order3Id);
        assertEquals(SalesOrder.Status.PICKING_PACKING, order3.status());
        assertEquals(0, order3.items().get(0).reserved().compareTo(BigDecimal.valueOf(4)));
    }

    /**
     * Helper method to set up an account with a manufacturable product
     * that has raw materials and can be manufactured.
     */
    private Account setupAccountWithManufacturableProduct() {
        Account account = setup
                .account()
                .category().create().account()
                .supplier().create().account()
                .location().create().account()
                .customer().create().account();

        // Create raw material
        var rawMaterialId = account.good()
                .withCategory(account.category().categoryId)
                .withDescription("raw material")
                .create().goodId;

        // Add raw materials to inventory (enough for manufacturing)
        account.inventory().withGood(rawMaterialId).withQuantity(BigDecimal.valueOf(100)).fill();

        // Create manufacturable product
        account.good()
                .withCategory(account.category().categoryId)
                .withDescription("manufacturable product")
                .withSellPrice(100)
                .withMarkup(BigDecimal.valueOf(1.5))
                .withMaterials(List.of(new RequiredMaterial(List.of(UUID.fromString(rawMaterialId)), BigDecimal.ONE, false, false, false, BigDecimal.ZERO, null, null)))
                .create();

        return account;
    }

    /**
     * Helper method to set up an account with a customizable product
     * that can have customizations and be manufactured.
     */
    private CustomizableProductTestData setupAccountWithCustomizableProduct() {
        Account account = setup
                .account()
                .category().withName(TestValueConstants.CATEGORIES.poll()).create().account()
                .supplier().withName(TestValueConstants.SUPPLIERS.poll()).create().account()
                .location().withName("HQ").create().account()
                .customer().create().account();

        // Create multiple materials for customization
        var materialId1 = account.good()
                .withCategory(account.category().categoryId)
                .withDescription("material 1")
                .create().goodId;

        var materialId2 = account.good()
                .withCategory(account.category().categoryId)
                .withDescription("material 2")
                .create().goodId;

        // Add materials to inventory
        account.inventory().withGood(materialId1).withQuantity(BigDecimal.valueOf(100)).fill();
        account.inventory().withGood(materialId2).withQuantity(BigDecimal.valueOf(100)).fill();

        // Create customizable product
        account.good()
                .withName(TestValueConstants.PRODUCTS.poll())
                .withCategory(account.category().categoryId)
                .withDescription("customizable product")
                .withSellPrice(100)
                .withMarkup(BigDecimal.valueOf(1.5))
                .withMaterials(List.of(
                        new RequiredMaterial(List.of(UUID.fromString(materialId1), UUID.fromString(materialId2)),
                                           BigDecimal.ONE, false, true, false, BigDecimal.ZERO, null, null)
                ))
                .create();

        return new CustomizableProductTestData(account, materialId2);
    }

    /**
     * Test data holder for customizable product tests
     */
    private static class CustomizableProductTestData {
        final Account account;
        final String customizableMaterialId;

        CustomizableProductTestData(Account account, String customizableMaterialId) {
            this.account = account;
            this.customizableMaterialId = customizableMaterialId;
        }
    }

}