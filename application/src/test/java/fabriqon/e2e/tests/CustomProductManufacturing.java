package fabriqon.e2e.tests;

import fabriqon.app.business.goods.RequiredMaterial;
import fabriqon.app.business.sales.SalesOrder;
import fabriqon.e2e.IntegrationTest;
import fabriqon.e2e.dsl.Account;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import static org.hamcrest.CoreMatchers.containsString;
import static org.junit.Assert.assertEquals;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class CustomProductManufacturing extends IntegrationTest {

    private String categoryId1;
    private String categoryId2;
    private String materialId1;
    private String materialId2;
    private String materialId3;
    private String materialId4;
    private String productId;
    private Account account;

    @Before
    public void setup() {
        account = setup
                .account()
                .location().create().account()
                .supplier().create().account()
                .customer().create().account();
        var categoryId1 = account.category().withName("custom product materials cat 1").create().categoryId;
        var categoryId2 = account.category().withName("custom product materials cat 2").create().categoryId;
        var categoryId3 = account.category().withName("custom product cat").create().categoryId;

        //create the raw materials
        materialId1 = account.good()
                .withName("custom_product_material_1")
                .withCategory(categoryId1)
                .withDescription("raw material")
                .create().goodId;
        account.inventory().withGood(materialId1).fill();

        materialId2 = account.good()
                .withName("custom_product_material_2")
                .withCategory(categoryId1)
                .withDescription("raw material")
                .create().goodId;
        account.inventory().withGood(materialId2).fill();

        materialId3 = account.good()
                .withName("custom_product_material_3")
                .withCategory(categoryId2)
                .withDescription("raw material")
                .create().goodId;
        account.inventory().withGood(materialId3).fill();

        materialId4 = account.good()
                .withName("custom_product_material_4")
                .withCategory(categoryId2)
                .withDescription("raw material")
                .create().goodId;
        account.inventory().withGood(materialId4).fill();

        productId = account.good().withName("custom product 1").withCategory(categoryId3).withSellPrice(100)
                .withMaterials(List.of(new RequiredMaterial(
                                List.of(UUID.fromString(materialId1), UUID.fromString(materialId2)),
                                BigDecimal.ONE, false, true, false, BigDecimal.ZERO, null, null),
                        new RequiredMaterial(
                                List.of(UUID.fromString(materialId3), UUID.fromString(materialId4)),
                                BigDecimal.ONE, false, false, true, BigDecimal.ZERO, null, null)
                ))
                .create().goodId;
    }

    @Test
    public void manufactureCustomProduct() throws Exception {
        //try to customize a material that is not customizable
        account.sale()
                .withProduct(productId)
                .withCustomizations(List.of(new SalesOrder.Item.Customization(UUID.fromString(materialId4), BigDecimal.ONE)))
                .createWithResult()
                .andExpect(status().is4xxClientError())
                .andExpect(jsonPath("message", containsString("Item is not marked as customizable")));

        //provide correct customization for customizable product
        var salesOrderId = account.sale()
                .withProduct(productId)
                .withCustomizations(List.of(new SalesOrder.Item.Customization(UUID.fromString(materialId2), BigDecimal.ONE)))
                .create()
                .salesOrderId;
        var salesOrder = account.sale().order(salesOrderId);
        assertEquals(1, salesOrder.items().get(0).customizations().size());
        assertEquals(materialId2, salesOrder.items().get(0).customizations().get(0).materialId().toString());

        var manufacturingOrderId = account.sale().manufactureMissing(productId, BigDecimal.ONE).manufacturingOrderId;
        var manufacturingOrder = account.manufacturing().order(manufacturingOrderId);
        assertEquals(2, manufacturingOrder.manufacturingOperations().getFirst().materials().size());
        assertEquals(1, manufacturingOrder.manufacturingOperations().getFirst().materials().get(0).materialGoods().size());
        assertEquals(materialId2, manufacturingOrder.manufacturingOperations().getFirst().materials().get(0).materialGoods().get(0).id().toString());
        assertEquals(2, manufacturingOrder.manufacturingOperations().getFirst().materials().get(1).materialGoods().size());
        assertEquals(materialId3, manufacturingOrder.manufacturingOperations().getFirst().materials().get(1).materialGoods().get(0).id().toString());
        assertEquals(materialId4, manufacturingOrder.manufacturingOperations().getFirst().materials().get(1).materialGoods().get(1).id().toString());
    }

}