package fabriqon.e2e.tests;

import com.jayway.jsonpath.JsonPath;
import fabriqon.app.http.controllers.fabrication.DevicePairingController;
import fabriqon.e2e.IntegrationTest;
import fabriqon.e2e.data.TestValueConstants;
import fabriqon.e2e.dsl.Account;
import fabriqon.misc.Json;
import org.junit.Test;
import org.springframework.http.MediaType;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.Matchers.empty;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class DevicePairingTests extends IntegrationTest {

    @Test
    public void testPairing() throws Exception {

        Account account = setup.account();

        var employeeName = TestValueConstants.EMPLOYEES.poll();
        var employeeId = account.employee().withName(employeeName).create().employeeId;

        var result = account.dsl().mvc()
                .perform(get("/employees/" + employeeId + "/initiate-device-pairing")
                        .header("Authorization", "Bearer " + account.accountId))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andReturn();
        String pairingLink = JsonPath.read(result.getResponse().getContentAsString(), "$.pairingLink");

        result = account.dsl().mvc()
                .perform(get("/fabrication/devices/pairing/initiate/" + pairingLink.substring(pairingLink.indexOf("/pairing") + 22))
                        .header("Authorization", "Bearer " + account.accountId))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("employeeName", is(employeeName)))
                .andReturn();
        String confirmationToken = JsonPath.read(result.getResponse().getContentAsString(), "$.confirmationToken");

        result = account.dsl().mvc()
                .perform(post("/fabrication/devices/pairing/confirm")
                        .content(Json.write(new DevicePairingController.PairingRequest(confirmationToken)))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andReturn();
        String accessToken = JsonPath.read(result.getResponse().getContentAsString(), "$.token");

        account.dsl().mvc()
                .perform(get("/fabrication/tasks/list")
                        .header("X-Auth", accessToken)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$", empty()));

    }
}
