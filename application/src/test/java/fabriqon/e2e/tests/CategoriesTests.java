package fabriqon.e2e.tests;

import fabriqon.app.http.controllers.goods.CategoriesController;
import fabriqon.e2e.IntegrationTest;
import fabriqon.misc.Json;
import org.junit.Test;
import org.springframework.http.MediaType;

import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class CategoriesTests extends IntegrationTest {

    @Test
    public void crudCategories() throws Exception {

        var account = setup.account();
        mvc.perform(get("/categories/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(content().string("[]"));

        //create
        var categoryId = account.category().withName("Office Supplies").create().categoryId;

        //read
        mvc.perform(get("/categories/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].details.name", is("Office Supplies")));

        //update
        mvc.perform(post("/categories/" + categoryId + "/update")
                        .content(Json.write(new CategoriesController.CreateCategory("Office Supplies Updated")))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        mvc.perform(get("/categories/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].details.name", is("Office Supplies Updated")));

        //delete
        mvc.perform(delete("/categories/" + categoryId + "/delete")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        mvc.perform(get("/categories/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(content().string("[]"));
    }


}
