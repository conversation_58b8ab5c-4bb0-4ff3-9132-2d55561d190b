package fabriqon.e2e.tests;

import com.jayway.jsonpath.JsonPath;
import fabriqon.app.business.goods.Dimensions;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.goods.RequiredMaterial;
import fabriqon.app.business.goods.materials.metals.DimensionValue;
import fabriqon.app.business.goods.materials.metals.MetalMaterialService;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.http.controllers.goods.GoodsController;
import fabriqon.e2e.IntegrationTest;
import fabriqon.e2e.data.TestValueConstants;
import fabriqon.e2e.dsl.Account;
import fabriqon.misc.Json;
import org.junit.Test;
import org.springframework.http.MediaType;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static fabriqon.e2e.data.Utils.mediumRandomInt;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.Matchers.closeTo;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class ProductsTests extends IntegrationTest {

    @Test
    public void getEmptyProductList() throws Exception {

        mvc.perform(get("/goods/list")
                        .header("Authorization", "Bearer " + setup.account().accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(content().string("[]"));
    }

    @Test
    public void createUpdateAndManageVariants()
            throws Exception {

        Account account = setup
                .account()
                .location().create().account()
                .supplier().create().account()
                .category().withName(TestValueConstants.CATEGORIES.poll()).create()
                .account();

        //create raw material
        var rawMaterialId = account.good()
                .withName(TestValueConstants.MATERIALS.poll())
                .withCategory(account.category().categoryId)
                .withDescription("raw material")
                .createRawMaterial().goodId;
        account.inventory().withGood(rawMaterialId).fill();

        //create the product
        var productName = TestValueConstants.PRODUCTS.poll();
        var parentProductId = account.good()
                .withName(productName)
                .withCategory(account.category().categoryId)
                .withDescription("something to write with")
                .withSellPrice(321)
                .withMarkup(BigDecimal.valueOf(1.5))
                .withMaterials(List.of(new RequiredMaterial(List.of(UUID.fromString(rawMaterialId)), BigDecimal.TEN, false, false, false, BigDecimal.ZERO, null, null)))
                .create()
                .goodId;

        mvc.perform(get("/goods/" + parentProductId + "/details")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.name", is(productName)));

        //generate product variants
        mvc.perform(post("/goods/" + parentProductId + "/variants/update")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(Json.write(account.good().variantOptions)))
                .andExpect(status().isOk());
        var result = mvc.perform(get("/goods/" + parentProductId + "/variants/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].name", is(productName + " Red - Small")))
                .andReturn();


        //test delete variant
        var variantId = JsonPath.read(result.getResponse().getContentAsString(), "[0].id");
        mvc.perform(get("/goods/" + parentProductId + "/details")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("variantCount", is(6)));
        mvc.perform(delete("/goods/" + variantId + "/delete")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
        mvc.perform(get("/goods/" + parentProductId + "/details")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("variantCount", is(5)));

        //re-generate the deleted variant and also create new ones
        mvc.perform(post("/goods/" + parentProductId + "/variants/update")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(Json.write(List.of(
                                new MaterialGood.VariantOption("Color", Set.of("Blue", "Red", "Green")),
                                new MaterialGood.VariantOption("Size", Set.of("Small", "Medium", "Large"))
                        ))))
                .andExpect(status().isOk());
        result = mvc.perform(get("/goods/" + parentProductId + "/variants/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(9))
                .andExpect(jsonPath("[5].name", is(productName + " Red - Small")))
                .andExpect(jsonPath("[6].name", is(productName + " Green - Small")))
                .andReturn();

        //update product
        variantId = JsonPath.read(result.getResponse().getContentAsString(), "[0].id");
        mvc.perform(post("/goods/" + variantId + "/update")
                        .content(Json.write(new GoodsController.GoodDefinition(
                                "red updated", null, null, null,
                                "updated description",
                                null,
                                null, null, null, MeasurementUnit.M,
                                null, false, BigDecimal.ONE, null, null
                        )))
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("Authorization", "Bearer " + account.accountId))
                .andExpect(status().isOk());

        mvc.perform(get("/goods/" + variantId + "/details")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.name", is("red updated")));

    }


    @Test
    public void customProductTests() throws Exception {
        Account account = setup
                .account()
                .location().create().account()
                .supplier().create().account();
        var categoryId1= account.category().withName("custom product materials cat 1").create().categoryId;
        var categoryId2= account.category().withName("custom product materials cat 2").create().categoryId;
        var categoryId3= account.category().withName("custom product cat").create().categoryId;

        //create the raw materials
        var rawMaterialId1 = account.good()
                .withName("custom_product_material_1")
                .withCategory(categoryId1)
                .withDescription("raw material")
                .createRawMaterial().goodId;
        account.inventory().withGood(rawMaterialId1).fill();

        var rawMaterialId2 = account.good()
                .withName("custom_product_material_2")
                .withCategory(categoryId1)
                .withDescription("raw material")
                .createRawMaterial().goodId;
        account.inventory().withGood(rawMaterialId2).fill();

        var rawMaterialId3 = account.good()
                .withName("custom_product_material_3")
                .withCategory(categoryId2)
                .withDescription("raw material")
                .createRawMaterial().goodId;
        account.inventory().withGood(rawMaterialId3).fill();

        var rawMaterialId4 = account.good()
                .withName("custom_product_material_4")
                .withCategory(categoryId2)
                .withDescription("raw material")
                .createRawMaterial().goodId;
        account.inventory().withGood(rawMaterialId4).fill();

        //common configs for product
        account.good().withName("custom product 1").withCategory(categoryId3).withSellPrice(100);

        //try to create a product with 2 materials in the same "row" but neither configurableWithOptions or repleaceableWithOptions true
        account.good()
                .withMaterials(List.of(new RequiredMaterial(
                        List.of(UUID.fromString(rawMaterialId1), UUID.fromString(rawMaterialId2)),
                        BigDecimal.ONE, false, false, false, BigDecimal.ZERO, null, null)))
                .createWithResult(true)
                .andExpect(status().is4xxClientError())
                .andExpect(jsonPath("message", is("when more than 1 material is specified either 'configurableWithOptions' or 'replaceableWithOptions' need to be true")));

        //try to create a product with 2 materials from different categories in the same "row"
        account.good()
                .withMaterials(List.of(new RequiredMaterial(
                        List.of(UUID.fromString(rawMaterialId1), UUID.fromString(rawMaterialId3)),
                        BigDecimal.ONE, false, true, false, BigDecimal.ZERO, null, null)))
                .createWithResult(true)
                .andExpect(status().is4xxClientError())
                .andExpect(jsonPath("message", is("materials in the same row must be from the same category")));

        //create a valid configuration
        var productId = account.good()
                .withMaterials(List.of(new RequiredMaterial(
                        List.of(UUID.fromString(rawMaterialId1), UUID.fromString(rawMaterialId2)),
                        BigDecimal.ONE, false, true, false, BigDecimal.ZERO, null, null),
                        new RequiredMaterial(
                                List.of(UUID.fromString(rawMaterialId3), UUID.fromString(rawMaterialId4)),
                                BigDecimal.ONE, false, false, true, BigDecimal.ZERO, null, null)
                        ))
                .create().goodId;

        var productDetails = account.good().details(productId);
        assertEquals(2, productDetails.manufacturingOperations().getFirst().materials().size());
        assertTrue(productDetails.manufacturingOperations().getFirst().materials().getFirst().configurableWithOptions());
        assertEquals(rawMaterialId1, productDetails.manufacturingOperations().getFirst().materials().getFirst().options().get(0).id().toString());
        assertEquals(rawMaterialId2, productDetails.manufacturingOperations().getFirst().materials().getFirst().options().get(1).id().toString());
        assertTrue(productDetails.manufacturingOperations().getFirst().materials().get(1).replaceableWithOptions());
        assertEquals(rawMaterialId3, productDetails.manufacturingOperations().getFirst().materials().get(1).options().get(0).id().toString());
        assertEquals(rawMaterialId4, productDetails.manufacturingOperations().getFirst().materials().get(1).options().get(1).id().toString());
    }

    @Test
    public void createProductWithDimensionsDefinition() throws Exception {

        Account account = setup
                .account()
                .location().create().account()
                .supplier().create().account()
                .category().withName(TestValueConstants.CATEGORIES.poll()).create()
                .account();

        //create raw material
        var result = account.dsl().mvc()
                .perform(post("/goods/create")
                        .content(Json.write(new GoodsController.GoodDefinition(
                                "raw material", "C-" + mediumRandomInt(), null, UUID.fromString(account.category().categoryId),
                                null,
                                null,
                                null, null, null, MeasurementUnit.KG,
                                null,
                                false,
                                BigDecimal.ONE,
                                new Dimensions(BigDecimal.TEN, BigDecimal.ONE, null, BigDecimal.valueOf(100)),
                                new MaterialGood.Material("aluminum.pure", MetalMaterialService.ShapeType.PLATE, List.of(new DimensionValue(MetalMaterialService.DimensionKey.THICKNESS, BigDecimal.valueOf(0.4))))
                        )))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
        var rawMaterialId = (String) JsonPath.read(result.andReturn().getResponse().getContentAsString(), "$.id");
        account.inventory().withGood(rawMaterialId).fill();

        //create the product
        var productName = "product with dimensions";
        var productId = account.good()
                .withName(productName)
                .withCategory(account.category().categoryId)
                .withDescription("something to write with")
                .withSellPrice(321)
                .withMaterials(List.of(new RequiredMaterial(List.of(UUID.fromString(rawMaterialId)), BigDecimal.TEN, false, false, false, BigDecimal.ZERO,
                        new Dimensions(BigDecimal.valueOf(1000), BigDecimal.valueOf(500), null, null), null)))
                .create()
                .goodId;

        mvc.perform(get("/goods/" + productId + "/details")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.name", is(productName)))
                .andExpect(jsonPath("$.manufacturingOperations[0].materials[0].quantity", closeTo(0.54, 0.01)))
                .andExpect(jsonPath("$.manufacturingOperations[0].materials[0].requiredDimensions.length", is(1000)))
                .andExpect(jsonPath("$.manufacturingOperations[0].materials[0].requiredDimensions.width", is(500)));
    }

}
