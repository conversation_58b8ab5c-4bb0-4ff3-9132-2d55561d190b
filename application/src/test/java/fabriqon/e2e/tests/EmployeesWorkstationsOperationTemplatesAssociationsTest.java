package fabriqon.e2e.tests;

import fabriqon.app.common.model.Money;
import fabriqon.app.http.controllers.EmployeesController;
import fabriqon.app.http.controllers.manufacturing.ManufacturingWorkstationsController;
import fabriqon.e2e.IntegrationTest;
import fabriqon.e2e.data.TestValueConstants;
import fabriqon.e2e.dsl.Account;
import fabriqon.misc.Json;
import org.junit.Test;
import org.springframework.http.MediaType;

import java.util.Currency;
import java.util.List;

import static java.util.UUID.fromString;
import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class EmployeesWorkstationsOperationTemplatesAssociationsTest extends IntegrationTest {

    @Test
    public void testAssociations() throws Exception {

        Account account = setup
                .account()
                .category().withName(TestValueConstants.CATEGORIES.poll()).create().account()
                .supplier().withName(TestValueConstants.SUPPLIERS.poll()).create().account()
                .location().withName("HQ").create().account()
                .customer().create().account();

        var firstEmployeeId = account.employee().withName(TestValueConstants.EMPLOYEES.poll()).create().employeeId;
        var secondEmployeeId = account.employee().withName(TestValueConstants.EMPLOYEES.poll()).create().employeeId;

        var cuttingWorkstationId = account.manufacturingWorkstation().withName("Cutting machine").create().workstationId;
        var paintingWorkstationId = account.manufacturingWorkstation().withName("Painting machine").create().workstationId;

        var cuttingOperationId = account.manufacturingOperationTemplate()
                .withName("cutting")
                .withWorkstations(List.of(fromString(cuttingWorkstationId)))
                .withEmployees(List.of(firstEmployeeId, secondEmployeeId))
                .create().operationTemplateId;

        var paintingOperationId = account.manufacturingOperationTemplate()
                .withName("painting")
                .withWorkstations(List.of(fromString(paintingWorkstationId)))
                .withEmployees(List.of(secondEmployeeId))
                .create().operationTemplateId;

        mvc.perform(get("/manufacturing/operations/templates/" + cuttingOperationId + "/details")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("workstations[0].id", is(cuttingWorkstationId)))
                .andExpect(jsonPath("employees[0].id", is(firstEmployeeId.toString())))
                .andExpect(jsonPath("employees[1].id", is(secondEmployeeId.toString())));


        //add a painting operation to first employee and change name
        mvc.perform(post("/employees/" + firstEmployeeId + "/update")
                        .content(Json.write(new EmployeesController.EmployeeDefinition("Arnold Delgado", null,
                                new Money(123123, Currency.getInstance("RON")),
                                List.of(new EmployeesController.EmployeeManufacturingOperation(fromString(cuttingOperationId), false),
                                        new EmployeesController.EmployeeManufacturingOperation(fromString(paintingOperationId), true))
                        )))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        mvc.perform(get("/employees/" + firstEmployeeId + "/details")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("name", is("Arnold Delgado")))
                .andExpect(jsonPath("manufacturingOperationTemplates[0].id", is(cuttingOperationId)))
                .andExpect(jsonPath("manufacturingOperationTemplates[1].id", is(paintingOperationId)))
                .andExpect(jsonPath("manufacturingOperationTemplates[1].preferential", is(true)));

        mvc.perform(get("/manufacturing/operations/templates/" + paintingOperationId + "/details")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("workstations[0].id", is(paintingWorkstationId)))
                .andExpect(jsonPath("employees[0].id", is(secondEmployeeId.toString())))
                .andExpect(jsonPath("employees[1].preferential", is(true)))
                .andExpect(jsonPath("employees[1].id", is(firstEmployeeId.toString())));

        mvc.perform(post("/manufacturing/workstations/" + cuttingWorkstationId + "/update")
                        .content(Json.write(new ManufacturingWorkstationsController.ManufacturingWorkstationDefinition(null, null,
                                List.of(fromString(cuttingOperationId), fromString(paintingOperationId)))))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        mvc.perform(get("/manufacturing/operations/templates/" + paintingOperationId + "/details")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("workstations[0].id", is(cuttingWorkstationId)))
                .andExpect(jsonPath("workstations[1].id", is(paintingWorkstationId)))
                .andExpect(jsonPath("employees[0].id", is(secondEmployeeId.toString())))
                .andExpect(jsonPath("employees[1].id", is(firstEmployeeId.toString())));

    }

}
