package fabriqon.e2e.tests;

import fabriqon.app.business.goods.RequiredMaterial;
import fabriqon.app.common.model.Money;
import fabriqon.e2e.IntegrationTest;
import fabriqon.e2e.SetupUtils;
import fabriqon.e2e.data.TestValueConstants;
import fabriqon.e2e.dsl.Account;
import fabriqon.e2e.dsl.Purchases;
import fabriqon.misc.Tuple;
import org.junit.Test;
import org.springframework.http.MediaType;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Currency;
import java.util.List;
import java.util.UUID;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class InventoryTests extends IntegrationTest {

    @Test
    public void calculatesStockForProduct() throws Exception {

        var categoryName = TestValueConstants.CATEGORIES.poll();
        Account account = setup
                .account()
                .category().withName(categoryName).create().account()
                .supplier().withName(TestValueConstants.SUPPLIERS.poll()).create().account()
                .location().withName("HQ").create().account();

        //create the product
        var productName = TestValueConstants.PRODUCTS.poll();
        account.good()
                .withName(productName)
                .withCategory(account.category().categoryId)
                .withDescription("something to write with")
                .withSellPrice(321)
                .withMarkup(BigDecimal.valueOf(1.5))
                .create();

        //first fill
        account.inventory()
                .withGood(account.good().goodId)
                .withQuantity(BigDecimal.TEN)
                .fill();

        mvc.perform(get("/inventory/adjustments/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON));


        //second fill
        account.inventory()
                .withGood(account.good().goodId)
                .withQuantity(BigDecimal.valueOf(-1))
                .fill();

        mvc.perform(get("/inventory/list?q=" + productName)
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].name", is(productName)))
                .andExpect(jsonPath("[0].quantity", closeTo(9.0, 0.001)))
                .andExpect(jsonPath("[0].category.name", is(categoryName)));
    }

    @Test
    public void itemHistory() throws Exception {
        Account account = setup
                .account()
                .category().withName(TestValueConstants.CATEGORIES.poll()).create().account()
                .supplier().withName(TestValueConstants.SUPPLIERS.poll()).create().account()
                .location().withName("HQ").create().account()
                .customer().create().account();

        //create raw material
        var rawMaterialId = account.good()
                .withName(TestValueConstants.MATERIALS.poll())
                .withCategory(account.category().categoryId)
                .withDescription("raw material")
                .create().goodId;
        //add raw materials to inventory
        account.inventory().withGood(rawMaterialId).withQuantity(BigDecimal.valueOf(100)).fill();

        //create the product
        var productName = TestValueConstants.PRODUCTS.poll();
        var materials = List.of(new RequiredMaterial(List.of(UUID.fromString(rawMaterialId)), BigDecimal.TEN, false, false, false, BigDecimal.ZERO, null, null));
        var productId = account.good()
                .withName(productName)
                .withCategory(account.category().categoryId)
                .withSellPrice(321)
                .withMaterials(materials)
                .create().goodId;

        //fill inventory for product
        account.inventory().withGood(productId).withQuantity(BigDecimal.valueOf(2)).fill();

        //create the sale order
        var salesOrderId = account.sale().withQuantity(BigDecimal.valueOf(5)).withSellPrice(321).create().salesOrderId;

        //order manufacturing for the missing products
        var toManufacture = BigDecimal.valueOf(3);
        account.sale().manufactureMissing(account.good().goodId, toManufacture);

        //manufacture the products
        account.manufacturing().withManufacturingOrderId(account.sale().manufacturingOrderId).completeTask();
        account.manufacturing().withManufacturingOrderId(account.sale().manufacturingOrderId)
                .recordConsumption(materials.stream().map(m -> Tuple.of(m.materialIds().get(0), m.quantity().multiply(toManufacture))).toList());

        mvc.perform(get("/inventory/" + productId + "/history")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.currentStock", closeTo(5, 0.01)))
                .andExpect(jsonPath("$.reservedStock", is(5)))
                .andExpect(jsonPath("$.incomingStock", is(0)))
                .andExpect(jsonPath("$.balance", closeTo(0, 0.01)))
                .andExpect(jsonPath("$.stockHistory.[0].manufacturingOrder.name", is("CV-0001 / 1")))
                .andExpect(jsonPath("$.stockHistory.[0].quantity", closeTo(3, 0.01)))
                .andExpect(jsonPath("$.stockHistory.[1].inventoryAdjustmentOrder.name", is("AS-0003")))
                .andExpect(jsonPath("$.stockHistory.[1].quantity", closeTo(2, 0.01)))
                .andExpect(jsonPath("$.committedTo.[0].salesOrder.id", is(salesOrderId)))
                .andExpect(jsonPath("$.committedTo.[0].salesOrder.name", is("CV-0001")))
                .andExpect(jsonPath("$.committedTo.[0].manufacturingOrder", nullValue()))
                .andExpect(jsonPath("$.incomingOrders", empty()));

        //create a purchase order for the raw material
        account.purchases().create(account.supplier().supplierId,
                List.of(new Purchases.ItemData(
                                null,
                                rawMaterialId,
                                BigDecimal.TEN,
                                new Money(10, Currency.getInstance("RON")),
                                LocalDate.now().plusDays(30)
                        )
                )
        );

        mvc.perform(get("/inventory/" + rawMaterialId + "/history")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.currentStock", closeTo(70, 0.01)))
                .andExpect(jsonPath("$.reservedStock", is(0)))
                .andExpect(jsonPath("$.incomingStock", is(10)))
                .andExpect(jsonPath("$.balance", closeTo(80, 0.01)))
                .andExpect(jsonPath("$.stockHistory.[0].manufacturingOrder.name", is("CV-0001 / 1")))
                .andExpect(jsonPath("$.stockHistory.[0].quantity", closeTo(-30, 0.01)))
                .andExpect(jsonPath("$.stockHistory.[1].inventoryAdjustmentOrder.name", is("AS-0002")))
                .andExpect(jsonPath("$.stockHistory.[1].quantity", closeTo(100, 0.01)))
                .andExpect(jsonPath("$.committedTo", empty()))
                .andExpect(jsonPath("$.incomingOrders.[0].supplier.id", is(account.supplier().supplierId)))
                .andExpect(jsonPath("$.incomingOrders.[0].quantity", is(10)));
    }

    @Test
    public void transferBetweenInventoryTypes() throws Exception {

        var categoryName = TestValueConstants.CATEGORIES.poll();
        Account account = setup
                .account()
                .category().withName(categoryName).create().account()
                .supplier().withName(TestValueConstants.SUPPLIERS.poll()).create().account()
                .location().withName("HQ").create().account();

        //create the product
        var productName = TestValueConstants.PRODUCTS.poll();
        account.good()
                .withName(productName)
                .withCategory(account.category().categoryId)
                .withDescription("something to write with")
                .withSellPrice(321)
                .withMarkup(BigDecimal.valueOf(1.5))
                .create();

        //first fill
        account.inventory()
                .withGood(account.good().goodId)
                .withQuantity(BigDecimal.TEN)
                .fill();

        mvc.perform(get("/inventory/adjustments/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON));


        //second fill
        account.inventory()
                .withGood(account.good().goodId)
                .withQuantity(BigDecimal.valueOf(-1))
                .fill();

        var defaultInventoryUnit = account.inventory().defaultInventoryUnit();
        var subInventoryUnit = account.inventory().subInventoryUnit();

        mvc.perform(get("/inventory/list?q=" + productName + "&inventoryUnitFilter=" + defaultInventoryUnit)
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].name", is(productName)))
                .andExpect(jsonPath("[0].quantity", closeTo(9.0, 0.001)))
                .andExpect(jsonPath("[0].category.name", is(categoryName)));

        account.inventory().moveToUnit(defaultInventoryUnit, subInventoryUnit,
                account.good().goodId, BigDecimal.valueOf(5));

        mvc.perform(get("/inventory/list?q=" + productName + "&inventoryUnitFilter=" + defaultInventoryUnit)
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].name", is(productName)))
                .andExpect(jsonPath("[0].quantity", closeTo(4.0, 0.001)))
                .andExpect(jsonPath("[0].inventoryUnit.name",is(SetupUtils.INVENTORY_HQ_UNIT)));

        mvc.perform(get("/inventory/list?q=" + productName + "&inventoryUnitFilter=" + subInventoryUnit)
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].name", is(productName)))
                .andExpect(jsonPath("[0].quantity", closeTo(5.0, 0.001)))
                .andExpect(jsonPath("[0].inventoryUnit.name",is(SetupUtils.INVENTORY_SUB_UNIT)));
    }

}
