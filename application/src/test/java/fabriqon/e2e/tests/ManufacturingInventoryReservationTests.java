package fabriqon.e2e.tests;

import fabriqon.app.business.goods.RequiredMaterial;
import fabriqon.e2e.IntegrationTest;
import fabriqon.e2e.data.TestValueConstants;
import fabriqon.e2e.dsl.Account;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import static org.junit.Assert.*;

public class ManufacturingInventoryReservationTests extends IntegrationTest {

    @Test
    public void testReservations() {
        Account account = setup
                .account()
                .category().withName(TestValueConstants.CATEGORIES.poll()).create().account()
                .supplier().withName(TestValueConstants.SUPPLIERS.poll()).create().account()
                .location().withName("HQ").create().account()
                .customer().create().account();

        //create raw material
        var rawMaterialId = account.good()
                .withName(TestValueConstants.MATERIALS.poll())
                .withCategory(account.category().categoryId)
                .withDescription("raw material")
                .create().goodId;

        //create the product
        account.good()
                .withCategory(account.category().categoryId)
                .withMaterials(List.of(new RequiredMaterial(List.of(UUID.fromString(account.good().goodId)), BigDecimal.TEN, false, false, false, BigDecimal.ZERO, null, null)))
                .create();

        //create the MOs
        var order1Id = account.manufacturing().orderManufacturing(account.good().goodId, BigDecimal.ONE).manufacturingOrderId;
        var order2Id = account.manufacturing().orderManufacturing(account.good().goodId, BigDecimal.ONE).manufacturingOrderId;

        var order1 = account.manufacturing().order(order1Id);
        assertFalse(order1.allMaterialsAvailable());
        var order2 = account.manufacturing().order(order2Id);
        assertFalse(order2.allMaterialsAvailable());

        //add 15 materials to stock so the first order can be fulfilled but the 2nd is only partially available
        account.inventory()
                .withGood(rawMaterialId)
                .withQuantity(BigDecimal.valueOf(15))
                .fill();

        order1 = account.manufacturing().order(order1Id);
        assertTrue(order1.allMaterialsAvailable());
        order2 = account.manufacturing().order(order2Id);
        assertFalse(order2.allMaterialsAvailable());
        assertEquals(0, order2.manufacturingOperations().getFirst().materials().get(0).reservedTotal().compareTo(BigDecimal.valueOf(5)));

        //add 10 materials to stock so the first order can be fulfilled but the 2nd is only partially available
        account.inventory()
                .withGood(rawMaterialId)
                .withQuantity(BigDecimal.valueOf(10))
                .fill();

        order1 = account.manufacturing().order(order1Id);
        assertTrue(order1.allMaterialsAvailable());
        order2 = account.manufacturing().order(order2Id);
        assertTrue(order2.allMaterialsAvailable());
        assertEquals(0, order2.manufacturingOperations().getFirst().materials().get(0).reservedTotal().compareTo(BigDecimal.valueOf(10)));

        //move 7 materials to a different inventory unit
        account.inventory().moveToUnit(account.inventory().defaultInventoryUnit(), account.inventory().subInventoryUnit(),
                rawMaterialId, BigDecimal.valueOf(7));

        order1 = account.manufacturing().order(order1Id);
        assertTrue(order1.allMaterialsAvailable());
        order2 = account.manufacturing().order(order2Id);
        assertFalse(order2.allMaterialsAvailable());
        assertEquals(0, order2.manufacturingOperations().getFirst().materials().get(0).reservedTotal().compareTo(BigDecimal.valueOf(8)));

    }

}