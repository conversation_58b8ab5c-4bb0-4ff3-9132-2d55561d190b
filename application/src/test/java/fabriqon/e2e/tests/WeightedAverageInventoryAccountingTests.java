package fabriqon.e2e.tests;

import fabriqon.e2e.IntegrationTest;
import fabriqon.e2e.data.TestValueConstants;
import fabriqon.e2e.dsl.Account;
import org.junit.Test;
import org.springframework.http.MediaType;

import java.math.BigDecimal;

import static fabriqon.app.business.accounts.Account.Settings.General.InventoryAccountingSettings.Method.WEIGHTED_AVERAGE;
import static org.hamcrest.Matchers.closeTo;
import static org.hamcrest.Matchers.equalTo;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class WeightedAverageInventoryAccountingTests extends IntegrationTest {

    @Test
    public void calculatesInventoryValue() throws Exception {

        Account account = setup
                .account()
                .settings().withAccountingMethod(WEIGHTED_AVERAGE).account()
                .category().withName(TestValueConstants.CATEGORIES.poll()).create().account()
                .supplier().withName(TestValueConstants.SUPPLIERS.poll()).create().account()
                .location().withName("HQ").create().account()
                .customer().create().account();

        //create 1st product
        var firstProductId = account.good()
                .withName("apron")
                .withCategory(account.category().categoryId)
                .withSellPrice(130)
                .create().goodId;

        //empty inventory
        mvc.perform(get("/inventory/list?materialType=products")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON));

        //first fill
        account.inventory()
                .withGood(firstProductId)
                .withQuantity(BigDecimal.TEN)
                .withPrice(100)
                .fill();
        //second fill
        account.inventory()
                .withGood(firstProductId)
                .withQuantity(BigDecimal.TEN)
                .withPrice(110)
                .fill();

        mvc.perform(get("/inventory/list?materialType=products")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].quantity", closeTo(20, 0.001)))
                .andExpect(jsonPath("[0].stockValue.amount", equalTo(2100)))
                .andExpect(jsonPath("[0].sellingPrice.amount", equalTo(130)))
                .andExpect(jsonPath("[0].profit.amount", equalTo(25)));

        //sell 5 products out of the 20 with acquisition price of 100
        account.sale().withProduct(firstProductId).withQuantity(BigDecimal.valueOf(5)).create().readyToShip().ship();

        mvc.perform(get("/inventory/list?materialType=products")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].quantity", closeTo(15, 0.001)))
                .andExpect(jsonPath("[0].stockValue.amount", equalTo(1600)))
                .andExpect(jsonPath("[0].sellingPrice.amount", equalTo(130)))
                .andExpect(jsonPath("[0].profit.amount", equalTo(24)));

        //sell 10 products out of the 15; 5 with acquisition price of 100 and 5 with acquisition price of 110
        account.sale().withProduct(firstProductId).withQuantity(BigDecimal.valueOf(10)).create().readyToShip().ship();

        mvc.perform(get("/inventory/list?materialType=products")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].quantity", closeTo(5, 0.001)))
                .andExpect(jsonPath("[0].stockValue.amount", equalTo(550)))
                .andExpect(jsonPath("[0].sellingPrice.amount", equalTo(130)))
                .andExpect(jsonPath("[0].profit.amount", equalTo(20)));
    }

}
