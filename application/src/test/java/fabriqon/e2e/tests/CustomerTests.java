package fabriqon.e2e.tests;

import fabriqon.app.common.model.BankAccount;
import fabriqon.app.common.model.Company;
import fabriqon.app.http.controllers.CustomersController;
import fabriqon.e2e.IntegrationTest;
import fabriqon.e2e.data.TestValueConstants;
import fabriqon.e2e.dsl.Account;
import fabriqon.misc.Json;
import org.junit.Test;
import org.springframework.http.MediaType;

import java.util.List;

import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class CustomerTests extends IntegrationTest {

    @Test
    public void crud() throws Exception {
        var customerName = TestValueConstants.CUSTOMERS.poll();
        Account account = setup
                .account()
                .customer().withName(customerName).create()
                .account();

        mvc.perform(get("/customers/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].name", is(customerName)))
                .andExpect(jsonPath("[0].addresses.[0].name", is("HQ")))
                .andExpect(jsonPath("[0].contacts.[0].name", is("First Last")))
                .andExpect(jsonPath("[0].bankAccounts.[0].name", is("RON CRT")));

        mvc.perform(post("/customers/" + account.customer().customerId + "/update")
                        .content(Json.write(new CustomersController.CustomerDefinition(
                                Company.Type.LOCAL_LEGAL_ENTITY,customerName + " U", null, null,
                                null, null,
                                List.of(
                                        new BankAccount("RON CRT", "GB29 NWBK 6016 1331 9268 19", "Banca Transilvania", null),
                                        new BankAccount("EUR CRT", "GB29 NWBK 6016 1331 9268 19", "Banca Transilvania", null)
                                ),
                                null)))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        mvc.perform(get("/customers/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].name", is(customerName + " U")))
                .andExpect(jsonPath("[0].bankAccounts.[1].name", is("EUR CRT")));
    }

}
