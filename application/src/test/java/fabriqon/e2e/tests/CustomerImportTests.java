//package fabriqon.e2e.tests;
//
//import fabriqon.e2e.IntegrationTest;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.core.io.ResourceLoader;
//import org.springframework.http.MediaType;
//import org.springframework.mock.web.MockMultipartFile;
//
//import static org.hamcrest.CoreMatchers.is;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
//import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
//
//public class CustomerImportTests extends IntegrationTest {
//
//    final String customers = "name,tax_identification_number,address_name,address_country,address_city,address_state,address_address1,address_address2,address_zip,contact_firstname,contact_lastname,contact_phonenumber,contact_email\n" +
//            "IKEA Import,tax_id_123,IKEA HQ,SE,Almhult, Kronoberg,Main road 1,,123123,Ingvar,Kamprad,+********,<EMAIL>";
//    @Autowired
//    private ResourceLoader resourceLoader;
//
//    @Test
//    public void importCustomers() throws Exception {
//        var account = setup.account();
//        mvc.perform(multipart("/customers/import/default")
//                        .file(new MockMultipartFile("csv", "customers.csv", "text/plain", customers.getBytes()))
//                        .header("Authorization", "Bearer " + account.accountId)
//                )
//                .andExpect(status().isOk())
//                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.imported", is(1)));
//    }
//
//    @Test
//    public void importCustomersFromSaga() throws Exception {
//        var account = setup.account();
//        mvc.perform(multipart("/customers/import/saga")
//                        .file(new MockMultipartFile("csv", "customers.csv", "text/plain", resourceLoader.getResource("classpath:csv/customers_saga.csv").getInputStream()))
//                        .header("Authorization", "Bearer " + account.accountId)
//                )
//                .andExpect(status().isOk())
//                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.imported", is(142)));
//    }
//
//
//}
