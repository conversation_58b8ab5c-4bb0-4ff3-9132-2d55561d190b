package fabriqon.e2e.dsl;

import fabriqon.app.business.accounts.Account.Settings.General.InventoryAccountingSettings.Method;
import fabriqon.misc.Json;
import lombok.SneakyThrows;
import org.springframework.http.MediaType;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class Settings {

    private fabriqon.e2e.dsl.Account account;

    public Settings(Account account) {
        this.account = account;
    }

    @SneakyThrows
    public Settings withAccountingMethod(Method accountingMethod) {
        var settings = settings();
        var update = new fabriqon.app.business.accounts.Account.Settings(
                new fabriqon.app.business.accounts.Account.Settings.General(
                        new fabriqon.app.business.accounts.Account.Settings.General.InventoryAccountingSettings(accountingMethod,
                                settings.general().inventoryAccountingSettings().unitDesignations()),
                        settings.general().defaultDeliveryTimeForSalesOrders(),
                        settings.general().defaultLanguage(),
                        settings.general().defaultCurrency(),
                        settings.general().defaultTimeZone()
                        ),
                settings.enabledMeasurementUnits(), settings.defaultVAT(), settings.manufacturing()
        );
        account.dsl().mvc()
                .perform(post("/account/settings/update")
                        .header("Authorization", "Bearer " + account.accountId)
                        .content(Json.write(update))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andReturn();
        return this;
    }

    public Account account() {
        return account;
    }

    @SneakyThrows
    private fabriqon.app.business.accounts.Account.Settings settings() {
        var result = account.dsl().mvc()
                .perform(get("/account/settings")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andReturn();
        return Json.read(result.getResponse().getContentAsString(), fabriqon.app.business.accounts.Account.Settings.class);
    }
}
