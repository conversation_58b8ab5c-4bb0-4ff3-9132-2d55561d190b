package fabriqon.e2e.dsl;

import com.jayway.jsonpath.JsonPath;
import fabriqon.app.common.model.Address;
import fabriqon.app.common.model.BankAccount;
import fabriqon.app.common.model.Company;
import fabriqon.app.common.model.ContactPerson;
import fabriqon.app.http.controllers.SuppliersController;
import fabriqon.misc.Json;
import lombok.SneakyThrows;
import org.springframework.http.MediaType;

import java.util.List;
import java.util.Set;

import static fabriqon.app.common.model.Address.Type.BILLING;
import static fabriqon.app.common.model.Address.Type.SHIPPING;
import static fabriqon.e2e.data.Utils.smallRandomInt;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class Supplier {
    private final Account account;
    private final Data data;
    public String supplierId;

    public Supplier(Account account) {
        this.account = account;
        this.data = new Data();
    }


    public Supplier withName(String name) {
        this.data.name = name;
        return this;
    }

    @SneakyThrows
    public Supplier create() {
        var result = account.dsl().mvc()
                .perform(post("/suppliers/create")
                        .content(Json.write(new SuppliersController.SupplierDefinition(data.name, data.identificationNumber, data.taxIdentificationNumber,
                                data.addresses, data.contacts, data.bankAccounts, null, 14)))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("id", notNullValue(String.class)))
                .andReturn();
        supplierId = JsonPath.read(result.getResponse().getContentAsString(), "$.id");
        return this;
    }

    public Account account() {
        return account;
    }

    private static class Data {
        String name = "supplier " + smallRandomInt();
        String identificationNumber = "id_" + smallRandomInt();
        String taxIdentificationNumber = "tax_id_" + smallRandomInt();
        List<Address> addresses = List.of(new Address("HQ", "RO", "Cluj-Napoca", "CJ", "Turzii 123", null, "400300", Set.of(BILLING, SHIPPING)));
        List<ContactPerson> contacts = List.of(new ContactPerson("First Last", "0740 123321", "<EMAIL>", null));
        List<BankAccount> bankAccounts = List.of(new BankAccount("RON CRT", "GB29 NWBK 6016 1331 9268 19", "Banca Transilvania", null));
    }
}
