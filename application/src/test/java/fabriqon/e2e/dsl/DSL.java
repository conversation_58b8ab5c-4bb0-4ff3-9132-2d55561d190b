package fabriqon.e2e.dsl;

import lombok.SneakyThrows;
import org.springframework.test.web.servlet.MockMvc;

import static fabriqon.e2e.SetupUtils.randomAccount;

public class DSL {

    private MockMvc mvc;

    public DSL(MockMvc mvc) {
        this.mvc = mvc;
    }

    public MockMvc mvc() {
        return mvc;
    }

    @SneakyThrows
    public Account account() {
        return new Account(this, randomAccount());
    }

}
