package fabriqon.e2e.dsl;

import com.jayway.jsonpath.JsonPath;
import fabriqon.app.business.financial.SystemCurrencies;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.common.model.Money;
import fabriqon.app.http.controllers.ServiceTemplatesController;
import fabriqon.misc.Json;
import lombok.SneakyThrows;
import org.springframework.http.MediaType;

import java.math.BigDecimal;

import static fabriqon.e2e.data.Utils.smallRandomInt;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class Service {
    private final Account account;
    private final Data data = new Data();
    public String serviceId;

    public Service(Account account) {
        this.account = account;
    }

    public Service withName(String name) {
        this.data.name = name;
        return this;
    }

    @SneakyThrows
    public Service create() {
        var result = account.dsl().mvc()
                .perform(post("/service-templates/create")
                        .content(Json.write(new ServiceTemplatesController.ServiceTemplateDefinition(data.name,
                                new BigDecimal("0.19"), MeasurementUnit.PIECE, new Money(1500, SystemCurrencies.RON), null)))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("id", notNullValue(String.class)))
                .andReturn();
        serviceId = JsonPath.read(result.getResponse().getContentAsString(), "$.id");
        return this;
    }

    public Account account() {
        return account;
    }

    private static class Data {
        String name = "random service " + smallRandomInt();
    }
}
