package fabriqon.e2e.dsl;

import com.jayway.jsonpath.JsonPath;
import fabriqon.app.common.model.Address;
import fabriqon.app.common.model.BankAccount;
import fabriqon.app.common.model.Company;
import fabriqon.app.common.model.ContactPerson;
import fabriqon.app.http.controllers.CustomersController;
import fabriqon.misc.Json;
import lombok.SneakyThrows;
import org.springframework.http.MediaType;

import java.util.List;
import java.util.Set;

import static fabriqon.app.common.model.Address.Type.BILLING;
import static fabriqon.app.common.model.Address.Type.SHIPPING;
import static fabriqon.e2e.data.Utils.smallRandomInt;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class Customer {
    private final Account account;
    public String customerId;
    private final Data data;

    public Customer(Account account) {
        this.account = account;
        this.data = new Data();
    }


    public Customer withName(String name) {
        this.data.name = name;
        return this;
    }

    public Customer withType(Company.Type type) {
        this.data.type = type;
        return this;
    }

    @SneakyThrows
    public Customer create() {
        var result = account.dsl().mvc()
                .perform(post("/customers/create")
                        .content(Json.write(new CustomersController.CustomerDefinition(
                                data.type, data.name, data.taxIdentificationNumber, data.taxIdentificationNumber, data.addresses, data.contacts, data.bankAccounts, null)))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("id", notNullValue(String.class)))
                .andReturn();
        customerId = JsonPath.read(result.getResponse().getContentAsString(), "$.id");
        return this;
    }

    public Account account() {
        return account;
    }

    private static class Data {
        Company.Type type = Company.Type.LOCAL_LEGAL_ENTITY;
        String name = "Customer " + smallRandomInt();
        String identificationNumber = "id_" + smallRandomInt();
        String taxIdentificationNumber = "tax_id_" + smallRandomInt();
        List<Address> addresses = List.of(new Address("HQ", "RO", "Cluj Napoca", "CJ", "Turzii 123", null, "400300", Set.of(BILLING, SHIPPING)));
        List<ContactPerson> contacts = List.of(new ContactPerson("First Last", "0740 123321", "<EMAIL>", null));
        List<BankAccount> bankAccounts = List.of(new BankAccount("RON CRT", "GB29 NWBK 6016 1331 9268 19", "Banca Transilvania", null));
    }
}
