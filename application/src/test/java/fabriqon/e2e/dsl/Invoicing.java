package fabriqon.e2e.dsl;

public class Invoicing {
    private final Account account;

    public String invoicingId;


    public Invoicing(Account account) {
        this.account = account;
    }


    public Invoicing withInvoicingId(String invoicingId) {
        this.invoicingId = invoicingId;
        return this;
    }


    //todo

    public Account account() {
        return account;
    }
}
