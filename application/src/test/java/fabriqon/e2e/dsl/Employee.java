package fabriqon.e2e.dsl;

import com.jayway.jsonpath.JsonPath;
import fabriqon.app.common.model.Money;
import fabriqon.app.http.controllers.EmployeesController;
import fabriqon.misc.Json;
import lombok.SneakyThrows;
import org.springframework.http.MediaType;

import java.util.Currency;
import java.util.UUID;

import static fabriqon.e2e.data.Utils.smallRandomInt;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class Employee {

    private final Account account;
    private final Data data = new Data();
    public UUID employeeId;

    public Employee(Account account) {
        this.account = account;
    }

    public Employee withName(String name) {
        this.data.name = name;
        return this;
    }

    @SneakyThrows
    public Employee create() {
        var result = account.dsl().mvc()
                .perform(post("/employees/create")
                        .content(Json.write(new EmployeesController.EmployeeDefinition(
                                data.name, data.position, data.monthlyGrossSalary, null
                        )))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("id", notNullValue(String.class)))
                .andReturn();
        employeeId = UUID.fromString(JsonPath.read(result.getResponse().getContentAsString(), "$.id"));
        return this;
    }

    public Account account() {
        return account;
    }

    private static class Data {
        String name = "John Doe " + smallRandomInt();
        String position = "Worker";
        Money monthlyGrossSalary = new Money(1000000, Currency.getInstance("RON"));
    }
}
