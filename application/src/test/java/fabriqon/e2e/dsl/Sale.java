package fabriqon.e2e.dsl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jayway.jsonpath.JsonPath;
import fabriqon.app.business.sales.SalesOrder;
import fabriqon.app.common.model.Address;
import fabriqon.app.common.model.Money;
import fabriqon.app.http.controllers.GoodsAccompanyingNotesController;
import fabriqon.app.http.controllers.SalesOrdersController;
import fabriqon.misc.Json;
import lombok.SneakyThrows;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.ResultActions;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Currency;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static fabriqon.e2e.data.Utils.mediumRandomInt;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class Sale {
    static final Address ADDRESS = new Address("billing & shipping", "RO", "Cluj-Napoca", "CJ", "G.V. Bibescu 39-43", "bloc C1, ap. 1", "400365", Set.of(Address.Type.BILLING, Address.Type.SHIPPING));
    private final Account account;
    public String salesOrderId;
    public String manufacturingOrderId;
    public String invoiceId;
    public String goodsAccompanyingNoteId;
    private CreateSaleData createSaleData;

    public Sale(Account account) {
        this.account = account;
        this.createSaleData = new CreateSaleData();
    }

    public Sale withSalesOrderId(String orderId) {
        salesOrderId = orderId;
        return this;
    }

    public Sale withProduct(String productId) {
        this.createSaleData.productId = productId;
        return this;
    }

    public Sale withService(String serviceId) {
        this.createSaleData.serviceId = serviceId;
        this.createSaleData.productId = null;//this is a service if it has a name so no productId can be present
        return this;
    }

    public Sale withQuantity(BigDecimal quantity) {
        this.createSaleData.quantity = quantity;
        return this;
    }

    public Sale withSellPrice(long price) {
        this.createSaleData.sellPrice = price;
        return this;
    }

    public Sale withCustomizations(List<SalesOrder.Item.Customization> customizations) {
        this.createSaleData.customizations = customizations;
        return this;
    }

    @SneakyThrows
    public Sale create() {
        var result = createWithResult()
                .andExpect(status().isOk())
                .andReturn();
        salesOrderId = JsonPath.read(result.getResponse().getContentAsString(), "$.id");
        return this;
    }

    @SneakyThrows
    public ResultActions createWithResult() {
        return account.dsl().mvc()
                .perform(post("/sales/orders/create")
                        .content(Json.write(new SalesOrdersController.CreateSalesOrder(LocalDateTime.now(),
                                createSaleData.deliveryDeadline, UUID.fromString(createSaleData.customerId), false,
                                List.of(new SalesOrdersController.Item(
                                        createSaleData.productId != null ? UUID.fromString(createSaleData.productId) : null,
                                        createSaleData.serviceId != null ? UUID.fromString(createSaleData.serviceId) : null,
                                        createSaleData.quantity,
                                        new Money(createSaleData.sellPrice, Currency.getInstance("RON")),
                                        null, null, createSaleData.customizations)),
                                ADDRESS, null, null, null, null, null, null
                        )))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON));
    }

    @SneakyThrows
    public Sale manufactureMissing(String productId, BigDecimal quantity) {
        return manufactureMissing(UUID.fromString(productId), null, quantity);
    }

    @SneakyThrows
    public Sale manufactureMissing(UUID productId, UUID serviceId, BigDecimal quantity) {
        var result = account.dsl().mvc()
                .perform(post("/sales/orders/" + salesOrderId + "/manufacture-missing")
                        .content(Json.write(new SalesOrdersController.ProductManufacturingOrder(
                                productId, serviceId, quantity, null)
                        ))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        //we only store the first order id
        manufacturingOrderId = JsonPath.read(result.getResponse().getContentAsString(), "id");
        return this;
    }

    @SneakyThrows
    public Sale readyToShip() {
        var result = account.dsl().mvc()
                .perform(post("/sales/orders/" + salesOrderId + "/ready-to-ship")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        return this;
    }

    @SneakyThrows
    public Sale ship() {
        var result = account.dsl().mvc()
                .perform(post("/sales/orders/" + salesOrderId + "/ship")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        return this;
    }

    @SneakyThrows
    public Sale invoice() {
        var result = account.dsl().mvc()
                .perform(post("/sales/orders/" + salesOrderId + "/invoice")
                        .content(Json.write(new SalesOrdersController.CreateInvoice(false, null, null, null)))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        invoiceId = JsonPath.read(result.getResponse().getContentAsString(), "$.id");
        return this;
    }

    @SneakyThrows
    public Sale goodsAccompanyingNote(LocalDate deliveryDate,
                                      Address from, Address to, UUID delegateId,
                                      String transportRegistrationNumber, String notes, List<GoodsAccompanyingNotesController.GoodsAccompanyingNoteDefinition.GoodsAccompanyingNoteDefinitionItem> items) {
        var result = account.dsl().mvc()
                .perform(post("/sales/orders/" + salesOrderId + "/goods-accompanying-note")
                        .content(Json.write(new GoodsAccompanyingNotesController.GoodsAccompanyingNoteDefinition(
                                deliveryDate, from, to, delegateId, transportRegistrationNumber, notes, items
                        )))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        goodsAccompanyingNoteId = JsonPath.read(result.getResponse().getContentAsString(), "$.id");
        return this;
    }

    @SneakyThrows
    public List<SalesOrdersController.SalesOrderDetails> list() {
        var result = account.dsl().mvc()
                .perform(get("/sales/orders/list")
                        .accept(MediaType.APPLICATION_JSON)
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        return Json.read(result.getResponse().getContentAsString(), new TypeReference<List<SalesOrdersController.SalesOrderDetails>>() {});
    }

    @SneakyThrows
    public SalesOrdersController.SalesOrderDetails order(String orderId) {
        var result = account.dsl().mvc()
                .perform(get("/sales/orders/" + orderId + "/details")
                        .accept(MediaType.APPLICATION_JSON)
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        return Json.read(result.getResponse().getContentAsString(), SalesOrdersController.SalesOrderDetails.class);
    }

    public Account account() {
        return account;
    }

    private class CreateSaleData {
        String productId = account.good().goodId;
        String serviceId = null;
        LocalDateTime deliveryDeadline = LocalDateTime.now().plusDays(30);
        BigDecimal quantity = BigDecimal.ONE;
        long sellPrice = mediumRandomInt();
        String customerId = account.customer().customerId;
        List<SalesOrder.Item.Customization> customizations;
    }
}
