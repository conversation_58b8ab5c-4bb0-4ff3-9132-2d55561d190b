package fabriqon.e2e.dsl;

import com.jayway.jsonpath.JsonPath;
import fabriqon.app.http.controllers.goods.CategoriesController;
import fabriqon.misc.Json;
import lombok.SneakyThrows;
import org.springframework.http.MediaType;

import static fabriqon.e2e.data.Utils.smallRandomInt;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class Category {
    private final Account account;
    private final Data data = new Data();
    public String categoryId;

    public Category(Account account) {
        this.account = account;
    }

    public Category withName(String name) {
        this.data.name = name;
        return this;
    }

    @SneakyThrows
    public Category create() {
        var result = account.dsl().mvc()
                .perform(post("/categories/create")
                        .content(Json.write(new CategoriesController.CreateCategory(data.name)))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("id", notNullValue(String.class)))
                .andReturn();
        categoryId = JsonPath.read(result.getResponse().getContentAsString(), "$.id");
        return this;
    }

    public Account account() {
        return account;
    }

    private static class Data {
        String name = "random cat. " + smallRandomInt();
    }
}
