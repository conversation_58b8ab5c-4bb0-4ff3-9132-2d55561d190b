package fabriqon.e2e.dsl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jayway.jsonpath.JsonPath;
import fabriqon.app.http.controllers.GoodsAccompanyingNotesController;
import fabriqon.app.http.controllers.manufacturing.ManufacturingOrdersController;
import fabriqon.app.http.controllers.services.ServicingOrdersController;
import fabriqon.misc.Json;
import lombok.SneakyThrows;
import org.springframework.http.MediaType;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class Servicing {
    private final Account account;

    public String servicingOrderId;


    public Servicing(Account account) {
        this.account = account;
    }


    public Servicing withManufacturingOrderId(String servicingOrderId) {
        this.servicingOrderId = servicingOrderId;
        return this;
    }

    @SneakyThrows
    public Servicing orderManufacturing(String productId, BigDecimal quantity) {
        var result = account.dsl().mvc()
                .perform(post("/servicing/orders/create")
                        .content(Json.write(new ManufacturingOrdersController.CreateManufacturingOrder(
                                LocalDateTime.now(),
                                UUID.fromString(productId), null,
                                quantity, null, false)))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        servicingOrderId = JsonPath.read(result.getResponse().getContentAsString(), "$.id");
        return this;
    }

    @SneakyThrows
    public Servicing orderServicing(String serviceId, BigDecimal quantity) {
        var result = account.dsl().mvc()
                .perform(post("/servicing/orders/create")
                        .content(Json.write(new ManufacturingOrdersController.CreateManufacturingOrder(
                                LocalDateTime.now(),
                                null, UUID.fromString(serviceId),
                                quantity, null, false)))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        servicingOrderId = JsonPath.read(result.getResponse().getContentAsString(), "$.id");
        return this;
    }

    @SneakyThrows
    public Servicing deleteOrder(String manufacturingOrderId) {
        account.dsl().mvc().perform(delete("/servicing/orders/" + manufacturingOrderId + "/delete")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        return this;
    }

    @SneakyThrows
    public List<ServicingOrdersController.ServicingOrderDetails> list() {
        var result = account.dsl().mvc()
                .perform(get("/servicing/orders/list")
                        .accept(MediaType.APPLICATION_JSON)
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        return Json.read(result.getResponse().getContentAsString(), new TypeReference<List<ServicingOrdersController.ServicingOrderDetails>>() {});
    }

    @SneakyThrows
    public ServicingOrdersController.ServicingOrderDetails order(String orderId) {
        var result = account.dsl().mvc()
                .perform(get("/servicing/orders/" + orderId + "/details")
                        .accept(MediaType.APPLICATION_JSON)
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        return Json.read(result.getResponse().getContentAsString(), ServicingOrdersController.ServicingOrderDetails.class);
    }

    @SneakyThrows
    public Servicing goodsAccompanyingNote(String serviceMoId, String materialId, BigDecimal quantity) {
        var result = account.dsl().mvc()
                .perform(post("/servicing/orders/" + serviceMoId + "/goods-accompanying-note")
                        .content(Json.write(new GoodsAccompanyingNotesController.GoodsAccompanyingNoteDefinition(
                                LocalDate.now(),
                                null, null, account.employee().employeeId, "transport", null,
                                List.of(new GoodsAccompanyingNotesController.GoodsAccompanyingNoteDefinition.GoodsAccompanyingNoteDefinitionItem(UUID.fromString(materialId), quantity)))))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        return this;
    }

    public Account account() {
        return account;
    }
}
