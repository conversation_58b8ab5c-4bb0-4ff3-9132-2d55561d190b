package fabriqon.e2e.dsl;

import com.jayway.jsonpath.JsonPath;
import fabriqon.app.http.controllers.LocationsController;
import fabriqon.misc.Json;
import lombok.SneakyThrows;
import org.springframework.http.MediaType;

import static fabriqon.e2e.data.Utils.smallRandomInt;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class Location {
    private final Account account;
    private final Location.Data data = new Location.Data();
    public String locationId;

    public Location(Account account) {
        this.account = account;
    }

    public Location withName(String name) {
        this.data.name = name;
        return this;
    }

    @SneakyThrows
    public Location create() {
        var result = account.dsl().mvc()
                .perform(post("/locations/create")
                        .content(Json.write(new LocationsController.CreateLocation(data.name)))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("id", notNullValue(String.class)))
                .andReturn();
        locationId = JsonPath.read(result.getResponse().getContentAsString(), "$.id");
        return this;
    }

    public Account account() {
        return account;
    }

    private static class Data {
        String name = "location " + smallRandomInt();
    }
}
