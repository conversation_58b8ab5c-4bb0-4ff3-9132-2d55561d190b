package fabriqon.e2e.dsl;

public class Account {
    public final String accountId;
    private final DSL dsl;
    private Supplier supplier;
    private Location location;
    private Category category;
    private Goods product;
    private Inventory inventory;
    private Sale sale;
    private Customer customer;
    private Manufacturing manufacturing;
    private Servicing servicing;
    private Employee employee;
    private ManufacturingWorkstation manufacturingWorkstation;
    private ManufacturingOperationTemplate manufacturingOperationTemplate;
    private Purchases purchases;
    private Invoicing invoicing;
    private Service service;

    public Account(DSL dsl, String accountId) {
        this.dsl = dsl;
        this.accountId = accountId;
    }

    public Settings settings() {
        return new Settings(this);
    }

    public Supplier supplier() {
        return supplier != null ? supplier : (supplier = new Supplier(this));
    }

    public Location location() {
        return location != null ? location : (location = new Location(this));
    }

    public Category category() {
        return category != null ? category : (category = new Category(this));
    }

    public Goods good() {
        return product != null ? product : (product = new Goods(this));
    }

    public Inventory inventory() {
        return inventory != null ? inventory : (inventory = new Inventory(this));
    }

    public Sale sale() {
        return sale != null ? sale : (sale = new Sale(this));
    }

    public Customer customer() {
        return customer != null ? customer : (customer = new Customer(this));
    }

    public Manufacturing manufacturing() {
        return manufacturing != null ? manufacturing : (manufacturing = new Manufacturing(this));
    }

    public Servicing servicing() {
        return servicing != null ? servicing : (servicing = new Servicing(this));
    }

    public ManufacturingWorkstation manufacturingWorkstation() {
        return manufacturingWorkstation != null ? manufacturingWorkstation : (manufacturingWorkstation = new ManufacturingWorkstation(this));
    }

    public ManufacturingOperationTemplate manufacturingOperationTemplate() {
        return manufacturingOperationTemplate != null ? manufacturingOperationTemplate : (manufacturingOperationTemplate = new ManufacturingOperationTemplate(this));
    }

    public Employee employee() {
        return employee != null ? employee : (employee = new Employee(this));
    }

    public Purchases purchases() {
        return purchases != null ? purchases : (purchases = new Purchases(this));
    }

    public Invoicing invoicing() {
        return invoicing != null ? invoicing : (invoicing = new Invoicing(this));
    }

    public Service service() {
        return service != null ? service : (service = new Service(this));
    }

    public DSL dsl() {
        return dsl;
    }

}
