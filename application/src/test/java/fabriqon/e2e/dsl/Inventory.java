package fabriqon.e2e.dsl;

import com.fasterxml.jackson.core.type.TypeReference;
import fabriqon.app.business.inventory.InventoryAdjustmentOrder;
import fabriqon.app.business.inventory.InventoryUnit;
import fabriqon.app.common.model.Money;
import fabriqon.app.http.controllers.inventory.InventoryAdjustmentsController;
import fabriqon.app.http.controllers.inventory.InventoryController;
import fabriqon.e2e.SetupUtils;
import fabriqon.misc.Json;
import lombok.SneakyThrows;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.http.MediaType;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Currency;
import java.util.List;
import java.util.UUID;

import static fabriqon.e2e.data.Utils.mediumRandomInt;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class Inventory {
    private final Account account;
    private FillData fillData;
    private UUID defaultInventoryUnit;
    private UUID subInventoryUnit;

    public Inventory(Account account) {
        this.account = account;
        this.fillData = new FillData();
    }

    public Inventory withGood(String productId) {
        this.fillData.productId = productId;
        return this;
    }

    public Inventory withQuantity(BigDecimal quantity) {
        this.fillData.quantity = quantity;
        return this;
    }

    public Inventory withPrice(long price) {
        this.fillData.price = price;
        return this;
    }

    public Inventory withInventoryUnit(UUID inventoryUnit) {
        this.fillData.inventoryUnit = inventoryUnit;
        return this;
    }

    @SneakyThrows
    public Inventory fill() {
        account.dsl().mvc()
                .perform(post("/inventory/adjustments/adjust")
                        .content(Json.write(new InventoryAdjustmentsController.AdjustOrder(
                                "reason",
                                List.of(
                                        new InventoryAdjustmentOrder.InventoryEntry(
                                                UUID.fromString(fillData.productId),
                                                UUID.fromString(fillData.locationId),
                                                UUID.fromString(fillData.supplierId),
                                                null,
                                                fillData.inventoryUnit,
                                                fillData.purchaseDate,
                                                fillData.expirationDate,
                                                new Money(fillData.price, Currency.getInstance("RON")),
                                                fillData.quantity,
                                                null, null
                                        )
                                )
                        )))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
        return this;
    }

    @SneakyThrows
    public Inventory moveToUnit(UUID from, UUID to, String productId, BigDecimal quantity) {
        account.dsl().mvc()
                .perform(post("/inventory/adjustments/move-to-unit")
                        .content(Json.write(new InventoryAdjustmentsController.MoveUnitOrder(
                                "moving to unit",
                                "deliverer name",
                                "receiver name",
                                List.of(
                                        new InventoryAdjustmentOrder.InventoryEntry(
                                                UUID.fromString(productId),
                                                UUID.fromString(account.location().locationId),
                                                UUID.fromString(account.supplier().supplierId),
                                                from, to,
                                                LocalDateTime.now(),
                                                LocalDateTime.now().plusDays(30),
                                                new Money(mediumRandomInt(), Currency.getInstance("RON")),
                                                quantity,
                                                null, null
                                        )
                                )
                        )))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
        return this;
    }

    @SneakyThrows
    public UUID defaultInventoryUnit() {
        if (defaultInventoryUnit == null) {
            defaultInventoryUnit = inventoryUnit(SetupUtils.INVENTORY_HQ_UNIT);
        }
        return defaultInventoryUnit;
    }

    @SneakyThrows
    public UUID subInventoryUnit() {
        if (subInventoryUnit == null) {
            subInventoryUnit = inventoryUnit(SetupUtils.INVENTORY_SUB_UNIT);
        }
        return subInventoryUnit;
    }

    @SneakyThrows
    public InventoryController.Item stockInformationFor(String materialGoodId, UUID inventoryUnitId) {
        var result = account.dsl().mvc()
                .perform(get("/inventory/list?ids=" + materialGoodId + "&inventoryUnit=" + inventoryUnitId)
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andReturn();
        return Json.read(result.getResponse().getContentAsString(), new TypeReference<List<InventoryController.Item>>() {}).get(0);
    }

    @SneakyThrows
    public InventoryController.ItemHistory itemHistory(String materialGoodId) {
        var result = account.dsl().mvc()
                .perform(get("/inventory/" + materialGoodId + "/history")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andReturn();
        return Json.read(result.getResponse().getContentAsString(), InventoryController.ItemHistory.class);
    }


    @SneakyThrows
    private UUID inventoryUnit(String name) {
        var result = account.dsl().mvc()
                .perform(get("/inventory/units/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andReturn();
        return Json.read(result.getResponse().getContentAsString(), new TypeReference<List<InventoryUnit>>() {})
                .stream()
                .filter(type -> type.name().equals(name))
                .map(InventoryUnit::id)
                .findFirst()
                .orElseThrow();
    }

    public Account account() {
        return account;
    }

    private class FillData {
        String productId;
        String locationId = account.location().locationId;
        String supplierId = account.supplier().supplierId;
        LocalDateTime purchaseDate = LocalDateTime.now();
        LocalDateTime expirationDate = LocalDateTime.now();
        long price = mediumRandomInt();
        BigDecimal quantity = BigDecimal.valueOf(RandomUtils.nextInt(1, 100));
        UUID inventoryUnit = defaultInventoryUnit();
    }
}
