package fabriqon.e2e.dsl;

import com.jayway.jsonpath.JsonPath;
import fabriqon.app.business.inventory.reception.ReceivedGood;
import fabriqon.app.business.inventory.reception.SupportingDocument;
import fabriqon.app.business.purchases.PurchaseOrder;
import fabriqon.app.common.model.EmailMessage;
import fabriqon.app.common.model.Money;
import fabriqon.app.http.controllers.inventory.InventoryReceptionsController;
import fabriqon.app.http.controllers.purchases.PurchaseOrdersController;
import fabriqon.app.http.controllers.purchases.PurchaseWishlistController;
import fabriqon.misc.Json;
import lombok.SneakyThrows;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Currency;
import java.util.List;
import java.util.UUID;

import static java.util.stream.Collectors.toList;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class Purchases {

    private final Account account;

    public String purchasesOrderId;

    public Purchases(Account account) {
        this.account = account;
    }

    @SneakyThrows
    public Purchases create(String supplierId, List<ItemData> items) {
        var result = account.dsl().mvc()
                .perform(post("/purchases/orders/create")
                        .content(Json.write(new PurchaseOrdersController.CreatePurchaseOrder(UUID.fromString(supplierId), PurchaseOrder.Status.SENT_FOR_QUOTE,
                                items.stream().map(
                                                item -> (
                                                        new PurchaseOrdersController.Item(
                                                                item.originalWishlistId != null ? UUID.fromString(item.originalWishlistId) : null,
                                                                UUID.fromString(item.materialGoodId),
                                                                item.quantity,
                                                                item.price)
                                                )
                                        )
                                        .collect(toList()),
                                null, null
                        )))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)
                )
                .andExpect(status().isOk())
                .andReturn();
        purchasesOrderId = JsonPath.read(result.getResponse().getContentAsString(), "$.id");
        return this;
    }

    @SneakyThrows
    public Purchases addToWishlist(String supplierId, String productId, BigDecimal quantity) {
        account.dsl().mvc()
                .perform(post("/purchases/wishlist/add")
                        .content(Json.write(List.of(new PurchaseWishlistController.WishlistItem(
                                        UUID.fromString(productId),
                                        quantity,
                                        UUID.fromString(supplierId),
                                        null)
                        )))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
        return this;
    }

    @SneakyThrows
    public Purchases delivered(String purchaseOrderId, List<ItemData> items) {
        var goodsReceived = Json.write(new InventoryReceptionsController.GoodsReceived(
                UUID.fromString(account.supplier().supplierId),
                UUID.fromString(purchaseOrderId),
                null,
                LocalDate.now(),
                new SupportingDocument(SupportingDocument.Type.INVOICE, "INV-1111", LocalDate.now()),
                Currency.getInstance("RON"),
                "some notes", "John Doe", "Jane Doe", "SJ04GXJ",
                items.stream()
                        .map(item -> new ReceivedGood(UUID.fromString(item.materialGoodId), item.quantity,
                                item.quantity, item.price, account.inventory().defaultInventoryUnit(),
                                null, null))
                        .toList(),
                List.of()
        ));
        MockMultipartFile goodsReceivedPart = new MockMultipartFile(
                "goodsReceived",
                "",
                MediaType.APPLICATION_JSON_VALUE,
                goodsReceived.getBytes()
        );
        var result = account.dsl().mvc()
                .perform(multipart("/purchases/orders/" + purchaseOrderId + "/delivered")
                        .file(goodsReceivedPart)
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.MULTIPART_FORM_DATA)
                )
                .andExpect(status().isOk());
        return this;
    }

    @SneakyThrows
    public Purchases send(String purchaseOrderId) {
        var result = account.dsl().mvc()
                .perform(post("/purchases/orders/" + purchaseOrderId + "/send")
                        .content(Json.write(new EmailMessage("new purchase order", "<b>email body</b>")))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON)
                )
                .andExpect(status().isOk());
        return this;
    }

    public Account account() {
        return account;
    }

    public record ItemData(
            String originalWishlistId,
            String materialGoodId,
            BigDecimal quantity,
            Money price,
            LocalDate expectedDelivery
    ) {
    }

}
