package fabriqon.e2e.dsl;

import com.jayway.jsonpath.JsonPath;
import fabriqon.app.common.model.Money;
import fabriqon.app.http.controllers.manufacturing.ManufacturingWorkstationsController;
import fabriqon.misc.Json;
import lombok.SneakyThrows;
import org.springframework.http.MediaType;

import java.util.Currency;

import static fabriqon.e2e.data.Utils.smallRandomInt;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class ManufacturingWorkstation {

    private final Account account;
    private final Data data = new Data();
    public String workstationId;

    public ManufacturingWorkstation(Account account) {
        this.account = account;
    }

    public ManufacturingWorkstation withName(String name) {
        this.data.name = name;
        return this;
    }

    @SneakyThrows
    public ManufacturingWorkstation create() {
        var result = account.dsl().mvc()
                .perform(post("/manufacturing/workstations/create")
                        .content(Json.write(new ManufacturingWorkstationsController.ManufacturingWorkstationDefinition(
                                data.name, data.costPerHour, null
                        )))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("id", notNullValue(String.class)))
                .andReturn();
        workstationId = JsonPath.read(result.getResponse().getContentAsString(), "$.id");
        return this;
    }

    public Account account() {
        return account;
    }

    private static class Data {
        String name = "Cutting machine " + smallRandomInt();
        Money costPerHour = new Money(30000, Currency.getInstance("RON"));
    }
}
