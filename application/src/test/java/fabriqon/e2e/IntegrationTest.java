package fabriqon.e2e;

import com.opentable.db.postgres.embedded.EmbeddedPostgres;
import fabriqon.app.FabriqonApplication;
import fabriqon.e2e.dsl.DSL;
import jakarta.annotation.PostConstruct;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.testcontainers.utility.DockerImageName;

import java.io.IOException;
import java.sql.SQLException;

@RunWith(SpringRunner.class)
@WebAppConfiguration
@SpringBootTest(classes = FabriqonApplication.class, webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@AutoConfigureMockMvc
@TestPropertySource(locations = "classpath:application-integrationtest.properties")
@DirtiesContext//required to rebuild the DB with liquibase
@Ignore//this test is only the parent of the integration tests
public class IntegrationTest {

    static EmbeddedPostgres postgres;
    @Autowired
    protected MockMvc mvc;
    protected DSL setup;

    @BeforeClass
    public static void beforeClass() {
        try {
            postgres = EmbeddedPostgres.builder().setImage(DockerImageName.parse("library/postgres:14.9")).start();
            SetupUtils.addContext(SetupUtils.DATASOURCE, postgres.getPostgresDatabase());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @AfterClass
    public static void afterClass() {
        try {
            postgres.close();
            SetupUtils.clearContext(SetupUtils.DATASOURCE);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @PostConstruct
    public void postConstruct() {
        setup = new DSL(mvc);
    }


}
