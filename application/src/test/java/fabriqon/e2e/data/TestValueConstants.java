package fabriqon.e2e.data;

import java.util.LinkedList;
import java.util.List;
import java.util.Queue;

public class TestValueConstants {

    public static final Queue<String> PRODUCTS = new LinkedList<>(List.of(
            "chair", "table", "bed", "spoon", "fork", "knife", "cup", "glass", "bottle", "pen", "paper", "ruler", "eraser", "cloth", "pencil", "book", "globe", "board"
    ));

    public static final Queue<String> SUBASSEMBLIES = new LinkedList<>(List.of(
            "control unit", "rack"
    ));

    public static final Queue<String> MATERIALS = new LinkedList<>(List.of(
            "metal", "plastic", "wood", "glass", "ceramics", "synthetic fibres", "composites", "timber", "gasoline", "paint", "nylon", "timber 100x10", "timber 100x20", "timber 100x30", "timber 200x10", "timber 300x10"
    ));

    public static final Queue<String> CATEGORIES = new LinkedList<>(List.of(
            "hardware", "furniture", "food", "toys", "clothes", "drugs", "desserts"
    ));

    public static final Queue<String> SUPPLIERS = new LinkedList<>(List.of(
            "IKEA", "JYSK", "CALENDA", "ROVERE", "MOBILI", "WOOD BUCOVINA", "CADORO", "LEMN", "CIOATA", "WOOK", "MOOD", "OAK TREE", "LEMONGRASS", "MOB1", "MOB2", "MOB3", "MOB4", "MOB5", "MOB6", "MOB7", "MOB8", "MOB9"
    ));

    public static final Queue<String> CUSTOMERS = new LinkedList<>(List.of(
            "BARACCA", "VIA", "KUPAJ", "CHIOS", "SAMSARA"
    ));


    public static final Queue<String> EMPLOYEES = new LinkedList<>(List.of(
            "Manny Delgado", "Jay Pritchett", "Gloria Delgado", "Phil Dunphy", "Claire Dunphy", "Alex Dunphy", "Haley Dunphy",
            "Luke Dunphy", "Mitchell Pritchett", "Cameron Tucker", "Lily Pritchett Tucker"
    ));

}
