package fabriqon.app.config;

import fabriqon.Notification;
import fabriqon.Notifier;
import fabriqon.Recipient;
import fabriqon.app.business.users.Auth0;
import fabriqon.e2e.SetupUtils;
import fabriqon.e2e.data.Utils;
import fabriqon.email.EmailSender;
import fabriqon.email.NoopEmailSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtException;
import org.springframework.web.filter.CommonsRequestLoggingFilter;

import javax.sql.DataSource;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;


@Configuration
public class TestConfiguration {

    @Bean
    @ConditionalOnProperty(name = "test.configuration", havingValue = "true")
    public DataSource dataSource() {
        return SetupUtils.getDataSource();
    }

    @Bean
    @Primary
    @ConditionalOnProperty(name = "test.configuration", havingValue = "true")
    public JwtDecoder jwtDecoder() {
        return new MockJwtDecoder();
    }

    @Bean
    public CommonsRequestLoggingFilter requestLoggingFilter() {
        CommonsRequestLoggingFilter loggingFilter = new CommonsRequestLoggingFilter();
        loggingFilter.setIncludeClientInfo(false);
        loggingFilter.setIncludeQueryString(true);
        loggingFilter.setIncludePayload(true);
        loggingFilter.setMaxPayloadLength(64000);
        return loggingFilter;
    }

    @Bean("notifier")
    @ConditionalOnProperty(name = "test.configuration", havingValue = "true")
    @Primary
    public Notifier notifier() {
        return new TestNotifier();
    }

    @Bean("emailSender")
    @ConditionalOnProperty(name = "test.configuration", havingValue = "true")
    @Primary
    public EmailSender emailSender() {
        return new NoopEmailSender();
    }

    @Bean("auth0")
    @ConditionalOnProperty(name = "test.configuration", havingValue = "true")
    @Primary
    public Auth0 auth0() {
        return new TestAuth0();
    }

    static class MockJwtDecoder implements JwtDecoder {

        @Override
        public Jwt decode(String token) throws JwtException {
            Instant issuedAt = Instant.now();
            Instant expiresAt = Instant.now().plus(Duration.ofMinutes(30));
            return new Jwt(
                    UUID.randomUUID().toString(),
                    issuedAt,
                    expiresAt,
                    Map.of(
                            "iss", "http://localhost:8080",
                            "sub", "test_user",
                            "aud", "http://localhost:8080",
                            "exp", expiresAt.getEpochSecond(),
                            "iat", issuedAt.getEpochSecond(),
                            "jti", UUID.randomUUID().toString()
                    ),
                    Map.of(
                            "accountId", token,
                            "sub", UUID.randomUUID().toString()
                    )
            );
        }
    }


    @Slf4j
    static class TestNotifier implements Notifier {
        @Override
        public void send(Notification notification, List<Recipient> recipients) {
            log.info("sending notification: [{}] to [{}]", notification, recipients);
        }
    }

    static class TestAuth0 implements Auth0 {
        @Override
        public UserData userData(String userId) {
            return new UserData("random_"  + Utils.smallRandomInt());
        }
    }

}
