package fabriqon.app.business.goods;

import fabriqon.app.common.model.MeasurementUnit;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

class DimensionToMeasurementUnitConverterTest {

    private DimensionToMeasurementUnitConverter converter;

    @BeforeEach
    void setUp() {
        converter = new DimensionToMeasurementUnitConverter(null);
    }

    // Helper method to create Dimensions for tests
    private Dimensions createDimensions(BigDecimal length, BigDecimal width,
                                        BigDecimal height, BigDecimal weight) {
        return new Dimensions(length, width, height, weight);
    }

    @Test
    @DisplayName("Test area conversion with the metal sheet example")
    void testAreaConversion_MetalSheet() {
        // Metal sheet of 2m x 1m with an order for 50cm x 50cm (which is 0.5m x 0.5m)
        Dimensions perUnitDimensions = createDimensions(
                new BigDecimal("2"),
                new BigDecimal("1"),
                null,
                null);

        Dimensions orderDimensions = createDimensions(
                new BigDecimal("0.5"),
                new BigDecimal("0.5"),
                null,
                null);

        // Expected: 0.5m * 0.5m / (2m * 1m) = 0.25 / 2 = 0.125 (or a 12.5% piece)
        BigDecimal expected = new BigDecimal("0.125000");
        BigDecimal result = converter.convert(perUnitDimensions, orderDimensions, MeasurementUnit.PIECE);

        assertEquals(expected, result);
    }

    @Test
    @DisplayName("Test volumetric conversion")
    void testVolumetricConversion() {
        // A unit cube of 1m x 1m x 1m with an order for 0.5m x 0.5m x 0.5m
        Dimensions perUnitDimensions = createDimensions(
                new BigDecimal("1"),
                new BigDecimal("1"),
                new BigDecimal("1"),
                null);

        Dimensions orderDimensions = createDimensions(
                new BigDecimal("0.5"),
                new BigDecimal("0.5"),
                new BigDecimal("0.5"),
                null);

        // Expected: 0.5m * 0.5m * 0.5m / (1m * 1m * 1m) = 0.125 (or 12.5% of a piece)
        BigDecimal expected = new BigDecimal("0.125000");
        BigDecimal result = converter.convert(perUnitDimensions, orderDimensions, MeasurementUnit.PIECE);

        assertEquals(expected, result);
    }

    @Test
    @DisplayName("Test length conversion")
    void testLengthConversion() {
        // A unit rod of 10m with an order for 2.5m
        Dimensions perUnitDimensions = createDimensions(
                new BigDecimal("10"),
                null,
                null,
                null);

        Dimensions orderDimensions = createDimensions(
                new BigDecimal("2.5"),
                null,
                null,
                null);

        // Expected: 2.5m / 10m = 0.25 (or 25% of a piece)
        BigDecimal expected = new BigDecimal("0.250000");
        BigDecimal result = converter.convert(perUnitDimensions, orderDimensions, MeasurementUnit.PIECE);

        assertEquals(expected, result);
    }

    @Test
    @DisplayName("Test mass conversion")
    void testMassConversion() {
        // A unit weight of 1000kg with an order for 250kg
        Dimensions perUnitDimensions = createDimensions(
                null,
                null,
                null,
                new BigDecimal("1000"));

        Dimensions orderDimensions = createDimensions(
                null,
                null,
                null,
                new BigDecimal("250"));

        // Expected: 250kg / 1000kg = 0.25 (or 25% of a piece)
        BigDecimal expected = new BigDecimal("0.250000");
        BigDecimal result = converter.convert(perUnitDimensions, orderDimensions, MeasurementUnit.PIECE);

        assertEquals(expected, result);
    }

    @Test
    @DisplayName("Test area to mass conversion")
    void testAreaToMassConversion() {
        // A unit sheet with 10 x 1 dimensions and 100kg weight
        Dimensions perUnitDimensions = createDimensions(
                BigDecimal.TEN,
                BigDecimal.ONE,
                null,
                new BigDecimal("100"));

        // Order for 1 x 1 sheet
        Dimensions orderDimensions = createDimensions(
                BigDecimal.ONE,
                BigDecimal.ONE,
                null,
                null);

        // Expected: (1 x 1) / (10 x 1) = 0.1 ratio * 100kg = 10kg
        BigDecimal expected = new BigDecimal("10.000000");
        BigDecimal result = converter.convert(perUnitDimensions, orderDimensions, MeasurementUnit.KG);

        assertEquals(expected, result);
    }

    @Test
    @DisplayName("Test volumetric to mass conversion")
    void testVolumetricToMassConversion() {
        // A unit volume with 2 x 3 x 4 dimensions and 240kg weight
        Dimensions perUnitDimensions = createDimensions(
                new BigDecimal("2"),
                new BigDecimal("3"),
                new BigDecimal("4"),
                new BigDecimal("240"));

        // Order for 1 x 1 x 1 volume
        Dimensions orderDimensions = createDimensions(
                BigDecimal.ONE,
                BigDecimal.ONE,
                BigDecimal.ONE,
                null);

        // Expected: (1 x 1 x 1) / (2 x 3 x 4) = 1/24 ratio * 240kg = 10kg
        BigDecimal expected = new BigDecimal("10.000080");
        BigDecimal result = converter.convert(perUnitDimensions, orderDimensions, MeasurementUnit.KG);

        assertEquals(expected, result);
    }

    @Test
    @DisplayName("Test exception for null dimensions")
    void testNullDimensions() {
        Dimensions perUnitDimensions = createDimensions(
                new BigDecimal("2"),
                new BigDecimal("1"),
                null,
                null);

        assertThrows(IllegalArgumentException.class, () -> {
            converter.convert(perUnitDimensions, null, MeasurementUnit.PIECE);
        });

        assertThrows(IllegalArgumentException.class, () -> {
            converter.convert(null, perUnitDimensions, MeasurementUnit.PIECE);
        });
    }

    @Test
    @DisplayName("Test exception for zero dimensions")
    void testZeroDimensions() {
        // Area with zero width
        Dimensions perUnitDimensionsZeroWidth = createDimensions(
                new BigDecimal("2"),
                BigDecimal.ZERO,
                null,
                null);

        Dimensions orderDimensions = createDimensions(
                new BigDecimal("0.5"),
                new BigDecimal("0.5"),
                null,
                null);

        assertThrows(IllegalArgumentException.class, () -> {
            converter.convert(perUnitDimensionsZeroWidth, orderDimensions, MeasurementUnit.PIECE);
        });

        // Length with zero length
        Dimensions perUnitDimensionsZeroLength = createDimensions(
                BigDecimal.ZERO,
                null,
                null,
                null);

        Dimensions lengthOrderDimensions = createDimensions(
                new BigDecimal("2.5"),
                null,
                null,
                null);

        assertThrows(IllegalArgumentException.class, () -> {
            converter.convert(perUnitDimensionsZeroLength, lengthOrderDimensions, MeasurementUnit.PIECE);
        });
    }

    @Test
    @DisplayName("Test missing weight for mass conversion")
    void testMissingWeightForMassConversion() {
        // Area dimensions without weight
        Dimensions perUnitDimensions = createDimensions(
                new BigDecimal("2"),
                new BigDecimal("1"),
                null,
                null); // No weight!

        Dimensions orderDimensions = createDimensions(
                new BigDecimal("0.5"),
                new BigDecimal("0.5"),
                null,
                null);

        assertThrows(IllegalArgumentException.class, () -> {
            converter.convert(perUnitDimensions, orderDimensions, MeasurementUnit.KG);
        });
    }
}