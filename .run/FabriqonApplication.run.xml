<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="FabriqonApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
    <module name="application" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="fabriqon.app.FabriqonApplication" />
    <option name="VM_PARAMETERS" value="-Duser.timezone=&quot;UTC&quot;" />
    <extension name="net.ashald.envfile">
      <option name="IS_ENABLED" value="true" />
      <option name="IS_SUBST" value="false" />
      <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
      <option name="IS_IGNORE_MISSING_FILES" value="false" />
      <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
      <ENTRIES>
        <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        <ENTRY IS_ENABLED="true" PARSER="env" IS_EXECUTABLE="false" PATH="local-config/local.env" />
      </ENTRIES>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>