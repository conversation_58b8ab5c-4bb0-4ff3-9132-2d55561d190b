stages:
  - build_release
  - deploy

build:
  stage: build_release
  image: docker
  only:
    - main
#  services:
#    - docker:dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_JOB_TOKEN $CI_REGISTRY
  script:
    - apk add make
    - make build
    - docker image tag fabriqon/backend registry.gitlab.com/ezbizproject/ezbiz-be/fabriqon/backend
    - docker push registry.gitlab.com/ezbizproject/ezbiz-be/fabriqon/backend

trigger_deploy:
  stage: deploy
  only:
    - main
  trigger:
    project: ezbizproject/test-deployer